# Patrones del Sistema: Arquitectura de la CLI de Scripts

Este documento describe la arquitectura y los patrones de diseño clave para la nueva herramienta CLI de scripts.

## Arquitectura General

La herramienta está diseñada siguiendo un patrón de **Arquitectura por Capas** y el **Patrón de Comando**, lo que promueve una clara separación de responsabilidades.

```mermaid
graph TD
    A[Entrada de Usuario (CLI)] --> B(cli.mjs - Orquestador);
    B --> C{Comandos};
    C --> D[db:start];
    C --> E[db:migrate];
    C --> F[app:test];
    C --> G[deps:check];

    subgraph "Capa de Comandos"
        D; E; F; G;
    end

    D & E & F & G --> H{Capa de Lógica de Negocio};

    subgraph "Capa de Lógica de Negocio (lib/)"
        H --> I[migrator.mjs];
        H --> J[dependency-analyzer.mjs];
        H --> K[db-client.mjs];
    end

    I & J & K --> L{Capa de Utilidades};

    subgraph "Capa de Utilidades (utils/)"
        L --> M[logger.mjs];
        L --> N[paths.mjs];
        L --> O[shell.mjs];
    end

    K --> P[Base de Datos];
    O --> Q[Sistema de Archivos/Shell];
```

### Capas

1.  **Capa de Orquestación (`cli.mjs`)**:
    *   **Responsabilidad**: Interpretar los argumentos de la línea de comandos y delegar la ejecución al comando apropiado.
    *   **Patrón**: Orquestador de Comandos.
    *   **Implementación**: Utiliza la librería `commander` para definir la estructura de la CLI (comandos, opciones, ayuda). No contiene lógica de negocio.

2.  **Capa de Comandos (`commands/`)**:
    *   **Responsabilidad**: Contener la lógica de alto nivel para cada comando específico. Orquesta las llamadas a la capa de lógica de negocio y gestiona la interacción con el usuario para ese comando.
    *   **Patrón**: Patrón de Comando. Cada archivo es un comando ejecutable.
    *   **Ejemplo**: `db-migrate.mjs` maneja la confirmación del usuario y luego invoca a `migrator.mjs` para hacer el trabajo pesado.

3.  **Capa de Lógica de Negocio (`lib/`)**:
    *   **Responsabilidad**: Implementar la lógica de negocio principal y las abstracciones complejas. Es el corazón del sistema.
    *   **Patrón**: Módulos de Servicio.
    *   **Ejemplos**:
        *   `db-client.mjs`: Abstrae toda la comunicación con la base de datos.
        *   `migrator.mjs`: Encapsula la lógica de encontrar y ejecutar migraciones SQL.

4.  **Capa de Utilidades (`utils/`)**:
    *   **Responsabilidad**: Proporcionar funciones de bajo nivel, reutilizables y sin estado que no contienen lógica de negocio.
    *   **Patrón**: Módulos de Utilidad.
    *   **Ejemplos**: `logger.mjs` para la salida de consola, `paths.mjs` para la gestión de rutas.

## Patrones de Diseño Clave

1.  **Inyección de Dependencias (Implícita)**:
    *   Los módulos de capas superiores importan y utilizan módulos de capas inferiores. Esto crea un flujo de dependencias unidireccional (Comandos -> Lógica -> Utilidades), lo que facilita las pruebas y el razonamiento sobre el código.

2.  **Configuración Centralizada**:
    *   Toda la configuración (rutas, credenciales, listas de ignorados) se carga y se exporta desde el directorio `config/`. Los módulos que necesitan configuración la importan desde allí, en lugar de leer archivos o variables de entorno por sí mismos. Esto desacopla la lógica de la configuración.

3.  **Abstracción de la Base de Datos**:
    *   Toda la interacción con la base de datos se realiza a través del `db-client.mjs`. Ningún otro módulo (excepto el `migrator` que lo usa) debe saber cómo se establece una conexión o se ejecuta una consulta. Esto permite cambiar la librería de base de datos o la estrategia de conexión en un solo lugar.

4.  **Manejo de Errores**:
    *   Los errores se capturan en la capa más baja posible (ej. en `db-client` o `shell`) y se relanzan como errores más específicos si es necesario. La capa de Comandos es la responsable final de capturar cualquier error no manejado y presentarlo al usuario de forma amigable usando el `logger`.
