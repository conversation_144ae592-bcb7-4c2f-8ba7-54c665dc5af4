# Resumen del Proyecto: Refactorización de Scripts de Utilidad

**Objetivo Principal:** Refactorizar la colección existente de scripts de utilidad en el directorio `scripts/` para transformarlos en una herramienta de línea de comandos (CLI) unificada, modular, robusta y mantenible.

**Alcance:**

1.  **Análisis y Diagnóstico:** Evaluar los scripts existentes (`check-dependencies.mjs`, `test_app.mjs`, y todos los scripts en `scripts/db/`) para identificar deficiencias en diseño, robustez y mantenibilidad.
2.  **Diseño de Nueva Arquitectura:**
    *   Crear una estructura de directorios lógica que separe responsabilidades: `commands`, `config`, `lib`, `utils`.
    *   Diseñar una interfaz CLI unificada usando la librería `commander`.
    *   Centralizar la configuración y la gestión de rutas.
3.  **Implementación de la Refactorización:**
    *   Reemplazar la dependencia de `docker exec` con un cliente de base de datos de red (`postgres`).
    *   Crear un sistema de migración de base de datos robusto.
    *   Reescribir cada script como un "comando" modular dentro de la nueva estructura.
4.  **Documentación:** Inicializar y mantener un `MemoryBank` para documentar la arquitectura, decisiones y progreso del proyecto.

**Entregables Clave:**

*   Una única herramienta CLI (`scripts/cli.mjs`) que sirva como punto de entrada para todas las operaciones de scripting.
*   Un conjunto de comandos modulares y bien estructurados.
*   Un sistema de migración de base de datos fiable.
*   Documentación completa del proyecto en el `MemoryBank`.

**Tecnologías Involucradas:**

*   Node.js (ESM)
*   PostgreSQL (a través de la librería `postgres`)
*   Docker (para el entorno de Supabase)
*   Commander.js (para la CLI)
*   Chalk (para el formato de la consola)
