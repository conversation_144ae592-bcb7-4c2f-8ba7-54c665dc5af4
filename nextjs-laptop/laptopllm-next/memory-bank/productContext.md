# Contexto del Producto: CLI de Scripts de Proyecto

## ¿Por qué existe este proyecto?

El proyecto `llm-laptop-lens` depende de una serie de scripts para tareas de desarrollo y mantenimiento, como la gestión de dependencias, las migraciones de bases de datos y las pruebas de integración. Inicialmente, estos scripts se crearon de forma aislada y para propósitos específicos. Con el tiempo, han crecido en número y complejidad, pero sin una estructura cohesiva.

Este proyecto de refactorización existe para abordar la "deuda técnica" acumulada en estos scripts. El objetivo es transformar un conjunto de herramientas dispares y frágiles en una utilidad de línea de comandos (CLI) robusta, profesional y centralizada que mejore la experiencia del desarrollador.

## ¿Qué problemas resuelve?

1.  **Fragilidad:** Los scripts actuales dependen de métodos poco fiables como `docker exec` y el parseo manual de la salida de `psql`, lo que los hace propensos a fallar con cambios menores en el entorno.
2.  **Mantenimiento Difícil:** La lógica monolítica y la configuración codificada hacen que sea difícil y arriesgado modificar o ampliar los scripts.
3.  **Falta de Cohesión:** No hay una forma unificada de ejecutar o descubrir los scripts disponibles. Cada uno es una isla, lo que aumenta la carga cognitiva para los desarrolladores.
4.  **Portabilidad Limitada:** El uso de rutas absolutas y dependencias de entorno específicas impide que otros desarrolladores puedan configurar y utilizar los scripts fácilmente.
5.  **Experiencia de Usuario (Desarrollador) Pobre:** La falta de una interfaz clara, mensajes de error estandarizados y un flujo de trabajo consistente reduce la productividad y aumenta la frustración.

## ¿Cómo debería funcionar?

La solución final debería funcionar como una única herramienta CLI invocada desde la raíz del proyecto. Debería proporcionar una experiencia similar a la de herramientas de desarrollo estándar como `git` o `npm`.

**Flujo de Usuario Ideal:**

1.  Un desarrollador abre una terminal en la raíz del proyecto.
2.  Puede ver todos los comandos disponibles ejecutando `node scripts/cli.mjs --help`.
3.  Puede ejecutar tareas complejas con comandos simples y claros, por ejemplo:
    *   `node scripts/cli.mjs db:migrate --env local`
    *   `node scripts/cli.mjs app:test`
    *   `node scripts/cli.mjs deps:check --fix`
4.  Recibe feedback claro y consistente en la consola, con colores para indicar éxito, advertencia o error.
5.  La configuración de los scripts (ej. rutas, listas de ignorados) se gestiona en archivos de configuración dedicados, no en el código.
