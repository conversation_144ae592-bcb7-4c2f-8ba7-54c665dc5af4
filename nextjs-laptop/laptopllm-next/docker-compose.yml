version: "3.8"
services:
  crawl4ai:
    image: unclecode/crawl4ai:latest
    container_name: crawl4ai
    ports:
      - "11235:11235"
    shm_size: '1g' # Asignar memoria compartida suficiente para el navegador
    networks:
      - app-network

  nextjs:
    build:
      context: .
      dockerfile: Dockerfile # Asumiendo que existe un Dockerfile para Next.js
    ports:
      - "3000:3000"
    environment:
      - CRAWLAI_API_URL=http://crawl4ai:11235
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    networks:
      - app-network
    depends_on:
      - crawl4ai

networks:
  app-network:
    driver: bridge
