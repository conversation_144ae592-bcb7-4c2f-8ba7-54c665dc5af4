# 04 - Pruebas

## Estrategias de Pruebas

*   **Pruebas Unitarias**: Escribir pruebas unitarias para funciones puras, lógica de negocio y componentes aislados.
    ```typescript
    // Testing para servicios de scraping
    describe('FirecrawlService', () => {
      it('should extract laptop data correctly', async () => {
        const mockData = { brand: 'Dell', model: 'XPS 13', price: 1200 };
        mockFirecrawlAPI.mockResolvedValue(mockData);

        const result = await firecrawlService.scrapeLaptop('https://example.com/laptop');
        expect(result).toMatchObject(mockData);
      });

      it('should handle rate limiting', async () => {
        mockFirecrawlAPI.mockRejectedValue(new RateLimitError());
        await expect(firecrawlService.scrapeLaptop('https://example.com')).rejects.toThrow();
      });
    });

    // Testing para componentes de búsqueda
    describe('LaptopSearchFilters', () => {
      it('should filter by price range', () => {
        render(<LaptopSearchFilters onFilterChange={mockOnChange} />);
        const priceSlider = screen.getByRole('slider', { name: /price range/i });
        fireEvent.change(priceSlider, { target: { value: '1000' } });
        expect(mockOnChange).toHaveBeenCalledWith({ maxPrice: 1000 });
      });

      it('should filter by LLM compatibility', () => {
        render(<LaptopSearchFilters onFilterChange={mockOnChange} />);
        const compatibilityFilter = screen.getByRole('checkbox', { name: /llm compatible/i });
        fireEvent.click(compatibilityFilter);
        expect(mockOnChange).toHaveBeenCalledWith({ llmCompatible: true });
      });
    });
    ```

*   **Pruebas de Integración**: Realizar pruebas de integración específicas para:
    - Integración Firecrawl API ↔ Servicio de Scraping
    - Servicio de Scraping ↔ Base de Datos (Prisma)
    - Componentes de Búsqueda ↔ Servicios de Filtrado
    - Cálculo de Compatibilidad LLM ↔ Especificaciones de Laptop

*   **Pruebas End-to-End (E2E)**: Implementar pruebas E2E para flujos críticos:
    - Flujo completo de búsqueda de laptops
    - Configuración de fuentes de scraping
    - Visualización de resultados de compatibilidad LLM
    - Dashboard de monitoreo de scraping

## Cobertura de Código

*   **Objetivos de Cobertura Específicos**:
    - **90%+ para servicios de scraping** (críticos para el negocio)
    - **85%+ para servicios de compatibilidad LLM** (lógica de negocio core)
    - **80%+ para componentes de búsqueda y filtrado**
    - **70%+ para componentes de UI y dashboard**
    - **60%+ para componentes de presentación**

*   **Herramientas de Cobertura**: Utilizar Vitest con configuración específica:
    ```typescript
    // vitest.config.ts
    export default defineConfig({
      test: {
        coverage: {
          provider: 'v8',
          reporter: ['text', 'json', 'html'],
          exclude: [
            'src/components/ui/**', // Componentes shadcn/ui
            '**/*.stories.tsx',     // Storybook stories
            '**/*.test.tsx',        // Archivos de test
            'src/types/**'          // Definiciones de tipos
          ],
          thresholds: {
            'src/services/scraping/**': { lines: 90, functions: 90, branches: 85 },
            'src/services/llm/**': { lines: 85, functions: 85, branches: 80 },
            'src/features/**/services/**': { lines: 80, functions: 80, branches: 75 }
          }
        }
      }
    });
    ```

## Uso de Mocks y Stubs

*   **Mocks para Dependencias Externas**: Usar mocks para simular el comportamiento de APIs externas, bases de datos o servicios de terceros en pruebas unitarias y de integración.
*   **Stubs para Datos de Prueba**: Crear stubs para datos de prueba consistentes y predecibles.

## Imprescindible:

*   **Vitest + Testing Library setup**
*   **Unit tests (80%+ coverage)**
*   **Integration tests para flujos críticos**
*   **E2E tests con Playwright**
*   **Mock strategies (MSW para APIs)**
*   **Test-driven development guidelines**
