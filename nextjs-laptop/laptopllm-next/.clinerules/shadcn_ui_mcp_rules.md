# Reglas para usar shadcn-ui-mcp-server

## Regla General

Cuando una tarea requiere construir o modificar una interfaz de usuario, **DEBES** usar las herramientas disponibles en el servidor shadcn-ui MCP.

---

## Regla de Planificación

Cuando planifiques una construcción de UI usando shadcn:

1. **Descubrir Assets**: Primero, usa `list_components()` y `list_blocks()` para ver todos los componentes y bloques disponibles en el servidor MCP.

2. **Mapear Solicitud a Assets**: Analiza la solicitud del usuario y mapea los elementos de UI requeridos a los componentes y bloques disponibles.

3. **Priorizar Bloques**: Debes priorizar el uso de bloques (`get_block()`) siempre que sea posible para patrones de UI comunes y complejos (ej. páginas de login, calendarios, dashboards). Los bloques proporcionan más estructura y aceleran el desarrollo. Usa componentes individuales (`get_component()`) para necesidades más pequeñas y específicas.

---

## Regla de Implementación

Cuando implementes la UI:

1. **Obtener una Demo Primero**: Antes de usar un componente, **DEBES** llamar la herramienta `get_component_demo(component_name)`. Esto es crítico para entender cómo se usa el componente, sus props requeridas y su estructura.

2. **Recuperar el Código**:
   - Para un componente individual, llama `get_component(component_name)`
   - Para un bloque compuesto, llama `get_block(block_name)`

3. **Implementar Correctamente**: Integra el código recuperado en la aplicación, personalizándolo con las props y lógica necesarias para cumplir con la solicitud del usuario.

---

## Flujo de Trabajo Recomendado

```
1. Analizar solicitud del usuario
2. Ejecutar list_components() y list_blocks()
3. Identificar componentes/bloques relevantes
4. Para cada componente/bloque necesario:
   a. Ejecutar get_component_demo() o equivalente
   b. Ejecutar get_component() o get_block()
   c. Integrar en la aplicación
5. Personalizar y probar la implementación
```

---

## Notas Importantes

- Siempre consulta las demos antes de implementar
- Prioriza bloques para patrones complejos
- Usa componentes individuales para elementos específicos
- Personaliza según las necesidades del usuario
- Mantén la estructura y mejores prácticas de shadcn/ui