# 11 - Estándares de Web Scraping

## Configuración de Scrapers

*   **Firecrawl como Servicio Principal**: Utilizar Firecrawl MCP como motor principal de scraping:
    ```typescript
    // Configuración base de Firecrawl
    const firecrawlConfig = {
      apiKey: process.env.FIRECRAWL_API_KEY,
      baseUrl: process.env.FIRECRAWL_BASE_URL || 'https://api.firecrawl.dev',
      defaultTimeout: 30000,
      maxRetries: 3,
      retryDelay: 1000
    };

    // Configuración específica por sitio
    const siteConfigs = {
      'revolico.com': {
        requiresFullBrowserAutomation: true,
        selectors: {
          title: '.classified-title',
          price: '.price-value',
          description: '.classified-description',
          specs: '.specs-list li'
        },
        rateLimit: 2000, // 2 segundos entre requests
        maxConcurrent: 2
      },
      'laptop-ventas.ola.click': {
        requiresFullBrowserAutomation: false,
        selectors: {
          title: 'h1.product-title',
          price: '.price-current',
          specs: '.product-specs .spec-item'
        },
        rateLimit: 1500,
        maxConcurrent: 3
      }
    };
    ```

*   **Puppeteer como Fallback**: Configurar Puppeteer para casos donde Firecrawl no funcione:
    ```typescript
    const puppeteerConfig = {
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu'
      ],
      defaultViewport: {
        width: 1366,
        height: 768
      },
      timeout: 30000
    };
    ```

## Rate Limiting y Respeto a Robots.txt

*   **Rate Limiting Estricto**: Implementar límites de velocidad conservadores:
    ```typescript
    const rateLimits = {
      'revolico.com': {
        requestsPerMinute: 30,
        burstLimit: 5,
        backoffMultiplier: 2
      },
      'laptop-ventas.ola.click': {
        requestsPerMinute: 40,
        burstLimit: 8,
        backoffMultiplier: 1.5
      },
      default: {
        requestsPerMinute: 20,
        burstLimit: 3,
        backoffMultiplier: 3
      }
    };
    ```

*   **Respeto a Robots.txt**: Verificar y respetar robots.txt antes de scraping:
    ```typescript
    async function checkRobotsTxt(domain: string): Promise<boolean> {
      try {
        const robotsUrl = `https://${domain}/robots.txt`;
        const response = await fetch(robotsUrl);
        const robotsText = await response.text();
        
        // Verificar si nuestro user-agent está permitido
        return !robotsText.includes('Disallow: /') || 
               robotsText.includes('Allow: /');
      } catch {
        // Si no hay robots.txt, proceder con precaución
        return true;
      }
    }
    ```

## Manejo de Errores y Reintentos

*   **Estrategia de Reintentos Exponencial**:
    ```typescript
    async function scrapeWithRetry<T>(
      scrapeFunction: () => Promise<T>,
      maxRetries: number = 3,
      baseDelay: number = 1000
    ): Promise<T> {
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          return await scrapeFunction();
        } catch (error) {
          if (attempt === maxRetries) throw error;
          
          const delay = baseDelay * Math.pow(2, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
      throw new Error('Max retries exceeded');
    }
    ```

*   **Manejo de Errores Específicos**:
    ```typescript
    class ScrapingError extends Error {
      constructor(
        message: string,
        public code: 'RATE_LIMITED' | 'TIMEOUT' | 'BLOCKED' | 'PARSE_ERROR',
        public url: string,
        public retryable: boolean = true
      ) {
        super(message);
        this.name = 'ScrapingError';
      }
    }

    // Manejo específico por tipo de error
    function handleScrapingError(error: unknown, url: string): ScrapingError {
      if (error instanceof Error) {
        if (error.message.includes('429')) {
          return new ScrapingError('Rate limited', 'RATE_LIMITED', url, true);
        }
        if (error.message.includes('timeout')) {
          return new ScrapingError('Request timeout', 'TIMEOUT', url, true);
        }
        if (error.message.includes('blocked')) {
          return new ScrapingError('IP blocked', 'BLOCKED', url, false);
        }
      }
      return new ScrapingError('Parse error', 'PARSE_ERROR', url, true);
    }
    ```

## Validación y Limpieza de Datos

*   **Validación de Datos Extraídos**:
    ```typescript
    const ScrapedLaptopSchema = z.object({
      title: z.string().min(5).max(200),
      price: z.number().positive().max(50000),
      currency: z.enum(['USD', 'EUR', 'CUP']),
      description: z.string().max(2000).optional(),
      specifications: z.object({
        cpu: z.string().optional(),
        ram: z.number().min(2).max(128).optional(),
        storage: z.number().min(64).optional(),
        gpu: z.string().optional(),
        screenSize: z.number().min(10).max(20).optional()
      }),
      imageUrls: z.array(z.string().url()).max(10),
      sourceUrl: z.string().url(),
      scrapedAt: z.date(),
      isActive: z.boolean().default(true)
    });

    function validateScrapedData(rawData: unknown): ScrapedLaptop {
      return ScrapedLaptopSchema.parse(rawData);
    }
    ```

*   **Limpieza y Normalización**:
    ```typescript
    function cleanScrapedData(rawData: any): Partial<ScrapedLaptop> {
      return {
        title: DOMPurify.sanitize(rawData.title?.trim() || ''),
        price: parseFloat(String(rawData.price).replace(/[^\d.]/g, '')),
        description: DOMPurify.sanitize(rawData.description?.trim() || ''),
        specifications: {
          cpu: normalizeProcessorName(rawData.cpu),
          ram: parseMemorySize(rawData.ram),
          storage: parseStorageSize(rawData.storage),
          gpu: normalizeGpuName(rawData.gpu)
        },
        imageUrls: (rawData.images || [])
          .filter((url: string) => isValidImageUrl(url))
          .slice(0, 10)
      };
    }
    ```

## Monitoreo y Logging

*   **Logging Detallado de Scraping**:
    ```typescript
    interface ScrapingLog {
      timestamp: Date;
      url: string;
      source: string;
      status: 'success' | 'error' | 'rate_limited';
      duration: number;
      itemsExtracted: number;
      errorMessage?: string;
      retryAttempt?: number;
    }

    async function logScrapingActivity(log: ScrapingLog): Promise<void> {
      await prisma.scrapingLog.create({
        data: log
      });
      
      // También log a consola en desarrollo
      if (process.env.NODE_ENV === 'development') {
        console.log(`[SCRAPING] ${log.status.toUpperCase()}: ${log.url} (${log.duration}ms)`);
      }
    }
    ```

*   **Métricas de Rendimiento**:
    ```typescript
    interface ScrapingMetrics {
      totalRequests: number;
      successfulRequests: number;
      failedRequests: number;
      averageResponseTime: number;
      rateLimitHits: number;
      lastSuccessfulScrape: Date;
    }

    async function updateScrapingMetrics(source: string, success: boolean, duration: number): Promise<void> {
      // Actualizar métricas en Redis o base de datos
      const key = `scraping:metrics:${source}`;
      await redis.hincrby(key, 'totalRequests', 1);
      await redis.hincrby(key, success ? 'successfulRequests' : 'failedRequests', 1);
      await redis.hset(key, 'lastResponseTime', duration);
      
      if (success) {
        await redis.hset(key, 'lastSuccessfulScrape', new Date().toISOString());
      }
    }
    ```

## Cumplimiento Legal y Ético

*   **Términos de Servicio**: Revisar y cumplir con los términos de servicio de cada sitio.
*   **Datos Personales**: No extraer ni almacenar información personal de usuarios.
*   **Frecuencia Responsable**: Mantener frecuencia de scraping que no impacte el rendimiento del sitio objetivo.
*   **Identificación**: Usar User-Agent identificable y proporcionar información de contacto.

## No negociable:

*   **Rate limiting estricto**
*   **Respeto a robots.txt**
*   **Manejo robusto de errores**
*   **Validación de datos extraídos**
*   **Logging completo de actividades**
*   **Cumplimiento legal y ético**
