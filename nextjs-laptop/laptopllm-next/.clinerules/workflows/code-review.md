# Code Review Personal

Ejecuta una revisión automática del código:

1. **<PERSON><PERSON><PERSON> calidad del código:**
   ```bash
   pnpm run lint
   pnpm run type-check
   ```

2. **Revisar cobertura de tests:**
   ```bash
   pnpm run test -- --coverage
   ```

3. **Análizar bundle size:**
   ```bash
   pnpm run build
   pnpm run analyze
   ```

4. **Revisar dependencias vulnerables:**
   ```bash
   pnpm audit
   ```

5. **Buscar código duplicado:**
   ```bash
   npx jscpd src/
   ```

6. **Generar reporte de problemas:**
   - Listar archivos con alta complejidad
   - Identificar funciones muy largas
   - Detectar patrones anti-patterns
   - Sugerir refactorizaciones

7. **Crear checklist de mejoras:**
   - [ ] Optimizar imports
   - [ ] Mejorar nombres de variables
   - [ ] Agregar tests faltantes
   - [ ] Documentar funciones complejas
