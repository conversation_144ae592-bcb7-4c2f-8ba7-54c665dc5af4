# 13 - Aseguramiento de Calidad de Datos

## Validación de Datos de Entrada

*   **Validación en Tiempo Real durante Scraping**:
    ```typescript
    interface DataQualityCheck {
      field: string;
      value: any;
      isValid: boolean;
      confidence: number; // 0-100
      issues: string[];
      suggestions: string[];
    }

    class DataQualityValidator {
      validateScrapedLaptop(rawData: any): DataQualityReport {
        const checks: DataQualityCheck[] = [];
        
        // Validar título
        checks.push(this.validateTitle(rawData.title));
        
        // Validar precio
        checks.push(this.validatePrice(rawData.price));
        
        // Validar especificaciones
        checks.push(this.validateSpecifications(rawData.specs));
        
        // Validar imágenes
        checks.push(this.validateImages(rawData.images));
        
        return {
          overallScore: this.calculateOverallScore(checks),
          checks,
          isAcceptable: checks.every(check => check.confidence >= 70),
          timestamp: new Date()
        };
      }

      private validateTitle(title: string): DataQualityCheck {
        const issues: string[] = [];
        const suggestions: string[] = [];
        let confidence = 100;

        if (!title || title.trim().length === 0) {
          issues.push('Título vacío');
          confidence = 0;
        } else if (title.length < 10) {
          issues.push('Título muy corto');
          suggestions.push('Verificar si es el título completo');
          confidence -= 30;
        } else if (title.length > 200) {
          issues.push('Título excesivamente largo');
          suggestions.push('Posible descripción en lugar de título');
          confidence -= 20;
        }

        // Verificar si contiene información de laptop
        const laptopKeywords = ['laptop', 'notebook', 'macbook', 'thinkpad'];
        if (!laptopKeywords.some(keyword => 
          title.toLowerCase().includes(keyword))) {
          issues.push('Título no parece ser de laptop');
          confidence -= 40;
        }

        return {
          field: 'title',
          value: title,
          isValid: confidence >= 70,
          confidence,
          issues,
          suggestions
        };
      }

      private validatePrice(price: any): DataQualityCheck {
        const issues: string[] = [];
        const suggestions: string[] = [];
        let confidence = 100;

        const numericPrice = parseFloat(String(price).replace(/[^\d.]/g, ''));

        if (isNaN(numericPrice) || numericPrice <= 0) {
          issues.push('Precio inválido o no encontrado');
          confidence = 0;
        } else if (numericPrice < 100) {
          issues.push('Precio sospechosamente bajo');
          suggestions.push('Verificar si es precio en otra moneda');
          confidence -= 30;
        } else if (numericPrice > 10000) {
          issues.push('Precio excepcionalmente alto');
          suggestions.push('Verificar si incluye accesorios o es precio incorrecto');
          confidence -= 20;
        }

        return {
          field: 'price',
          value: numericPrice,
          isValid: confidence >= 70,
          confidence,
          issues,
          suggestions
        };
      }
    }
    ```

*   **Validación de Especificaciones Técnicas**:
    ```typescript
    function validateTechnicalSpecs(specs: any): DataQualityCheck {
      const issues: string[] = [];
      const suggestions: string[] = [];
      let confidence = 100;

      // Validar RAM
      if (specs.ram) {
        const ramValue = parseMemorySize(specs.ram);
        if (ramValue < 2 || ramValue > 128) {
          issues.push(`RAM fuera de rango esperado: ${ramValue}GB`);
          confidence -= 25;
        }
      } else {
        issues.push('Información de RAM no encontrada');
        confidence -= 30;
      }

      // Validar almacenamiento
      if (specs.storage) {
        const storageValue = parseStorageSize(specs.storage);
        if (storageValue < 64 || storageValue > 8000) {
          issues.push(`Almacenamiento fuera de rango: ${storageValue}GB`);
          confidence -= 25;
        }
      } else {
        issues.push('Información de almacenamiento no encontrada');
        confidence -= 30;
      }

      // Validar CPU
      if (!specs.cpu || specs.cpu.trim().length < 5) {
        issues.push('Información de CPU insuficiente');
        confidence -= 20;
      }

      return {
        field: 'specifications',
        value: specs,
        isValid: confidence >= 70,
        confidence,
        issues,
        suggestions
      };
    }
    ```

## Detección de Duplicados

*   **Algoritmo de Detección de Duplicados**:
    ```typescript
    interface DuplicateDetectionResult {
      isDuplicate: boolean;
      similarityScore: number;
      matchedLaptopId?: string;
      matchingFields: string[];
      confidence: number;
    }

    class DuplicateDetector {
      async checkForDuplicates(
        newLaptop: LaptopSpecifications
      ): Promise<DuplicateDetectionResult> {
        // Buscar laptops similares por título y especificaciones
        const candidates = await this.findSimilarLaptops(newLaptop);
        
        let bestMatch: DuplicateDetectionResult = {
          isDuplicate: false,
          similarityScore: 0,
          matchingFields: [],
          confidence: 0
        };

        for (const candidate of candidates) {
          const similarity = this.calculateSimilarity(newLaptop, candidate);
          if (similarity.similarityScore > bestMatch.similarityScore) {
            bestMatch = similarity;
          }
        }

        return bestMatch;
      }

      private calculateSimilarity(
        laptop1: LaptopSpecifications,
        laptop2: LaptopSpecifications
      ): DuplicateDetectionResult {
        const matchingFields: string[] = [];
        let totalScore = 0;
        let maxScore = 0;

        // Comparar título (peso: 30%)
        const titleSimilarity = this.calculateStringSimilarity(
          laptop1.title, laptop2.title
        );
        totalScore += titleSimilarity * 30;
        maxScore += 30;
        if (titleSimilarity > 0.8) matchingFields.push('title');

        // Comparar especificaciones (peso: 50%)
        const specsSimilarity = this.compareSpecifications(
          laptop1, laptop2
        );
        totalScore += specsSimilarity * 50;
        maxScore += 50;
        if (specsSimilarity > 0.9) matchingFields.push('specifications');

        // Comparar precio (peso: 20%)
        const priceSimilarity = this.comparePrices(
          laptop1.price, laptop2.price
        );
        totalScore += priceSimilarity * 20;
        maxScore += 20;
        if (priceSimilarity > 0.95) matchingFields.push('price');

        const similarityScore = totalScore / maxScore;

        return {
          isDuplicate: similarityScore > 0.85,
          similarityScore,
          matchedLaptopId: laptop2.id,
          matchingFields,
          confidence: Math.round(similarityScore * 100)
        };
      }

      private calculateStringSimilarity(str1: string, str2: string): number {
        // Implementar algoritmo de Levenshtein o similar
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        
        if (longer.length === 0) return 1.0;
        
        const distance = this.levenshteinDistance(longer, shorter);
        return (longer.length - distance) / longer.length;
      }
    }
    ```

## Monitoreo de Calidad Continuo

*   **Métricas de Calidad de Datos**:
    ```typescript
    interface DataQualityMetrics {
      totalRecords: number;
      validRecords: number;
      invalidRecords: number;
      duplicateRecords: number;
      averageConfidenceScore: number;
      fieldCompleteness: Record<string, number>;
      commonIssues: Array<{
        issue: string;
        frequency: number;
        affectedFields: string[];
      }>;
      lastUpdated: Date;
    }

    class DataQualityMonitor {
      async generateQualityReport(
        timeRange: { start: Date; end: Date }
      ): Promise<DataQualityMetrics> {
        const records = await prisma.laptop.findMany({
          where: {
            scrapedAt: {
              gte: timeRange.start,
              lte: timeRange.end
            }
          },
          include: {
            qualityChecks: true
          }
        });

        return {
          totalRecords: records.length,
          validRecords: records.filter(r => r.qualityChecks?.isValid).length,
          invalidRecords: records.filter(r => !r.qualityChecks?.isValid).length,
          duplicateRecords: await this.countDuplicates(records),
          averageConfidenceScore: this.calculateAverageConfidence(records),
          fieldCompleteness: this.calculateFieldCompleteness(records),
          commonIssues: this.identifyCommonIssues(records),
          lastUpdated: new Date()
        };
      }

      private calculateFieldCompleteness(
        records: LaptopSpecifications[]
      ): Record<string, number> {
        const fieldCounts = {
          title: 0,
          price: 0,
          cpu: 0,
          ram: 0,
          storage: 0,
          gpu: 0,
          images: 0
        };

        records.forEach(record => {
          if (record.title?.trim()) fieldCounts.title++;
          if (record.price > 0) fieldCounts.price++;
          if (record.processor?.model) fieldCounts.cpu++;
          if (record.memory?.totalRam) fieldCounts.ram++;
          if (record.storage?.totalStorage) fieldCounts.storage++;
          if (record.graphics?.integrated || record.graphics?.dedicated) fieldCounts.gpu++;
          if (record.imageUrls?.length > 0) fieldCounts.images++;
        });

        const total = records.length;
        return Object.fromEntries(
          Object.entries(fieldCounts).map(([field, count]) => [
            field,
            Math.round((count / total) * 100)
          ])
        );
      }
    }
    ```

*   **Alertas de Calidad**:
    ```typescript
    interface QualityAlert {
      type: 'LOW_QUALITY' | 'HIGH_DUPLICATES' | 'MISSING_DATA' | 'ANOMALY';
      severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
      message: string;
      affectedRecords: number;
      threshold: number;
      currentValue: number;
      timestamp: Date;
    }

    class QualityAlertSystem {
      async checkQualityThresholds(): Promise<QualityAlert[]> {
        const alerts: QualityAlert[] = [];
        const metrics = await this.dataQualityMonitor.generateQualityReport({
          start: new Date(Date.now() - 24 * 60 * 60 * 1000), // últimas 24h
          end: new Date()
        });

        // Verificar calidad general
        const validPercentage = (metrics.validRecords / metrics.totalRecords) * 100;
        if (validPercentage < 80) {
          alerts.push({
            type: 'LOW_QUALITY',
            severity: validPercentage < 60 ? 'CRITICAL' : 'HIGH',
            message: `Calidad de datos por debajo del umbral: ${validPercentage.toFixed(1)}%`,
            affectedRecords: metrics.invalidRecords,
            threshold: 80,
            currentValue: validPercentage,
            timestamp: new Date()
          });
        }

        // Verificar duplicados
        const duplicatePercentage = (metrics.duplicateRecords / metrics.totalRecords) * 100;
        if (duplicatePercentage > 10) {
          alerts.push({
            type: 'HIGH_DUPLICATES',
            severity: duplicatePercentage > 20 ? 'HIGH' : 'MEDIUM',
            message: `Alto porcentaje de duplicados: ${duplicatePercentage.toFixed(1)}%`,
            affectedRecords: metrics.duplicateRecords,
            threshold: 10,
            currentValue: duplicatePercentage,
            timestamp: new Date()
          });
        }

        return alerts;
      }
    }
    ```

## Limpieza y Corrección Automática

*   **Corrección Automática de Datos**:
    ```typescript
    class DataCleaner {
      async cleanAndCorrectData(laptop: LaptopSpecifications): Promise<LaptopSpecifications> {
        const cleaned = { ...laptop };

        // Limpiar título
        cleaned.title = this.cleanTitle(cleaned.title);
        
        // Normalizar precio
        cleaned.price = this.normalizePrice(cleaned.price);
        
        // Corregir especificaciones
        cleaned.processor = this.correctProcessorInfo(cleaned.processor);
        cleaned.memory = this.correctMemoryInfo(cleaned.memory);
        cleaned.storage = this.correctStorageInfo(cleaned.storage);
        
        // Validar y corregir imágenes
        cleaned.imageUrls = await this.validateAndCleanImages(cleaned.imageUrls);

        return cleaned;
      }

      private cleanTitle(title: string): string {
        return title
          .trim()
          .replace(/\s+/g, ' ')
          .replace(/[^\w\s\-()]/g, '')
          .substring(0, 200);
      }

      private normalizePrice(price: number): number {
        // Detectar y corregir errores comunes de precio
        if (price < 10) {
          // Posiblemente precio en miles
          return price * 1000;
        }
        if (price > 100000) {
          // Posiblemente precio en centavos
          return price / 100;
        }
        return price;
      }

      private async validateAndCleanImages(imageUrls: string[]): Promise<string[]> {
        const validImages: string[] = [];
        
        for (const url of imageUrls) {
          try {
            const response = await fetch(url, { method: 'HEAD' });
            if (response.ok && response.headers.get('content-type')?.startsWith('image/')) {
              validImages.push(url);
            }
          } catch {
            // Ignorar URLs inválidas
          }
        }
        
        return validImages.slice(0, 10); // Máximo 10 imágenes
      }
    }
    ```

## Auditoría y Trazabilidad

*   **Registro de Cambios de Calidad**:
    ```typescript
    interface QualityAuditLog {
      id: string;
      laptopId: string;
      action: 'VALIDATION' | 'CORRECTION' | 'DUPLICATE_DETECTION' | 'CLEANUP';
      beforeState: any;
      afterState: any;
      issues: string[];
      corrections: string[];
      confidence: number;
      timestamp: Date;
      source: 'AUTOMATIC' | 'MANUAL' | 'SYSTEM';
    }

    async function logQualityAction(log: Omit<QualityAuditLog, 'id' | 'timestamp'>): Promise<void> {
      await prisma.qualityAuditLog.create({
        data: {
          ...log,
          id: generateId(),
          timestamp: new Date()
        }
      });
    }
    ```

## No negociable:

*   **Validación automática de todos los datos de entrada**
*   **Detección y manejo de duplicados**
*   **Monitoreo continuo de métricas de calidad**
*   **Sistema de alertas para problemas de calidad**
*   **Auditoría completa de cambios de datos**
*   **Corrección automática de errores comunes**
