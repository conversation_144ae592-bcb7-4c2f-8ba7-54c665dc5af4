# 03 - Manejo de Datos y APIs

## Estrategias de Fetching de Datos

*   **Centralización Específica para Scraping**: Centralizar la lógica de fetching en servicios especializados:
    ```typescript
    src/services/
    ├── scraping/
    │   ├── firecrawl.service.ts      # Servicio principal de Firecrawl
    │   ├── puppeteer.service.ts      # Fallback con Puppeteer
    │   └── scraping-orchestrator.ts  # Orquestador de scrapers
    ├── laptop/
    │   ├── laptop-search.service.ts  # Búsqueda y filtrado
    │   └── laptop-specs.service.ts   # Gestión de especificaciones
    └── llm/
        └── compatibility.service.ts  # Cálculo de compatibilidad
    ```

*   **Librerías de Fetching**: Utilizar React Query para manejar estado de servidor, con configuraciones específicas:
    ```typescript
    // Configuración específica para scraping
    const scrapingQueryConfig = {
      staleTime: 5 * 60 * 1000,     // 5 minutos para datos de scraping
      cacheTime: 30 * 60 * 1000,    // 30 minutos en cache
      retry: 3,                      // 3 reintentos para scraping
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)
    };
    ```

*   **Manejo de Estados Específicos**: Estados especializados para operaciones de scraping:
    ```typescript
    interface ScrapingState {
      isLoading: boolean;
      isScraping: boolean;
      scrapingProgress: number;
      lastScrapedAt: Date | null;
      error: ScrapingError | null;
      results: ScrapedLaptop[];
    }
    ```

*   **Paginación para Resultados de Laptops**: Implementar paginación eficiente para grandes conjuntos de datos de laptops con filtros dinámicos.

## Validación de Datos

*   **Validación de Entrada (Frontend)**: Validar filtros de búsqueda y configuraciones de scraping en el frontend.

*   **Esquemas Zod Específicos del Dominio**: Definir esquemas estrictos para datos de laptops y scraping:
    ```typescript
    // Esquema para especificaciones de laptop
    export const LaptopSpecsSchema = z.object({
      cpu: z.string().min(1),
      ram: z.number().min(4).max(128),
      gpu: z.string().optional(),
      storage: z.number().min(128),
      price: z.number().positive(),
      currency: z.enum(['USD', 'EUR', 'CUP']),
      brand: z.enum(['Dell', 'HP', 'Lenovo', 'Apple', 'Asus', 'Acer']),
      screenSize: z.number().min(11).max(18).optional()
    });

    // Esquema para configuración de scraping
    export const ScrapingConfigSchema = z.object({
      url: z.string().url(),
      requiresFullBrowserAutomation: z.boolean(),
      selectors: z.record(z.string()),
      rateLimit: z.number().min(1000),
      allowedDomains: z.array(z.string()),
      maxRetries: z.number().min(1).max(5)
    });

    // Esquema para compatibilidad LLM
    export const LLMCompatibilitySchema = z.object({
      modelName: z.string(),
      minRam: z.number().min(8),
      minVram: z.number().optional(),
      recommendedRam: z.number(),
      compatibilityScore: z.number().min(0).max(100),
      canRunOffline: z.boolean()
    });
    ```

*   **Transformación de Datos de Scraping**: Normalizar datos extraídos a formato consistente:
    ```typescript
    function normalizeScrapedLaptop(rawData: unknown): LaptopSpecifications {
      // Normalizar precios a USD
      // Estandarizar nombres de marcas
      // Convertir especificaciones a formato uniforme
      return LaptopSpecsSchema.parse(transformedData);
    }
    ```

## Manejo de Errores

*   **Captura Centralizada**: Implementar un mecanismo centralizado para capturar errores de API y de la aplicación (ej. `ErrorBoundary` para errores de React, interceptores de Axios para errores de red).
*   **Mensajes de Error Claros**: Proporcionar mensajes de error claros y útiles al usuario, evitando exponer detalles internos del sistema.
*   **Logging**: Registrar errores importantes en el servidor y/o en servicios de monitoreo de errores (ej. Sentry, Datadog) para facilitar la depuración y el análisis.
*   **Reintentos y Circuit Breakers**: Implementar lógicas de reintento para operaciones de red fallidas y patrones de "circuit breaker" para evitar sobrecargar servicios inestables.

## Esencial:

*   **React Query/SWR para data fetching**
*   **Zod/Yup para validación de schemas**
*   **Error boundaries para manejo de errores**
*   **Loading states y skeleton screens**
*   **Retry mechanisms y timeout handling**
*   **Type-safe API contracts**
