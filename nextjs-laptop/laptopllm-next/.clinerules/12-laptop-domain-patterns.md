# 12 - Patrones del Dominio de Laptops

## Modelado de Datos de Laptops

*   **Esquema Principal de Laptop**:
    ```typescript
    interface LaptopSpecifications {
      // Identificación
      id: string;
      title: string;
      brand: LaptopB<PERSON>;
      model: string;
      sku?: string;
      
      // Especificaciones técnicas
      processor: ProcessorSpecs;
      memory: MemorySpecs;
      storage: StorageSpecs;
      graphics: GraphicsSpecs;
      display: DisplaySpecs;
      
      // Información comercial
      price: PriceInfo;
      availability: AvailabilityInfo;
      
      // Metadatos
      sourceUrl: string;
      scrapedAt: Date;
      lastUpdated: Date;
      isActive: boolean;
    }

    interface ProcessorSpecs {
      brand: 'Intel' | 'AMD' | 'Apple';
      model: string;
      generation?: string;
      cores: number;
      threads: number;
      baseFrequency: number; // GHz
      maxFrequency?: number; // GHz
      architecture: string; // x64, ARM, etc.
    }

    interface MemorySpecs {
      totalRam: number; // GB
      ramType: 'DDR4' | 'DDR5' | 'LPDDR4' | 'LPDDR5';
      ramSpeed?: number; // MHz
      maxRamSupported?: number; // GB
      ramSlots?: number;
      isUpgradeable: boolean;
    }

    interface StorageSpecs {
      primary: StorageDevice;
      secondary?: StorageDevice;
      totalStorage: number; // GB
    }

    interface StorageDevice {
      type: 'SSD' | 'HDD' | 'eMMC' | 'NVMe';
      capacity: number; // GB
      interface?: 'SATA' | 'NVMe' | 'PCIe';
      isRemovable: boolean;
    }

    interface GraphicsSpecs {
      integrated?: {
        brand: 'Intel' | 'AMD';
        model: string;
        vram?: number; // GB (shared)
      };
      dedicated?: {
        brand: 'NVIDIA' | 'AMD';
        model: string;
        vram: number; // GB
        memoryType: 'GDDR6' | 'GDDR5' | 'HBM';
      };
    }
    ```

*   **Información de Compatibilidad LLM**:
    ```typescript
    interface LLMCompatibility {
      laptopId: string;
      llmModelId: string;
      compatibilityScore: number; // 0-100
      canRun: boolean;
      performance: 'Poor' | 'Fair' | 'Good' | 'Excellent';
      limitations: string[];
      recommendations: string[];
      calculatedAt: Date;
    }

    interface LLMModel {
      id: string;
      name: string;
      provider: 'Ollama' | 'Hugging Face' | 'OpenAI' | 'Anthropic';
      modelSize: number; // GB
      minRamRequired: number; // GB
      recommendedRam: number; // GB
      minVramRequired?: number; // GB
      supportsCpu: boolean;
      supportsGpu: boolean;
      quantizationLevels: string[]; // '4bit', '8bit', '16bit', 'fp32'
    }
    ```

## Algoritmos de Compatibilidad LLM

*   **Cálculo de Puntuación de Compatibilidad**:
    ```typescript
    function calculateLLMCompatibility(
      laptop: LaptopSpecifications,
      llmModel: LLMModel
    ): LLMCompatibility {
      let score = 0;
      const limitations: string[] = [];
      const recommendations: string[] = [];

      // Evaluación de RAM (40% del score)
      const ramScore = evaluateRamCompatibility(laptop.memory, llmModel);
      score += ramScore * 0.4;
      
      // Evaluación de CPU (30% del score)
      const cpuScore = evaluateCpuCompatibility(laptop.processor, llmModel);
      score += cpuScore * 0.3;
      
      // Evaluación de GPU (20% del score)
      const gpuScore = evaluateGpuCompatibility(laptop.graphics, llmModel);
      score += gpuScore * 0.2;
      
      // Evaluación de almacenamiento (10% del score)
      const storageScore = evaluateStorageCompatibility(laptop.storage, llmModel);
      score += storageScore * 0.1;

      return {
        laptopId: laptop.id,
        llmModelId: llmModel.id,
        compatibilityScore: Math.round(score),
        canRun: score >= 60,
        performance: getPerformanceLevel(score),
        limitations,
        recommendations,
        calculatedAt: new Date()
      };
    }

    function evaluateRamCompatibility(memory: MemorySpecs, llm: LLMModel): number {
      if (memory.totalRam < llm.minRamRequired) return 0;
      if (memory.totalRam >= llm.recommendedRam) return 100;
      
      // Interpolación lineal entre mínimo y recomendado
      const ratio = (memory.totalRam - llm.minRamRequired) / 
                   (llm.recommendedRam - llm.minRamRequired);
      return 50 + (ratio * 50);
    }

    function evaluateCpuCompatibility(processor: ProcessorSpecs, llm: LLMModel): number {
      let score = 0;
      
      // Puntuación base por número de cores
      score += Math.min(processor.cores * 10, 50);
      
      // Bonificación por frecuencia
      score += Math.min(processor.baseFrequency * 10, 30);
      
      // Bonificación por arquitectura moderna
      if (processor.architecture === 'x64' && processor.generation) {
        const gen = parseInt(processor.generation);
        if (gen >= 10) score += 20; // Intel 10th gen+ o AMD equivalente
      }
      
      return Math.min(score, 100);
    }
    ```

*   **Recomendaciones Específicas**:
    ```typescript
    function generateLLMRecommendations(
      laptop: LaptopSpecifications,
      llmModel: LLMModel,
      score: number
    ): string[] {
      const recommendations: string[] = [];
      
      if (laptop.memory.totalRam < llmModel.recommendedRam) {
        if (laptop.memory.isUpgradeable) {
          recommendations.push(
            `Considerar actualizar RAM a ${llmModel.recommendedRam}GB para mejor rendimiento`
          );
        } else {
          recommendations.push(
            `RAM limitada (${laptop.memory.totalRam}GB). Considerar modelos cuantizados.`
          );
        }
      }
      
      if (!laptop.graphics.dedicated && llmModel.minVramRequired) {
        recommendations.push(
          'GPU dedicada recomendada para mejor rendimiento con este modelo LLM'
        );
      }
      
      if (laptop.storage.primary.type !== 'NVMe' && laptop.storage.primary.type !== 'SSD') {
        recommendations.push(
          'SSD NVMe recomendado para carga rápida de modelos grandes'
        );
      }
      
      return recommendations;
    }
    ```

## Patrones de Búsqueda y Filtrado

*   **Filtros Especializados**:
    ```typescript
    interface LaptopSearchFilters {
      // Filtros básicos
      priceRange: { min: number; max: number };
      brands: LaptopBrand[];
      
      // Filtros de especificaciones
      minRam: number;
      maxRam?: number;
      storageTypes: StorageType[];
      minStorage: number;
      
      // Filtros de compatibilidad LLM
      llmCompatible: boolean;
      minCompatibilityScore?: number;
      targetLLMModels: string[];
      
      // Filtros de rendimiento
      minCpuCores: number;
      hasGpu: boolean;
      gpuVramMin?: number;
      
      // Filtros de disponibilidad
      inStock: boolean;
      maxAge: number; // días desde última actualización
    }

    function buildLaptopQuery(filters: LaptopSearchFilters): Prisma.LaptopWhereInput {
      const where: Prisma.LaptopWhereInput = {
        isActive: true,
        price: {
          gte: filters.priceRange.min,
          lte: filters.priceRange.max
        }
      };

      if (filters.brands.length > 0) {
        where.brand = { in: filters.brands };
      }

      if (filters.minRam) {
        where.memory = {
          totalRam: { gte: filters.minRam }
        };
      }

      if (filters.llmCompatible && filters.minCompatibilityScore) {
        where.compatibilities = {
          some: {
            compatibilityScore: { gte: filters.minCompatibilityScore }
          }
        };
      }

      return where;
    }
    ```

*   **Algoritmos de Ordenamiento**:
    ```typescript
    type SortOption = 
      | 'price_asc' 
      | 'price_desc' 
      | 'compatibility_desc' 
      | 'performance_desc' 
      | 'newest_first'
      | 'best_value';

    function applySorting(
      query: Prisma.LaptopFindManyArgs,
      sortBy: SortOption
    ): Prisma.LaptopFindManyArgs {
      switch (sortBy) {
        case 'price_asc':
          query.orderBy = { price: 'asc' };
          break;
        case 'price_desc':
          query.orderBy = { price: 'desc' };
          break;
        case 'compatibility_desc':
          query.orderBy = {
            compatibilities: {
              _max: { compatibilityScore: 'desc' }
            }
          };
          break;
        case 'best_value':
          // Ordenar por ratio compatibilidad/precio
          query.orderBy = [
            { compatibilities: { _max: { compatibilityScore: 'desc' } } },
            { price: 'asc' }
          ];
          break;
        default:
          query.orderBy = { lastUpdated: 'desc' };
      }
      return query;
    }
    ```

## Normalización de Datos

*   **Normalización de Marcas y Modelos**:
    ```typescript
    const BRAND_MAPPINGS: Record<string, LaptopBrand> = {
      'hp': 'HP',
      'hewlett packard': 'HP',
      'dell': 'Dell',
      'lenovo': 'Lenovo',
      'apple': 'Apple',
      'macbook': 'Apple',
      'asus': 'Asus',
      'acer': 'Acer',
      'msi': 'MSI',
      'alienware': 'Dell' // Alienware es de Dell
    };

    function normalizeBrand(rawBrand: string): LaptopBrand {
      const normalized = rawBrand.toLowerCase().trim();
      return BRAND_MAPPINGS[normalized] || 'Other';
    }

    function normalizeProcessorName(rawCpu: string): string {
      return rawCpu
        .replace(/\s+/g, ' ')
        .replace(/processor/gi, '')
        .replace(/cpu/gi, '')
        .trim();
    }

    function parseMemorySize(rawMemory: string): number {
      const match = rawMemory.match(/(\d+)\s*(gb|mb)/i);
      if (!match) return 0;
      
      const value = parseInt(match[1]);
      const unit = match[2].toLowerCase();
      
      return unit === 'gb' ? value : Math.round(value / 1024);
    }
    ```

## Validación de Integridad de Datos

*   **Validaciones de Negocio**:
    ```typescript
    function validateLaptopData(laptop: LaptopSpecifications): ValidationResult {
      const errors: string[] = [];
      const warnings: string[] = [];

      // Validaciones críticas
      if (laptop.price <= 0) {
        errors.push('Precio debe ser mayor a 0');
      }

      if (laptop.memory.totalRam < 2) {
        errors.push('RAM mínima debe ser 2GB');
      }

      if (laptop.storage.totalStorage < 64) {
        errors.push('Almacenamiento mínimo debe ser 64GB');
      }

      // Validaciones de advertencia
      if (laptop.price > 10000) {
        warnings.push('Precio excepcionalmente alto, verificar');
      }

      if (laptop.memory.totalRam > 128) {
        warnings.push('RAM excepcionalmente alta, verificar');
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings
      };
    }
    ```

## No negociable:

*   **Validación estricta de especificaciones técnicas**
*   **Normalización consistente de marcas y modelos**
*   **Algoritmos de compatibilidad LLM precisos**
*   **Integridad referencial en base de datos**
*   **Versionado de esquemas de datos**
