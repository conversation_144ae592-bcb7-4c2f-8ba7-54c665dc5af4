# LaptopLLM Finder

This project aims to help users find laptops capable of running Large Language Models (LLMs) locally.

## Project Structure

The project follows a monorepo structure with `pnpm` as the package manager.

- `app/`: Next.js App Router pages and API routes.
- `components/`: Reusable UI components (shadcn/ui and custom).
- `hooks/`: Custom React hooks.
- `lib/`: Utility functions, database configurations, and third-party integrations.
- `public/`: Static assets.
- `scripts/`: Database migration and seeding scripts.
- `src/features/`: Feature-specific modules, following an Atomic Design pattern (components, hooks, services, types).
  - `dashboard/`: Dashboard related components and logic.
  - `laptop-search/`: Laptop search and filtering functionality.
  - `urlSource/`: Web scraping and URL source management.
- `src/services/`: Centralized API client and HTTP client.
- `src/shared/types/`: Shared TypeScript types across features.
- `styles/`: Global CSS.

## Getting Started

1. **Install Dependencies**:
  \`\`\`bash
  pnpm install
  \`\`\`
2. **Set up Environment Variables**:
  Create a `.env.local` file based on `.env.example` and fill in your database and API keys.
  \`\`\`
  DATABASE_URL="your_neon_database_url"
  FIRECRAWL_API_KEY="your_firecrawl_api_key"
  \`\`\`
3. **Run Database Migrations**:
  Execute the SQL scripts in `scripts/sql/` to set up your database schema.
  \`\`\`bash
  
  # Example for Neon/Postgres (adjust based on your setup)
  
  # You might use a tool like `psql` or a database client to run these.
  
  # Alternatively, if you have a script runner, you can use that.
  
  # For v0, you can use the "run-script" action.
  
  \`\`\`
4. **Run the Development Server**:
  \`\`\`bash
  pnpm dev
  \`\`\`
  Open [http://localhost:3000](http://localhost:3000) in your browser.

## Features

- **Dashboard**: Overview of scraping jobs and statistics.
- **URL Source Management**: Add and manage URLs for scraping.
- **Laptop Search**: Search and filter laptops based on specifications.
- **Price Trends (Planned)**: Visualize historical price data.

## Technologies Used

- Next.js (App Router)
- React
- TypeScript
- Tailwind CSS
- shadcn/ui
- Neon (PostgreSQL)
- Firecrawl API (for web scraping)
- Vercel Blob (for asset storage, if implemented)
- Upstash (for caching/rate limiting, if implemented)