
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.13.0
 * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
 */
Prisma.prismaVersion = {
  client: "6.13.0",
  engine: "361e86d0ea4987e9f53a565309b3eed797a6bcbd"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.BrandsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  website: 'website',
  logo_url: 'logo_url',
  manufacturer_id: 'manufacturer_id',
  created_at: 'created_at'
};

exports.Prisma.Cpu_architecturesScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  technology_nm: 'technology_nm',
  release_year: 'release_year'
};

exports.Prisma.CpusScalarFieldEnum = {
  id: 'id',
  manufacturer_id: 'manufacturer_id',
  model: 'model',
  generation: 'generation',
  cores: 'cores',
  threads: 'threads',
  base_clock_ghz: 'base_clock_ghz',
  boost_clock_ghz: 'boost_clock_ghz',
  tdp_watts: 'tdp_watts',
  cache_mb: 'cache_mb',
  architecture_id: 'architecture_id',
  supports_avx512: 'supports_avx512'
};

exports.Prisma.DisplaysScalarFieldEnum = {
  id: 'id',
  laptop_id: 'laptop_id',
  size_inches: 'size_inches',
  resolution_id: 'resolution_id',
  refresh_rate: 'refresh_rate',
  panel_type_id: 'panel_type_id',
  is_touchscreen: 'is_touchscreen',
  brightness_nits: 'brightness_nits',
  color_gamut: 'color_gamut',
  hdr_support: 'hdr_support',
  response_time_ms: 'response_time_ms'
};

exports.Prisma.GpusScalarFieldEnum = {
  id: 'id',
  manufacturer_id: 'manufacturer_id',
  model: 'model',
  vram_gb: 'vram_gb',
  memory_type_id: 'memory_type_id',
  base_clock_mhz: 'base_clock_mhz',
  boost_clock_mhz: 'boost_clock_mhz',
  tdp_watts: 'tdp_watts',
  ray_tracing_support: 'ray_tracing_support',
  tensor_cores: 'tensor_cores',
  cuda_cores: 'cuda_cores',
  compute_units: 'compute_units',
  supports_dlss: 'supports_dlss',
  supports_fsr: 'supports_fsr'
};

exports.Prisma.Laptop_cpusScalarFieldEnum = {
  laptop_id: 'laptop_id',
  cpu_id: 'cpu_id',
  performance_score: 'performance_score'
};

exports.Prisma.Laptop_gpusScalarFieldEnum = {
  laptop_id: 'laptop_id',
  gpu_id: 'gpu_id',
  is_discrete: 'is_discrete',
  performance_score: 'performance_score'
};

exports.Prisma.Laptop_listingsScalarFieldEnum = {
  id: 'id',
  laptop_id: 'laptop_id',
  source_id: 'source_id',
  price: 'price',
  url: 'url',
  in_stock: 'in_stock',
  shipping_cost: 'shipping_cost',
  rating: 'rating',
  reviews_count: 'reviews_count',
  listing_date: 'listing_date',
  free_shipping: 'free_shipping',
  estimated_delivery_days: 'estimated_delivery_days',
  has_warranty: 'has_warranty',
  warranty_months: 'warranty_months',
  processed: 'processed'
};

exports.Prisma.Laptop_llm_compatibilityScalarFieldEnum = {
  laptop_id: 'laptop_id',
  llm_id: 'llm_id',
  estimated_tokens_per_second: 'estimated_tokens_per_second',
  max_context_length: 'max_context_length',
  score: 'score',
  qualitative_assessment: 'qualitative_assessment',
  can_run_offline: 'can_run_offline',
  recommended_batch_size: 'recommended_batch_size',
  estimated_memory_usage_gb: 'estimated_memory_usage_gb'
};

exports.Prisma.Laptop_os_compatibilityScalarFieldEnum = {
  laptop_id: 'laptop_id',
  os_id: 'os_id',
  is_officially_supported: 'is_officially_supported',
  has_driver_issues: 'has_driver_issues',
  notes: 'notes'
};

exports.Prisma.Laptop_ramScalarFieldEnum = {
  laptop_id: 'laptop_id',
  ram_configuration_id: 'ram_configuration_id',
  slots_used: 'slots_used',
  max_slots: 'max_slots',
  expandable: 'expandable',
  max_supported_gb: 'max_supported_gb'
};

exports.Prisma.Laptop_scoresScalarFieldEnum = {
  id: 'id',
  laptop_id: 'laptop_id',
  overall_score: 'overall_score',
  llm_performance_score: 'llm_performance_score',
  battery_life_score: 'battery_life_score',
  build_quality_score: 'build_quality_score',
  display_score: 'display_score',
  keyboard_score: 'keyboard_score',
  performance_score: 'performance_score',
  value_score: 'value_score',
  thermal_score: 'thermal_score',
  noise_score: 'noise_score',
  portability_score: 'portability_score',
  last_evaluated_at: 'last_evaluated_at'
};

exports.Prisma.Laptop_storageScalarFieldEnum = {
  laptop_id: 'laptop_id',
  storage_id: 'storage_id',
  is_primary: 'is_primary',
  slot_type: 'slot_type'
};

exports.Prisma.Laptop_tagsScalarFieldEnum = {
  laptop_id: 'laptop_id',
  tag_id: 'tag_id',
  relevance_score: 'relevance_score'
};

exports.Prisma.LaptopsScalarFieldEnum = {
  id: 'id',
  model_name: 'model_name',
  brand_id: 'brand_id',
  release_date: 'release_date',
  description: 'description',
  image_url: 'image_url',
  is_available: 'is_available',
  msrp: 'msrp',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Llm_modelsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  parameters_billions: 'parameters_billions',
  quantization_bits: 'quantization_bits',
  min_ram_gb: 'min_ram_gb',
  min_vram_gb: 'min_vram_gb',
  requires_gpu: 'requires_gpu',
  description: 'description',
  model_card_url: 'model_card_url',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.ManufacturersScalarFieldEnum = {
  id: 'id',
  name: 'name',
  website: 'website',
  country: 'country',
  founded_year: 'founded_year'
};

exports.Prisma.Memory_typesScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  max_bandwidth_gbps: 'max_bandwidth_gbps'
};

exports.Prisma.Operating_systemsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  version: 'version',
  is_linux: 'is_linux',
  is_windows: 'is_windows',
  is_macos: 'is_macos',
  min_ram_gb: 'min_ram_gb',
  min_storage_gb: 'min_storage_gb'
};

exports.Prisma.Panel_typesScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  advantages: 'advantages',
  disadvantages: 'disadvantages'
};

exports.Prisma.Physical_specsScalarFieldEnum = {
  id: 'id',
  laptop_id: 'laptop_id',
  weight_kg: 'weight_kg',
  height_mm: 'height_mm',
  width_mm: 'width_mm',
  depth_mm: 'depth_mm',
  material: 'material',
  color: 'color',
  has_fingerprint_reader: 'has_fingerprint_reader',
  has_webcam: 'has_webcam',
  webcam_resolution: 'webcam_resolution',
  has_backlit_keyboard: 'has_backlit_keyboard'
};

exports.Prisma.Port_typesScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  max_data_rate_gbps: 'max_data_rate_gbps',
  supports_power_delivery: 'supports_power_delivery',
  max_power_delivery_watts: 'max_power_delivery_watts'
};

exports.Prisma.Price_historyScalarFieldEnum = {
  id: 'id',
  listing_id: 'listing_id',
  price: 'price',
  recorded_at: 'recorded_at'
};

exports.Prisma.Ram_configurationsScalarFieldEnum = {
  id: 'id',
  memory_type_id: 'memory_type_id',
  size_gb: 'size_gb',
  speed_mhz: 'speed_mhz',
  manufacturer_id: 'manufacturer_id',
  is_dual_channel: 'is_dual_channel',
  cas_latency: 'cas_latency'
};

exports.Prisma.Resolution_typesScalarFieldEnum = {
  id: 'id',
  name: 'name',
  width: 'width',
  height: 'height',
  aspect_ratio: 'aspect_ratio'
};

exports.Prisma.Scraping_historyScalarFieldEnum = {
  id: 'id',
  source_id: 'source_id',
  start_time: 'start_time',
  end_time: 'end_time',
  status: 'status',
  items_found: 'items_found',
  error_message: 'error_message'
};

exports.Prisma.ScrapingSourceScalarFieldEnum = {
  id: 'id',
  name: 'name',
  isActive: 'isActive',
  lastScrapedAt: 'lastScrapedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  selectors: 'selectors',
  requiresFullBrowserAutomation: 'requiresFullBrowserAutomation',
  url: 'url'
};

exports.Prisma.ScrapingJobScalarFieldEnum = {
  id: 'id',
  type: 'type',
  status: 'status',
  priority: 'priority',
  data: 'data',
  result: 'result',
  error: 'error',
  metrics: 'metrics',
  attempts: 'attempts',
  maxAttempts: 'maxAttempts',
  createdAt: 'createdAt',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  updatedAt: 'updatedAt',
  parentJobId: 'parentJobId',
  batchId: 'batchId',
  tags: 'tags'
};

exports.Prisma.JobLogScalarFieldEnum = {
  id: 'id',
  jobId: 'jobId',
  level: 'level',
  message: 'message',
  data: 'data',
  timestamp: 'timestamp'
};

exports.Prisma.QueueMetricsScalarFieldEnum = {
  id: 'id',
  queueName: 'queueName',
  timestamp: 'timestamp',
  waiting: 'waiting',
  active: 'active',
  completed: 'completed',
  failed: 'failed',
  delayed: 'delayed',
  paused: 'paused',
  throughputPerHour: 'throughputPerHour',
  avgProcessingTime: 'avgProcessingTime',
  successRate: 'successRate'
};

exports.Prisma.SourcesScalarFieldEnum = {
  id: 'id',
  name: 'name',
  url: 'url',
  api_endpoint: 'api_endpoint',
  is_active: 'is_active',
  last_updated: 'last_updated',
  update_frequency: 'update_frequency',
  selectors: 'selectors',
  last_scrape_attempt: 'last_scrape_attempt',
  scrape_success_count: 'scrape_success_count',
  scrape_failure_count: 'scrape_failure_count'
};

exports.Prisma.Storage_devicesScalarFieldEnum = {
  id: 'id',
  interface_id: 'interface_id',
  capacity_gb: 'capacity_gb',
  manufacturer_id: 'manufacturer_id',
  read_speed_mbps: 'read_speed_mbps',
  write_speed_mbps: 'write_speed_mbps',
  is_nvme: 'is_nvme',
  has_dram_cache: 'has_dram_cache'
};

exports.Prisma.Storage_interfacesScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  max_throughput_gbps: 'max_throughput_gbps'
};

exports.Prisma.Tag_categoriesScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.TagsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  category_id: 'category_id',
  description: 'description'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};


exports.Prisma.ModelName = {
  brands: 'brands',
  cpu_architectures: 'cpu_architectures',
  cpus: 'cpus',
  displays: 'displays',
  gpus: 'gpus',
  laptop_cpus: 'laptop_cpus',
  laptop_gpus: 'laptop_gpus',
  laptop_listings: 'laptop_listings',
  laptop_llm_compatibility: 'laptop_llm_compatibility',
  laptop_os_compatibility: 'laptop_os_compatibility',
  laptop_ram: 'laptop_ram',
  laptop_scores: 'laptop_scores',
  laptop_storage: 'laptop_storage',
  laptop_tags: 'laptop_tags',
  laptops: 'laptops',
  llm_models: 'llm_models',
  manufacturers: 'manufacturers',
  memory_types: 'memory_types',
  operating_systems: 'operating_systems',
  panel_types: 'panel_types',
  physical_specs: 'physical_specs',
  port_types: 'port_types',
  price_history: 'price_history',
  ram_configurations: 'ram_configurations',
  resolution_types: 'resolution_types',
  scraping_history: 'scraping_history',
  ScrapingSource: 'ScrapingSource',
  ScrapingJob: 'ScrapingJob',
  JobLog: 'JobLog',
  QueueMetrics: 'QueueMetrics',
  sources: 'sources',
  storage_devices: 'storage_devices',
  storage_interfaces: 'storage_interfaces',
  tag_categories: 'tag_categories',
  tags: 'tags'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
