#!/usr/bin/env node

/**
 * Simple test script to verify Crawl4AI integration
 * This script tests the new scraping hierarchy: Crawl4AI → Firecrawl → Puppeteer
 */

const { scrapingService } = require('./src/lib/scraping/scraping-service');

async function testCrawl4AIIntegration() {
  console.log('🚀 Testing Crawl4AI Integration');
  console.log('================================');

  try {
    // Test 1: Health Check
    console.log('\n1. Testing Health Check...');
    const health = await scrapingService.getHealth();
    console.log('Health Status:', {
      overall: health.healthy ? '✅ Healthy' : '❌ Unhealthy',
      crawl4ai: health.scrapers.crawl4ai ? '✅' : '❌',
      firecrawl: health.scrapers.firecrawl ? '✅' : '❌',
      puppeteer: health.scrapers.puppeteer ? '✅' : '❌',
    });

    // Test 2: Simple Scraping
    console.log('\n2. Testing Simple Scraping...');
    const testUrl = 'https://example.com';
    console.log(`Testing URL: ${testUrl}`);
    
    const scrapingConfig = {
      url: testUrl,
      timeout: 10000,
    };

    const result = await scrapingService.scrape(scrapingConfig);
    
    if (result.success) {
      console.log('✅ Scraping successful!');
      console.log(`Scraper used: ${result.scraper}`);
      console.log(`Duration: ${result.duration}ms`);
      console.log(`Content length: ${result.data?.content?.length || 0} characters`);
      console.log(`Title: ${result.data?.title || 'N/A'}`);
    } else {
      console.log('❌ Scraping failed:', result.error);
      console.log(`Last scraper tried: ${result.scraper}`);
    }

    // Test 3: Laptop Data Extraction (if Crawl4AI is available)
    if (health.scrapers.crawl4ai) {
      console.log('\n3. Testing Laptop Data Extraction...');
      const laptopUrl = 'https://www.amazon.com/dp/B0CX23V2ZK'; // Example laptop URL
      
      try {
        const laptopData = await scrapingService.extractLaptopData(laptopUrl, 'amazon');
        console.log('✅ Laptop extraction successful!');
        console.log('Extracted data keys:', Object.keys(laptopData || {}));
      } catch (error) {
        console.log('⚠️ Laptop extraction failed (expected if Crawl4AI is not running):', error.message);
      }
    } else {
      console.log('\n3. Skipping laptop extraction test (Crawl4AI not available)');
    }

    console.log('\n🎉 Integration test completed!');
    console.log('\nNext steps:');
    console.log('1. Start Crawl4AI service: docker-compose up crawl4ai');
    console.log('2. Test the admin interface: http://localhost:3000/admin/crawl-test');
    console.log('3. Verify the scraping hierarchy is working as expected');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    // Cleanup
    try {
      await scrapingService.cleanup();
      console.log('\n🧹 Cleanup completed');
    } catch (cleanupError) {
      console.warn('⚠️ Cleanup warning:', cleanupError.message);
    }
  }
}

// Run the test
if (require.main === module) {
  testCrawl4AIIntegration()
    .then(() => {
      console.log('\n✅ Test script finished successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test script failed:', error);
      process.exit(1);
    });
}

module.exports = { testCrawl4AIIntegration };
