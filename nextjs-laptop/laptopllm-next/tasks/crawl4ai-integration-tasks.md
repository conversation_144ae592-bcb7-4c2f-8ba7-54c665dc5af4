# Tareas de Integración de Crawl4AI

Este archivo rastrea el progreso de la integración de `crawl4ai` en el proyecto LaptopLLM-Finder, siguiendo el plan detallado en `docs/crawl4ai-integration-plan.md`.

## Fase 1: Estrategia de Arquitectura

- [ ] **Análisis de Viabilidad y Estrategia de Integración:**
    - [ ] Evaluar características, dependencias y requisitos de `crawl4ai`.
    - [ ] Analizar compatibilidad con el stack tecnológico actual (Node.js, Next.js, Prisma, PostgreSQL).
    - [ ] Definir la arquitectura de integración: Microservicio independiente en Docker con cola de mensajes para producción.
    - [ ] Confirmar el enfoque híbrido (docker-compose para PoC, cola de mensajes para producción).

---

## Fase 2: Prueba de Concepto (PoC) con `docker-compose`

- [ ] **Configurar Entorno de Desarrollo Aislado:**
    - [ ] Crear `docker-compose.yml` en la raíz del proyecto.
    - [ ] Crear el endpoint de API en `src/app/api/crawl/route.ts`.
    - [ ] Crear la página de prueba de UI en `src/app/admin/crawl-test/page.tsx`.
    - [ ] Corregir el error de ESLint en `src/app/admin/crawl-test/page.tsx` (definir tipo para `result`).

- [ ] **Ejecutar la PoC:**
    - [ ] Asegurarse de que Docker esté en ejecución.
    - [ ] Ejecutar `docker-compose up --build`.
    - [ ] Acceder a `http://localhost:3000/admin/crawl-test`.
    - [ ] Realizar una prueba de rastreo con una URL de ejemplo.
    - [ ] Verificar que el resultado se muestra correctamente.

---

## Fase 3: Plan de Implementación Detallado (Producción)

- [ ] **Diseño de la Interfaz con Cola de Mensajes:**
    - [ ] Seleccionar y configurar una cola de mensajes (ej. RabbitMQ).
    - [ ] Definir colas: `crawl-jobs-queue`, `crawl-results-queue`.
    - [ ] Implementar el flujo de publicación/consumo de mensajes.
    -   [ ] Modificar el endpoint de Next.js para publicar trabajos en la cola.
    -   [ ] Desarrollar el worker de Node.js para consumir de la cola, ejecutar `crawl4ai` (o su API), y guardar resultados en PostgreSQL vía Prisma.

- [ ] **Gestión de Configuración y Seguridad:**
    *   [ ] Configurar Kubernetes Secrets para credenciales (PostgreSQL, RabbitMQ).
    *   [ ] Configurar Kubernetes ConfigMaps para parámetros de `crawl4ai` (colas, user-agents).

- [ ] **Manejo de Errores y Logging:**
    *   [ ] Implementar Dead-Letter Queue (DLQ) en RabbitMQ.
    *   [ ] Establecer logging estructurado (JSON) con `correlationId`.

---

## Fase 4 y 5: Pruebas, Despliegue y Monitorización

- [ ] **Pruebas:**
    *   [ ] Escribir pruebas unitarias para el worker de Node.js y la lógica de la cola.
    *   [ ] Implementar pruebas de integración para el flujo completo (Next.js -> Cola -> Worker -> DB).
    *   [ ] Añadir pruebas E2E para la funcionalidad de rastreo.
    *   [ ] Ejecutar pruebas de regresión en la aplicación principal.

- [ ] **Despliegue:**
    *   [ ] Crear un pipeline de CI/CD (GitHub Actions) para construir y desplegar la imagen Docker del microservicio `crawl4ai`.
    *   [ ] Desplegar en entorno de Staging.
    *   [ ] Implementar estrategia de despliegue Canary en Producción.

- [ ] **Monitorización:**
    *   [ ] Configurar dashboards en Grafana para métricas clave (cola, uso de recursos, tasa de éxito).
    *   [ ] Establecer alertas para problemas críticos.

---

**Estado Actual:** PoC configurada y lista para ejecución.
