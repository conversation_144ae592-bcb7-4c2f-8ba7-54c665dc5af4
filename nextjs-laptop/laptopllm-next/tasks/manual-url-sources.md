# Tarea: Implementar Gestión Manual de Fuentes de URL

- **Título:** Implementar Gestión Manual de Fuentes de URL
- **Descripción:** Desarrollar una funcionalidad completa para que los administradores puedan gestionar las fuentes de URL para el scraping. La interfaz debe permitir crear, leer, actualizar y eliminar (CRUD) fuentes de URL de forma individual y masiva. El sistema de scraping solo debe procesar las fuentes que estén marcadas como activas y debe registrar la fecha del último raspado exitoso.
- **Estado:** Pendiente

---

## Checklist de Subtareas

-   [x] **1. Backend: Definir Modelo Prisma `UrlSource`**
    -   A<PERSON>dir el modelo `UrlSource` al fichero `prisma/schema.prisma` con los campos: `id`, `name`, `url`, `isActive`, `lastScrapedAt`, `createdAt`, `updatedAt`.
    -   Generar y aplicar la migración de la base de datos.

-   [x] **2. Backend: Implementar API REST**
    -   Crear los endpoints RESTful para las operaciones CRUD sobre `UrlSource`.
        -   `POST /api/url-sources` (Crear una fuente)
        -   `GET /api/url-sources` (Listar fuentes, con paginación y ordenamiento)
        -   `GET /api/url-sources/:id` (Obtener una fuente)
        -   `PUT /api/url-sources/:id` (Actualizar una fuente)
        -   `DELETE /api/url-sources/:id` (Eliminar una fuente)
    -   Crear un endpoint para la carga masiva: `POST /api/url-sources/bulk` (acepta un array de fuentes o un CSV).

-   [x] **3. Backend: Añadir Validación**
    -   Implementar validación en el backend para asegurar que el campo `url` sea una URL válida.
    -   Asegurar que la URL sea única en la base de datos para evitar duplicados.

-   [x] **4. Frontend: Crear Formulario de Gestión**
    -   Diseñar un formulario con `shadcn/ui` para crear y editar una `UrlSource`.
    -   Implementar validación en tiempo real en el formulario (p. ej., con `zod` y `react-hook-form`).

-   [x] **5. Frontend: Implementar Tabla de Datos**
    -   Crear un componente de tabla paginada y ordenable (usando `tanstack-table`) para mostrar las `UrlSource`.
    -   La tabla debe mostrar los campos `name`, `url`, `isActive` y `lastScrapedAt`.
    -   Incluir acciones en cada fila: editar (abrir modal o formulario inline) y eliminar (con diálogo de confirmación).

-   [x] **6. Frontend: Implementar Carga Masiva**
    -   Añadir una sección en la UI para la carga masiva.
    -   Debe soportar la entrada a través de un `textarea` (una URL por línea) y la subida de un fichero CSV.
    -   Utilizar una mutación de React Query para enviar los datos al endpoint de carga masiva.

-   [x] **7. Integración: Conectar Scraper con Fuentes Activas**
    -   Modificar el servicio de scraping (`scraper-service`) para que obtenga únicamente las `UrlSource` con `isActive: true` de la base de datos antes de iniciar el proceso.

-   [x] **8. Integración: Actualizar `lastScrapedAt`**
    -   Asegurar que después de un scraping exitoso para una fuente, se llame al endpoint `PUT /api/url-sources/:id` o se actualice directamente en la base de datos para establecer el valor de `lastScrapedAt` a la fecha y hora actuales.

-   [ ] **9. Pruebas (Testing)**
    -   Escribir pruebas unitarias y de integración para los endpoints del backend.
    -   Escribir pruebas para los componentes de la UI (formulario, tabla) y las interacciones del usuario utilizando Vitest y React Testing Library.
