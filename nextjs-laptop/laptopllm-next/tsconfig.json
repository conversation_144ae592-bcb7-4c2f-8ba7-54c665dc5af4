{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "noImplicitAny": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/features/*": ["./src/features/*"], "@/lib/*": ["./src/lib/*"], "@/shared/*": ["./src/shared/*"], "@/providers/*": ["./src/providers/*"], "@/styles/*": ["./src/styles/*"], "@/test/*": ["./src/test/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/**/*", "tests/**/*", "prisma/**/*"], "exclude": ["node_modules", "generated"]}