# Tareas para el Agente Roo: Proyecto LLM Laptop Lens

Este documento detalla las tareas que el agente Roo debe implementar para la reconstrucción del proyecto "LLM Laptop Lens", siguiendo el "Plan de Reconstrucción Estratégica" y las "Reglas de Ingeniería".

## Instrucciones Generales para el Agente

- **Prioridad:** Siempre consulta el `agent-prompt-with-rules` para el contexto completo, stack tecnológico y reglas de ingeniería.

- **Contexto:** Antes de cada tarea, **DEBES** leer y comprender los archivos relevantes en `memory-bank/` y `docs/`.

- **Planificación:** Antes de implementar, elabora un plan conciso para la tarea actual.

- **Verificación:** Después de cada implementación significativa, ejecuta `pnpm test`, `pnpm lint` y `pnpm type-check`.

- **Documentación:** Actualiza `memory-bank/` y `docs/` con el progreso y los detalles relevantes de la tarea completada.

- **Comunicación:** Si hay ambigüedad o necesitas una decisión fuera de tu alcance, solicita aclaración.

## 🚀 Hito 1: Fundación Técnica

### Objetivo: Establecer entorno de desarrollo moderno y CI/CD

1. **Inicialización del Proyecto:**
   
   - [ ] Ejecutar el script `setup-structure.mjs` para crear la estructura de directorios y archivos base.
   
   - [ ] Asegurar que PostgreSQL esté ejecutándose localmente.
   
   - [ ] Crear la base de datos `llm_laptop_lens` (`createdb llm_laptop_lens`).
   
   - [ ] Instalar todas las dependencias del proyecto (`pnpm install`).
   
   - [ ] Copiar `.env.example` a `.env` y configurar `DATABASE_URL`.
   
   - [ ] Generar el cliente Prisma (`npx prisma generate`).
   
   - [ ] Empujar el esquema de Prisma a la base de datos (`npx prisma db push`).
   
   - [ ] Iniciar el servidor de desarrollo (`pnpm dev`).

2. **Configuración de Calidad de Código:**
   
   - [ ] Verificar la configuración de ESLint y Prettier.
   
   - [ ] Confirmar que los comandos `pnpm lint` y el formateo automático funcionan correctamente.

3. **Configuración de Testing:**
   
   - [ ] Verificar que Vitest esté configurado (`vitest.config.ts`, `tests/setup.ts`).
   
   - [ ] Confirmar que los comandos de prueba (`pnpm test`, `pnpm test:watch`, `pnpm test:coverage`) funcionan.

4. **Configuración de CI/CD (GitHub Actions):**
   
   - [ ] Crear un flujo de trabajo básico de GitHub Actions para linting, testing y build.

## 🗄️ Hito 2: Capa de Datos

### Objetivo: Reconstruir persistencia y servicios de datos

1. **Revisión y Consolidación del Esquema Prisma:**
   
   - [ ] Revisar `prisma/schema.prisma` para asegurar que los modelos `Laptop`, `LLMCompatibility` y `ScrapingConfig` estén correctos y normalizados.
   
   - [ ] Asegurar que los campos `specifications` y `requirements` usen el tipo `Json` y que se mapeen a interfaces TypeScript específicas (`LaptopSpecifications`, `LLMRequirements`).

2. **Implementación de Conexión a Base de Datos:**
   
   - [ ] Asegurar que `src/lib/database.ts` maneje correctamente la conexión y desconexión de Prisma.

3. **Desarrollo de Repositorios/Servicios de Datos:**
   
   - [ ] Crear servicios o repositorios en `src/features/laptops/services/`, `src/features/llm-compatibility/services/`, `src/features/scraper/services/` para interactuar con los modelos de Prisma.
   
   - [ ] Implementar operaciones CRUD básicas para `Laptop`, `LLMCompatibility` y `ScrapingConfig`.

4. **Scripts de Migraciones y Seeding:**
   
   - [ ] Verificar la configuración de migraciones de Prisma.
   
   - [ ] Implementar y probar `scripts/db/seed_llm_data.mjs` para datos iniciales de LLMs.
   
   - [ ] Implementar y probar `scripts/db/seed_laptops.mjs` para datos de laptops de ejemplo.

## 🕸️ Hito 3: Sistema de Scraping

### Objetivo: Motor de scraping moderno con Firecrawl-first

1. **Diseño de Abstracción del Scraper:**
   
   - [ ] Definir una interfaz o clase abstracta `Scraper` (ej., en `src/features/scraper/types.ts`) con un método `scrape(url: string, config?: any): Promise<LaptopData[]>`.

2. **Implementación de Firecrawl Scraper:**
   
   - [ ] Crear `src/features/scraper/firecrawlScraper.ts` que implemente la interfaz `Scraper` y utilice la API de Firecrawl.
   
   - [ ] Integrar la clave `FIRECRAWL_API_KEY` desde `.env`.
   
   - [ ] Implementar manejo de `actions` y `waitFor` de Firecrawl según sea necesario.

3. **Implementación de Puppeteer Scraper (Fallback):**
   
   - [ ] Crear `src/features/scraper/puppeteerScraper.ts` que implemente la interfaz `Scraper` y utilice Puppeteer.
   
   - [ ] Asegurar que pueda manejar sitios que requieran `requiresFullBrowserAutomation`.

4. **Módulo de Orquestación del Scraper:**
   
   - [ ] Desarrollar un servicio (ej., `src/features/scraper/services/scraperOrchestrator.ts`) que decida qué scraper usar (Firecrawl primario, Puppeteer fallback) basándose en la configuración de `ScrapingConfig`.
   
   - [ ] Implementar la lógica para acceder a `https://laptop-ventas.ola.click/products` específicamente.

5. **Transformación y Almacenamiento de Datos de Scraping:**
   
   - [ ] Implementar lógica en el orquestador o en un servicio de transformación (`src/features/scraper/utils/dataTransformer.ts`) para convertir los datos crudos del scraping a `LaptopData`.
   
   - [ ] Integrar el almacenamiento de los `LaptopData` procesados en la base de datos a través de los servicios del Hito 2.

6. **Sistema de Configuración y Scheduling:**
   
   - [ ] Desarrollar la lógica para leer y aplicar las configuraciones de `ScrapingConfig` desde la base de datos.
   
   - [ ] Implementar un mecanismo de scheduling (ej., usando `node-cron` si el backend lo permite) para ejecutar las tareas de scraping periódicamente.

## 🖥️ Hito 4: Interfaz de Usuario

### Objetivo: UI moderna con Shadcn/UI

1. **Configuración Inicial de UI:**
   
   - [ ] Verificar la configuración de Tailwind CSS y Shadcn UI.
   
   - [ ] Asegurar que `src/styles/globals.css` y `tailwind.config.js` estén correctos.

2. **Añadir Componentes UI Esenciales (Shadcn/UI):**
   
   - [ ] Añadir componentes UI básicos (`button`, `input`, `card`, `dialog`, `table`, `form`, `select`, `checkbox`, `radio-group`, `slider`, `sonner`) usando `npx shadcn-ui@latest add [componente]`.

3. **Implementación de Páginas Principales:**
   
   - [ ] Crear la página de listado de laptops (`src/app/laptops/page.tsx` o similar).
   
   - [ ] Crear la página de configuración de scraping (`src/app/scraper-config/page.tsx` o similar).
   
   - [ ] Crear la página de compatibilidad con LLMs (`src/app/llm-compatibility/page.tsx` o similar).

4. **Configuración de Routing:**
   
   - [ ] Implementar la navegación entre las páginas principales usando React Router DOM.

5. **Integración de UI con Servicios de Datos:**
   
   - [ ] Conectar las páginas y componentes UI con los servicios de datos del Hito 2 (usando React Query para la gestión del estado del servidor).
   
   - [ ] Implementar filtros dinámicos en la UI.

6. **Desarrollo de Dashboard Principal:**
   
   - [ ] Crear el dashboard principal con resumen de extracciones, tendencias de precios (gráficos simples usando `recharts`).
   
   - [ ] Implementar alertas configurables y accesos rápidos.

7. **Responsividad de la UI:**
   
   - [ ] Asegurar que toda la interfaz sea completamente responsive para desktop, tablet y mobile.

## 🧪 Hito 5: Testing y Optimización

### Objetivo: Calidad, estabilidad y rendimiento

1. **Desarrollo de Tests Unitarios:**
   
   - [ ] Escribir tests unitarios para la lógica de negocio en `src/features/`.
   
   - [ ] Escribir tests unitarios para hooks personalizados en `src/shared/hooks/`.
   
   - [ ] Escribir tests unitarios para componentes UI complejos en `src/components/`.

2. **Desarrollo de Tests de Integración:**
   
   - [ ] Escribir tests de integración para flujos clave (ej., scraping -> procesamiento -> almacenamiento).
   
   - [ ] Utilizar mocks (ej. MSW) para simular APIs externas.

3. **Tests End-to-End (E2E) con Playwright:**
   
   - [ ] Configurar Playwright para pruebas E2E.
   
   - [ ] Escribir pruebas E2E para los flujos de usuario más críticos.

4. **Análisis y Optimización de Rendimiento:**
   
   - [ ] Realizar análisis de bundle size.
   
   - [ ] Aplicar optimizaciones de React (memoización, `useCallback`, `useMemo`).
   
   - [ ] Implementar lazy loading y code splitting donde sea apropiado.

5. **Revisión y Resolución de Calidad:**
   
   - [ ] Ejecutar `pnpm lint` y resolver todos los warnings y errores.
   
   - [ ] Resolver todos los errores de TypeScript y eliminar el uso de `any` injustificado.
   
   - [ ] Asegurar que la cobertura de pruebas alcance el 80% para la lógica de negocio.

6. **Implementación de Logs y Métricas:**
   
   - [ ] Integrar `winston` para logs detallados de scraping y otras operaciones críticas.
   
   - [ ] Recopilar y mostrar métricas de rendimiento de scraping en el dashboard.
   
   - [ ] Desarrollar el sistema de alertas para notificaciones de fallos o hallazgos.

## ✅ Responsabilidades Continuas

- [ ] **Adherencia a Reglas de Ingeniería:** Aplicar constantemente todas las reglas de `.clinerules` en cada tarea.

- [ ] **Documentación en Código:** Mantener JSDoc/TSDoc actualizado para todas las APIs públicas.

- [ ] **Actualización de READMEs:** Crear y mantener `README.md` para cada directorio principal y módulo significativo.

- [ ] **Auditorías de Seguridad:** Realizar auditorías de dependencias regularmente.

- [ ] **Gestión de Versiones:** Mantener `package.json` con versiones explícitas y scripts claros.

- [ ] **Comunicación Proactiva:** Informar sobre cualquier bloqueo o decisión importante.
