# Plan de Integración de Crawl4AI

Este documento detalla la estrategia y los pasos técnicos para integrar el servicio de web scraping `crawl4ai` en el proyecto LaptopLLM-Finder.

## Información del Proyecto

*   **Propósito:** Plataforma de analítica de datos para marketing digital, enfocada en la búsqueda y compatibilidad de laptops para LLMs.
*   **Stack Tecnológico:** Frontend en Next.js (React), Backend con API Routes de Next.js, ORM Prisma, Base de datos PostgreSQL, y despliegue en un clúster de Kubernetes en GCP.
*   **Objetivo de la Integración:** Complementar nuestras capacidades de scraping existentes (Firecrawl, Puppeteer) con `crawl4ai` para tareas de extracción complejas, almacenando los resultados en nuestra base de datos PostgreSQL a través de nuestro backend.

---

## Fase 1: Estrategia de Arquitectura

Se adopta un **enfoque híbrido** para la integración, combinando simplicidad para el desarrollo local y robustez para producción.

1.  **Desarrollo Local y Prueba de Concepto (PoC):**
    *   **Arquitectura:** Orquestación con `docker-compose`.
    *   **Comunicación:** Llamada HTTP directa desde un endpoint de la API de Next.js al servicio `crawl4ai`.
    *   **Ventajas:** Rápida implementación, ideal para validación y pruebas iniciales.

2.  **Staging y Producción:**
    *   **Arquitectura:** Microservicio `crawl4ai` independiente, orquestado por Kubernetes.
    *   **Comunicación:** Asíncrona, a través de una **Cola de Mensajes** (ej. RabbitMQ, Kafka).
    *   **Ventajas:** Alta escalabilidad, resiliencia, desacoplamiento y manejo eficiente de la carga.

---

## Fase 2: Prueba de Concepto (PoC) con `docker-compose`

El objetivo es validar la funcionalidad básica de `crawl4ai` en un entorno local controlado.

### 2.1. Archivo `docker-compose.yml`

Crear este archivo en la raíz del proyecto para gestionar los servicios de Next.js y `crawl4ai`.

```yaml
version: "3.8"
services:
  crawl4ai:
    image: unclecode/crawl4ai:latest
    container_name: crawl4ai
    ports:
      - "11235:11235"
    shm_size: '1g' # Asignar memoria compartida suficiente para el navegador
    networks:
      - app-network

  nextjs:
    build:
      context: .
      dockerfile: Dockerfile # Asumiendo que existe un Dockerfile para Next.js
    ports:
      - "3000:3000"
    environment:
      - CRAWLAI_API_URL=http://crawl4ai:11235
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    networks:
      - app-network
    depends_on:
      - crawl4ai

networks:
  app-network:
    driver: bridge
```

### 2.2. Endpoint de API en Next.js

Crear un endpoint para actuar como proxy y comunicarse con el servicio `crawl4ai`.

**Ruta:** `src/app/api/crawl/route.ts`

```typescript
import { NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: Request) {
  const { url } = await request.json();

  if (!url) {
    return NextResponse.json({ error: 'URL es requerida' }, { status: 400 });
  }

  const CRAWLAI_API_URL = process.env.CRAWLAI_API_URL || 'http://localhost:11235';

  try {
    // Enviar la tarea de rastreo a crawl4ai
    const response = await axios.post(`${CRAWLAI_API_URL}/crawl`, {
      urls: [url],
    });

    const taskId = response.data.task_id;
    if (!taskId) {
      throw new Error('No se pudo obtener el ID de la tarea de crawl4ai');
    }

    // Esperar y consultar el resultado
    let resultData;
    while (true) {
      await new Promise(resolve => setTimeout(resolve, 3000)); // Esperar 3 segundos
      const resultResponse = await axios.get(`${CRAWLAI_API_URL}/task/${taskId}`);
      
      if (resultResponse.data.status === 'completed') {
        resultData = resultResponse.data.result;
        break;
      }
      
      if (resultResponse.data.status === 'failed') {
        throw new Error(`La tarea de rastreo falló: ${resultResponse.data.error}`);
      }
    }

    return NextResponse.json({ success: true, data: resultData });

  } catch (error) {
    console.error('Error al comunicarse con Crawl4AI:', error);
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
    return NextResponse.json({ error: 'Error al comunicarse con el servicio de Crawl4AI', details: errorMessage }, { status: 500 });
  }
}
```

### 2.3. Interfaz de Usuario para Pruebas

Crear una página simple para enviar URLs y ver los resultados.

**Ruta:** `src/app/admin/crawl-test/page.tsx`

```tsx
"use client";

import { useState } from 'react';

export default function CrawlTestPage() {
  const [url, setUrl] = useState<string>('https://www.nbcnews.com/business');
  const [result, setResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/crawl', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error en la solicitud');
      }

      const data = await response.json();
      setResult(data.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Ocurrió un error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{ padding: '2rem' }}>
      <h1>Prueba de Integración con Crawl4AI</h1>
      <form onSubmit={handleSubmit}>
        <input
          type="text"
          placeholder="Ingresar URL"
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          style={{ width: '400px', padding: '8px' }}
        />
        <button type="submit" disabled={isLoading} style={{ padding: '8px 12px', marginLeft: '10px' }}>
          {isLoading ? 'Rastreando...' : 'Rastrear'}
        </button>
      </form>

      {error && <div style={{ color: 'red', marginTop: '1rem' }}>Error: {error}</div>}
      
      {result && (
        <div style={{ marginTop: '1rem' }}>
          <h2>Resultado del Rastreo</h2>
          <pre style={{ background: '#f4f4f4', padding: '1rem', whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
```

### 2.4. Pasos de Ejecución

1.  Asegurarse de que Docker esté en ejecución.
2.  Ejecutar `docker-compose up --build` desde la raíz del proyecto.
3.  Acceder a `http://localhost:3000/admin/crawl-test`.
4.  Ingresar una URL y ejecutar la prueba.

---

## Fase 3: Plan de Implementación para Producción

Esta fase se ejecutará después de validar la PoC.

*   **Diseño de la Interfaz con Cola de Mensajes:**
    *   **Tecnología:** Se seleccionará RabbitMQ por su simplicidad y robustez.
    *   **Colas:**
        *   `crawl-jobs-queue`: Para enviar nuevas tareas de rastreo.
        *   `crawl-results-queue`: Para recibir notificaciones de finalización.
    *   **Flujo:**
        1.  El backend de Next.js publica un mensaje en `crawl-jobs-queue`.
        2.  El microservicio `crawl4ai` consume el mensaje.
        3.  El microservicio `crawl4ai` publica el resultado en bruto en la cola `crawl-results-queue`.
        4.  Un **worker de Node.js** en nuestro backend consume el resultado, lo valida y lo guarda en la base de datos usando **Prisma Client**.

*   **Gestión de Configuración y Seguridad:**
    *   Las credenciales (PostgreSQL, RabbitMQ) se gestionarán a través de **Kubernetes Secrets**.
    *   Las configuraciones (nombre de las colas, user-agents) se gestionarán con **Kubernetes ConfigMaps**.

*   **Manejo de Errores y Logging:**
    *   Se implementará una **Dead-Letter Queue (DLQ)** en RabbitMQ para manejar trabajos fallidos.
    *   Se utilizará un sistema de logging estructurado (JSON) con un `correlationId` para trazabilidad completa.

---

## Fase 4 y 5: Pruebas, Despliegue y Monitorización

*   **Pruebas:** Se crearán pruebas de integración para el flujo completo con la cola de mensajes y pruebas E2E para la funcionalidad de rastreo.
*   **Despliegue:** Se creará un pipeline de CI/CD en GitHub Actions para construir y desplegar la imagen del crawler en nuestro clúster de Kubernetes. Se usará una estrategia **Canary** para el despliegue en producción.
*   **Monitorización:** Se crearán dashboards en Grafana para monitorizar las métricas clave, incluyendo la profundidad de la cola de RabbitMQ, el uso de recursos del crawler y la tasa de éxito de los trabajos.
