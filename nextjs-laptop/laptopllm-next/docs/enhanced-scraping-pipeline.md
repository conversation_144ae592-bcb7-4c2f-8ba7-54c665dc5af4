# Enhanced Scraping Pipeline

## Overview

The Enhanced Scraping Pipeline is a comprehensive, production-ready scraping system built on top of the existing LaptopLLM Finder scraping infrastructure. It provides robust job queue management, real-time monitoring, automated retry mechanisms, and scalable processing capabilities.

## Architecture

### Core Components

1. **Enhanced Scraping Service** (`src/lib/scraping/enhanced-scraping-service.ts`)
   - Main orchestration layer
   - Integrates queue management with existing scraping services
   - Provides high-level API for job submission and management

2. **Queue Manager** (`src/lib/queue/queue-manager.ts`)
   - BullMQ-based job queue management
   - Redis-backed persistence
   - Multiple queue support with different priorities

3. **Job Manager** (`src/lib/jobs/job-manager.ts`)
   - Database persistence for job metadata
   - Job lifecycle tracking
   - Statistics and metrics collection

4. **Data Processor** (`src/lib/queue/processors/data-processor.ts`)
   - Validation and transformation of scraped data
   - Duplicate detection and merging
   - LLM compatibility enrichment

5. **Scraping Worker** (`src/lib/queue/workers/scraping-worker.ts`)
   - Processes scraping jobs from the queue
   - Batch processing with concurrency control
   - Progress tracking and error handling

## Features

### ✅ Implemented Features

- **Robust Job Queue System**: BullMQ + Redis for persistent job management
- **Multi-tier Scraping**: Crawl4AI → Firecrawl → Puppeteer fallback hierarchy
- **Batch Processing**: Process multiple URLs efficiently with concurrency control
- **Real-time Monitoring**: Live dashboard with queue and job statistics
- **Data Validation**: Schema-based validation with Zod
- **Duplicate Detection**: Smart deduplication with configurable merge strategies
- **LLM Compatibility**: Automatic compatibility scoring for scraped laptops
- **Error Handling**: Comprehensive error handling with retry mechanisms
- **Rate Limiting**: Configurable rate limits per site
- **Progress Tracking**: Real-time progress updates for long-running jobs
- **Health Monitoring**: System health checks and alerting
- **Graceful Shutdown**: Proper cleanup on service termination

### 🔄 Queue System

- **Multiple Queues**: Separate queues for scraping, data processing, and notifications
- **Priority Levels**: Low, normal, high, and critical priority jobs
- **Retry Logic**: Exponential backoff with configurable retry attempts
- **Circuit Breaker**: Automatic failure detection and recovery
- **Metrics Collection**: Comprehensive job and queue metrics

### 📊 Monitoring & Analytics

- **Real-time Dashboard**: Live monitoring of jobs and queues
- **Performance Metrics**: Success rates, processing times, throughput
- **Health Status**: Overall system health with warning/critical alerts
- **Historical Data**: Job history and trend analysis

## Installation & Setup

### Prerequisites

- Redis server running (for job queue)
- PostgreSQL database (for job persistence)
- Node.js 18+ with npm/yarn/pnpm

### Environment Variables

```bash
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/laptopllm

# Scraping Service URLs
CRAWL4AI_URL=http://localhost:11235
FIRECRAWL_API_KEY=your_firecrawl_api_key

# Monitoring & Alerts
ALERT_EMAIL=<EMAIL>
ALERT_WEBHOOK=https://hooks.slack.com/your-webhook
SLACK_WEBHOOK=https://hooks.slack.com/your-slack-webhook
```

### Database Migration

The enhanced scraping system requires additional database tables:

```bash
npx prisma db push
```

### Install Dependencies

```bash
npm install
# or
yarn install
# or
pnpm install
```

## Usage

### Starting the System

1. **Start the Next.js Application**:
   ```bash
   npm run dev
   ```

2. **Start the Workers** (in a separate terminal):
   ```bash
   npm run workers
   # or for development with auto-restart:
   npm run workers:dev
   ```

### API Endpoints

#### Submit Scraping Job
```bash
POST /api/enhanced-scraping
Content-Type: application/json

{
  "sourceId": "source_123",
  "priority": "normal",
  "config": {
    "batchSize": 10,
    "maxConcurrent": 3,
    "enableScreenshots": false,
    "saveToDatabase": true
  }
}
```

#### Submit Batch Jobs
```bash
PUT /api/enhanced-scraping
Content-Type: application/json

{
  "sourceIds": ["source_1", "source_2", "source_3"],
  "priority": "high",
  "config": {
    "batchSize": 5,
    "maxConcurrent": 2
  },
  "staggerDelay": 5000
}
```

#### Get Job Status
```bash
GET /api/enhanced-scraping/{jobId}
```

#### Get System Statistics
```bash
GET /api/enhanced-scraping/stats?timeRange=24h&includeQueueStats=true&includeJobStats=true
```

### Dashboard Access

Visit `/scraper/dashboard` to access the real-time monitoring dashboard.

## Configuration

### Site-Specific Configuration

The system includes pre-configured settings for major laptop retailers:

- **Amazon**: Rate limited, 2s delays, 5 retry attempts
- **Best Buy**: Moderate rate limits, 1.5s delays
- **Newegg**: Higher throughput, 1s delays
- **Micro Center**: Conservative rate limits, 2.5s delays
- **B&H Photo**: Balanced settings, 0.8s delays
- **Adorama**: Standard configuration, 1.2s delays

### Queue Configuration

```typescript
// Example queue configuration
{
  queueConfig: {
    redis: {
      host: 'localhost',
      port: 6379,
      password: 'your_password'
    },
    queues: [
      {
        name: 'scraping-jobs',
        concurrency: 3,
        rateLimiter: {
          max: 10,
          duration: 60000 // 10 requests per minute
        }
      }
    ]
  }
}
```

## Monitoring

### Health Checks

The system provides comprehensive health monitoring:

- **Queue Health**: Monitor queue lengths and processing rates
- **Job Success Rates**: Track success/failure ratios
- **Processing Times**: Monitor average processing durations
- **System Resources**: Memory and CPU usage tracking

### Alerts

Configurable alerts for:
- High failure rates (>20%)
- Long queue lengths (>100 jobs)
- Slow processing times (>5 minutes)
- System resource exhaustion

## Development

### Adding New Scrapers

1. Create scraper service in `src/lib/scraping/services/`
2. Add site configuration in `enhanced-scraping.config.ts`
3. Update the scraping orchestrator to include the new service
4. Add site-specific selectors and rate limits

### Extending Data Processing

1. Modify validation schemas in `data-processor.ts`
2. Add new transformation logic
3. Extend LLM compatibility calculations
4. Update database schema if needed

### Custom Job Types

1. Define new job types in `src/lib/queue/types.ts`
2. Create corresponding processors
3. Add queue configuration
4. Update API endpoints

## Troubleshooting

### Common Issues

1. **Redis Connection Errors**
   - Verify Redis server is running
   - Check connection credentials
   - Ensure network connectivity

2. **Job Processing Failures**
   - Check worker logs for errors
   - Verify scraping service availability
   - Review rate limiting settings

3. **Database Connection Issues**
   - Verify PostgreSQL is running
   - Check database credentials
   - Ensure schema is up to date

### Debugging

Enable debug logging:
```bash
NODE_ENV=development npm run workers
```

Check job logs in the database:
```sql
SELECT * FROM "JobLog" WHERE "jobId" = 'your_job_id' ORDER BY "createdAt" DESC;
```

## Performance Optimization

### Scaling Workers

- Increase worker concurrency for higher throughput
- Run multiple worker processes across different machines
- Use Redis Cluster for high-availability queue management

### Database Optimization

- Add indexes on frequently queried columns
- Implement data archiving for old job records
- Use read replicas for analytics queries

### Memory Management

- Configure job retention policies
- Implement data cleanup schedules
- Monitor memory usage patterns

## Security Considerations

- Secure Redis with authentication and encryption
- Implement rate limiting to prevent abuse
- Validate all input data thoroughly
- Use environment variables for sensitive configuration
- Implement proper error handling to prevent information leakage

## Future Enhancements

- **Distributed Processing**: Support for multiple worker nodes
- **Advanced Scheduling**: Cron-based job scheduling
- **Machine Learning**: Intelligent retry strategies and failure prediction
- **Real-time Notifications**: WebSocket-based live updates
- **Advanced Analytics**: Detailed performance analytics and reporting
