# GraphQL API Documentation

## Overview

The LaptopLLM Finder GraphQL API provides a comprehensive interface for querying laptop data, compatibility information, deals, and analytics. Built with Apollo Server and integrated with Next.js, it offers type-safe queries and mutations for all laptop-related operations.

## Endpoint

- **Development**: `http://localhost:3000/api/graphql`
- **Production**: `https://your-domain.com/api/graphql`

## Features

- 🔍 **Advanced Search**: Full-text search with filters and pagination
- 📊 **Analytics**: Comprehensive laptop statistics and insights
- 🏷️ **Deals Management**: Hot deals and discount tracking
- 🔧 **Compatibility Analysis**: LLM compatibility scoring and recommendations
- 📱 **Real-time Updates**: Live data with caching strategies
- 🛡️ **Type Safety**: Full TypeScript integration
- 🚀 **Performance**: Optimized queries with intelligent caching

## Schema Overview

### Core Types

#### Laptop
The main entity representing a laptop with all its specifications and metadata.

```graphql
type Laptop {
  id: ID!
  title: String!
  brand: String!
  model: String!
  price: Float
  currency: String!
  rating: Float
  url: String!
  isAvailable: Boolean!
  
  # Relations
  specifications: Specifications
  features: Features
  compatibility: Compatibility
  deals: [Deal!]!
  reviews: [Review!]!
}
```

#### Specifications
Detailed technical specifications including CPU, memory, storage, display, and GPU.

```graphql
type Specifications {
  cpu: CPU
  memory: Memory
  storage: Storage
  display: Display
  gpu: GPU
  connectivity: Connectivity
}
```

#### Compatibility
LLM compatibility analysis with detailed scoring and recommendations.

```graphql
type Compatibility {
  averageScore: Int!
  cpuScore: Int!
  memoryScore: Int!
  gpuScore: Int!
  overallRating: String!
  recommendedModels: [String!]
  topModels: [LLMModel!]!
}
```

## Common Queries

### 1. Search Laptops

```graphql
query SearchLaptops($input: LaptopSearchInput) {
  laptops(input: $input) {
    edges {
      node {
        id
        title
        brand
        model
        price
        currency
        compatibility {
          averageScore
          overallRating
        }
        specifications {
          cpu {
            manufacturer
            model
            cores
          }
          memory {
            size
            type
          }
        }
      }
    }
    pageInfo {
      hasNextPage
      totalCount
      currentPage
    }
  }
}
```

**Variables:**
```json
{
  "input": {
    "query": "gaming laptop",
    "filters": {
      "brands": ["ASUS", "MSI"],
      "priceRange": {
        "min": 1000,
        "max": 3000
      },
      "minCompatibilityScore": 75
    },
    "pagination": {
      "page": 1,
      "limit": 20
    },
    "sort": {
      "sortBy": "COMPATIBILITY",
      "sortOrder": "DESC"
    }
  }
}
```

### 2. Get Single Laptop

```graphql
query GetLaptop($id: ID!) {
  laptop(id: $id) {
    id
    title
    brand
    model
    price
    originalPrice
    currency
    rating
    reviewCount
    description
    
    specifications {
      cpu {
        manufacturer
        model
        cores
        threads
        baseFrequency
        architecture
      }
      memory {
        size
        type
        speed
        isUpgradeable
      }
      storage {
        capacity
        type
        speed
      }
      gpu {
        manufacturer
        model
        type
        memory
      }
    }
    
    compatibility {
      averageScore
      cpuScore
      memoryScore
      gpuScore
      storageScore
      overallRating
      recommendedModels
      topModels {
        model
        score
        estimatedPerformance
      }
    }
    
    deals {
      title
      discountPercentage
      discountedPrice
      expiresAt
      store
    }
    
    reviews {
      rating
      title
      content
      author
      date
      verified
    }
  }
}
```

### 3. Get Analytics

```graphql
query GetAnalytics {
  laptopAnalytics {
    totalLaptops
    averagePrice
    brandDistribution {
      brand
      count
    }
    compatibilityStats {
      averageScore
      distribution {
        excellent
        good
        fair
        poor
      }
      topPerformers {
        id
        title
        brand
        compatibility {
          averageScore
        }
      }
    }
  }
}
```

### 4. Get Hot Deals

```graphql
query GetHotDeals($limit: Int = 10) {
  hotDeals(limit: $limit) {
    id
    title
    description
    originalPrice
    discountedPrice
    discountPercentage
    store
    expiresAt
    isActive
  }
}
```

### 5. Get Recommendations

```graphql
query GetRecommendations($input: RecommendationInput!) {
  getRecommendations(input: $input) {
    recommendations {
      laptop {
        id
        title
        brand
        price
        compatibility {
          averageScore
        }
      }
      score
      reasons
      pros
      cons
      matchPercentage
    }
    summary
  }
}
```

**Variables:**
```json
{
  "input": {
    "budget": {
      "min": 1500,
      "max": 2500
    },
    "primaryUse": "AI Development",
    "performanceImportance": 9,
    "portabilityImportance": 6,
    "preferredBrands": ["ASUS", "Lenovo"]
  }
}
```

## Mutations

### 1. Create Laptop (Admin)

```graphql
mutation CreateLaptop($input: CreateLaptopInput!) {
  createLaptop(input: $input) {
    id
    title
    brand
    model
    price
    url
  }
}
```

### 2. Update Laptop (Admin)

```graphql
mutation UpdateLaptop($id: ID!, $input: UpdateLaptopInput!) {
  updateLaptop(id: $id, input: $input) {
    id
    title
    price
    isAvailable
  }
}
```

### 3. Trigger Scraping

```graphql
mutation TriggerScraping($laptopId: ID) {
  triggerScraping(laptopId: $laptopId) {
    id
    status
    lastScrapedAt
    nextScheduledAt
  }
}
```

## Filtering and Sorting

### Available Filters

- **brands**: Array of brand names
- **priceRange**: Min/max price range
- **specifications**: Hardware specifications filters
- **minCompatibilityScore**: Minimum LLM compatibility score
- **inStock**: Availability filter
- **hasDeals**: Active deals filter

### Sort Options

- **PRICE**: Sort by price
- **COMPATIBILITY**: Sort by LLM compatibility score
- **RATING**: Sort by user rating
- **NAME**: Sort alphabetically
- **NEWEST**: Sort by creation date
- **POPULARITY**: Sort by review count

## Error Handling

The API returns structured errors with helpful messages:

```json
{
  "errors": [
    {
      "message": "Laptop not found",
      "locations": [{"line": 2, "column": 3}],
      "path": ["laptop"],
      "extensions": {
        "code": "NOT_FOUND"
      }
    }
  ]
}
```

## Rate Limiting

- **Development**: No rate limiting
- **Production**: 1000 requests per hour per IP

## Caching

The API implements intelligent caching:

- **Laptop data**: 5 minutes
- **Analytics**: 15 minutes
- **Deals**: 1 minute
- **Search results**: 2 minutes

## Authentication

Currently, the API is public for read operations. Admin mutations require authentication (to be implemented).

## GraphQL Playground

In development mode, access the GraphQL Playground at:
`http://localhost:3000/api/graphql`

This provides:
- Interactive query builder
- Schema documentation
- Query validation
- Real-time testing

## Client Integration

### React Hooks

```typescript
import { useSearchLaptops, useGetLaptop } from '@/lib/graphql/hooks'

// Search laptops
const { data, loading, error } = useSearchLaptops({
  query: "gaming",
  filters: { brands: ["ASUS"] }
})

// Get single laptop
const { data: laptop } = useGetLaptop("laptop-id")
```

### Apollo Client

```typescript
import { apolloClient } from '@/lib/graphql/client'
import { SEARCH_LAPTOPS } from '@/lib/graphql/queries'

const { data } = await apolloClient.query({
  query: SEARCH_LAPTOPS,
  variables: { input: { query: "laptop" } }
})
```

## Performance Tips

1. **Use fragments** for reusable field selections
2. **Implement pagination** for large result sets
3. **Cache results** with Apollo Client
4. **Request only needed fields** to minimize payload
5. **Use variables** instead of string interpolation

## Support

For API support and questions:
- Check the GraphQL Playground documentation
- Review error messages for debugging hints
- Use TypeScript for better development experience
