## LaptopLLM-Finder

Crie uma aplicação web completa chamada "LaptopLLM-Finder" que realize web scraping de sites de venda de laptops, processe os dados, e apresente uma interface de usuário intuitiva para ajudar os usuários a encontrar laptops com a melhor relação custo-benefício para executar modelos LLM open-source do Ollama. A aplicação é desenvolvida utilizando Next.js (com React e TypeScript) para o frontend, Node.js para o backend, Prisma ORM com **PostgreSQL** para o banco de dados, e **Firecrawl MCP como motor de web scraping primário, com Puppeteer como fallback configurável**. O gerenciamento de pacotes é feito com **pnpm** em uma **estrutura de monorepo**.

**Requisitos Detalhados:**

**1. Configuração Inicial:**

- A aplicação deve ser capaz de configurar múltiplas fontes de dados (URLs base) através de uma interface de usuário dedicada.
- URLs base iniciais: `revolico.com`, `smart-things.ola.click`, `laptop-ventas.ola.click`.
- Deve haver um mecanismo para gerenciar credenciais e tokens de acesso (se aplicável) e limites de taxa por site.
- A configuração de cada fonte de scraping (`ScrapingSource`) deve permitir a definição de se a automação completa do navegador (Puppeteer) é necessária (`requiresFullBrowserAutomation`).

**2. Filtros de Busca:**

- Implementar filtros de busca configuráveis pelo usuário na interface:
  - **Termos principais:** `["laptop", "notebook", "portátil"]`
  - **Termos de exclusão:** `["accesorios", "partes", "repuestos"]`
  - **Categoria:** `"Laptops/Computadoras portáteis"`
  - **Rango de preços:** Configurável pelo usuário (mínimo e máximo).
  - **Antiguidade máxima:** 30 dias (para resultados de scraping).

**3. Extração de Dados (Web Scraping):**

- A aplicação deve priorizar o uso do **Firecrawl MCP** para a extração de dados, utilizando `firecrawl.service.ts` e implementações de scraper baseadas em Firecrawl (`FirecrawlHtmlScraper`, `FirecrawlApiScraper`).
- Para sites que exigem automação de navegador mais complexa ou JavaScript pesado, o **Puppeteer** deve ser usado como um mecanismo de fallback configurável.
- O scraping deve ser robusto e lidar com diferentes estruturas HTML e conteúdo dinâmico, utilizando as capacidades de `actions` e `waitFor` do Firecrawl quando necessário.
- A URL `laptop-ventas.ola.click` deve ser acessada via `https://laptop-ventas.ola.click/products` para os produtos.

**4. Processamento e Armazenamento de Dados:**

- **Validação:** Validar a integridade e o formato dos dados extraídos, utilizando tipagem estrita (`TypeScript strict mode`) e evitando sempre o tipo `any`.
- **Normalização:** Normalizar preços (para uma moeda comum, se necessário) e especificações (para um formato consistente).
- **Eliminação de Duplicados:** Implementar lógica para identificar e remover entradas duplicadas.
- **Armazenamento:** Salvar os dados em um banco de dados **PostgreSQL** (via Prisma ORM) com um esquema normalizado que inclui tabelas como `laptop_specifications`, `components`, `llm_compatibility` e `pricing`. O campo `especificacoes` deve ser mapeado para interfaces TypeScript específicas como `LaptopSpecifications` e `LLMRequirements`.

**5. Visualização Integrada (UI/UX):**

- **Dashboard Principal:** Criar um dashboard interativo que inclua:
  - Resumo das extrações (número de laptops, última atualização).
  - Tendências de preços (gráficos simples).
  - Alertas configuráveis (ex: novo laptop abaixo de X preço).
  - Filtros dinâmicos (marca, modelo, preço, especificações, etc.).
- **Navegação Fluida:** Permitir navegação intuitiva entre os módulos da aplicação.
- **Acessos Rápidos:** Implementar acessos rápidos personalizáveis para funcionalidades comuns.
- **Interface Unificada:** A UI deve permitir configurar fontes e filtros, visualizar resultados em tempo real, exportar dados, gerenciar alertas e analisar tendências.
- **Responsividade:** A interface deve ser totalmente responsiva para funcionar em diferentes tamanhos de tela (desktop, tablet, mobile).

**6. Monitoramento e Manutenção:**

- **Logs Detalhados:** Gerar logs detalhados de cada execução do scraping (sucesso, falhas, erros) utilizando a classe `Logger`.
- **Métricas de Desempenho:** Coletar e exibir métricas de desempenho do scraping (tempo de execução, número de itens processados).
- **Sistema de Alertas:** Notificaciones para falhas no scraping o cuando nuevos laptops que atiendan a criterios específicos son encontrados.
- **Actualización Automática de Seletores:** Considerar un mecanismo para facilitar la actualización de seletores de scraping caso las estructuras dos sites muden (puede ser manual vía interface de configuración o un sistema más avanzado).

**Instrucciones de Implementación:**

- Continue refatorando a estrutura do projeto existente, focando na sanitização de tipos e resolução de erros fundamentais (Phase 1).
- Integre o Firecrawl como o motor de scraping primário, desenvolvendo implementações de scraper baseadas em Firecrawl e mantendo o Puppeteer como um fallback configurável.
- Implemente as APIs de backend para gerenciar o scraping, dados e progresso.
- Construa o frontend iterativamente, começando pelo dashboard e filtros.
- Garanta que a aplicação seja modular e fácil de estender, aderindo aos `clinerules` do projeto.
- Priorize a estabilidade e a robustez do scraping, com ênfase na tipagem estrita e na eliminação de `any` types.