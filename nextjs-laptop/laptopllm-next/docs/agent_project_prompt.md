# Project: LaptopLLM-Finder - Agent Prompt

## Objective General

<PERSON><PERSON> uma aplicação web completa chamada "LaptopLLM-Finder" que realize web scraping de sites de venda de laptops, processe os dados, e apresente uma interface de usuário intuitiva para ajudar os usuários a encontrar laptops com a melhor relação custo-benefício para executar modelos LLM open-source do Ollama.

## Stack Tecnológico

A aplicação é desenvolvida utilizando:

* **Frontend:** Next.js (^15.4.4), React, TypeScript (^5.8.3), Shadcn UI (Radix UI), Tailwind CSS (^4.1.11), React Context API, React Query (^5.83.0).
* **Backend:** Node.js (pnpm@10.13.1), TypeScript (^5.8.3).
* **Banco de Dados:** PostgreSQL (latest LTS) via Prisma ORM (^6.12.0). SQLite para desenvolvimento local.
* **Web Scraping:** Firecrawl MCP (primário), Puppeteer (^24.15.0) (fallback configurável).
* **Gerenciamento de Pacotes:** pnpm em uma estrutura de monorepo.
* **Testes:** Vitest (^3.2.4), React Testing Library (^16.3.0), Playwright (^1.54.1).
* **Ferramentas de Desenvolvimento:** ESLint (^9.31.0), Prettier, Vite (^7.0.6).
* **Controle de Versão:** Git.

## Requisitos Detalhados

### 1. Configuração Inicial

* A aplicação deve ser capaz de configurar múltiplas fontes de dados (URLs base) através de uma interface de usuário dedicada.
* URLs base iniciais: `revolico.com`, `smart-things.ola.click`, `laptop-ventas.ola.click`.
* Mecanismo para gerenciar credenciais, tokens de acesso e limites de taxa por site.
* A configuração de cada fonte de scraping (`ScrapingSource`) deve permitir a definição de se a automação completa do navegador (Puppeteer) é necessária (`requiresFullBrowserAutomation`).

### 2. Filtros de Busca

Implementar filtros de busca configuráveis pelo usuário na interface:

* **Termos principais:** `["laptop", "notebook", "portátil"]`
* **Termos de exclusão:** `["accesorios", "partes", "repuestos"]`
* **Categoria:** `"Laptops/Computadoras portáteis"`
* **Rango de preços:** Configurável pelo usuário (mínimo e máximo).
* **Antiguidade máxima:** 30 dias (para resultados de scraping).

### 3. Extração de Dados (Web Scraping)

* Priorizar o uso do **Firecrawl MCP** para a extração de dados, utilizando `firecrawl.service.ts` e implementações de scraper baseadas em Firecrawl (`FirecrawlHtmlScraper`, `FirecrawlApiScraper`).
* Para sites que exigem automação de navegador mais complexa ou JavaScript pesado, o **Puppeteer** deve ser usado como um mecanismo de fallback configurável.
* O scraping deve ser robusto e lidar com diferentes estruturas HTML e conteúdo dinâmico, utilizando as capacidades de `actions` e `waitFor` do Firecrawl quando necessário.
* A URL `laptop-ventas.ola.click` deve ser acessada via `https://laptop-ventas.ola.click/products` para os produtos.

### 4. Processamento e Armazenamento de Dados

* **Validação:** Validar a integridade e o formato dos dados extraídos, utilizando tipagem estrita (`TypeScript strict mode`) e evitando sempre o tipo `any`.
* **Normalização:** Normalizar preços (para uma moeda comum, se necessário) e especificações (para um formato consistente).
* **Eliminação de Duplicados:** Implementar lógica para identificar e remover entradas duplicadas.
* **Armazenamento:** Salvar os dados em um banco de dados **PostgreSQL** (via Prisma ORM) com um esquema normalizado que inclui tabelas como `laptop_specifications`, `components`, `llm_compatibility` e `pricing`. O campo `especificacoes` deve ser mapeado para interfaces TypeScript específicas como `LaptopSpecifications` e `LLMRequirements`.

### 5. Visualização Integrada (UI/UX)

* **Dashboard Principal:** Criar um dashboard interativo que inclua:
  * Resumo das extrações (número de laptops, última atualização).
  * Tendências de preços (gráficos simples).
  * Alertas configuráveis (ex: novo laptop abaixo de X preço).
  * Filtros dinâmicos (marca, modelo, preço, especificações, etc.).
* **Navegação Fluida:** Permitir navegação intuitiva entre os módulos da aplicação.
* **Acessos Rápidos:** Implementar acessos rápidos personalizáveis para funcionalidades comuns.
* **Interface Unificada:** A UI deve permitir configurar fontes e filtros, visualizar resultados em tempo real, exportar dados, gerenciar alertas e analisar tendências.
* **Responsividade:** A interface deve ser totalmente responsiva para funcionar em diferentes tamanhos de tela (desktop, tablet, mobile).

### 6. Monitoramento e Manutenção

* **Logs Detalhados:** Gerar logs detalhados de cada execução do scraping (sucesso, falhas, erros) utilizando a classe `Logger`.
* **Métricas de Desempenho:** Coletar e exibir métricas de desempenho do scraping (tempo de execução, número de itens processados).
* **Sistema de Alertas:** Notificaciones para falhas no scraping o cuando nuevos laptops que atiendan a criterios específicos son encontrados.
* **Actualización Automática de Seletores:** Considerar un mecanismo para facilitar la actualización de seletores de scraping caso las estructuras dos sites muden (puede ser manual vía interface de configuración o un sistema más avanzado).

## Estrutura de Diretórios (Monorepo)

```
project-root/
├─ prisma/
│    └─ schema.prisma
├─ src/
│    ├─ app/                  (Next.js App Router - rutas UI y API)
│    │    ├─ feature-dashboard/  (rutas UI páginas por característica)
│    │    ├─ api/
│    │    │   └─ url-source/
│    │    │         └─ route.ts
│    │    └─ (route-groups)/
│    ├─ features/             (Código organizado por característica/dominio de negocio)
│    │    └─ urlSource/
│    │         ├─ components/
│    │         ├─ hooks/
│    │         ├─ services/
│    │         └─ types.ts
│    ├─ components/           (Componentes UI)
│    │    ├─ ui/               (Shadcn UI, genéricos y reutilizables)
│    │    ├─ atoms/            (Atomic Design - átomos)
│    │    ├─ molecules/        (Atomic Design - moléculas)
│    │    ├─ organisms/        (Atomic Design - organismos)
│    │    └─ shared/           (Componentes reutilizables no específicos de característica)
│    ├─ hooks/                (Hooks personalizados compartidos)
│    ├─ services/             (Lógica de negocio, llamadas a API, utilidades compartidas)
│    ├─ repositories/         (Lógica de acceso a datos, abstracción de DB/APIs externas)
│    ├─ contexts/             (Manejo de estado global con React Context API)
│    ├─ types/                (Definiciones de tipos globales o compartidos)
│    └─ pages/                (Legacy Page Router - si aplica, para migración gradual)
├─ packages/                 (Sub-paquetes del monorepo, ej. `scraper-service`)
│    └─ scraper-service/
├─ tasks/
├─ docs/
├─ .clinerules/
├─ README.md
└─ package.json
```

## Reglas de Ingeniería de Software (clinerules)

El agente debe adherirse estrictamente a las siguientes reglas, detalladas en el directorio `.clinerules/`:

### 01 - Calidad de Código y Estilo

* **Convenciones de Nomenclatura:** `camelCase` para variables/funciones, `PascalCase` para clases/componentes, `SCREAMING_SNAKE_CASE` para constantes globales, `kebab-case` para archivos.
* **Formato de Código:** ESLint y Prettier, 2 espacios de indentación, longitud de línea 100-120 caracteres, comillas simples.
* **Legibilidad:** Comentarios claros para lógica compleja, JSDoc/TSDoc para funciones/clases/interfaces/tipos.
* **Principios SOLID:** Adherencia a SRP, OCP, LSP, ISP, DIP.
* **TypeScript Strict Mode:** Uso obligatorio de tipado estricto, evitar `any` y variables globales.

### 02 - Arquitectura y Estructura de Componentes

* **Organización de Directorios:** Estructura basada en características (`src/features/`), componentes categorizados (`src/components/ui/`, `atoms/`, `molecules/`, `organisms/`, `shared/`), hooks (`src/hooks/`), servicios (`src/services/`), repositorios (`src/repositories/`), contextos (`src/contexts/`), tipos (`src/types/`).
* **Patrones de Componentes:** Preferir componentes funcionales con hooks, separación de preocupaciones, reutilización, composición sobre herencia.
* **Manejo de Estado:** `useState`/`useReducer` para local, React Context API para global simple, React Query/SWR para estado de servidor.
* **Modularidad de Hooks:** Evitar hooks monolíticos, promover la reutilización y testabilidad.
* **Debe incluir:** Arquitectura por capas, Atomic Design, Custom hooks, Barrel exports, Dependency injection patterns, Single Responsibility Principle.

### 03 - Manejo de Datos y APIs

* **Estrategias de Fetching:** Centralización, React Query/SWR, manejo consistente de carga/errores, paginación.
* **Validación de Datos:** Validación en frontend y backend (Zod/Yup), transformación de datos.
* **Manejo de Errores:** Captura centralizada (Error Boundaries), mensajes claros, logging, reintentos y circuit breakers.
* **Esencial:** React Query/SWR, Zod/Yup, Error boundaries, Loading states, Retry mechanisms, Type-safe API contracts.

### 04 - Pruebas

* **Estrategias:** Pruebas unitarias, de integración, End-to-End (E2E).
* **Cobertura:** Establecer objetivos de cobertura (ej. 80% para lógica de negocio).
* **Herramientas:** Vitest, React Testing Library, Playwright.
* **Mocks:** Uso de mocks para dependencias externas (MSW para APIs).
* **Imprescindible:** Vitest + Testing Library setup, Unit tests, Integration tests, E2E tests with Playwright, Mock strategies, Test-driven development guidelines.

### 05 - Documentación

* **Uso del Memory Bank:** Leer y actualizar `memory-bank/` (`projectbrief.md`, `productContext.md`, `activeContext.md`, `systemPatterns.md`, `techContext.md`, `progress.md`) al inicio de cada tarea.
* **Documentación en Código:** JSDoc/TSDoc para funciones/clases/interfaces/tipos.
* **READMEs:** En cada directorio principal o módulo significativo.
* **Documentación de Stack Tecnológico:** Registro detallado de versiones exactas.

### 06 - Rendimiento y Optimización

* **Componentes React:** Memoización (`React.memo`, `useCallback`, `useMemo`), virtualización de listas.
* **Carga de Recursos:** Lazy Loading (`React.lazy`, `Suspense`), Code Splitting, optimización de imágenes.
* **Consultas a DB:** Índices, consultas eficientes, caching.
* **Monitoreo:** Herramientas de desarrollo, APM en producción.
* **Crítico:** Code splitting, lazy loading, Memoization, Bundle analysis, Image optimization, Web Vitals monitoring, Caching strategies.

### 07 - Seguridad

* **Manejo de Credenciales:** Variables de entorno, no hardcodear, gestores de secretos.
* **Validación de Entradas:** Sanitización y validación (frontend y backend).
* **Autenticación/Autorización:** Librerías seguras, JWT, RBAC.
* **Dependencias:** Auditoría y actualización regular.
* **No negociable:** Input validation, HTTPS, CSP, Authentication/Authorization patterns, Secrets management, OWASP Top 10 compliance.

### 08 - Diseño y Experiencia de Usuario (UI/UX)

* **Principios de Diseño:** Coherencia visual, jerarquía, feedback al usuario, minimización de carga cognitiva.
* **Componentes UI:** Uso consistente, reutilización, documentación.
* **Diseño Responsivo:** Mobile-First, breakpoints, Flexbox/Grid.
* **Flujos de Usuario:** Claridad en navegación, minimización de pasos, manejo de estados.
* **Accesibilidad (A11y):** Semántica HTML, contraste, navegación por teclado, atributos ARIA.
* **Rendimiento Perceptual:** Esqueletos de carga, animaciones suaves, optimización de activos.

### 09 - Despliegue y DevOps

* **CI/CD Pipelines:** Automatización de construcción, prueba y despliegue.
* **Gestión de Entornos:** Entornos separados, configuración por entorno.
* **Monitoreo y Logging:** Observabilidad, logs centralizados, alertas.
* **Estrategias de Rollback:** Despliegues atómicos, versionado.

### 10 - Git y Control de Versiones

* **Convenciones de Commits:** Conventional Commits, commits atómicos.
* **Nomenclatura de Ramas:** `feature/`, `fix/`, `release/`, `hotfix/`.
* **Plantillas de Pull Request (PR):** Uso de plantillas, revisión de código.
* **Git Hooks:** Husky para linting, formateo, pruebas unitarias (pre-commit, pre-push).

## Instrucciones para el Agente de IA

1. **Compreensão del Contexto:** Antes de iniciar cualquier tarea, **DEBE** leer y comprender a fondo todos los archivos en el directorio `memory-bank/` y `docs/`. Esto es crucial para mantener la continuidad y el contexto del proyecto.
2. **Planificación:** Siempre elabore un plan claro y conciso antes de la implementación. Si el plan es complejo, compártalo para aprobación.
3. **Implementación:** Adhiera estrictamente a las "Reglas de Ingeniería de Software" (`.clinerules/`) durante toda la implementación.
4. **Verificación:** Después de cada cambio significativo, ejecute las pruebas relevantes (`pnpm run test`), el linter (`pnpm run lint`), y el type-checker (`pnpm run type-check`) para asegurar la calidad y estabilidad del código.
5. **Documentación:** Mantenga la documentación actualizada, especialmente los archivos en `memory-bank/` y `docs/`, reflejando los cambios y el progreso.
6. **Comunicación:** Si hay ambigüedad o se requiere una decisión que va más allá del alcance definido, solicite aclaración.
7. **Autonomía:** Proceda con la implementación de manera autónoma una vez que el plan sea aprobado o la tarea sea clara.
