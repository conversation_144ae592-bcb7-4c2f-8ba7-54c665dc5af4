---
type: "agent_requested"
description: "Example description"
---
# 06 - Rendimiento y Optimización

## Optimización de Componentes React

*   **Memoización Específica para Búsqueda de Laptops**:
    ```typescript
    // Memoización para filtros complejos de laptops
    const filteredLaptops = useMemo(() =>
      laptops.filter(laptop => matchesFilters(laptop, filters)),
      [laptops, filters]
    );

    // Memoización para cálculos de compatibilidad LLM
    const compatibilityScores = useMemo(() =>
      laptops.map(laptop => calculateLLMCompatibility(laptop, llmRequirements)),
      [laptops, llmRequirements]
    );

    // Debounce para búsqueda en tiempo real
    const debouncedSearchTerm = useDebounce(searchTerm, 300);

    // Callback memoizado para filtros
    const handleFilterChange = useCallback((newFilters: FilterOptions) => {
      setFilters(prev => ({ ...prev, ...newFilters }));
    }, []);
    ```

*   **Virtualización para Listas de Laptops**: Implementar virtualización para grandes conjuntos de resultados:
    ```typescript
    import { FixedSizeList as List } from 'react-window';

    const LaptopList = ({ laptops }: { laptops: Laptop[] }) => (
      <List
        height={600}
        itemCount={laptops.length}
        itemSize={120}
        itemData={laptops}
      >
        {({ index, style, data }) => (
          <div style={style}>
            <LaptopCard laptop={data[index]} />
          </div>
        )}
      </List>
    );
    ```

## Carga de Recursos

*   **Lazy Loading (Carga Perezosa)**: Utilizar `React.lazy` y `Suspense` para cargar componentes de forma asíncrona.
*   **Code Splitting (División de Código)**: Dividir el bundle de JavaScript en chunks más pequeños para reducir el tiempo de carga inicial.
*   **Optimización de Imágenes**: Comprimir y optimizar imágenes, usar formatos modernos (WebP), y considerar CDNs.

## Optimización de Consultas a Base de Datos

*   **Índices Específicos para Búsqueda de Laptops**:
    ```sql
    -- Índices para búsqueda y filtrado eficiente
    CREATE INDEX idx_laptops_price ON laptops(price);
    CREATE INDEX idx_laptops_brand_model ON laptops(brand, model);
    CREATE INDEX idx_laptops_specs_gin ON laptops USING gin(specifications);
    CREATE INDEX idx_laptops_created_at ON laptops(created_at DESC);

    -- Índices para compatibilidad LLM
    CREATE INDEX idx_llm_compatibility_score ON laptop_llm_compatibility(score DESC);
    CREATE INDEX idx_llm_compatibility_laptop ON laptop_llm_compatibility(laptop_id);

    -- Índices para scraping
    CREATE INDEX idx_scraping_logs_timestamp ON scraping_logs(timestamp DESC);
    CREATE INDEX idx_scraping_logs_source ON scraping_logs(source_url);
    ```

*   **Consultas Eficientes con Prisma**:
    ```typescript
    // Búsqueda optimizada con filtros
    const laptops = await prisma.laptop.findMany({
      where: {
        price: { gte: minPrice, lte: maxPrice },
        brand: { in: selectedBrands },
        isActive: true
      },
      include: {
        compatibilities: {
          where: { score: { gte: 70 } },
          include: { llmModel: true }
        }
      },
      orderBy: { price: 'asc' },
      take: 50,
      skip: page * 50
    });

    // Agregaciones para dashboard
    const stats = await prisma.laptop.aggregate({
      _count: { id: true },
      _avg: { price: true },
      _min: { price: true },
      _max: { price: true },
      where: { isActive: true }
    });
    ```

*   **Caching Estratégico**:
    ```typescript
    // Cache para datos de compatibilidad LLM (cambian poco)
    const llmModels = await redis.get('llm-models') ||
      await prisma.llmModel.findMany();

    // Cache para resultados de búsqueda frecuentes
    const cacheKey = `search:${JSON.stringify(filters)}`;
    const cachedResults = await redis.get(cacheKey);
    ```

## Monitoreo del Rendimiento

*   **Herramientas de Desarrollo**: Usar las herramientas de rendimiento del navegador (ej. Lighthouse, React DevTools Profiler) para identificar cuellos de botella.
*   **Monitoreo en Producción**: Implementar herramientas de monitoreo de rendimiento de aplicaciones (APM) como Sentry, Datadog, o New Relic.

## Crítico:

*   **Code splitting y lazy loading**
*   **Memoization strategies (React.memo, useMemo)**
*   **Bundle analysis y tree shaking**
*   **Image optimization**
*   **Web Vitals monitoring**
*   **Caching strategies**
