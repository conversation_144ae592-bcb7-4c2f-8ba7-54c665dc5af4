---
type: "agent_requested"
description: "Example description"
---
# Setup Nueva Feature

Pasos para crear una nueva feature:

1. **Crear branch desde main:**
   ```bash
   git checkout main
   git pull origin main
   git checkout -b feature/[FEATURE-NAME]
   ```

2. **Crear estructura de archivos:**
   ```bash
   mkdir -p src/components/[FEATURE-NAME]
   mkdir -p src/hooks/[FEATURE-NAME]
   mkdir -p src/services/[FEATURE-NAME]
   ```

3. **Crear archivos base:**
   - `src/components/[FEATURE-NAME]/index.tsx`
   - `src/components/[FEATURE-NAME]/[FEATURE-NAME].test.tsx`
   - `src/hooks/[FEATURE-NAME]/use[FEATURE-NAME].ts`
   - `src/services/[FEATURE-NAME]/[FEATURE-NAME].service.ts`

4. **Actualizar barrel exports:**
   - Agregar exports en `src/components/index.ts`
   - Agregar exports en `src/shared/hooks/index.ts`
   - Agregar exports en `src/shared/services/index.ts`

5. **Crear test básico:**
   ```typescript
   import { render, screen } from '@testing-library/react'
   import { [FEATURE-NAME] } from './[FEATURE-NAME]'

   describe('[FEATURE-NAME]', () => {
     it('renders correctly', () => {
       render(<[FEATURE-NAME] />)
       expect(screen.getByText('[FEATURE-NAME]')).toBeInTheDocument()
     })
   })
   ```
