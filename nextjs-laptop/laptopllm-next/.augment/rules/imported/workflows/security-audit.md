---
type: "agent_requested"
description: "Example description"
---
# Auditoría de Seguridad

1. **Scan de dependencias:**
   ```bash
   pnpm audit --audit-level=high
   npx better-npm-audit audit
   ```

2. **Buscar credenciales expuestas:**
   ```bash
   git log --all --grep='password\|secret\|key\|token' -i
   grep -r "password\|secret\|key\|token" src/ --exclude-dir=node_modules
   ```

3. **Verificar configuraciones de seguridad:**
   - Revisar headers de seguridad
   - Verificar configuración CORS
   - Confirmar HTTPS en producción

4. **<PERSON><PERSON><PERSON> código vulnerable:**
   ```bash
   npx semgrep --config=auto src/
   ```

5. **Generar reporte de seguridad:**
   - Vulnerabilidades encontradas
   - Recomendaciones de fixes
   - Prioridad de cada issue
