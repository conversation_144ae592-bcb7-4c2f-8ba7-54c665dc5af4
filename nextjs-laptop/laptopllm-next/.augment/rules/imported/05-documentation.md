---
type: "agent_requested"
description: "Example description"
---
# 05 - Documentación

## Directrices de Documentación

*   **Uso del Memory Bank Específico para LaptopLLM**: Mantener y actualizar regularmente los archivos del `memory-bank/`:
    ```
    memory-bank/
    ├── projectbrief.md          # Objetivo: LaptopLLM Finder
    ├── productContext.md        # Contexto: búsqueda de laptops para LLMs
    ├── activeContext.md         # Estado actual del desarrollo
    ├── systemPatterns.md        # Patrones de scraping y búsqueda
    ├── techContext.md          # Stack: Next.js, Firecrawl, Prisma
    ├── progress.md             # Progreso de features
    └── scraping/               # Documentación específica de scraping
        ├── firecrawl-integration.md
        ├── data-sources.md
        ├── scraping-patterns.md
        └── troubleshooting.md
    ```

*   **Documentación en Código Específica del Dominio**:
    ```typescript
    /**
     * Extrae especificaciones de laptop desde una URL usando Firecrawl
     * @param url - URL del producto a extraer
     * @param config - Configuración de scraping específica del sitio
     * @returns Promise<ScrapedLaptop> - Datos extraídos y normalizados
     * @throws {ScrapingError} - Error específico de scraping con detalles de rate limiting
     * @example
     * ```typescript
     * const laptop = await scrapeLaptopSpecs(
     *   'https://revolico.com/laptop/123',
     *   { requiresFullBrowserAutomation: true }
     * );
     * ```
     */
    async function scrapeLaptopSpecs(url: string, config: ScrapingConfig): Promise<ScrapedLaptop>

    /**
     * Calcula la compatibilidad de un laptop con modelos LLM específicos
     * @param specs - Especificaciones técnicas del laptop
     * @param llmRequirements - Requisitos del modelo LLM
     * @returns CompatibilityScore - Puntuación de 0-100 con detalles
     */
    function calculateLLMCompatibility(specs: LaptopSpecifications, llmRequirements: LLMRequirements): CompatibilityScore
    ```

*   **READMEs Específicos por Feature**: Cada feature debe contener documentación específica:
    ```
    src/features/scraping/README.md        # Configuración de scrapers, troubleshooting
    src/features/laptop-search/README.md   # Filtros disponibles, algoritmos de búsqueda
    src/features/llm-compatibility/README.md # Cálculos de compatibilidad, modelos soportados
    src/features/dashboard/README.md       # Métricas disponibles, configuración de alertas
    ```

*   **Documentación de APIs de Scraping**: Documentar integraciones con APIs externas:
    - Firecrawl API: endpoints, rate limits, configuración
    - Puppeteer: configuración de browser, selectores por sitio
    - Sitios objetivo: revolico.com, laptop-ventas.ola.click, smart-things.ola.click

*   **Documentación de Stack Tecnológico LaptopLLM**: Mantener registro de versiones específicas:
    - Next.js 15.4.4, React 19.1.0, TypeScript ^5
    - Firecrawl MCP, Puppeteer ^24.15.0
    - Prisma ^6.12.0, PostgreSQL
    - Vitest ^3.2.4, Playwright ^1.54.1

## Herramientas de Documentación

*   **Generadores de Documentación**: Considerar el uso de herramientas como TypeDoc para generar documentación automáticamente a partir de los comentarios en el código.
*   **Herramientas de Diagramación**: Utilizar herramientas que permitan la creación de diagramas como código (ej. Mermaid, PlantUML) para facilitar su mantenimiento y versionado.

## Debe cubrir:

*   **JSDoc para funciones públicas**
*   **README.md structure**
*   **Storybook para componentes**
*   **API documentation (OpenAPI/Swagger)**
*   **Architecture Decision Records (ADRs)**
*   **Onboarding documentation**
