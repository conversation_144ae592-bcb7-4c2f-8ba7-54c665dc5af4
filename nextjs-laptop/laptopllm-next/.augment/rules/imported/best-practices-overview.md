---
type: "manual"
---

## Resumen de los cambios realizados:

### Archivos Modificados:

1. **01-code-quality-and-style.md** - Añadidas convenciones específicas del dominio LaptopLLM
2. **02-architecture-and-component-structure.md** - Estructura específica para features de scraping, búsqueda y compatibilidad
3. **03-data-and-api-handling.md** - Estrategias de fetching y validación específicas para scraping
4. **04-testing.md** - Estrategias de testing para servicios de scraping y componentes de búsqueda
5. **05-documentation.md** - Documentación específica para Memory Bank y APIs de scraping
6. **06-performance-and-optimization.md** - Optimizaciones para búsqueda de laptops y consultas de BD
7. **07-security.md** - Seguridad específica para scraping y validación de datos
8. **08-ui-ux-design.md** - Principios de diseño para componentes de laptops y compatibilidad LLM
9. **10-git-and-version-control.md** - Convenciones de commits y ramas específicas del dominio

### Archivos Nuevos Creados:

10. **11-web-scraping-standards.md** - Estándares completos para web scraping con Firecrawl y Puppeteer
11. **12-laptop-domain-patterns.md** - Patrones específicos del dominio de laptops y compatibilidad LLM
12. **13-data-quality-assurance.md** - Aseguramiento de calidad de datos para información de laptops

## Características principales de las adaptaciones:

### Dominio Específico:
- **Convenciones de nomenclatura** para entidades de laptops, scraping y LLM
- **Estructura de features** organizada por dominio (laptop-search, llm-compatibility, scraping, dashboard)
- **Esquemas Zod** específicos para validación de datos de laptops
- **Algoritmos de compatibilidad LLM** con puntuación 0-100

### Tecnologías Específicas:
- **Firecrawl MCP** como motor principal de scraping
- **Puppeteer** como fallback para scraping
- **Prisma** con esquemas optimizados para laptops
- **shadcn/ui** con componentes específicos del dominio
- **React Query** con configuraciones para scraping

### Calidad y Seguridad:
- **Rate limiting** estricto para scraping responsable
- **Validación robusta** de datos extraídos
- **Detección de duplicados** automática
- **Monitoreo de calidad** continuo
- **Seguridad específica** para operaciones de scraping

### Testing y Documentación:
- **Cobertura específica** por tipo de servicio (90%+ para scraping)
- **Testing de integración** para APIs de scraping
- **Documentación Memory Bank** adaptada al proyecto
- **Storybook** para componentes de laptops

Todos los archivos mantienen la estructura y formato originales pero con contenido completamente adaptado al contexto específico del proyecto LaptopLLM Finder. Las reglas ahora proporcionan guías concretas y ejemplos de código específicos para el desarrollo de esta aplicación de búsqueda de laptops compatibles con LLMs.
