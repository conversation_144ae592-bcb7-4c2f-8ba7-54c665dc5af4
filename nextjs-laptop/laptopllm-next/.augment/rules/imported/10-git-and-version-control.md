---
type: "agent_requested"
description: "Example description"
---
# 10 - Git y Control de Versiones

## Convenciones de Commits

*   **Conventional Commits para LaptopLLM**: Seguir especificación con prefijos específicos del dominio:
    ```
    feat(scraping): add Firecrawl integration for laptop data extraction
    feat(search): implement LLM compatibility filtering
    feat(ui): add laptop comparison component
    feat(compatibility): add LLM model scoring algorithm

    fix(scraping): handle rate limiting errors in Firecrawl service
    fix(search): correct price range filtering logic
    fix(compatibility): fix LLM score calculation for GPU requirements
    fix(ui): resolve laptop card layout issues on mobile

    perf(search): optimize laptop filtering with database indexes
    perf(scraping): implement concurrent scraping with rate limiting
    perf(db): add indexes for laptop specifications queries

    docs(scraping): add troubleshooting guide for common scraping issues
    docs(api): document laptop search API endpoints
    docs(setup): add Firecrawl configuration instructions

    test(scraping): add unit tests for data transformation
    test(compatibility): add integration tests for LLM scoring
    test(search): add e2e tests for laptop filtering

    refactor(services): extract common scraping utilities
    refactor(components): reorganize laptop display components
    refactor(types): consolidate laptop specification types

    chore(deps): update Firecrawl SDK to latest version
    chore(config): update scraping rate limits configuration
    chore(db): run database migrations for laptop schema
    ```

*   **Atomic Commits por Feature**: Realizar commits que representen unidades lógicas del dominio:
    - Un commit por servicio de scraping implementado
    - Un commit por componente de UI de laptop
    - Un commit por algoritmo de compatibilidad LLM
    - Un commit por configuración de fuente de datos

## Convenciones de Nomenclatura de Ramas

*   **Ramas de Características**: Usar `feature/dominio-funcionalidad` específicas para LaptopLLM:
    ```
    feature/scraping-firecrawl-integration
    feature/search-llm-compatibility-filter
    feature/ui-laptop-comparison-component
    feature/dashboard-scraping-metrics
    feature/compatibility-scoring-algorithm
    feature/search-price-range-filter
    feature/scraping-revolico-source
    feature/ui-mobile-responsive-layout
    ```

*   **Ramas de Corrección de Errores**: Usar `fix/dominio-descripcion-del-bug`:
    ```
    fix/scraping-rate-limit-handling
    fix/search-price-filter-validation
    fix/compatibility-gpu-score-calculation
    fix/ui-laptop-card-mobile-layout
    fix/db-laptop-specs-indexing
    fix/scraping-timeout-error-handling
    ```

*   **Ramas de Lanzamiento**: Usar `release/version` con versionado semántico:
    ```
    release/1.0.0    # Primera versión con scraping básico
    release/1.1.0    # Añadir compatibilidad LLM
    release/1.2.0    # Mejorar UI y filtros
    release/2.0.0    # Refactoring mayor de arquitectura
    ```

*   **Ramas de Hotfix**: Usar `hotfix/dominio-descripcion-critica`:
    ```
    hotfix/scraping-firecrawl-api-down
    hotfix/db-connection-timeout
    hotfix/security-xss-vulnerability
    ```

## Plantillas de Pull Request (PR)

*   **Uso de Plantillas**: Implementar plantillas de PR para asegurar que todas las solicitudes de extracción contengan la información necesaria (ej. descripción, cambios, pruebas, capturas de pantalla).
*   **Revisión de Código**: Fomentar la revisión de código por pares y establecer criterios claros para la aprobación de PRs.

## Git Hooks

*   **Husky**: Utilizar Husky para automatizar tareas antes de los commits o pushes (ej. linting, formateo, pruebas unitarias).
*   **Pre-commit hooks**: Ejecutar linters y formatters para asegurar la calidad del código antes de cada commit.
*   **Pre-push hooks**: Ejecutar pruebas unitarias o de integración antes de cada push para evitar subir código roto.
