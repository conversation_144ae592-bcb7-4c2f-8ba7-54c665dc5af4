---
type: "agent_requested"
description: "Example description"
---
# 07 - Seguridad

## Manejo de Credenciales

*   **Variables de Entorno Específicas para Scraping**: Almacenar credenciales de APIs de scraping de forma segura:
    ```env
    # Firecrawl API
    FIRECRAWL_API_KEY=fc-xxx
    FIRECRAWL_BASE_URL=https://api.firecrawl.dev

    # Base de datos
    DATABASE_URL=postgresql://user:pass@localhost:5432/laptopllm

    # APIs de sitios objetivo (si requieren autenticación)
    REVOLICO_API_KEY=xxx
    LAPTOP_VENTAS_API_KEY=xxx

    # Configuración de seguridad
    NEXTAUTH_SECRET=xxx
    NEXTAUTH_URL=http://localhost:3000
    ```

*   **No Hardcodear URLs ni Selectores Sensibles**: Mantener configuraciones de scraping en variables de entorno:
    ```typescript
    const scrapingConfig = {
      allowedDomains: process.env.ALLOWED_SCRAPING_DOMAINS?.split(',') || [],
      maxConcurrentRequests: parseInt(process.env.MAX_CONCURRENT_SCRAPING || '3'),
      defaultTimeout: parseInt(process.env.SCRAPING_TIMEOUT || '30000')
    };
    ```

*   **Gestores de Secretos**: Para producción, usar gestores seguros para claves de APIs de scraping.

## Validación de Entradas

*   **Sanitización y Validación Específica para Scraping**: Validar y sanitizar datos de scraping para prevenir ataques:
    ```typescript
    import DOMPurify from 'dompurify';
    import { z } from 'zod';

    // Validación de URLs de scraping
    const SafeUrlSchema = z.string().url().refine(url => {
      const allowedDomains = ['revolico.com', 'laptop-ventas.ola.click', 'smart-things.ola.click'];
      const urlObj = new URL(url);
      return allowedDomains.includes(urlObj.hostname);
    }, 'Dominio no permitido para scraping');

    // Sanitización de datos extraídos
    function sanitizeScrapedContent(content: string): string {
      return DOMPurify.sanitize(content, {
        ALLOWED_TAGS: [],
        ALLOWED_ATTR: []
      });
    }

    // Validación de filtros de búsqueda
    const SearchFiltersSchema = z.object({
      minPrice: z.number().min(0).max(10000),
      maxPrice: z.number().min(0).max(10000),
      brands: z.array(z.enum(['Dell', 'HP', 'Lenovo', 'Apple', 'Asus', 'Acer'])),
      minRam: z.number().min(4).max(128)
    });
    ```

*   **Validación en Frontend y Backend**: Realizar validación tanto en el lado del cliente (para UX) como en el lado del servidor (para seguridad).

*   **Rate Limiting para Scraping**: Implementar límites de velocidad para prevenir abuso:
    ```typescript
    const rateLimiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutos
      max: 100, // máximo 100 requests por ventana
      message: 'Demasiadas solicitudes de scraping'
    });
    ```

## Autenticación y Autorización

*   **Uso de Librerías Seguras**: Implementar autenticación y autorización utilizando librerías y frameworks probados y seguros (ej. Passport.js, NextAuth.js).
*   **Tokens JWT**: Si se usan tokens JWT, asegurar que se validen correctamente (firma, expiración) y se almacenen de forma segura (ej. HttpOnly cookies).
*   **Control de Acceso Basado en Roles (RBAC)**: Implementar RBAC para asegurar que los usuarios solo puedan acceder a los recursos y funcionalidades para los que tienen permiso.

## Dependencias

*   **Auditoría de Dependencias**: Auditar regularmente las dependencias del proyecto en busca de vulnerabilidades conocidas (ej. `npm audit`, Snyk, Dependabot).
*   **Actualización de Dependencias**: Mantener las dependencias actualizadas para beneficiarse de los parches de seguridad.

## Logging y Monitoreo de Seguridad

*   **Registro de Eventos de Seguridad**: Registrar eventos relevantes de seguridad (ej. intentos de inicio de sesión fallidos, cambios de permisos).
*   **Monitoreo de Anomalías**: Implementar monitoreo para detectar actividades sospechosas o patrones de ataque.

## No negociable:

*   **Input validation y sanitization**
*   **HTTPS enforcement**
*   **CSP (Content Security Policy)**
*   **Authentication/Authorization patterns**
*   **Secrets management**
*   **OWASP Top 10 compliance**
