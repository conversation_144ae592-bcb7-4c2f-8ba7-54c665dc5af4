---
type: "agent_requested"
description: "Example description"
---
# 02 - Arquitectura y Estructura de Componentes

## Organización de Directorios

*   **Estructura Basada en Características (Feature-based)**: Organizar el código por características del dominio LaptopLLM en lugar de por tipo de archivo.

### Estructura Específica para LaptopLLM Finder

```
src/features/
├── laptop-search/              # Búsqueda y filtrado de laptops
│   ├── components/            # LaptopCard, SearchFilters, SpecsDisplay
│   ├── hooks/                # useLaptopSearch, useLaptopFilters
│   ├── services/             # laptopSearchService.ts
│   └── types.ts              # LaptopSearchParams, FilterOptions
├── llm-compatibility/          # Compatibilidad con LLMs
│   ├── components/           # CompatibilityScore, LLMRequirements
│   ├── hooks/               # useLLMCompatibility, useCompatibilityScore
│   ├── services/            # llmCompatibilityService.ts
│   └── types.ts             # CompatibilityData, LLMRequirements
├── scraping/                  # Web scraping y fuentes de datos
│   ├── components/          # ScrapingStatus, SourceConfig, ScrapingLogs
│   ├── services/            # firecrawlService.ts, puppeteerService.ts
│   ├── utils/               # dataTransformer.ts, scraperOrchestrator.ts
│   └── types.ts             # ScrapingConfig, ScrapedData, ScrapingResult
└── dashboard/                 # Dashboard y visualizaciones
    ├── components/           # MetricsCard, TrendChart, AlertsPanel
    ├── hooks/               # useDashboardData, useScrapingMetrics
    └── services/            # dashboardService.ts
```

*   **Componentes**:
    *   `src/components/ui/`: Componentes shadcn/ui y genéricos reutilizables.
    *   `src/components/atoms/`: `PriceTag`, `SpecBadge`, `LoadingSpinner`, `StatusIndicator`.
    *   `src/components/molecules/`: `LaptopCard`, `FilterGroup`, `ScrapingStatusCard`, `CompatibilityBadge`.
    *   `src/components/organisms/`: `LaptopGrid`, `SearchInterface`, `DashboardLayout`, `ScrapingDashboard`.
    *   `src/components/shared/`: Componentes reutilizables no específicos de una característica.
*   **Hooks**: `src/hooks/` para hooks personalizados compartidos entre features.
*   **Servicios**: `src/services/` organizados por dominio (scraping, laptop, llm).
*   **Repositorios**: `src/repositories/` para abstracción de acceso a datos de Prisma.
*   **Contextos/Proveedores**: `src/contexts/` para estado global (ScrapingContext, SearchContext).
*   **Tipos**: `src/types/` para definiciones de tipos globales compartidos.
*   **Monorepo**: Estructura con `packages/` para módulos separados como `scraper-service`.

## Patrones de Componentes

*   **Componentes Puros/Funcionales**: Preferir componentes funcionales con hooks.
*   **Separación de Preocupaciones**: Separar la lógica de presentación de la lógica de negocio. Los componentes deben ser lo más "tontos" posible, recibiendo datos y callbacks a través de props.
*   **Reutilización**: Diseñar componentes pensando en la reutilización. Evitar la duplicación de código.
*   **Composición sobre Herencia**: Favorecer la composición de componentes para construir interfaces complejas.

## Manejo de Estado

*   **Estado Local de Componentes**: Usar `useState` y `useReducer` para el estado interno de los componentes.
*   **Estado Global**:
    *   Para estado global simple o temas (ej. autenticación, configuración de usuario), usar React Context API (`src/contexts/` o `src/providers/`).
    *   Para estado global complejo o que requiere manejo de efectos secundarios y optimizaciones, considerar librerías como Redux, Zustand, Recoil, o Jotai.
*   **Estado de Servidor (Server State)**: Usar librerías como React Query o SWR para manejar el fetching, caching, sincronización y actualización de datos del servidor.

## Modularidad de Hooks

*   **Evitar Hooks Monolíticos**: Refactorizar hooks grandes que manejan múltiples funcionalidades distintas en hooks más pequeños y enfocados. Cada hook debe abordar una única preocupación (ej. gestión de estado para una característica específica, obtención de datos para un recurso particular).
*   **Promover la Reutilización**: Los hooks más pequeños y bien definidos son más fáciles de reutilizar en diferentes componentes y partes de la aplicación.
*   **Mejorar la Testabilidad**: Desglosar la lógica compleja en hooks más pequeños hace que las pruebas unitarias sean más manejables y efectivas.
*   **Mejorar la Legibilidad y Mantenibilidad**: Las unidades de código más pequeñas son más fáciles de entender, depurar y modificar.

## Debe incluir:

*   **Arquitectura por capas**: (presentation, business, data)
*   **Atomic Design para componentes UI**
*   **Custom hooks para lógica reutilizable**
*   **Barrel exports**: (index.ts files)
*   **Dependency injection patterns**
*   **Single Responsibility Principle**
