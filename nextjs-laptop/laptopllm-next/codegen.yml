# GraphQL Code Generator Configuration
# Generates TypeScript types from GraphQL schema

overwrite: true
schema: "src/lib/graphql/schema.ts"
documents: "src/lib/graphql/queries.ts"
generates:
  src/lib/graphql/generated.ts:
    plugins:
      - "typescript"
      - "typescript-operations"
      - "typescript-react-apollo"
    config:
      withHooks: true
      withComponent: false
      withHOC: false
      scalars:
        DateTime: string
        JSON: any
      namingConvention:
        typeNames: pascal-case#
        enumValues: upper-case#
      avoidOptionals:
        field: true
        inputValue: false
        object: false
      maybeValue: T | null | undefined
      inputMaybeValue: T | null | undefined
      strictScalars: true
      skipTypename: false
      exportFragmentSpreadSubTypes: true
      dedupeFragments: true
      inlineFragmentTypes: combine
      preResolveTypes: true
