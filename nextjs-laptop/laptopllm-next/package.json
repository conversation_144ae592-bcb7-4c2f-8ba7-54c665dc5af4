{"name": "laptopllm-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "workers": "tsx scripts/start-workers.ts", "workers:dev": "tsx watch scripts/start-workers.ts", "db:optimize": "tsx scripts/apply-database-optimizations.ts", "db:stats": "tsx -e \"import { databaseOptimizationService } from './src/lib/database/optimization.service'; databaseOptimizationService.getOptimizationStats().then(stats => { console.log(JSON.stringify(stats, null, 2)); process.exit(0); })\"", "test:run": "vitest run", "test:type-safety": "vitest run tests/unit/type-safety", "test:type-safety:watch": "vitest tests/unit/type-safety", "audit:type-safety": "tsx scripts/type-safety-audit.ts", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "graphql:codegen": "graphql-codegen --config codegen.yml", "graphql:watch": "graphql-codegen --config codegen.yml --watch", "postinstall": "prisma generate"}, "dependencies": {"@apollo/client": "^3.13.9", "@apollo/server": "^5.0.0", "@as-integrations/next": "^4.0.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.2.1", "@mendable/firecrawl-js": "^1.29.2", "@prisma/client": "^6.13.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.1", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@types/pg": "^8.15.5", "axios": "^1.11.0", "bullmq": "^5.36.0", "chalk": "^5.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "embla-carousel-react": "^8.6.0", "graphql": "^16.11.0", "graphql-tag": "^2.12.6", "input-otp": "^1.4.2", "ioredis": "^5.4.2", "lucide-react": "^0.533.0", "next": "15.4.4", "next-themes": "^0.4.6", "node-cron": "^3.0.3", "pg": "^8.16.3", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "prisma": "^6.13.0", "puppeteer": "^24.15.0", "react": "19.1.1", "react-day-picker": "^9.8.1", "react-dom": "19.1.1", "react-hook-form": "^7.61.1", "react-resizable-panels": "^3.0.3", "recharts": "2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "uuid": "^11.0.4", "vaul": "^1.1.2", "zod": "^4.0.13"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.32.0", "@playwright/test": "^1.54.1", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.1.0", "@types/node-cron": "^3.0.11", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react-swc": "^3.11.0", "eslint": "^9.32.0", "eslint-config-next": "15.4.4", "eslint-plugin-react": "^7.37.5", "globals": "^16.3.0", "jiti": "^2.5.1", "jsdom": "^26.1.0", "playwright": "^1.54.1", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.2", "tw-animate-css": "^1.3.6", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0", "vitest": "^3.2.4"}}