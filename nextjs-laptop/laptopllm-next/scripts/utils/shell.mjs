import { execSync } from 'child_process';
import logger from './logger.mjs';

/**
 * Executes a shell command.
 * @param {string} command The command to execute.
 * @param {import('child_process').ExecSyncOptions} [options] The options for execSync.
 * @returns {string} The stdout from the command.
 */
export function execute(command, options) {
  try {
    logger.info(`> ${command}`);
    const output = execSync(command, { stdio: 'pipe', ...options });
    return output.toString();
  } catch (error) {
    logger.error(`Command failed: ${command}`);
    logger.error(error.stderr.toString());
    throw error;
  }
}

/**
 * Executes a shell command with inherited stdio.
 * @param {string} command The command to execute.
 * @param {import('child_process').ExecSyncOptions} [options] The options for execSync.
 */
export function executeInherit(command, options) {
  try {
    logger.info(`> ${command}`);
    execSync(command, { stdio: 'inherit', ...options });
  } catch (error) {
    logger.error(`Command failed: ${command}`);
    throw error;
  }
}
