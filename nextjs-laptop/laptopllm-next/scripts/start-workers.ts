#!/usr/bin/env tsx

/**
 * Worker Startup Script
 * Start the enhanced scraping workers
 */

import { getEnhancedScrapingService, shutdownEnhancedScrapingService } from '../src/lib/scraping/init-enhanced-scraping'
import pino from 'pino'

const logger = pino({
  name: 'worker-startup',
  level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
  transport: process.env.NODE_ENV === 'development' ? {
    target: 'pino-pretty',
    options: { colorize: true }
  } : undefined
})

async function startWorkers() {
  try {
    logger.info('Starting Enhanced Scraping Workers...')
    
    // Initialize the enhanced scraping service
    const service = await getEnhancedScrapingService()
    
    logger.info('Enhanced Scraping Workers started successfully')
    logger.info('Workers are now processing jobs from the queue')
    
    // Keep the process alive
    process.on('SIGTERM', async () => {
      logger.info('Received SIGTERM, shutting down workers...')
      await shutdownEnhancedScrapingService()
      process.exit(0)
    })

    process.on('SIGINT', async () => {
      logger.info('Received SIGINT, shutting down workers...')
      await shutdownEnhancedScrapingService()
      process.exit(0)
    })

    // Log periodic status
    setInterval(async () => {
      try {
        const [queueStats, jobStats] = await Promise.all([
          service.getQueueStats(),
          service.getJobStats()
        ])
        
        const totalActive = queueStats.reduce((sum, queue) => sum + queue.active, 0)
        const totalWaiting = queueStats.reduce((sum, queue) => sum + queue.waiting, 0)
        
        logger.info(`Status: ${totalActive} active, ${totalWaiting} waiting jobs`)
      } catch (error) {
        logger.error('Failed to get status:', error)
      }
    }, 60000) // Every minute

  } catch (error) {
    logger.error('Failed to start workers:', error)
    process.exit(1)
  }
}

// Start the workers
startWorkers().catch((error) => {
  logger.error('Unhandled error:', error)
  process.exit(1)
})
