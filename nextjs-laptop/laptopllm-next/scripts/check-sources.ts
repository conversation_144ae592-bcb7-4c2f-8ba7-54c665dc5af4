#!/usr/bin/env tsx

/**
 * Script para verificar las fuentes existentes en la base de datos
 */

import { PrismaClient } from '../generated/prisma'

const prisma = new PrismaClient()

async function checkSources() {
  console.log('🔍 Checking existing sources...')

  try {
    await prisma.$connect()
    console.log('✅ Connected to database')

    // Check ScrapingSource table
    console.log('\n📋 ScrapingSource table:')
    const scrapingSources = await prisma.scrapingSource.findMany({
      select: { id: true, name: true, url: true, isActive: true },
    })
    
    console.log(`Found ${scrapingSources.length} scraping sources:`)
    scrapingSources.forEach(source => {
      console.log(`  • ${source.name} (ID: ${source.id}) - Active: ${source.isActive}`)
    })

    // Check sources table
    console.log('\n📋 sources table:')
    const sources = await prisma.sources.findMany({
      select: { id: true, name: true, url: true, is_active: true },
    })
    
    console.log(`Found ${sources.length} sources:`)
    sources.forEach(source => {
      console.log(`  • ${source.name} (ID: ${source.id}) - Active: ${source.is_active}`)
    })

    // Check scraping_history
    console.log('\n📋 scraping_history table:')
    const history = await prisma.scraping_history.findMany({
      include: {
        sources: {
          select: { id: true, name: true },
        },
      },
      orderBy: { start_time: 'desc' },
      take: 5,
    })
    
    console.log(`Found ${history.length} recent scraping history entries:`)
    history.forEach(entry => {
      console.log(`  • ${entry.sources.name} (${entry.status}) - ${entry.start_time}`)
    })

  } catch (error) {
    console.error('❌ Error checking sources:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Ejecutar el script
if (require.main === module) {
  checkSources()
    .then(() => {
      console.log('\n✨ Source check completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Source check failed:', error)
      process.exit(1)
    })
}

export { checkSources }
