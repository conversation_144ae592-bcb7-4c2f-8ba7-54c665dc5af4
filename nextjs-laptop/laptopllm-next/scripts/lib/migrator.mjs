import fs from 'fs/promises';
import path from 'path';
import { connect, disconnect } from './db-client.mjs';
import logger from '../utils/logger.mjs';
import { scriptsDir } from '../utils/paths.mjs';

const MIGRATIONS_DIR = path.join(scriptsDir, 'db', 'migrations');

async function getMigrationFiles() {
  const files = await fs.readdir(MIGRATIONS_DIR);
  return files.filter(file => file.endsWith('.sql')).sort();
}

export async function runMigrations(specificFiles = []) {
  let sql;
  try {
    sql = await connect();
    const allFiles = await getMigrationFiles();
    
    const filesToRun = specificFiles.length > 0
      ? allFiles.filter(file => specificFiles.some(spec => file.includes(spec)))
      : allFiles;

    if (filesToRun.length === 0) {
      logger.warn('No migration files found or specified.');
      return;
    }

    logger.info('Starting database migration...');
    
    for (const file of filesToRun) {
      const filePath = path.join(MIGRATIONS_DIR, file);
      logger.info(`Executing ${file}...`);
      const query = await fs.readFile(filePath, 'utf8');
      await sql.unsafe(query);
      logger.success(`Successfully executed ${file}`);
    }

    logger.success('Migration completed successfully!');
  } catch (error) {
    logger.error('Migration failed:');
    logger.error(error.message);
    throw error;
  } finally {
    if (sql) {
      await disconnect();
    }
  }
}
