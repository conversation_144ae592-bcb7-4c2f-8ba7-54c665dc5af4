import fs from 'fs';
import path from 'path';
import { rootDir } from '../utils/paths.mjs';
import { depCheckConfig } from '../config/dependency-check.config.mjs';

/**
 * Finds all unique package imports in a given directory.
 * @returns {{imports: Set<string>, files: string[]}}
 */
export function findImports() {
  const imports = new Set();
  const files = [];
  const scanPath = path.join(rootDir, depCheckConfig.scanDir);

  function scanDir(currentDir) {
    const entries = fs.readdirSync(currentDir, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(currentDir, entry.name);

      if (entry.name === 'node_modules' || entry.name === '.git') {
        continue;
      }

      if (entry.isDirectory()) {
        scanDir(fullPath);
      } else if (entry.isFile() && depCheckConfig.extensions.includes(path.extname(entry.name))) {
        files.push(fullPath);
        const content = fs.readFileSync(fullPath, 'utf8');
        
        // Regex to find imports/requires: from 'package', from "package", require('package'), require("package")
        const importRegex = /(?:from|require)\s*\(?\s*['"]([^./][^'"]*)['"]/g;
        let match;

        while ((match = importRegex.exec(content)) !== null) {
          const rawPackageName = match[1];
          // Handle scoped packages like @tanstack/react-query
          const packageName = rawPackageName.startsWith('@')
            ? rawPackageName.split('/').slice(0, 2).join('/')
            : rawPackageName.split('/')[0];
          
          imports.add(packageName);
        }
      }
    }
  }

  scanDir(scanPath);
  return { imports, files };
}
