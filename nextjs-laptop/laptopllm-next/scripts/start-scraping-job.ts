#!/usr/bin/env tsx

/**
 * Script para iniciar un job de scraping de prueba
 * Este script crea y ejecuta un job de scraping para una fuente específica
 */

import { PrismaClient } from '../generated/prisma'

const prisma = new PrismaClient()

async function startScrapingJob(sourceId?: string) {
  console.log('🚀 Starting scraping job...')

  try {
    // Conectar a la base de datos
    await prisma.$connect()
    console.log('✅ Connected to database')

    // Obtener fuentes activas
    const activeSources = await prisma.scrapingSource.findMany({
      where: { isActive: true },
      select: { id: true, name: true, url: true },
    })

    if (activeSources.length === 0) {
      console.log('❌ No active sources found')
      return
    }

    console.log(`📊 Found ${activeSources.length} active sources:`)
    activeSources.forEach((source, index) => {
      console.log(`  ${index + 1}. ${source.name} (ID: ${source.id})`)
    })

    // Seleccionar fuente
    let selectedSource
    if (sourceId) {
      selectedSource = activeSources.find(s => s.id === sourceId)
      if (!selectedSource) {
        console.log(`❌ Source with ID ${sourceId} not found or not active`)
        return
      }
    } else {
      // Usar la primera fuente activa
      selectedSource = activeSources[0]
    }

    console.log(`🎯 Selected source: ${selectedSource.name}`)

    // Crear job de scraping usando la API
    const response = await fetch('http://localhost:3000/api/scraping-jobs', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sourceId: selectedSource.id,
        priority: 'high',
      }),
    })

    if (!response.ok) {
      const errorData = await response.json()
      console.error('❌ Failed to create scraping job:', errorData)
      return
    }

    const jobData = await response.json()
    console.log('✅ Scraping job created successfully!')
    console.log(`📋 Job ID: ${jobData.data.id}`)
    console.log(`🔗 Source: ${selectedSource.name}`)
    console.log(`📊 Status: ${jobData.data.status}`)

    // Monitorear el progreso del job
    console.log('\n🔍 Monitoring job progress...')
    await monitorJob(jobData.data.id)

  } catch (error) {
    console.error('❌ Error starting scraping job:', error)
  } finally {
    await prisma.$disconnect()
  }
}

async function monitorJob(jobId: string) {
  const maxAttempts = 30 // 5 minutos máximo
  let attempts = 0

  while (attempts < maxAttempts) {
    try {
      const response = await fetch(`http://localhost:3000/api/scraping-jobs/${jobId}`)
      
      if (!response.ok) {
        console.error('❌ Failed to fetch job status')
        break
      }

      const jobData = await response.json()
      const job = jobData.data

      console.log(`📊 Status: ${job.status} | Progress: ${job.progress}% | Items: ${job.itemsProcessed}/${job.itemsTotal || '?'}`)

      if (job.status === 'completed') {
        console.log('🎉 Job completed successfully!')
        console.log(`📈 Final stats: ${job.itemsProcessed} items processed`)
        break
      } else if (job.status === 'failed') {
        console.log('❌ Job failed!')
        if (job.errorMessage) {
          console.log(`💥 Error: ${job.errorMessage}`)
        }
        break
      } else if (job.status === 'cancelled') {
        console.log('⏹️ Job was cancelled')
        break
      }

      // Esperar 10 segundos antes del siguiente check
      await new Promise(resolve => setTimeout(resolve, 10000))
      attempts++

    } catch (error) {
      console.error('❌ Error monitoring job:', error)
      break
    }
  }

  if (attempts >= maxAttempts) {
    console.log('⏰ Monitoring timeout reached')
  }
}

// Obtener argumentos de línea de comandos
const args = process.argv.slice(2)
const sourceId = args[0] // Opcional: ID de la fuente específica

// Ejecutar el script
if (require.main === module) {
  startScrapingJob(sourceId)
    .then(() => {
      console.log('\n✨ Scraping job process completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Scraping job failed:', error)
      process.exit(1)
    })
}

export { startScrapingJob }
