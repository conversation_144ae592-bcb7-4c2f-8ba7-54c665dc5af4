#!/usr/bin/env node

import { Command } from 'commander';
import { startSupabase } from './commands/db-start.mjs';
import { migrateDatabase } from './commands/db-migrate.mjs';
import { testApp } from './commands/test-app.mjs';
import { checkDependencies } from './commands/check-deps.mjs';
import logger from './utils/logger.mjs';

const program = new Command();

program
  .name('project-scripts')
  .description('A collection of utility scripts for the llm-laptop-lens project.')
  .version('1.0.0');

const db = program.command('db')
  .description('Commands for managing the database');

db.command('start')
  .description('Starts the local Supabase development environment via Docker.')
  .action(startSupabase);

db.command('migrate')
  .description('Runs database migrations from the scripts/db/migrations directory.')
  .option('-f, --file <filename>', 'Run a specific migration file.')
  .action(migrateDatabase);

const app = program.command('app')
    .description('Commands for managing the application');

app.command('test')
    .description('Runs a sanity check on the application, verifying DB connections and table structures.')
    .action(testApp);

const deps = program.command('deps')
    .description('Commands for managing project dependencies');

deps.command('check')
    .description('Checks for missing, unused, and vulnerable dependencies.')
    .action(checkDependencies);

program.parseAsync(process.argv).catch(err => {
    logger.error('An unexpected error occurred in the CLI.');
    logger.error(err);
    process.exit(1);
});
