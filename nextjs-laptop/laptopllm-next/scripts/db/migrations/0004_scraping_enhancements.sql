-- Fase 4: <PERSON><PERSON><PERSON> para el proceso de scraping con Firecrawl
-- <PERSON><PERSON> script introduce una función más robusta para procesar los datos JSON de Firecrawl

-- Paso 1: Crear una función para procesar los datos JSON de un listing específico
CREATE OR REPLACE FUNCTION process_firecrawl_data(p_listing_id INT)
RETURNS void AS $$
DECLARE
    raw_data JSONB;
    v_brand_id INT;
    v_laptop_id INT;
    v_cpu_id INT;
    v_gpu_id INT;
    v_ram_id INT;
    v_storage_id INT;
    v_display_id INT;
    v_manufacturer_id INT;
    v_memory_type_id INT;
    v_storage_interface_id INT;
    v_resolution_id INT;
    v_panel_type_id INT;
BEGIN
    -- Obtener los datos crudos del listing
    SELECT specs INTO raw_data FROM laptop_listings WHERE id = p_listing_id;

    -- Si no hay datos, no hacer nada
    IF raw_data IS NULL THEN
        RAISE NOTICE 'No raw data found for listing_id %', p_listing_id;
        RETURN;
    END IF;

    -- Extraer y normalizar el nombre del fabricante y la marca
    -- (Aquí se podría añadir lógica para limpiar los nombres)
    SELECT id INTO v_manufacturer_id FROM manufacturers WHERE name ILIKE (raw_data->>'manufacturer') LIMIT 1;
    IF v_manufacturer_id IS NULL THEN
        INSERT INTO manufacturers (name) VALUES (raw_data->>'manufacturer') RETURNING id INTO v_manufacturer_id;
    END IF;

    SELECT id INTO v_brand_id FROM brands WHERE name ILIKE (raw_data->>'brand') LIMIT 1;
    IF v_brand_id IS NULL THEN
        INSERT INTO brands (name, manufacturer_id) VALUES (raw_data->>'brand', v_manufacturer_id) RETURNING id INTO v_brand_id;
    END IF;

    -- Insertar o actualizar la laptop
    INSERT INTO laptops (model_name, brand_id, description, image_url, msrp)
    VALUES (
        raw_data->>'model_name',
        v_brand_id,
        raw_data->>'description',
        raw_data->>'image_url',
        (raw_data->>'price')::numeric
    )
    ON CONFLICT (brand_id, model_name) DO UPDATE SET
        description = EXCLUDED.description,
        image_url = EXCLUDED.image_url,
        msrp = EXCLUDED.msrp,
        updated_at = NOW()
    RETURNING id INTO v_laptop_id;

    -- Procesar y insertar CPU
    IF raw_data->'specs'->>'cpu' IS NOT NULL THEN
        -- (Lógica de normalización y búsqueda de CPU aquí)
        INSERT INTO cpus (manufacturer_id, model, cores, threads, base_clock_ghz)
        VALUES (
            v_manufacturer_id,
            raw_data->'specs'->>'cpu',
            (raw_data->'specs'->>'cpu_cores')::int,
            (raw_data->'specs'->>'cpu_threads')::int,
            (raw_data->'specs'->>'cpu_base_clock_ghz')::numeric
        )
        ON CONFLICT (manufacturer_id, model, generation) DO NOTHING;
        
        SELECT id INTO v_cpu_id FROM cpus WHERE model = raw_data->'specs'->>'cpu' LIMIT 1;

        INSERT INTO laptop_cpus (laptop_id, cpu_id) VALUES (v_laptop_id, v_cpu_id) ON CONFLICT DO NOTHING;
    END IF;

    -- Procesar y insertar GPU
    IF raw_data->'specs'->>'gpu' IS NOT NULL THEN
        -- (Lógica de normalización y búsqueda de GPU aquí)
        INSERT INTO gpus (manufacturer_id, model, vram_gb)
        VALUES (
            v_manufacturer_id,
            raw_data->'specs'->>'gpu',
            (raw_data->'specs'->>'gpu_vram_gb')::int
        )
        ON CONFLICT (manufacturer_id, model, vram_gb) DO NOTHING;

        SELECT id INTO v_gpu_id FROM gpus WHERE model = raw_data->'specs'->>'gpu' LIMIT 1;

        INSERT INTO laptop_gpus (laptop_id, gpu_id) VALUES (v_laptop_id, v_gpu_id) ON CONFLICT DO NOTHING;
    END IF;

    -- (Aquí iría la lógica para RAM, almacenamiento, pantalla, etc., siguiendo el mismo patrón)

    -- Marcar el listing como procesado (opcional)
    -- UPDATE laptop_listings SET processed = TRUE WHERE id = p_listing_id;

    RAISE NOTICE 'Successfully processed data for listing_id %', p_listing_id;

EXCEPTION
    WHEN others THEN
        RAISE WARNING 'Error processing listing_id %: %', p_listing_id, SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Paso 2: Añadir una columna 'processed' a laptop_listings para rastrear el estado
ALTER TABLE laptop_listings ADD COLUMN processed BOOLEAN DEFAULT FALSE;

-- Paso 3: Crear un trigger que ejecute la función después de insertar un nuevo listing
CREATE OR REPLACE FUNCTION trigger_process_firecrawl_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Llamar a la función principal en un proceso separado para no bloquear la inserción
    -- Esto requeriría una extensión como pg_background o un sistema de colas
    -- Por simplicidad, aquí lo llamamos directamente, pero en producción sería mejor asíncrono
    PERFORM process_firecrawl_data(NEW.id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER on_new_listing
AFTER INSERT ON laptop_listings
FOR EACH ROW
EXECUTE FUNCTION trigger_process_firecrawl_data();

