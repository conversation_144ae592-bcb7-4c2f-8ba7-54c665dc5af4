-- Database Optimization Migration
-- This migration adds indexes, constraints, and optimizations for better performance

-- ============================================================================
-- PERFORMANCE INDEXES
-- ============================================================================

-- Laptop Listings Optimization (Most Critical for Deals System)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptop_listings_price ON laptop_listings(price);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptop_listings_in_stock ON laptop_listings(in_stock);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptop_listings_date ON laptop_listings(listing_date);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptop_listings_processed ON laptop_listings(processed);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptop_listings_laptop_price ON laptop_listings(laptop_id, price);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptop_listings_laptop_stock_price ON laptop_listings(laptop_id, in_stock, price);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptop_listings_source_date ON laptop_listings(source_id, listing_date);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptop_listings_price_date ON laptop_listings(price, listing_date);

-- LLM Compatibility Optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_llm_compatibility_score ON laptop_llm_compatibility(score);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_llm_compatibility_laptop_score ON laptop_llm_compatibility(laptop_id, score);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_llm_compatibility_llm_score ON laptop_llm_compatibility(llm_id, score);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_llm_compatibility_offline ON laptop_llm_compatibility(can_run_offline);

-- Laptops Table Optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptops_available ON laptops(is_available);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptops_msrp ON laptops(msrp);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptops_release_date ON laptops(release_date);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptops_created_at ON laptops(created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptops_updated_at ON laptops(updated_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptops_brand_available ON laptops(brand_id, is_available);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptops_brand_price ON laptops(brand_id, msrp);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptops_available_price ON laptops(is_available, msrp);

-- Scraping Jobs Optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scraping_jobs_status_type ON scraping_jobs(status, type);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scraping_jobs_status_priority ON scraping_jobs(status, priority);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scraping_jobs_type_priority ON scraping_jobs(type, priority);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scraping_jobs_batch_status ON scraping_jobs(batch_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scraping_jobs_created_status ON scraping_jobs(created_at, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scraping_jobs_started_at ON scraping_jobs(started_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scraping_jobs_completed_at ON scraping_jobs(completed_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scraping_jobs_attempts ON scraping_jobs(attempts);

-- Price History Optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_price_history_recorded_at ON price_history(recorded_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_price_history_price ON price_history(price);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_price_history_listing_date ON price_history(listing_id, recorded_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_price_history_listing_price ON price_history(listing_id, price);

-- Laptop Scores Optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptop_scores_overall ON laptop_scores(overall_score);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptop_scores_llm ON laptop_scores(llm_performance_score);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptop_scores_value ON laptop_scores(value_score);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptop_scores_performance ON laptop_scores(performance_score);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_laptop_scores_evaluated_at ON laptop_scores(last_evaluated_at);

-- LLM Models Optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_llm_models_parameters ON llm_models(parameters_billions);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_llm_models_min_ram ON llm_models(min_ram_gb);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_llm_models_min_vram ON llm_models(min_vram_gb);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_llm_models_requires_gpu ON llm_models(requires_gpu);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_llm_models_quantization ON llm_models(quantization_bits);

-- ============================================================================
-- OPTIMIZED VIEWS
-- ============================================================================

-- Drop existing views if they exist
DROP VIEW IF EXISTS laptop_deals_summary CASCADE;
DROP VIEW IF EXISTS laptop_compatibility_summary CASCADE;
DROP VIEW IF EXISTS laptop_search_optimized CASCADE;

-- Optimized view for laptop deals with all necessary data
CREATE VIEW laptop_deals_summary AS
SELECT 
    ll.id as listing_id,
    ll.laptop_id,
    l.model_name,
    b.name as brand_name,
    ll.price,
    ll.url,
    ll.in_stock,
    ll.rating,
    ll.reviews_count,
    ll.listing_date,
    ll.free_shipping,
    ll.shipping_cost,
    s.name as source_name,
    s.url as source_url,
    -- Price statistics
    (SELECT MIN(price) FROM laptop_listings ll2 WHERE ll2.laptop_id = ll.laptop_id AND ll2.in_stock = true) as min_price,
    (SELECT AVG(price) FROM laptop_listings ll3 WHERE ll3.laptop_id = ll.laptop_id AND ll3.in_stock = true) as avg_price,
    -- Compatibility score
    (SELECT MAX(score) FROM laptop_llm_compatibility llc WHERE llc.laptop_id = ll.laptop_id) as max_compatibility_score,
    -- Overall laptop score
    ls.overall_score,
    ls.llm_performance_score,
    ls.value_score
FROM laptop_listings ll
JOIN laptops l ON ll.laptop_id = l.id
JOIN brands b ON l.brand_id = b.id
JOIN sources s ON ll.source_id = s.id
LEFT JOIN laptop_scores ls ON l.id = ls.laptop_id
WHERE ll.processed = true;

-- Optimized view for laptop compatibility with LLM models
CREATE VIEW laptop_compatibility_summary AS
SELECT 
    llc.laptop_id,
    l.model_name,
    b.name as brand_name,
    llc.llm_id,
    llm.name as llm_name,
    llm.parameters_billions,
    llc.score,
    llc.estimated_tokens_per_second,
    llc.can_run_offline,
    llc.estimated_memory_usage_gb,
    llc.recommended_batch_size,
    -- Laptop specs summary
    (SELECT COUNT(*) FROM laptop_cpus lc WHERE lc.laptop_id = l.id) as cpu_count,
    (SELECT COUNT(*) FROM laptop_gpus lg WHERE lg.laptop_id = l.id) as gpu_count,
    (SELECT SUM(rc.size_gb) FROM laptop_ram lr JOIN ram_configurations rc ON lr.ram_configuration_id = rc.id WHERE lr.laptop_id = l.id) as total_ram_gb
FROM laptop_llm_compatibility llc
JOIN laptops l ON llc.laptop_id = l.id
JOIN brands b ON l.brand_id = b.id
JOIN llm_models llm ON llc.llm_id = llm.id
WHERE l.is_available = true;

-- Optimized view for laptop search with all relevant data
CREATE VIEW laptop_search_optimized AS
SELECT 
    l.id,
    l.model_name,
    b.name as brand_name,
    l.msrp,
    l.is_available,
    l.release_date,
    l.image_url,
    -- Price information
    (SELECT MIN(price) FROM laptop_listings ll WHERE ll.laptop_id = l.id AND ll.in_stock = true) as min_price,
    (SELECT COUNT(*) FROM laptop_listings ll WHERE ll.laptop_id = l.id AND ll.in_stock = true) as available_listings,
    -- Scores
    ls.overall_score,
    ls.llm_performance_score,
    ls.value_score,
    ls.performance_score,
    -- Compatibility
    (SELECT MAX(score) FROM laptop_llm_compatibility llc WHERE llc.laptop_id = l.id) as max_compatibility_score,
    (SELECT COUNT(*) FROM laptop_llm_compatibility llc WHERE llc.laptop_id = l.id AND llc.score >= 70) as high_compatibility_count,
    -- Hardware summary
    (SELECT COUNT(*) FROM laptop_cpus lc WHERE lc.laptop_id = l.id) as cpu_count,
    (SELECT COUNT(*) FROM laptop_gpus lg WHERE lg.laptop_id = l.id) as gpu_count,
    (SELECT SUM(rc.size_gb) FROM laptop_ram lr JOIN ram_configurations rc ON lr.ram_configuration_id = rc.id WHERE lr.laptop_id = l.id) as total_ram_gb
FROM laptops l
JOIN brands b ON l.brand_id = b.id
LEFT JOIN laptop_scores ls ON l.id = ls.laptop_id;

-- ============================================================================
-- PERFORMANCE FUNCTIONS
-- ============================================================================

-- Function to get laptop deals with filters
CREATE OR REPLACE FUNCTION get_laptop_deals(
    p_brand_ids INTEGER[] DEFAULT NULL,
    p_min_price DECIMAL DEFAULT NULL,
    p_max_price DECIMAL DEFAULT NULL,
    p_min_score INTEGER DEFAULT NULL,
    p_in_stock_only BOOLEAN DEFAULT true,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    listing_id INTEGER,
    laptop_id INTEGER,
    model_name VARCHAR,
    brand_name VARCHAR,
    price DECIMAL,
    url VARCHAR,
    in_stock BOOLEAN,
    rating DECIMAL,
    source_name VARCHAR,
    min_price DECIMAL,
    max_compatibility_score INTEGER,
    overall_score INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        lds.listing_id,
        lds.laptop_id,
        lds.model_name,
        lds.brand_name,
        lds.price,
        lds.url,
        lds.in_stock,
        lds.rating,
        lds.source_name,
        lds.min_price,
        lds.max_compatibility_score,
        lds.overall_score
    FROM laptop_deals_summary lds
    WHERE 
        (p_brand_ids IS NULL OR lds.laptop_id IN (
            SELECT l.id FROM laptops l WHERE l.brand_id = ANY(p_brand_ids)
        ))
        AND (p_min_price IS NULL OR lds.price >= p_min_price)
        AND (p_max_price IS NULL OR lds.price <= p_max_price)
        AND (p_min_score IS NULL OR lds.max_compatibility_score >= p_min_score)
        AND (NOT p_in_stock_only OR lds.in_stock = true)
    ORDER BY lds.price ASC, lds.listing_date DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- MAINTENANCE TRIGGERS
-- ============================================================================

-- Function to update laptop scores when compatibility changes
CREATE OR REPLACE FUNCTION update_laptop_scores_on_compatibility_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the laptop's LLM performance score based on compatibility scores
    UPDATE laptop_scores 
    SET 
        llm_performance_score = (
            SELECT COALESCE(AVG(score), 0)::INTEGER 
            FROM laptop_llm_compatibility 
            WHERE laptop_id = COALESCE(NEW.laptop_id, OLD.laptop_id)
        ),
        last_evaluated_at = CURRENT_TIMESTAMP
    WHERE laptop_id = COALESCE(NEW.laptop_id, OLD.laptop_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update laptop scores when compatibility changes
DROP TRIGGER IF EXISTS trigger_update_laptop_scores_on_compatibility ON laptop_llm_compatibility;
CREATE TRIGGER trigger_update_laptop_scores_on_compatibility
    AFTER INSERT OR UPDATE OR DELETE ON laptop_llm_compatibility
    FOR EACH ROW
    EXECUTE FUNCTION update_laptop_scores_on_compatibility_change();

-- ============================================================================
-- STATISTICS UPDATE
-- ============================================================================

-- Update table statistics for better query planning
ANALYZE laptop_listings;
ANALYZE laptop_llm_compatibility;
ANALYZE laptops;
ANALYZE scraping_jobs;
ANALYZE price_history;
ANALYZE laptop_scores;
ANALYZE llm_models;

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE 'Database optimization migration completed successfully!';
    RAISE NOTICE 'Added % indexes for improved query performance', 
        (SELECT COUNT(*) FROM pg_indexes WHERE schemaname = 'public' AND indexname LIKE 'idx_%');
    RAISE NOTICE 'Created optimized views: laptop_deals_summary, laptop_compatibility_summary, laptop_search_optimized';
    RAISE NOTICE 'Added performance functions and maintenance triggers';
END $$;
