
-- update_revolico_source.sql
-- Script para actualizar la configuración de la fuente Revolico.com
-- Incluye capacidades de filtrado por fecha y rango de precios dinámico

-- Verificar si la fuente Revolico ya existe en la base de datos
DO $$
DECLARE
    source_id UUID;
    default_selectors JSONB := '{
        "graphql": {
            "query": "query SearchAds($categories: [Int!], $provinces: [String!], $phrase: String, $priceGte: Float, $priceLte: Float, $updatedOnToOrder: Boolean, $daysAgo: Int, $page: Int!) { search(categories: $categories, provinces: $provinces, phrase: $phrase, price_gte: $priceGte, price_lte: $priceLte, updated_on_to_order: $updatedOnToOrder, days_ago: $daysAgo, page: $page) { ads { id title description price updated_on province { name } images { medium } currency phone_visible emails { email } phones { number } attributes } pagination { current_page total_pages total_items } } }",
            "variables": {
                "categories": [860],
                "phrase": "laptop",
                "priceGte": 800,
                "priceLte": 2000,
                "updatedOnToOrder": true,
                "daysAgo": 7,
                "page": 1
            },
            "pageParam": "page",
            "maxPages": 5,
            "filters": {
                "priceRange": {
                    "enabled": true,
                    "min": 800,
                    "max": 2000,
                    "minParam": "priceGte",
                    "maxParam": "priceLte"
                },
                "dateFilter": {
                    "enabled": true,
                    "daysAgo": 7,
                    "param": "daysAgo"
                }
            },
            "dataMapping": {
                "items": "data.search.ads",
                "name": "title",
                "description": "description",
                "price": "price",
                "currency": "currency",
                "imageUrl": "images[0].medium",
                "publishDate": "updated_on",
                "specs": {
                    "mapping": "attributes",
                    "transforms": {
                        "ram": "attributes.ram",
                        "cpu": "attributes.processor",
                        "gpu": "attributes.graphic_card",
                        "storage": "attributes.storage"
                    }
                },
                "contactInfo": {
                    "phone": "phones[0].number",
                    "email": "emails[0].email"
                }
            }
        }
    }';
BEGIN
    -- Verificar si existe la tabla scraping_sources
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'scraping_sources') THEN
        -- Crear la tabla si no existe
        CREATE TABLE public.scraping_sources (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            name TEXT NOT NULL,
            base_url TEXT NOT NULL,
            enabled BOOLEAN NOT NULL DEFAULT true,
            last_scraped_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            selectors JSONB
        );
    END IF;

    -- Verificar si la columna selectors existe, si no, añadirla
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'scraping_sources'
        AND column_name = 'selectors'
    ) THEN
        ALTER TABLE public.scraping_sources ADD COLUMN selectors JSONB;
    END IF;

    -- Verificar si ya existe la fuente de Revolico
    SELECT id INTO source_id FROM public.scraping_sources WHERE name = 'Revolico.com' LIMIT 1;

    -- Si la fuente ya existe, actualizarla
    IF source_id IS NOT NULL THEN
        UPDATE public.scraping_sources 
        SET 
            selectors = default_selectors,
            updated_at = now()
        WHERE id = source_id;
        
        RAISE NOTICE 'Fuente Revolico.com actualizada con éxito';
    ELSE
        -- Si la fuente no existe, crearla
        INSERT INTO public.scraping_sources (name, base_url, enabled, selectors)
        VALUES ('Revolico.com', 'https://api.revolico.com/graphql', true, default_selectors);
        
        RAISE NOTICE 'Fuente Revolico.com creada con éxito';
    END IF;
END $$;

-- Ver la configuración actualizada
SELECT name, base_url, selectors 
FROM public.scraping_sources 
WHERE name = 'Revolico.com';
