-- Script de migración para LLM Laptop Lens
-- Este script importa datos de scraping en el esquema refinado

-- Primero ejecutar los scrapers para obtener datos actualizados
-- Los datos serán importados desde las APIs de OlaClick y Revolico

-- Mantener solo los tipos de referencia que no se pueden obtener del scraping
INSERT INTO panel_types (name, description, advantages, disadvantages)
VALUES 
('IPS', 'In-Plane Switching', 'Better color accuracy and viewing angles', 'Higher response times, more expensive'),
('TN', 'Twisted Nematic', 'Fast response times, cheaper', 'Poor viewing angles and color reproduction'),
('VA', 'Vertical Alignment', 'Good contrast ratios and color depth', 'Slower response times than TN'),
('OLED', 'Organic Light-Emitting Diode', 'Perfect blacks, vibrant colors, fast response', 'Burn-in risk, expensive'),
('Mini-LED', 'Mini Light-Emitting Diode', 'High brightness, good contrast', 'More expensive than traditional LED'),
('QLED', 'Quantum Dot LED', 'Enhanced color and brightness', 'Not as good as OLED for blacks');

INSERT INTO resolution_types (name, width, height, aspect_ratio)
VALUES 
('HD', 1280, 720, '16:9'),
('FHD', 1920, 1080, '16:9'),
('WUXGA', 1920, 1200, '16:10'),
('QHD', 2560, 1440, '16:9'),
('WQHD', 3440, 1440, '21:9'),
('4K UHD', 3840, 2160, '16:9'),
('5K', 5120, 2880, '16:9'),
('8K UHD', 7680, 4320, '16:9');

-- Importar datos de fabricantes y marcas desde los scrapers
-- Esto se hará mediante una función que procesa los resultados del scraping
CREATE OR REPLACE FUNCTION import_scraped_data() RETURNS void AS $$
DECLARE
  scraped_data JSON;
BEGIN
  -- Obtener datos de los scrapers (ejemplo simplificado)
  -- En la implementación real esto vendría de las APIs
  SELECT json_agg(laptop) INTO scraped_data 
  FROM (
    SELECT 
      id,
      name,
      brand,
      price,
      specifications->>'cpu' as cpu,
      specifications->>'ram' as ram,
      specifications->>'storage' as storage,
      specifications->>'gpu' as gpu,
      imageUrl,
      description,
      source
    FROM laptops_scraped
  ) laptop;

  -- Procesar datos para insertar en las tablas correspondientes
  -- (implementación detallada iría aquí)
END;
$$ LANGUAGE plpgsql;

-- Ejecutar la importación
-- SELECT import_scraped_data();

-- Crear índices adicionales para mejorar el rendimiento
CREATE INDEX IF NOT EXISTS idx_laptops_brand_id ON laptops(brand_id);
CREATE INDEX IF NOT EXISTS idx_laptop_cpus_cpu_id ON laptop_cpus(cpu_id);
CREATE INDEX IF NOT EXISTS idx_laptop_gpus_gpu_id ON laptop_gpus(gpu_id);
CREATE INDEX IF NOT EXISTS idx_laptop_ram_ram_id ON laptop_ram(ram_configuration_id);
CREATE INDEX IF NOT EXISTS idx_laptop_storage_storage_id ON laptop_storage(storage_id);
CREATE INDEX IF NOT EXISTS idx_displays_laptop_id ON displays(laptop_id);
CREATE INDEX IF NOT EXISTS idx_physical_specs_laptop_id ON physical_specs(laptop_id);
CREATE INDEX IF NOT EXISTS idx_laptop_listings_laptop_id ON laptop_listings(laptop_id);
CREATE INDEX IF NOT EXISTS idx_laptop_listings_source_id ON laptop_listings(source_id);
CREATE INDEX IF NOT EXISTS idx_price_history_listing_id ON price_history(listing_id);
