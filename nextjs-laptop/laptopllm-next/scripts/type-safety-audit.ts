/**
 * Comprehensive TypeScript `any` type scanner and fixer
 * 
 * This script identifies all instances of the `any` type throughout the project,
 * provides suggestions for proper type replacements, and generates a report.
 */

import { readFileSync, readdirSync, statSync, writeFileSync, existsSync } from 'fs';
import { join, extname, relative } from 'path';
import chalk from 'chalk';

// Interfaces
interface AnyTypeUsage {
  file: string;
  line: number;
  column: number;
  context: string;
  type: 'variable' | 'parameter' | 'return-type' | 'property' | 'generic' | 'assertion' | 'unknown';
  severity: 'critical' | 'high' | 'medium' | 'low';
  suggestion: string;
  codeSnippet: string;
}

interface TypeScanResult {
  totalFiles: number;
  scannedFiles: number;
  anyUsages: AnyTypeUsage[];
  summary: {
    critical: number;
    high: number;
    medium: number;
    low: number;
    byType: Record<string, number>;
    byFile: Record<string, number>;
  };
}

interface CriticalFix {
  file: string;
  line: number;
  originalCode: string;
  fixedCode: string;
  imports: string[];
  reasoning: string;
  priority: 'critical' | 'high';
  category: 'scraping' | 'database' | 'api' | 'components' | 'types';
}

class TypeSafetyAuditor {
  private projectRoot: string;
  private excludePatterns: RegExp[];
  private includeExtensions: string[];
  private criticalFiles: string[];

  constructor(projectRoot: string) {
    this.projectRoot = projectRoot;
    this.excludePatterns = [
      /node_modules/,
      /\.next/,
      /dist/,
      /build/,
      /coverage/,
      /\.git/,
      /generated/,
      /playwright-report/,
      /test-results/,
      /\.d\.ts$/,
      /\.config\./,
      /\.setup\./,
      /scripts\/type-safety-audit\.ts/ // Exclude self
    ];
    this.includeExtensions = ['.ts', '.tsx'];
    this.criticalFiles = [
        'src/lib/scraping/crawl4ai.service.ts',
        'src/lib/scraping/firecrawl.service.ts',
        'src/lib/scraping/base-scraper.ts',
        'src/shared/types/index.ts',
        'src/shared/types/api.ts',
        'src/features/scraper/types/index.ts',
        'src/features/laptops/types/index.ts',
        'src/lib/services/base.service.ts'
      ];
  }

  public async runAudit() {
    console.log(chalk.blue('Starting Type Safety Audit...'));

    const scanResult = await this.scanProject();
    const criticalFixes = await this.analyzeCriticalFiles();

    this.generateCombinedReport(scanResult, criticalFixes);

    console.log(chalk.green('Audit complete. Report generated at `type-safety-report.md`'));
  }

  private getAllTypeScriptFiles(): string[] {
    const files: string[] = [];
    const scanDirectory = (dir: string): void => {
      try {
        const entries = readdirSync(dir);
        for (const entry of entries) {
          const fullPath = join(dir, entry);
          const relativePath = relative(this.projectRoot, fullPath);
          if (this.excludePatterns.some(pattern => pattern.test(relativePath))) {
            continue;
          }
          const stat = statSync(fullPath);
          if (stat.isDirectory()) {
            scanDirectory(fullPath);
          } else if (this.includeExtensions.includes(extname(fullPath))) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        // ignore
      }
    };
    scanDirectory(this.projectRoot);
    return files;
  }

  private async scanProject(): Promise<TypeScanResult> {
    const files = this.getAllTypeScriptFiles();
    const anyUsages: AnyTypeUsage[] = [];
    let scannedFiles = 0;

    for (const file of files) {
      try {
        const usages = await this.scanFile(file);
        anyUsages.push(...usages);
        scannedFiles++;
      } catch (error) {
        console.warn(chalk.yellow(`Failed to scan file ${file}:`), error);
      }
    }

    return {
      totalFiles: files.length,
      scannedFiles,
      anyUsages,
      summary: this.generateSummary(anyUsages),
    };
  }

  private async scanFile(filePath: string): Promise<AnyTypeUsage[]> {
    const content = readFileSync(filePath, 'utf-8');
    const lines = content.split('\n');
    const usages: AnyTypeUsage[] = [];
    const relativePath = relative(this.projectRoot, filePath);

    const patterns = [
        { regex: /(?:let|const|var)\s+(\w+)\s*:\s*any\b/g, type: 'variable' as const, severity: 'high' as const },
        { regex: /(?:function\s+\w+|(?:const|let)\s+\w+\s*=\s*(?:async\s+)?(?:function|\()|(?:async\s+)?(?:function|\()|:\s*(?:async\s+)?(?:function|\()).*?\(\s*[^)]*?(\w+)\s*:\s*any\b/g, type: 'parameter' as const, severity: 'critical' as const },
        { regex: /\)\s*:\s*any\b/g, type: 'return-type' as const, severity: 'high' as const },
        { regex: /(\w+)\s*:\s*any\b/g, type: 'property' as const, severity: 'medium' as const },
        { regex: /<[^>]*?=\s*any\b/g, type: 'generic' as const, severity: 'medium' as const },
        { regex: /\bas\s+any\b/g, type: 'assertion' as const, severity: 'high' as const },
        { regex: /any\[\]/g, type: 'property' as const, severity: 'medium' as const }
      ];

    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex];
      if (line === undefined) continue;
      const lineNumber = lineIndex + 1;

      if (line.trim().startsWith('//') || line.trim().startsWith('*')) {
        continue;
      }

      for (const pattern of patterns) {
        let match;
        pattern.regex.lastIndex = 0;
        while ((match = pattern.regex.exec(line)) !== null) {
          const column = match.index + 1;
          const suggestion = this.generateTypeSuggestion(relativePath, line, pattern.type);
          usages.push({
            file: relativePath,
            line: lineNumber,
            column,
            context: line.trim(),
            type: pattern.type,
            severity: this.determineSeverity(relativePath, pattern.type),
            suggestion,
            codeSnippet: this.getCodeSnippet(lines, lineIndex),
          });
        }
      }
    }
    return usages;
  }

  private generateTypeSuggestion(filePath: string, line: string, usageType: string): string {
    if (filePath.includes('scraping') || filePath.includes('scraper')) {
        if (line.includes('response') || line.includes('result')) return 'ScrapingResult | ScrapedData | Crawl4AIResponse';
        if (line.includes('config') || line.includes('options')) return 'ScrapingConfig | Crawl4AIConfig';
        if (line.includes('axios') || line.includes('http')) return 'AxiosInstance | AxiosResponse<T>';
    }
    if (filePath.includes('laptop') || filePath.includes('llm')) {
        if (line.includes('spec') || line.includes('specification')) return 'LaptopSpecifications | LaptopSpecs';
        if (line.includes('compatibility')) return 'LLMCompatibilityScore | CompatibilityResult';
        if (line.includes('data') || line.includes('laptop')) return 'LaptopData | ScrapedLaptopData';
    }
    if (filePath.includes('api') || filePath.includes('service')) {
        if (line.includes('response')) return 'ApiResponse<T> | Response';
        if (line.includes('request')) return 'ApiRequest | RequestConfig';
    }
    switch (usageType) {
        case 'parameter': return 'unknown | Record<string, unknown> | T (generic)';
        case 'return-type': return 'Promise<T> | T | void';
        case 'property': return 'unknown | string | number | Record<string, unknown>';
        case 'assertion': return 'Remove assertion or use proper type';
        default: return 'unknown | Record<string, unknown>';
    }
  }

  private determineSeverity(filePath: string, usageType: string): 'critical' | 'high' | 'medium' | 'low' {
    if (filePath.includes('services') || filePath.includes('api') || filePath.includes('database')) {
      return usageType === 'parameter' ? 'critical' : 'high';
    }
    if (filePath.includes('scraping') || filePath.includes('types') || filePath.includes('schemas')) {
      return usageType === 'parameter' ? 'high' : 'medium';
    }
    if (filePath.includes('components') || filePath.includes('hooks')) {
      return 'medium';
    }
    return 'low';
  }

  private getCodeSnippet(lines: string[], lineIndex: number): string {
    const start = Math.max(0, lineIndex - 2);
    const end = Math.min(lines.length, lineIndex + 3);
    return lines.slice(start, end).map((line, index) => {
        const actualLineNumber = start + index + 1;
        const marker = actualLineNumber === lineIndex + 1 ? '>>> ' : '    ';
        return `${marker}${actualLineNumber}: ${line}`;
      }).join('\n');
  }

  private generateSummary(usages: AnyTypeUsage[]) {
    const summary = {
      critical: 0, high: 0, medium: 0, low: 0,
      byType: {} as Record<string, number>,
      byFile: {} as Record<string, number>,
    };
    for (const usage of usages) {
      summary[usage.severity]++;
      summary.byType[usage.type] = (summary.byType[usage.type] || 0) + 1;
      summary.byFile[usage.file] = (summary.byFile[usage.file] || 0) + 1;
    }
    return summary;
  }

  private async analyzeCriticalFiles(): Promise<CriticalFix[]> {
    const fixes: CriticalFix[] = [];
    for (const file of this.criticalFiles) {
      const fullPath = join(this.projectRoot, file);
      if (existsSync(fullPath)) {
        try {
          const fileFixes = await this.analyzeFileForFixes(fullPath, file);
          fixes.push(...fileFixes);
        } catch (error) {
          console.warn(chalk.yellow(`Failed to analyze ${file} for fixes:`), error);
        }
      }
    }
    return fixes.sort((a, b) => {
      const priorityOrder = { critical: 0, high: 1 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    });
  }

  private async analyzeFileForFixes(filePath: string, relativePath: string): Promise<CriticalFix[]> {
    const content = readFileSync(filePath, 'utf-8');
    const lines = content.split('\n');
    const fixes: CriticalFix[] = [];
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      if (line === undefined) continue;
      const lineNumber = i + 1;
      if (line.trim().startsWith('//') || line.trim().startsWith('*')) {
        continue;
      }
      const fix = this.generateFix(relativePath, lineNumber, line);
      if (fix) {
        fixes.push(fix);
      }
    }
    return fixes;
  }

  private generateFix(file: string, line: number, code: string): CriticalFix | null {
    const trimmedCode = code.trim();
    if (file.includes('scraping') || file.includes('scraper')) {
        if (trimmedCode.includes('axiosInstance: any')) return { file, line, originalCode: trimmedCode, fixedCode: trimmedCode.replace(': any', ': AxiosInstance'), imports: ['import type { AxiosInstance } from "axios"'], reasoning: 'Axios provides proper typing for HTTP client instances', priority: 'critical', category: 'scraping' };
        if (trimmedCode.includes('response') && trimmedCode.includes(': any')) return { file, line, originalCode: trimmedCode, fixedCode: trimmedCode.replace(': any', ': AxiosResponse<ScrapingResult>'), imports: ['import type { AxiosResponse } from "axios"', 'import type { ScrapingResult } from "@/shared/types"'], reasoning: 'HTTP responses should be properly typed with expected data structure', priority: 'critical', category: 'scraping' };
        if (trimmedCode.includes('config') && trimmedCode.includes(': any')) return { file, line, originalCode: trimmedCode, fixedCode: trimmedCode.replace(': any', ': ScrapingConfig'), imports: ['import type { ScrapingConfig } from "@/shared/types"'], reasoning: 'Configuration objects should use defined interfaces', priority: 'high', category: 'scraping' };
        if (trimmedCode.includes('extracted_content') && trimmedCode.includes(': any')) return { file, line, originalCode: trimmedCode, fixedCode: trimmedCode.replace(': any', ': ExtractedContent'), imports: [`interface ExtractedContent {\n  links?: Array<{ text: string; href: string }>\n  products?: Array<Record<string, unknown>>\n  structured_data?: Record<string, unknown>\n  [key: string]: unknown\n}`], reasoning: 'Extracted content should have a defined structure', priority: 'high', category: 'scraping' };
    }
    if (file.includes('api') || file.includes('service') || file.includes('database')) {
        if (trimmedCode.includes('data: any')) return { file, line, originalCode: trimmedCode, fixedCode: trimmedCode.replace('data: any', 'data: unknown'), imports: [], reasoning: 'Use unknown for type safety, then validate with type guards', priority: 'critical', category: 'api' };
        if (trimmedCode.includes('apiResponse') && trimmedCode.includes(': any')) return { file, line, originalCode: trimmedCode, fixedCode: trimmedCode.replace(': any', ': ApiResponse<T>'), imports: ['import type { ApiResponse } from "@/shared/types"'], reasoning: 'API responses should use generic typing', priority: 'critical', category: 'api' };
    }
    if (file.includes('types')) {
        if (trimmedCode.includes(': any') && !trimmedCode.includes('//')) {
            const propertyName = trimmedCode.split(':')[0]?.trim();
            if (propertyName?.includes('laptop') || propertyName?.includes('spec')) return { file, line, originalCode: trimmedCode, fixedCode: trimmedCode.replace(': any', ': LaptopSpecifications | unknown'), imports: ['import type { LaptopSpecifications } from "@/shared/schemas"'], reasoning: 'Laptop-related properties should use defined specifications', priority: 'high', category: 'types' };
            if (propertyName?.includes('compatibility')) return { file, line, originalCode: trimmedCode, fixedCode: trimmedCode.replace(': any', ': LLMCompatibilityScore'), imports: ['import type { LLMCompatibilityScore } from "@/shared/schemas"'], reasoning: 'Compatibility properties should use defined scoring types', priority: 'high', category: 'types' };
            return { file, line, originalCode: trimmedCode, fixedCode: trimmedCode.replace(': any', ': unknown'), imports: [], reasoning: 'Replace any with unknown for type safety', priority: 'high', category: 'types' };
        }
    }
    if (file.includes('component') || file.includes('hook')) {
        if (trimmedCode.includes('props') && trimmedCode.includes(': any')) return { file, line, originalCode: trimmedCode, fixedCode: trimmedCode.replace(': any', ': Record<string, unknown>'), imports: [], reasoning: 'Component props should be explicitly typed', priority: 'high', category: 'components' };
        if (trimmedCode.includes('event') && trimmedCode.includes(': any')) return { file, line, originalCode: trimmedCode, fixedCode: trimmedCode.replace(': any', ': React.MouseEvent | React.ChangeEvent'), imports: ['import type { MouseEvent, ChangeEvent } from "react"'], reasoning: 'Event handlers should use proper React event types', priority: 'high', category: 'components' };
    }
    return null;
  }

  private generateCombinedReport(scanResult: TypeScanResult, criticalFixes: CriticalFix[]) {
    let report = '# Type Safety Audit Report\n\n';
    report += `## Overall Summary\n\n`;
    report += `- **Total files scanned:** ${scanResult.scannedFiles}\n`;
    report += `- **Total 'any' usages found:** ${scanResult.anyUsages.length}\n`;
    report += `- **Actionable critical fixes identified:** ${criticalFixes.length}\n\n`;

    report += `### 'any' Usages by Severity\n`;
    report += `- ${chalk.red('Critical:')} ${scanResult.summary.critical}\n`;
    report += `- ${chalk.yellow('High:')} ${scanResult.summary.high}\n`;
    report += `- ${chalk.blue('Medium:')} ${scanResult.summary.medium}\n`;
    report += `- ${chalk.gray('Low:')} ${scanResult.summary.low}\n\n`;

    report += this.generateFixReport(criticalFixes);

    report += `## Detailed 'any' Usage Analysis\n\n`;
    const criticalUsages = scanResult.anyUsages.filter(u => u.severity === 'critical');
    if (criticalUsages.length > 0) {
        report += `### Critical Usages\n\n`;
        criticalUsages.forEach(usage => {
            report += this.formatUsage(usage);
        });
    }
    
    const highUsages = scanResult.anyUsages.filter(u => u.severity === 'high');
    if (highUsages.length > 0) {
        report += `### High Priority Usages\n\n`;
        highUsages.forEach(usage => {
            report += this.formatUsage(usage);
        });
    }

    writeFileSync(join(this.projectRoot, 'type-safety-report.md'), report);
  }

  private generateFixReport(fixes: CriticalFix[]): string {
    let report = '## Actionable Critical Fixes\n\n';
    if (fixes.length === 0) {
        return report + "No critical fixes identified. Well done!\n\n";
    }
    
    const criticalFixes = fixes.filter(f => f.priority === 'critical');
    const highFixes = fixes.filter(f => f.priority === 'high');
    
    if (criticalFixes.length > 0) {
      report += `### Critical Fixes (Immediate Action Required)\n\n`;
      criticalFixes.forEach((fix, index) => {
        report += this.formatFix(fix, index + 1);
      });
    }

    if (highFixes.length > 0) {
      report += `### High Priority Fixes\n\n`;
      highFixes.forEach((fix, index) => {
        report += this.formatFix(fix, index + 1);
      });
    }

    return report;
  }

  private formatFix(fix: CriticalFix, index: number): string {
    let formatted = `#### ${index}. ${fix.file}:${fix.line}\n\n`;
    formatted += `**Category:** ${fix.category} | **Priority:** ${fix.priority}\n\n`;
    formatted += `**Original Code:**\n\`\`\`typescript\n${fix.originalCode}\n\`\`\`\n\n`;
    formatted += `**Suggested Fix:**\n\`\`\`typescript\n${fix.fixedCode}\n\`\`\`\n\n`;
    if (fix.imports.length > 0) {
      formatted += `**Required Imports/Interfaces:**\n\`\`\`typescript\n${fix.imports.join('\n')}\n\`\`\`\n\n`;
    }
    formatted += `**Reasoning:** ${fix.reasoning}\n\n---\n\n`;
    return formatted;
  }

  private formatUsage(usage: AnyTypeUsage): string {
    let formatted = `#### ${usage.file}:${usage.line}:${usage.column}\n\n`;
    formatted += `**Severity:** ${usage.severity} | **Type:** ${usage.type}\n\n`;
    formatted += `**Context:** \`${usage.context}\`\n\n`;
    formatted += `**Suggestion:** ${usage.suggestion}\n\n`;
    formatted += `**Code Snippet:**\n\`\`\`typescript\n${usage.codeSnippet}\n\`\`\`\n\n---\n\n`;
    return formatted;
  }
}

// Main execution block
(async () => {
  try {
    const auditor = new TypeSafetyAuditor(process.cwd());
    await auditor.runAudit();
  } catch (error) {
    console.error(chalk.red('An unexpected error occurred during the audit:'), error);
    process.exit(1);
  }
})();
