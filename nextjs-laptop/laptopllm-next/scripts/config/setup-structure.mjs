#!/usr/bin/env node
/**

* Script para crear la estructura de directorios del proyecto LLM Laptop Lens
* Ejecutar: node setup-structure.mjs
  */

import { mkdirSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';

const projectStructure = {
  // Directorios principales ya preservados
  // docs/, memories/, memory-bank/, scripts/ - ya existen

  // Estructura nueva a crear
  'prisma': [],
  'public': [],
  'src': {
    'components': {
      'atoms': [],
      'molecules': [],
      'organisms': [],
      'shared': [],
      'ui': [] // shadcn/ui components
    },
    'data': [], // Considerar si se usará para datos estáticos/fixtures o si la lógica de datos va en 'features'
    'features': {
      'admin': [],
      'laptops': [],
      'llm-compatibility': [],
      'llm-models': [],
      'scraper': []
    },
    'lib': [],
    'providers': [],
    'shared': {
      'hooks': [],
      'types': [], // Ubicación principal para tipos globales e interfaces
      'services': [],
      'utils': []
    },
    'styles': []
    // 'types': [] // Eliminado, usar 'src/shared/types' en su lugar
  },
  'tests': {
    'unit': [],
    'integration': [],
    'e2e': [],
    '__mocks__': []
  }
};

const initialFiles = {
  'prisma/schema.prisma': `// Esquema base de Prisma para PostgreSQL local
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Modelo base para laptops
model Laptop {
  id            String   @id @default(cuid())
  brand         String
  model         String
  price         Float
  specifications Json
  source        String
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relaciones
  compatibilities LLMCompatibility[]

  @@map("laptops")
}

// Modelo para compatibilidad con LLMs
model LLMCompatibility {
  id           String @id @default(cuid())
  laptopId     String
  modelName    String
  compatibility String // 'high' | 'medium' | 'low'
  requirements Json
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relaciones
  laptop Laptop @relation(fields: [laptopId], references: [id], onDelete: Cascade)

  @@map("llm_compatibilities")
}

// Modelo para configuración de scraping
model ScrapingConfig {
  id        String   @id @default(cuid())
  source    String   @unique
  enabled   Boolean  @default(true)
  schedule  String   // Cron expression
  lastRun   DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("scraping_configs")
}`,

  'src/app.tsx': `import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ThemeProvider } from 'next-themes';
import { Toaster } from 'sonner';
import './styles/globals.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        <BrowserRouter>
          <div className="min-h-screen bg-background text-foreground">
            <h1 className="text-4xl font-bold text-center py-8">
              LLM Laptop Lens
            </h1>
            <p className="text-center text-muted-foreground">
              Sistema de análisis de laptops y compatibilidad con LLMs
            </p>
            <p className="text-center text-sm text-muted-foreground mt-2">
              Conectado a PostgreSQL local
            </p>
          </div>
          <Toaster />
        </BrowserRouter>
      </ThemeProvider>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}

export default App;`,

  'src/main.tsx': `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './app';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
);`,

  'src/styles/globals.css': `@import 'tailwindcss';

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {

* {
  @apply border-border;
  }
  body {
  @apply bg-background text-foreground;
  }
  }`,
  
  'src/lib/database.ts': `import { PrismaClient } from '@prisma/client';

declare global {
  var __prisma: PrismaClient | undefined;
}

// Prevenir múltiples instancias de Prisma en desarrollo
const prisma = globalThis.__prisma || new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
});

if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma;
}

export { prisma };

// Función para verificar conexión
export const checkDatabaseConnection = async () => {
  try {
    await prisma.$connect();
    console.log('✅ Conexión a PostgreSQL establecida');
    return true;
  } catch (error) {
    console.error('❌ Error conectando a PostgreSQL:', error);
    return false;
  }
};

// Función para cerrar conexión
export const closeDatabaseConnection = async () => {
  await prisma.$disconnect();
};`,

  'src/shared/types/index.ts': `// Tipos globales del proyecto
export interface LaptopData {
  id: string;
  brand: string;
  model: string;
  price: number;
  specifications: Record<string, any>;
  source: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ScrapingConfig {
  source: string;
  enabled: boolean;
  schedule: string;
  lastRun?: Date;
}

export interface LLMCompatibility {
  laptopId: string;
  modelName: string;
  compatibility: 'high' | 'medium' | 'low';
  requirements: Record<string, any>;
}`,

  'vite.config.ts': `import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    open: true,
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
});`,

  'tailwind.config.js': `/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "calc(var(--radius))",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}`,

  'vitest.config.ts': `import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react-swc';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'tests/',
        '**/*.d.ts',
        '**/*.config.*',
      ],
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
});`,

  'tests/setup.ts': `import '@testing-library/jest-dom';
import { expect, afterEach } from 'vitest';
import { cleanup } from '@testing-library/react';

// Cleanup después de cada test
afterEach(() => {
  cleanup();
});`,

  '.env.example': `# PostgreSQL Local
DATABASE_URL="postgresql://username:password@localhost:5432/llm_laptop_lens"

# Opcional: URL para conexión directa sin Prisma

POSTGRES_USER=your_postgres_user
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DB=llm_laptop_lens
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# Firecrawl (opcional)

FIRECRAWL_API_KEY=your_firecrawl_api_key

# Desarrollo

NODE_ENV=development`,

  'index.html': `<!doctype html>

<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LLM Laptop Lens</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>`
};

function createDirectory(basePath, structure) {
  for (const [key, value] of Object.entries(structure)) {
    const currentPath = join(basePath, key);

    if (Array.isArray(value)) {
      // Es un directorio terminal
      if (!existsSync(currentPath)) {
        mkdirSync(currentPath, { recursive: true });
        console.log(`✅ Creado directorio: ${currentPath}`);
      }
    } else {
      // Es un objeto con subdirectorios
      if (!existsSync(currentPath)) {
        mkdirSync(currentPath, { recursive: true });
        console.log(`✅ Creado directorio: ${currentPath}`);
      }
      createDirectory(currentPath, value);
    }

  }
}

function createInitialFiles() {
  for (const [filePath, content] of Object.entries(initialFiles)) {
    // Asegurarse de que el directorio padre exista antes de crear el archivo
    const dirPath = join(process.cwd(), filePath, '..');
    if (!existsSync(dirPath)) {
      mkdirSync(dirPath, { recursive: true });
    }

    if (!existsSync(filePath)) {
      writeFileSync(filePath, content);
      console.log(`✅ Creado archivo: ${filePath}`);
    } else {
      console.log(`⚠️  Ya existe: ${filePath}`);
    }

  }
}

// Ejecutar
console.log('🚀 Iniciando configuración de estructura del proyecto...\n');

// Crear estructura de directorios
createDirectory('.', projectStructure);

console.log('\n📁 Estructura de directorios creada.\n');

// Crear archivos iniciales
createInitialFiles();

console.log('\n✨ ¡Configuración completada!');
console.log('\n📋 Próximos pasos:');
console.log('1. Asegurar que PostgreSQL esté ejecutándose localmente');
console.log('2. Crear base de datos: createdb llm_laptop_lens');
console.log('3. pnpm install');
console.log('4. Copiar .env.example a .env y configurar DATABASE_URL');
console.log('5. npx prisma generate');
console.log('6. npx prisma db push');
console.log('7. pnpm dev');
console.log('\n🎯 ¡Tu proyecto está listo para comenzar el desarrollo!');
