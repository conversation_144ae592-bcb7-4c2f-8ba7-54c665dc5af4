export const depCheckConfig = {
  // List of extensions to scan for imports
  extensions: ['.mjs','.js', '.jsx', '.ts', 'tsx'],
  
  // Directory to scan
  scanDir: 'src',

  // List of built-in Node.js modules to ignore
  nodeBuiltins: [
    'fs', 'path', 'http', 'https', 'url', 'util', 'os', 'stream', 'events',
    'crypto', 'zlib', 'querystring', 'child_process', 'buffer', 'assert',
    'cluster', 'dgram', 'dns', 'domain', 'net', 'readline', 'repl', 'tls',
    'tty', 'v8', 'vm', 'worker_threads'
  ],

  // List of dependencies to ignore when checking for unused packages
  ignoreUnused: [
    '@types/',
    'eslint',
    'typescript',
    'vite',
    'vitest',
    'tailwind',
    'postcss',
    'autoprefixer',
    'react-refresh',
    // Add any other dev-time only packages here
  ],
};
