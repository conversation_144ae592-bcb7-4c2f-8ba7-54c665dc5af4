import { connect, disconnect } from '../lib/db-client.mjs';
import logger from '../utils/logger.mjs';
import { appTestConfig } from '../config/app-test.config.mjs';

async function checkTables(sql) {
  logger.info('Verifying essential tables...');
  const result = await sql`
    SELECT table_name FROM information_schema.tables 
    WHERE table_schema = 'public'
  `;
  const existingTables = result.map(row => row.table_name);
  const missingTables = appTestConfig.essentialTables.filter(
    table => !existingTables.includes(table)
  );

  if (missingTables.length > 0) {
    logger.error(`Missing tables: ${missingTables.join(', ')}`);
    return false;
  }
  
  logger.success('All essential tables exist.');
  return true;
}

async function checkViews(sql) {
    logger.info('Verifying essential views...');
    const result = await sql`
      SELECT table_name FROM information_schema.views 
      WHERE table_schema = 'public'
    `;
    const existingViews = result.map(row => row.table_name);
    const missingViews = appTestConfig.essentialViews.filter(
      view => !existingViews.includes(view)
    );
  
    if (missingViews.length > 0) {
      logger.warn(`Missing views: ${missingViews.join(', ')}`);
      return false;
    }
    
    logger.success('All essential views exist.');
    return true;
  }

async function checkData(sql) {
    logger.info('Verifying table data...');
    let allOk = true;
    for (const table of appTestConfig.essentialTables) {
        const countResult = await sql`SELECT COUNT(*) as count FROM ${sql(table)}`;
        const count = parseInt(countResult[0].count, 10);
        if (count === 0) {
            logger.warn(`Table '${table}' is empty.`);
            allOk = false;
        } else {
            logger.info(`Table '${table}' contains ${count} rows.`);
        }
    }

    if(allOk) {
        logger.success('All essential tables contain data.');
    } else {
        logger.warn('Some essential tables are empty. Consider running a seed script.');
    }
    return allOk;
}

export async function testApp() {
  let sql;
  let finalStatus = 'SUCCESS';

  try {
    logger.info('=== Starting Application Sanity Test ===');
    sql = await connect();

    const tablesOk = await checkTables(sql);
    if (!tablesOk) finalStatus = 'FAILED';

    const viewsOk = await checkViews(sql);
    if (!viewsOk) finalStatus = 'WARNING';
    
    const dataOk = await checkData(sql);
    if (!dataOk && tablesOk) finalStatus = 'WARNING';

    logger.info('=== Test Summary ===');
    if (finalStatus === 'SUCCESS') {
        logger.success('✅ Application test passed successfully.');
    } else if (finalStatus === 'WARNING') {
        logger.warn('⚠️ Application test completed with warnings.');
        logger.warn('The basic structure is OK, but some views are missing or tables are empty.');
        logger.warn('Consider running migrations (db:migrate) or seeding data.');
    }
    else {
        logger.error('❌ Application test failed.');
        logger.error('Essential database tables are missing. Run migrations with `db:migrate`.');
        process.exit(1);
    }

  } catch {
    logger.error('An unexpected error occurred during the application test.');
    process.exit(1);
  } finally {
    if (sql) {
      await disconnect();
    }
  }
}
