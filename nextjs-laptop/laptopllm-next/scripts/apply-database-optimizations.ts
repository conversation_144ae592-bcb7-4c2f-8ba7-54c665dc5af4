#!/usr/bin/env tsx

/**
 * Database Optimization Application Script
 * 
 * This script applies database optimizations including indexes, views, and functions
 * to improve query performance for the LaptopLLM Finder application.
 */

import { execSync } from 'child_process'
import { readFileSync, existsSync } from 'fs'
import { join } from 'path'
import { databaseOptimizationService } from '../src/lib/database/optimization.service'

interface OptimizationResult {
  step: string
  success: boolean
  message: string
  duration?: number
}

class DatabaseOptimizationApplicator {
  private results: OptimizationResult[] = []

  async run(): Promise<void> {
    console.log('🚀 Starting Database Optimization Process...\n')

    try {
      // Check current status
      await this.checkCurrentStatus()

      // Apply SQL migration
      await this.applySQLMigration()

      // Regenerate Prisma client
      await this.regeneratePrismaClient()

      // Verify optimizations
      await this.verifyOptimizations()

      // Update statistics
      await this.updateStatistics()

      // Generate report
      await this.generateReport()

      console.log('\n✅ Database optimization process completed successfully!')
      
    } catch (error) {
      console.error('\n❌ Database optimization process failed:', error)
      process.exit(1)
    } finally {
      await databaseOptimizationService.disconnect()
    }
  }

  private async checkCurrentStatus(): Promise<void> {
    const startTime = Date.now()
    
    try {
      console.log('📊 Checking current optimization status...')
      
      const status = await databaseOptimizationService.checkOptimizationStatus()
      
      console.log(`   • Indexes found: ${status.indexCount}`)
      console.log(`   • Views found: ${status.viewCount}`)
      console.log(`   • Functions found: ${status.functionCount}`)
      console.log(`   • Status: ${status.applied ? '✅ Already optimized' : '⚠️ Needs optimization'}`)

      this.results.push({
        step: 'Status Check',
        success: true,
        message: `Found ${status.indexCount} indexes, ${status.viewCount} views, ${status.functionCount} functions`,
        duration: Date.now() - startTime
      })

      if (status.applied) {
        console.log('\n⚠️ Optimizations appear to already be applied. Continuing anyway...')
      }

    } catch (error) {
      this.results.push({
        step: 'Status Check',
        success: false,
        message: `Failed to check status: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
      throw error
    }
  }

  private async applySQLMigration(): Promise<void> {
    const startTime = Date.now()
    
    try {
      console.log('\n🔧 Applying SQL optimization migration...')
      
      const migrationPath = join(process.cwd(), 'scripts/db/migrations/0010_database_optimization.sql')
      
      if (!existsSync(migrationPath)) {
        throw new Error(`Migration file not found: ${migrationPath}`)
      }

      // Check if DATABASE_URL is set
      if (!process.env.DATABASE_URL) {
        throw new Error('DATABASE_URL environment variable is not set')
      }

      // Apply migration using psql
      console.log('   • Executing SQL migration...')
      
      try {
        const output = execSync(`psql "${process.env.DATABASE_URL}" -f "${migrationPath}"`, {
          encoding: 'utf8',
          stdio: 'pipe'
        })
        
        console.log('   • Migration executed successfully')
        
        // Check for any errors in output
        if (output.includes('ERROR')) {
          console.warn('   ⚠️ Some errors occurred during migration:')
          console.warn(output)
        }
        
      } catch (execError: unknown) {
        // Some "errors" might be expected (like indexes already existing)
        if (execError.stdout && execError.stdout.includes('already exists')) {
          console.log('   • Some optimizations already exist (expected)')
        } else {
          throw execError
        }
      }

      this.results.push({
        step: 'SQL Migration',
        success: true,
        message: 'Database optimization migration applied successfully',
        duration: Date.now() - startTime
      })

    } catch (error) {
      this.results.push({
        step: 'SQL Migration',
        success: false,
        message: `Failed to apply migration: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
      throw error
    }
  }

  private async regeneratePrismaClient(): Promise<void> {
    const startTime = Date.now()
    
    try {
      console.log('\n🔄 Regenerating Prisma client...')
      
      execSync('npx prisma generate', {
        encoding: 'utf8',
        stdio: 'inherit'
      })

      console.log('   • Prisma client regenerated successfully')

      this.results.push({
        step: 'Prisma Generation',
        success: true,
        message: 'Prisma client regenerated with new schema',
        duration: Date.now() - startTime
      })

    } catch (error) {
      this.results.push({
        step: 'Prisma Generation',
        success: false,
        message: `Failed to regenerate Prisma client: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
      throw error
    }
  }

  private async verifyOptimizations(): Promise<void> {
    const startTime = Date.now()
    
    try {
      console.log('\n✅ Verifying applied optimizations...')
      
      const status = await databaseOptimizationService.checkOptimizationStatus()
      
      console.log(`   • Indexes: ${status.indexCount} (expected: 30+)`)
      console.log(`   • Views: ${status.viewCount} (expected: 3)`)
      console.log(`   • Functions: ${status.functionCount} (expected: 2+)`)

      if (!status.applied) {
        throw new Error('Optimizations were not applied correctly')
      }

      console.log('   • All optimizations verified successfully ✅')

      this.results.push({
        step: 'Verification',
        success: true,
        message: `Verified ${status.indexCount} indexes, ${status.viewCount} views, ${status.functionCount} functions`,
        duration: Date.now() - startTime
      })

    } catch (error) {
      this.results.push({
        step: 'Verification',
        success: false,
        message: `Failed to verify optimizations: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
      throw error
    }
  }

  private async updateStatistics(): Promise<void> {
    const startTime = Date.now()
    
    try {
      console.log('\n📈 Updating table statistics...')
      
      await databaseOptimizationService.updateTableStatistics()
      
      console.log('   • Table statistics updated for better query planning')

      this.results.push({
        step: 'Statistics Update',
        success: true,
        message: 'Table statistics updated successfully',
        duration: Date.now() - startTime
      })

    } catch (error) {
      this.results.push({
        step: 'Statistics Update',
        success: false,
        message: `Failed to update statistics: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
      // Don't throw here, statistics update is not critical
      console.warn('   ⚠️ Statistics update failed, but continuing...')
    }
  }

  private async generateReport(): Promise<void> {
    const startTime = Date.now()
    
    try {
      console.log('\n📊 Generating optimization report...')
      
      const stats = await databaseOptimizationService.getOptimizationStats()
      
      console.log('\n' + '='.repeat(60))
      console.log('📋 DATABASE OPTIMIZATION REPORT')
      console.log('='.repeat(60))
      
      // Summary
      console.log('\n📈 SUMMARY:')
      console.log(`   • Total indexes: ${stats.indexUsage.length}`)
      console.log(`   • Active indexes: ${stats.indexUsage.filter(i => i.idx_scan > 0).length}`)
      console.log(`   • Monitored queries: ${stats.queryPerformance.length}`)
      console.log(`   • Recommendations: ${stats.optimizationRecommendations.length}`)

      // Top performing indexes
      console.log('\n🚀 TOP PERFORMING INDEXES:')
      const topIndexes = stats.indexUsage
        .filter(i => i.idx_scan > 0)
        .sort((a, b) => b.idx_scan - a.idx_scan)
        .slice(0, 5)
      
      topIndexes.forEach(idx => {
        console.log(`   • ${idx.indexname}: ${idx.idx_scan} scans`)
      })

      // Performance recommendations
      if (stats.optimizationRecommendations.length > 0) {
        console.log('\n⚠️ RECOMMENDATIONS:')
        stats.optimizationRecommendations.slice(0, 3).forEach(rec => {
          console.log(`   • [${rec.priority.toUpperCase()}] ${rec.description}`)
        })
      }

      // Process summary
      console.log('\n🔄 PROCESS SUMMARY:')
      this.results.forEach(result => {
        const status = result.success ? '✅' : '❌'
        const duration = result.duration ? ` (${result.duration}ms)` : ''
        console.log(`   ${status} ${result.step}: ${result.message}${duration}`)
      })

      console.log('\n' + '='.repeat(60))

      this.results.push({
        step: 'Report Generation',
        success: true,
        message: 'Optimization report generated successfully',
        duration: Date.now() - startTime
      })

    } catch (error) {
      this.results.push({
        step: 'Report Generation',
        success: false,
        message: `Failed to generate report: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
      // Don't throw here, report generation is not critical
      console.warn('   ⚠️ Report generation failed, but optimization completed')
    }
  }
}

// Run the optimization process
if (require.main === module) {
  const applicator = new DatabaseOptimizationApplicator()
  applicator.run().catch(error => {
    console.error('Fatal error:', error)
    process.exit(1)
  })
}

export { DatabaseOptimizationApplicator }
