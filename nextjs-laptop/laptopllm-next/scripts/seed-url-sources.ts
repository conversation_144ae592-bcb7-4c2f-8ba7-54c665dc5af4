#!/usr/bin/env tsx

/**
 * Script para poblar la base de datos con fuentes URL iniciales
 * Este script crea fuentes de scraping para sitios web de laptops populares
 */

import { PrismaClient } from '../generated/prisma'

const prisma = new PrismaClient()

const urlSources = [
  {
    name: 'Amazon Laptops',
    url: 'https://www.amazon.com/s?k=laptop&ref=nb_sb_noss',
    description: 'Amazon laptop search results',
    isActive: true,
    scrapingFrequency: 'daily' as const,
    requiresFullBrowserAutomation: true,
    selectors: {
      productLinks: '[data-component-type="s-search-result"] h3 a',
      nextPage: '.s-pagination-next',
    },
  },
  {
    name: 'Best Buy Laptops',
    url: 'https://www.bestbuy.com/site/computers-tablets/laptops/abcat0502000.c',
    description: 'Best Buy laptop category page',
    isActive: true,
    scrapingFrequency: 'daily' as const,
    requiresFullBrowserAutomation: true,
    selectors: {
      productLinks: '.sku-item .sku-header a',
      nextPage: '.sr-pagination .btn-next',
    },
  },
  {
    name: 'Newegg Laptops',
    url: 'https://www.newegg.com/Laptops-Notebooks/Category/ID-223',
    description: 'Newegg laptop category',
    isActive: true,
    scrapingFrequency: 'daily' as const,
    requiresFullBrowserAutomation: true,
    selectors: {
      productLinks: '.item-container .item-title',
      nextPage: '.list-tool-pagination .btn-next',
    },
  },
  {
    name: 'HP Official Store',
    url: 'https://www.hp.com/us-en/shop/cat/laptops',
    description: 'HP official laptop store',
    isActive: true,
    scrapingFrequency: 'weekly' as const,
    requiresFullBrowserAutomation: true,
    selectors: {
      productLinks: '.product-tile .product-title a',
      nextPage: '.pagination .next',
    },
  },
  {
    name: 'Dell Official Store',
    url: 'https://www.dell.com/en-us/shop/dell-laptops/sc/laptops',
    description: 'Dell official laptop store',
    isActive: true,
    scrapingFrequency: 'weekly' as const,
    requiresFullBrowserAutomation: true,
    selectors: {
      productLinks: '.ps-product .ps-title a',
      nextPage: '.pagination .next',
    },
  },
  {
    name: 'Lenovo Official Store',
    url: 'https://www.lenovo.com/us/en/c/laptops/',
    description: 'Lenovo official laptop store',
    isActive: true,
    scrapingFrequency: 'weekly' as const,
    requiresFullBrowserAutomation: true,
    selectors: {
      productLinks: '.product-card .product-title a',
      nextPage: '.pagination .next',
    },
  },
  {
    name: 'ASUS Official Store',
    url: 'https://www.asus.com/laptops/',
    description: 'ASUS official laptop store',
    isActive: true,
    scrapingFrequency: 'weekly' as const,
    requiresFullBrowserAutomation: true,
    selectors: {
      productLinks: '.product-item .product-title a',
      nextPage: '.pagination .next',
    },
  },
  {
    name: 'MSI Official Store',
    url: 'https://www.msi.com/Laptops',
    description: 'MSI official laptop store',
    isActive: false, // Inicialmente desactivado para testing
    scrapingFrequency: 'weekly' as const,
    requiresFullBrowserAutomation: true,
    selectors: {
      productLinks: '.product-item .product-name a',
      nextPage: '.pagination .next',
    },
  },
  {
    name: 'Acer Official Store',
    url: 'https://www.acer.com/us-en/laptops',
    description: 'Acer official laptop store',
    isActive: false, // Inicialmente desactivado para testing
    scrapingFrequency: 'weekly' as const,
    requiresFullBrowserAutomation: true,
    selectors: {
      productLinks: '.product-tile .product-title a',
      nextPage: '.pagination .next',
    },
  },
  {
    name: 'Micro Center Laptops',
    url: 'https://www.microcenter.com/category/4294967288/laptops-notebooks',
    description: 'Micro Center laptop category',
    isActive: true,
    scrapingFrequency: 'daily' as const,
    requiresFullBrowserAutomation: true,
    selectors: {
      productLinks: '.product_wrapper .product_title a',
      nextPage: '.pagination .next',
    },
  },
]

async function seedUrlSources() {
  console.log('🌱 Seeding URL sources...')

  try {
    // Verificar conexión a la base de datos
    await prisma.$connect()
    console.log('✅ Connected to database')

    // Limpiar fuentes existentes (opcional)
    const existingCount = await prisma.scrapingSource.count()
    console.log(`📊 Found ${existingCount} existing URL sources`)

    if (existingCount > 0) {
      console.log('⚠️  URL sources already exist. Skipping seed to avoid duplicates.')
      console.log('💡 To force re-seed, delete existing sources first.')
      return
    }

    // Crear fuentes URL
    console.log('📝 Creating URL sources...')

    for (const source of urlSources) {
      try {
        const created = await prisma.scrapingSource.create({
          data: {
            name: source.name,
            url: source.url,
            isActive: source.isActive,
            requiresFullBrowserAutomation: source.requiresFullBrowserAutomation,
            selectors: source.selectors,
          },
        })

        console.log(`✅ Created: ${created.name} (ID: ${created.id})`)
      } catch (error) {
        console.error(`❌ Failed to create ${source.name}:`, error)
      }
    }

    // Verificar resultados
    const finalCount = await prisma.scrapingSource.count()
    const activeCount = await prisma.scrapingSource.count({ where: { isActive: true } })
    
    console.log('\n🎉 Seeding completed!')
    console.log(`📊 Total URL sources: ${finalCount}`)
    console.log(`🟢 Active sources: ${activeCount}`)
    console.log(`🔴 Inactive sources: ${finalCount - activeCount}`)

    // Mostrar fuentes activas
    const activeSources = await prisma.scrapingSource.findMany({
      where: { isActive: true },
      select: { id: true, name: true, url: true },
    })

    console.log('\n🔍 Active sources ready for scraping:')
    activeSources.forEach(source => {
      console.log(`  • ${source.name} - ID: ${source.id}`)
    })

  } catch (error) {
    console.error('❌ Error seeding URL sources:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Ejecutar el script
if (require.main === module) {
  seedUrlSources()
    .then(() => {
      console.log('\n✨ URL sources seeding completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Seeding failed:', error)
      process.exit(1)
    })
}

export { seedUrlSources }
