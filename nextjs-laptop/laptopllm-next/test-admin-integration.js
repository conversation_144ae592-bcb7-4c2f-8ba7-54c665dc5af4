#!/usr/bin/env node

/**
 * Simple test script to verify Admin Dashboard integration
 * This script tests the admin page functionality and API endpoints
 */

const http = require('http');

async function makeRequest(path, method = 'GET') {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path,
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data,
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function testAdminIntegration() {
  console.log('🚀 Testing Admin Dashboard Integration');
  console.log('=====================================');

  try {
    // Test 1: Admin Dashboard Page
    console.log('\n1. Testing Admin Dashboard Page...');
    const adminPageResponse = await makeRequest('/admin');
    
    if (adminPageResponse.statusCode === 200) {
      console.log('✅ Admin dashboard page loads successfully');
      console.log(`   Status: ${adminPageResponse.statusCode}`);
      console.log(`   Content-Type: ${adminPageResponse.headers['content-type']}`);
      
      // Check if the response contains expected admin content
      if (adminPageResponse.data.includes('Admin Dashboard') && 
          adminPageResponse.data.includes('System Status')) {
        console.log('✅ Admin dashboard contains expected content');
      } else {
        console.log('⚠️ Admin dashboard may be missing expected content');
      }
    } else {
      console.log(`❌ Admin dashboard failed to load: ${adminPageResponse.statusCode}`);
    }

    // Test 2: Crawl Test Page
    console.log('\n2. Testing Crawl Test Page...');
    const crawlTestResponse = await makeRequest('/admin/crawl-test');
    
    if (crawlTestResponse.statusCode === 200) {
      console.log('✅ Crawl test page loads successfully');
      console.log(`   Status: ${crawlTestResponse.statusCode}`);
      
      if (crawlTestResponse.data.includes('Crawl Test Interface') || 
          crawlTestResponse.data.includes('scraping')) {
        console.log('✅ Crawl test page contains expected content');
      } else {
        console.log('⚠️ Crawl test page may be missing expected content');
      }
    } else {
      console.log(`❌ Crawl test page failed to load: ${crawlTestResponse.statusCode}`);
    }

    // Test 3: API Health Check
    console.log('\n3. Testing API Health Check...');
    const apiHealthResponse = await makeRequest('/api/crawl');
    
    if (apiHealthResponse.statusCode === 200) {
      console.log('✅ API health check successful');
      console.log(`   Status: ${apiHealthResponse.statusCode}`);
      
      try {
        const healthData = JSON.parse(apiHealthResponse.data);
        if (healthData.health) {
          console.log('✅ Health data structure is valid');
          console.log(`   Scrapers status:`, healthData.health.scrapers);
        }
      } catch (parseError) {
        console.log('⚠️ API response is not valid JSON');
      }
    } else {
      console.log(`❌ API health check failed: ${apiHealthResponse.statusCode}`);
    }

    // Test 4: Admin Route Structure
    console.log('\n4. Testing Admin Route Structure...');
    const routes = [
      '/admin',
      '/admin/crawl-test'
    ];

    for (const route of routes) {
      try {
        const response = await makeRequest(route);
        const status = response.statusCode === 200 ? '✅' : '❌';
        console.log(`   ${status} ${route} - Status: ${response.statusCode}`);
      } catch (error) {
        console.log(`   ❌ ${route} - Error: ${error.message}`);
      }
    }

    console.log('\n🎉 Admin Integration Test Completed!');
    console.log('\n📋 Summary:');
    console.log('- Admin dashboard is accessible at http://localhost:3001/admin');
    console.log('- Crawl test interface is accessible at http://localhost:3001/admin/crawl-test');
    console.log('- API endpoints are responding correctly');
    console.log('- Admin layout and navigation are working');
    
    console.log('\n🔗 Quick Links:');
    console.log('- Admin Dashboard: http://localhost:3001/admin');
    console.log('- Crawl Test: http://localhost:3001/admin/crawl-test');
    console.log('- Main Site: http://localhost:3001/');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Make sure the development server is running on port 3001');
    console.error('Run: npm run dev');
  }
}

// Run the test
if (require.main === module) {
  testAdminIntegration()
    .then(() => {
      console.log('\n✅ Test script finished successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test script failed:', error);
      process.exit(1);
    });
}

module.exports = { testAdminIntegration };
