generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model brands {
  id              Int            @id @default(autoincrement())
  name            String         @unique @db.VarChar(255)
  website         String?        @db.VarChar(512)
  logo_url        String?        @db.VarChar(512)
  manufacturer_id Int?
  created_at      DateTime?      @default(now()) @db.Timestamp(6)
  manufacturers   manufacturers? @relation(fields: [manufacturer_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  laptops         laptops[]
}

model cpu_architectures {
  id            Int     @id @default(autoincrement())
  name          String  @unique @db.VarChar(50)
  description   String?
  technology_nm Int?
  release_year  Int?
  cpus          cpus[]
}

model cpus {
  id                Int                @id @default(autoincrement())
  manufacturer_id   Int
  model             String             @db.VarChar(255)
  generation        String?            @db.VarChar(100)
  cores             Int
  threads           Int
  base_clock_ghz    Decimal            @db.Decimal(4, 2)
  boost_clock_ghz   Decimal?           @db.Decimal(4, 2)
  tdp_watts         Int?
  cache_mb          Int?
  architecture_id   Int?
  supports_avx512   Boolean?           @default(false)
  cpu_architectures cpu_architectures? @relation(fields: [architecture_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  manufacturers     manufacturers      @relation(fields: [manufacturer_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  laptop_cpus       laptop_cpus[]

  @@unique([manufacturer_id, model, generation])
}

model displays {
  id               Int              @id @default(autoincrement())
  laptop_id        Int
  size_inches      Decimal          @db.Decimal(4, 2)
  resolution_id    Int
  refresh_rate     Int?
  panel_type_id    Int?
  is_touchscreen   Boolean?         @default(false)
  brightness_nits  Int?
  color_gamut      String?          @db.VarChar(50)
  hdr_support      Boolean?         @default(false)
  response_time_ms Int?
  laptops          laptops          @relation(fields: [laptop_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  panel_types      panel_types?     @relation(fields: [panel_type_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  resolution_types resolution_types @relation(fields: [resolution_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([laptop_id], map: "idx_displays_laptop_id")
}

model gpus {
  id                  Int           @id @default(autoincrement())
  manufacturer_id     Int
  model               String        @db.VarChar(255)
  vram_gb             Int
  memory_type_id      Int?
  base_clock_mhz      Int?
  boost_clock_mhz     Int?
  tdp_watts           Int?
  ray_tracing_support Boolean?      @default(false)
  tensor_cores        Int?
  cuda_cores          Int?
  compute_units       Int?
  supports_dlss       Boolean?      @default(false)
  supports_fsr        Boolean?      @default(false)
  manufacturers       manufacturers @relation(fields: [manufacturer_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  memory_types        memory_types? @relation(fields: [memory_type_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  laptop_gpus         laptop_gpus[]

  @@unique([manufacturer_id, model, vram_gb])
}

model laptop_cpus {
  laptop_id         Int
  cpu_id            Int
  performance_score Int?
  cpus              cpus    @relation(fields: [cpu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  laptops           laptops @relation(fields: [laptop_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([laptop_id, cpu_id])
  @@index([cpu_id], map: "idx_laptop_cpus_cpu_id")
}

model laptop_gpus {
  laptop_id         Int
  gpu_id            Int
  is_discrete       Boolean? @default(true)
  performance_score Int?
  gpus              gpus     @relation(fields: [gpu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  laptops           laptops  @relation(fields: [laptop_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([laptop_id, gpu_id])
  @@index([gpu_id], map: "idx_laptop_gpus_gpu_id")
}

model laptop_listings {
  id                      Int             @id @default(autoincrement())
  laptop_id               Int
  source_id               Int
  price                   Decimal         @db.Decimal(10, 2)
  url                     String          @db.VarChar(512)
  in_stock                Boolean?        @default(true)
  shipping_cost           Decimal?        @db.Decimal(8, 2)
  rating                  Decimal?        @db.Decimal(3, 2)
  reviews_count           Int?
  listing_date            DateTime?       @default(now()) @db.Timestamp(6)
  free_shipping           Boolean?        @default(false)
  estimated_delivery_days Int?
  has_warranty            Boolean?        @default(true)
  warranty_months         Int?
  processed               Boolean?        @default(false)
  laptops                 laptops         @relation(fields: [laptop_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  sources                 sources         @relation(fields: [source_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  price_history           price_history[]

  @@unique([laptop_id, source_id, url])
  @@index([laptop_id], map: "idx_laptop_listings_laptop_id")
  @@index([source_id], map: "idx_laptop_listings_source_id")
  @@index([price], map: "idx_laptop_listings_price")
  @@index([in_stock], map: "idx_laptop_listings_in_stock")
  @@index([listing_date], map: "idx_laptop_listings_date")
  @@index([processed], map: "idx_laptop_listings_processed")
  @@index([laptop_id, price], map: "idx_laptop_listings_laptop_price")
  @@index([laptop_id, in_stock, price], map: "idx_laptop_listings_laptop_stock_price")
  @@index([source_id, listing_date], map: "idx_laptop_listings_source_date")
  @@index([price, listing_date], map: "idx_laptop_listings_price_date")
}

model laptop_llm_compatibility {
  laptop_id                   Int
  llm_id                      Int
  estimated_tokens_per_second Int?
  max_context_length          Int?
  score                       Int?
  qualitative_assessment      String?
  can_run_offline             Boolean?   @default(false)
  recommended_batch_size      Int?
  estimated_memory_usage_gb   Decimal?   @db.Decimal(5, 2)
  laptops                     laptops    @relation(fields: [laptop_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  llm_models                  llm_models @relation(fields: [llm_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([laptop_id, llm_id])
  @@index([score], map: "idx_llm_compatibility_score")
  @@index([laptop_id, score], map: "idx_llm_compatibility_laptop_score")
  @@index([llm_id, score], map: "idx_llm_compatibility_llm_score")
  @@index([can_run_offline], map: "idx_llm_compatibility_offline")
}

model laptop_os_compatibility {
  laptop_id               Int
  os_id                   Int
  is_officially_supported Boolean?          @default(true)
  has_driver_issues       Boolean?          @default(false)
  notes                   String?
  laptops                 laptops           @relation(fields: [laptop_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  operating_systems       operating_systems @relation(fields: [os_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([laptop_id, os_id])
}

model laptop_ram {
  laptop_id            Int
  ram_configuration_id Int
  slots_used           Int                @default(1)
  max_slots            Int?
  expandable           Boolean?           @default(false)
  max_supported_gb     Int?
  laptops              laptops            @relation(fields: [laptop_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  ram_configurations   ram_configurations @relation(fields: [ram_configuration_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([laptop_id, ram_configuration_id])
  @@index([ram_configuration_id], map: "idx_laptop_ram_ram_id")
}

model laptop_scores {
  id                    Int       @id @default(autoincrement())
  laptop_id             Int       @unique
  overall_score         Int
  llm_performance_score Int
  battery_life_score    Int?
  build_quality_score   Int?
  display_score         Int?
  keyboard_score        Int?
  performance_score     Int?
  value_score           Int?
  thermal_score         Int?
  noise_score           Int?
  portability_score     Int?
  last_evaluated_at     DateTime? @default(now()) @db.Timestamp(6)
  laptops               laptops   @relation(fields: [laptop_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([overall_score], map: "idx_laptop_scores_overall")
  @@index([llm_performance_score], map: "idx_laptop_scores_llm")
  @@index([value_score], map: "idx_laptop_scores_value")
  @@index([performance_score], map: "idx_laptop_scores_performance")
  @@index([last_evaluated_at], map: "idx_laptop_scores_evaluated_at")
}

model laptop_storage {
  laptop_id       Int
  storage_id      Int
  is_primary      Boolean?        @default(false)
  slot_type       String?         @db.VarChar(50)
  laptops         laptops         @relation(fields: [laptop_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  storage_devices storage_devices @relation(fields: [storage_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([laptop_id, storage_id])
  @@index([storage_id], map: "idx_laptop_storage_storage_id")
}

model laptop_tags {
  laptop_id       Int
  tag_id          Int
  relevance_score Int?    @default(100)
  laptops         laptops @relation(fields: [laptop_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  tags            tags    @relation(fields: [tag_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([laptop_id, tag_id])
}

model laptops {
  id                       Int                        @id @default(autoincrement())
  model_name               String                     @db.VarChar(255)
  brand_id                 Int
  release_date             DateTime?                  @db.Date
  description              String?
  image_url                String?                    @db.VarChar(512)
  is_available             Boolean?                   @default(true)
  msrp                     Decimal?                   @db.Decimal(10, 2)
  created_at               DateTime?                  @default(now()) @db.Timestamp(6)
  updated_at               DateTime?                  @default(now()) @db.Timestamp(6)
  displays                 displays[]
  laptop_cpus              laptop_cpus[]
  laptop_gpus              laptop_gpus[]
  laptop_listings          laptop_listings[]
  laptop_llm_compatibility laptop_llm_compatibility[]
  laptop_os_compatibility  laptop_os_compatibility[]
  laptop_ram               laptop_ram[]
  laptop_scores            laptop_scores?
  laptop_storage           laptop_storage[]
  laptop_tags              laptop_tags[]
  brands                   brands                     @relation(fields: [brand_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  physical_specs           physical_specs?

  @@unique([brand_id, model_name])
  @@index([brand_id], map: "idx_laptops_brand_id")
  @@index([model_name, brand_id], map: "idx_laptops_model_brand")
  @@index([is_available], map: "idx_laptops_available")
  @@index([msrp], map: "idx_laptops_msrp")
  @@index([release_date], map: "idx_laptops_release_date")
  @@index([created_at], map: "idx_laptops_created_at")
  @@index([updated_at], map: "idx_laptops_updated_at")
  @@index([brand_id, is_available], map: "idx_laptops_brand_available")
  @@index([brand_id, msrp], map: "idx_laptops_brand_price")
  @@index([is_available, msrp], map: "idx_laptops_available_price")
}

model llm_models {
  id                       Int                        @id @default(autoincrement())
  name                     String                     @unique @db.VarChar(255)
  parameters_billions      Decimal?                   @db.Decimal(10, 2)
  quantization_bits        Int?
  min_ram_gb               Int?
  min_vram_gb              Int?
  requires_gpu             Boolean?                   @default(true)
  description              String?
  model_card_url           String?                    @db.VarChar(512)
  created_at               DateTime?                  @default(now()) @db.Timestamp(6)
  updated_at               DateTime?                  @default(now()) @db.Timestamp(6)
  laptop_llm_compatibility laptop_llm_compatibility[]

  @@index([parameters_billions], map: "idx_llm_models_parameters")
  @@index([min_ram_gb], map: "idx_llm_models_min_ram")
  @@index([min_vram_gb], map: "idx_llm_models_min_vram")
  @@index([requires_gpu], map: "idx_llm_models_requires_gpu")
  @@index([quantization_bits], map: "idx_llm_models_quantization")
}

model manufacturers {
  id                 Int                  @id @default(autoincrement())
  name               String               @unique @db.VarChar(100)
  website            String?              @db.VarChar(512)
  country            String?              @db.VarChar(100)
  founded_year       Int?
  brands             brands[]
  cpus               cpus[]
  gpus               gpus[]
  ram_configurations ram_configurations[]
  storage_devices    storage_devices[]
}

model memory_types {
  id                 Int                  @id @default(autoincrement())
  name               String               @unique @db.VarChar(50)
  description        String?
  max_bandwidth_gbps Decimal?             @db.Decimal(8, 2)
  gpus               gpus[]
  ram_configurations ram_configurations[]
}

model operating_systems {
  id                      Int                       @id @default(autoincrement())
  name                    String                    @unique @db.VarChar(100)
  version                 String?                   @db.VarChar(50)
  is_linux                Boolean?                  @default(false)
  is_windows              Boolean?                  @default(false)
  is_macos                Boolean?                  @default(false)
  min_ram_gb              Int?
  min_storage_gb          Int?
  laptop_os_compatibility laptop_os_compatibility[]
}

model panel_types {
  id            Int        @id @default(autoincrement())
  name          String     @unique @db.VarChar(50)
  description   String?
  advantages    String?
  disadvantages String?
  displays      displays[]
}

model physical_specs {
  id                     Int      @id @default(autoincrement())
  laptop_id              Int      @unique
  weight_kg              Decimal  @db.Decimal(5, 2)
  height_mm              Decimal? @db.Decimal(6, 2)
  width_mm               Decimal? @db.Decimal(6, 2)
  depth_mm               Decimal? @db.Decimal(6, 2)
  material               String?  @db.VarChar(100)
  color                  String?  @db.VarChar(100)
  has_fingerprint_reader Boolean? @default(false)
  has_webcam             Boolean? @default(true)
  webcam_resolution      String?  @db.VarChar(50)
  has_backlit_keyboard   Boolean? @default(false)
  laptops                laptops  @relation(fields: [laptop_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([laptop_id], map: "idx_physical_specs_laptop_id")
}

model port_types {
  id                       Int      @id @default(autoincrement())
  name                     String   @unique @db.VarChar(50)
  description              String?
  max_data_rate_gbps       Decimal? @db.Decimal(8, 2)
  supports_power_delivery  Boolean? @default(false)
  max_power_delivery_watts Int?
}

model price_history {
  id              Int             @id @default(autoincrement())
  listing_id      Int
  price           Decimal         @db.Decimal(10, 2)
  recorded_at     DateTime?       @default(now()) @db.Timestamp(6)
  laptop_listings laptop_listings @relation(fields: [listing_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([listing_id], map: "idx_price_history_listing_id")
  @@index([recorded_at], map: "idx_price_history_recorded_at")
  @@index([price], map: "idx_price_history_price")
  @@index([listing_id, recorded_at], map: "idx_price_history_listing_date")
  @@index([listing_id, price], map: "idx_price_history_listing_price")
}

model ram_configurations {
  id              Int            @id @default(autoincrement())
  memory_type_id  Int
  size_gb         Int
  speed_mhz       Int
  manufacturer_id Int?
  is_dual_channel Boolean?       @default(false)
  cas_latency     Int?
  laptop_ram      laptop_ram[]
  manufacturers   manufacturers? @relation(fields: [manufacturer_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  memory_types    memory_types   @relation(fields: [memory_type_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model resolution_types {
  id           Int        @id @default(autoincrement())
  name         String     @unique @db.VarChar(50)
  width        Int
  height       Int
  aspect_ratio String?    @db.VarChar(10)
  displays     displays[]
}

model scraping_history {
  id            Int       @id @default(autoincrement())
  source_id     Int
  start_time    DateTime  @default(now()) @db.Timestamp(6)
  end_time      DateTime? @db.Timestamp(6)
  status        String    @default("running") @db.VarChar(50)
  items_found   Int?      @default(0)
  error_message String?
  sources       sources   @relation(fields: [source_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model ScrapingSource {
  id                            String    @id @default(cuid())
  name                          String
  isActive                      Boolean   @default(true) @map("enabled")
  lastScrapedAt                 DateTime? @map("last_scraped_at")
  createdAt                     DateTime  @default(now()) @map("created_at")
  updatedAt                     DateTime  @updatedAt @map("updated_at")
  selectors                     Json?
  requiresFullBrowserAutomation Boolean   @default(false) @map("requires_full_browser_automation")
  url                           String    @unique

  @@map("scraping_sources")
}

model ScrapingJob {
  id           String    @id @default(cuid())
  type         String    @db.VarChar(50)
  status       String    @default("waiting") @db.VarChar(50)
  priority     String    @default("normal") @db.VarChar(20)
  data         Json
  result       Json?
  error        String?
  metrics      Json?
  attempts     Int       @default(0)
  maxAttempts  Int       @default(3) @map("max_attempts")
  createdAt    DateTime  @default(now()) @map("created_at")
  startedAt    DateTime? @map("started_at")
  completedAt  DateTime? @map("completed_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  parentJobId  String?   @map("parent_job_id")
  batchId      String?   @map("batch_id")
  tags         String[]  @default([])

  // Self-referential relation for parent-child jobs
  parentJob ScrapingJob?  @relation("JobHierarchy", fields: [parentJobId], references: [id])
  childJobs ScrapingJob[] @relation("JobHierarchy")

  @@index([status])
  @@index([type])
  @@index([priority])
  @@index([batchId])
  @@index([createdAt])
  @@index([status, type])
  @@index([status, priority])
  @@index([type, priority])
  @@index([batchId, status])
  @@index([createdAt, status])
  @@index([startedAt])
  @@index([completedAt])
  @@index([attempts])
  @@map("scraping_jobs")
}

model JobLog {
  id        String   @id @default(cuid())
  jobId     String   @map("job_id")
  level     String   @db.VarChar(20) // info, warn, error, debug
  message   String
  data      Json?
  timestamp DateTime @default(now())

  @@index([jobId])
  @@index([level])
  @@index([timestamp])
  @@map("job_logs")
}

model QueueMetrics {
  id            String   @id @default(cuid())
  queueName     String   @map("queue_name") @db.VarChar(100)
  timestamp     DateTime @default(now())
  waiting       Int      @default(0)
  active        Int      @default(0)
  completed     Int      @default(0)
  failed        Int      @default(0)
  delayed       Int      @default(0)
  paused        Int      @default(0)
  throughputPerHour Int @default(0) @map("throughput_per_hour")
  avgProcessingTime Int @default(0) @map("avg_processing_time")
  successRate   Float    @default(100.0) @map("success_rate")

  @@index([queueName])
  @@index([timestamp])
  @@map("queue_metrics")
}

model sources {
  id                   Int                @id @default(autoincrement())
  name                 String             @unique @db.VarChar(255)
  url                  String?            @db.VarChar(512)
  api_endpoint         String?            @db.VarChar(512)
  is_active            Boolean?           @default(true)
  last_updated         DateTime?          @db.Timestamp(6)
  update_frequency     String?            @db.VarChar(50)
  selectors            Json?              @default("{}")
  last_scrape_attempt  DateTime?          @db.Timestamp(6)
  scrape_success_count Int?               @default(0)
  scrape_failure_count Int?               @default(0)
  laptop_listings      laptop_listings[]
  scraping_history     scraping_history[]
}

model storage_devices {
  id                 Int                @id @default(autoincrement())
  interface_id       Int
  capacity_gb        Int
  manufacturer_id    Int?
  read_speed_mbps    Int?
  write_speed_mbps   Int?
  is_nvme            Boolean?           @default(false)
  has_dram_cache     Boolean?           @default(false)
  laptop_storage     laptop_storage[]
  storage_interfaces storage_interfaces @relation(fields: [interface_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  manufacturers      manufacturers?     @relation(fields: [manufacturer_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model storage_interfaces {
  id                  Int               @id @default(autoincrement())
  name                String            @unique @db.VarChar(50)
  description         String?
  max_throughput_gbps Decimal?          @db.Decimal(8, 2)
  storage_devices     storage_devices[]
}

model tag_categories {
  id          Int     @id @default(autoincrement())
  name        String  @unique @db.VarChar(100)
  description String?
  tags        tags[]
}

model tags {
  id             Int             @id @default(autoincrement())
  name           String          @unique @db.VarChar(100)
  category_id    Int?
  description    String?
  laptop_tags    laptop_tags[]
  tag_categories tag_categories? @relation(fields: [category_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}
