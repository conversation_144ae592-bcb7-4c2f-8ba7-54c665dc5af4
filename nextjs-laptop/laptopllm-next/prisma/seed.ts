import { PrismaClient } from '../generated/prisma'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create manufacturers
  const intel = await prisma.manufacturers.upsert({
    where: { name: 'Intel' },
    update: {},
    create: {
      name: 'Intel',
      website: 'https://www.intel.com',
      country: 'United States',
      founded_year: 1968,
    },
  })

  const amd = await prisma.manufacturers.upsert({
    where: { name: 'AMD' },
    update: {},
    create: {
      name: 'AMD',
      website: 'https://www.amd.com',
      country: 'United States',
      founded_year: 1969,
    },
  })

  const nvidia = await prisma.manufacturers.upsert({
    where: { name: 'NVIDIA' },
    update: {},
    create: {
      name: 'NVIDIA',
      website: 'https://www.nvidia.com',
      country: 'United States',
      founded_year: 1993,
    },
  })

  // Create brands
  const dell = await prisma.brands.upsert({
    where: { name: '<PERSON>' },
    update: {},
    create: {
      name: '<PERSON>',
      website: 'https://www.dell.com',
      manufacturer_id: intel.id,
    },
  })

  const hp = await prisma.brands.upsert({
    where: { name: 'HP' },
    update: {},
    create: {
      name: 'HP',
      website: 'https://www.hp.com',
      manufacturer_id: intel.id,
    },
  })

  const lenovo = await prisma.brands.upsert({
    where: { name: 'Lenovo' },
    update: {},
    create: {
      name: 'Lenovo',
      website: 'https://www.lenovo.com',
      manufacturer_id: intel.id,
    },
  })

  // Create memory types
  const ddr4 = await prisma.memory_types.upsert({
    where: { name: 'DDR4' },
    update: {},
    create: {
      name: 'DDR4',
      description: 'Fourth generation of DDR SDRAM',
      max_bandwidth_gbps: 25.6,
    },
  })

  const ddr5 = await prisma.memory_types.upsert({
    where: { name: 'DDR5' },
    update: {},
    create: {
      name: 'DDR5',
      description: 'Fifth generation of DDR SDRAM',
      max_bandwidth_gbps: 51.2,
    },
  })

  // Create CPU architectures
  const x86_64 = await prisma.cpu_architectures.upsert({
    where: { name: 'x86-64' },
    update: {},
    create: {
      name: 'x86-64',
      description: '64-bit x86 instruction set architecture',
      technology_nm: 7,
      release_year: 2003,
    },
  })

  // Create sample CPUs
  const i7_13700H = await prisma.cpus.upsert({
    where: { 
      manufacturer_id_model_generation: {
        manufacturer_id: intel.id,
        model: 'Core i7-13700H',
        generation: '13th Gen'
      }
    },
    update: {},
    create: {
      manufacturer_id: intel.id,
      model: 'Core i7-13700H',
      generation: '13th Gen',
      cores: 14,
      threads: 20,
      base_clock_ghz: 2.4,
      boost_clock_ghz: 5.0,
      tdp_watts: 45,
      cache_mb: 24,
      architecture_id: x86_64.id,
      supports_avx512: true,
    },
  })

  // Create sample GPUs
  const rtx4060 = await prisma.gpus.upsert({
    where: {
      manufacturer_id_model_vram_gb: {
        manufacturer_id: nvidia.id,
        model: 'GeForce RTX 4060',
        vram_gb: 8
      }
    },
    update: {},
    create: {
      manufacturer_id: nvidia.id,
      model: 'GeForce RTX 4060',
      vram_gb: 8,
      memory_type_id: ddr5.id,
      base_clock_mhz: 1830,
      boost_clock_mhz: 2460,
      tdp_watts: 115,
      ray_tracing_support: true,
      tensor_cores: 128,
      cuda_cores: 3072,
      supports_dlss: true,
    },
  })

  // Create resolution types
  const fhd = await prisma.resolution_types.upsert({
    where: { name: 'Full HD' },
    update: {},
    create: {
      name: 'Full HD',
      width: 1920,
      height: 1080,
      aspect_ratio: '16:9',
    },
  })

  // Create LLM models
  const llama2_7b = await prisma.llm_models.upsert({
    where: { name: 'Llama 2 7B' },
    update: {},
    create: {
      name: 'Llama 2 7B',
      parameters_billions: 7.0,
      quantization_bits: 16,
      min_ram_gb: 8,
      min_vram_gb: 4,
      requires_gpu: false,
      description: 'Meta\'s Llama 2 model with 7 billion parameters',
      requirements: {
        min_ram_gb: 8,
        recommended_ram_gb: 16,
        min_vram_gb: 4,
        recommended_vram_gb: 8,
        supports_cpu_inference: true,
      },
    },
  })

  // Create scraping sources
  const revolico = await prisma.ScrapingSource.upsert({
    where: { url: 'https://www.revolico.com' },
    update: {},
    create: {
      name: 'Revolico',
      url: 'https://www.revolico.com',
      isActive: true,
      selectors: {
        laptop_links: '.classified-ad a',
        title: '.classified-title',
        price: '.price',
        description: '.description',
        images: '.gallery img',
      },
      requiresFullBrowserAutomation: true,
      rateLimitMs: 3000,
      maxRetries: 3,
      timeoutMs: 30000,
    },
  })

  const laptopVentas = await prisma.ScrapingSource.upsert({
    where: { url: 'https://laptop-ventas.ola.click' },
    update: {},
    create: {
      name: 'Laptop Ventas',
      url: 'https://laptop-ventas.ola.click',
      isActive: true,
      selectors: {
        laptop_links: '.product-item a',
        title: '.product-title',
        price: '.product-price',
        specs: '.product-specs',
        images: '.product-images img',
      },
      requiresFullBrowserAutomation: false,
      rateLimitMs: 2000,
      maxRetries: 3,
      timeoutMs: 25000,
    },
  })

  console.log('✅ Database seeding completed!')
  console.log(`Created:`)
  console.log(`- ${3} manufacturers`)
  console.log(`- ${3} brands`)
  console.log(`- ${2} memory types`)
  console.log(`- ${1} CPU architecture`)
  console.log(`- ${1} CPU`)
  console.log(`- ${1} GPU`)
  console.log(`- ${1} resolution type`)
  console.log(`- ${1} LLM model`)
  console.log(`- ${2} scraping sources`)
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
