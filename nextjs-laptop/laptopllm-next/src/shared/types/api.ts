// API-specific types for LaptopLLM Finder

import type {
  Laptop,
  LLMModel,
  SearchFilters,
  PaginationParams,
  SearchResults,
  ApiResponse,
  LaptopWithCompatibility,
  LaptopRecommendation,
  RecommendationRequest,
  ScrapingJobResult,
  UserPreferences,
} from './index'

// ============================================================================
// REQUEST TYPES
// ============================================================================

export interface GetLaptopsRequest extends PaginationParams {
  filters?: SearchFilters
  includeCompatibility?: boolean
  includePriceHistory?: boolean
}

export interface GetLaptopByIdRequest {
  id: number
  includeCompatibility?: boolean
  includePriceHistory?: boolean
  includeRecommendations?: boolean
}

export interface SearchLaptopsRequest extends PaginationParams {
  query: string
  filters?: SearchFilters
  includeCompatibility?: boolean
  fuzzySearch?: boolean
}

export interface GetCompatibilityRequest {
  laptopId: number
  llmModelId?: number
  includeAllModels?: boolean
}

export interface CalculateCompatibilityRequest {
  laptopSpecs: {
    ramGb: number
    cpuCores: number
    cpuArchitecture: string
    gpuVramGb?: number
    storageGb: number
    storageType: string
  }
  llmModelId?: number
}

export interface CreateScrapingJobRequest {
  sourceIds: string[]
  priority?: 'low' | 'normal' | 'high'
  scheduledFor?: Date
  config?: {
    maxPages?: number
    rateLimitMs?: number
    timeoutMs?: number
  }
}

export interface GetRecommendationsRequest {
  preferences: UserPreferences
  filters?: SearchFilters
  maxResults?: number
  includeAlternatives?: boolean
}

export interface UpdateUserPreferencesRequest {
  userId: string
  preferences: Partial<UserPreferences>
}

// ============================================================================
// RESPONSE TYPES
// ============================================================================

export interface GetLaptopsResponse extends ApiResponse<SearchResults<LaptopWithCompatibility>> {}

export interface GetLaptopByIdResponse extends ApiResponse<LaptopWithCompatibility & {
  priceHistory?: Array<{
    price: number
    currency: string
    source: string
    timestamp: Date
  }>
  recommendations?: LaptopWithCompatibility[]
}> {}

export interface SearchLaptopsResponse extends ApiResponse<SearchResults<LaptopWithCompatibility & {
  relevanceScore: number
}>> {}

export interface GetCompatibilityResponse extends ApiResponse<{
  laptopId: number
  compatibility: Array<{
    llmModel: LLMModel
    score: number
    canRun: boolean
    estimatedPerformance: string
    tokensPerSecond?: number
    maxContextLength?: number
    memoryUsageGb?: number
  }>
}> {}

export interface CalculateCompatibilityResponse extends ApiResponse<{
  overallScore: number
  breakdown: {
    ramScore: number
    cpuScore: number
    gpuScore: number
    storageScore: number
  }
  compatibleModels: Array<{
    model: LLMModel
    score: number
    canRun: boolean
    estimatedPerformance: string
  }>
  recommendations: string[]
}> {}

export interface CreateScrapingJobResponse extends ApiResponse<{
  jobId: string
  status: string
  estimatedCompletionTime?: Date
}> {}

export interface GetScrapingJobStatusResponse extends ApiResponse<ScrapingJobResult> {}

export interface GetRecommendationsResponse extends ApiResponse<{
  recommendations: LaptopRecommendation[]
  totalFound: number
  searchCriteria: RecommendationRequest
}> {}

export interface GetBrandsResponse extends ApiResponse<Array<{
  id: number
  name: string
  logoUrl?: string
  laptopCount: number
  averagePrice?: number
}>> {}

export interface GetLLMModelsResponse extends ApiResponse<Array<LLMModel & {
  compatibleLaptopCount: number
  averageCompatibilityScore?: number
}>> {}

export interface GetStatsResponse extends ApiResponse<{
  totalLaptops: number
  totalBrands: number
  totalLLMModels: number
  averagePrice: number
  priceRange: {
    min: number
    max: number
  }
  lastUpdated: Date
  dataQuality: {
    completeness: number
    accuracy: number
    freshness: number
  }
}> {}

// ============================================================================
// WEBHOOK TYPES
// ============================================================================

export interface ScrapingJobWebhook {
  jobId: string
  status: 'completed' | 'failed' | 'cancelled'
  result?: ScrapingJobResult
  timestamp: Date
}

export interface PriceAlertWebhook {
  laptopId: number
  oldPrice: number
  newPrice: number
  currency: string
  changePercentage: number
  source: string
  timestamp: Date
}

// ============================================================================
// ERROR TYPES
// ============================================================================

export interface ApiError {
  code: string
  message: string
  details?: Record<string, any>
  timestamp: Date
  requestId?: string
}

export interface ValidationError extends ApiError {
  code: 'VALIDATION_ERROR'
  fields: Array<{
    field: string
    message: string
    value?: Record<string, unknown> | string | number | null
  }>
}

export interface NotFoundError extends ApiError {
  code: 'NOT_FOUND'
  resource: string
  identifier: string | number
}

export interface RateLimitError extends ApiError {
  code: 'RATE_LIMIT_EXCEEDED'
  retryAfter: number
  limit: number
  remaining: number
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type SortableFields = 'price' | 'compatibility' | 'performance' | 'newest' | 'popularity' | 'rating'
export type SortOrder = 'asc' | 'desc'

export interface SortConfig {
  field: SortableFields
  order: SortOrder
}

export interface FilterConfig {
  field: string
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'contains' | 'startsWith' | 'endsWith'
  value: Record<string, unknown> | string | number | null
}

export type ApiMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'

export interface ApiEndpoint {
  method: ApiMethod
  path: string
  description: string
  requestType?: string
  responseType?: string
  requiresAuth?: boolean
  rateLimit?: {
    requests: number
    windowMs: number
  }
}
