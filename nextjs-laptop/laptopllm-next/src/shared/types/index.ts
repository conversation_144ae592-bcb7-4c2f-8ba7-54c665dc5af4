// Re-export Prisma types
export type {
  laptops as Laptop,
  brands as Brand,
  cpus as CPU,
  gpus as GPU,
  llm_models as LLMModel,
  laptop_llm_compatibility as LaptopLLMCompatibility,
  ScrapingSource,
  ScrapingJob,
  ScrapingLog,
  ScrapingStatus,
  LogLevel,
} from '../../../generated/prisma'

// Import types for internal use
import type {
  laptops as Laptop,
  llm_models as LLMModel,
} from '../../../generated/prisma'

// Define ScrapingStatus enum
export type ScrapingStatus = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'

// Domain-specific types
export interface LaptopSpecifications {
  cpu: {
    model: string
    cores: number
    threads: number
    baseClockGhz: number
    boostClockGhz?: number
    architecture: string
  }
  ram: {
    sizeGb: number
    type: string
    speed: number
    expandable: boolean
    maxSupportedGb?: number
  }
  gpu?: {
    model: string
    vramGb: number
    memoryType: string
    isDiscrete: boolean
  }
  storage: {
    type: string
    capacityGb: number
    interface: string
    readSpeedMbps?: number
    writeSpeedMbps?: number
  }[]
  display: {
    sizeInches: number
    resolution: {
      width: number
      height: number
      name: string
    }
    refreshRate?: number
    panelType?: string
    isTouchscreen: boolean
  }
  physical: {
    weightKg: number
    dimensions: {
      heightMm: number
      widthMm: number
      depthMm: number
    }
    material?: string
    color?: string
  }
}

export interface LLMCompatibilityScore {
  laptopId: number
  llmId: number
  overallScore: number
  canRunOffline: boolean
  estimatedTokensPerSecond?: number
  maxContextLength?: number
  recommendedBatchSize?: number
  estimatedMemoryUsageGb?: number
  qualitativeAssessment?: string
  breakdown: {
    cpuScore: number
    ramScore: number
    gpuScore: number
    storageScore: number
  }
}

export interface ScrapedLaptopData {
  title: string
  brand?: string
  model?: string
  price: number
  currency: string
  url: string
  imageUrls: string[]
  description?: string
  specifications?: Partial<LaptopSpecifications>
  sourceId: string
  scrapedAt: Date
}

export interface SearchFilters {
  brands?: string[]
  priceRange?: {
    min: number
    max: number
  }
  ramRange?: {
    min: number
    max: number
  }
  storageRange?: {
    min: number
    max: number
  }
  cpuBrands?: string[]
  gpuBrands?: string[]
  displaySizes?: number[]
  llmCompatibility?: {
    modelId: number
    minScore: number
  }
  sortBy?: 'price' | 'compatibility' | 'performance' | 'newest'
  sortOrder?: 'asc' | 'desc'
}

export interface PaginationParams {
  page: number
  limit: number
}

export interface SearchResults<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: {
    message: string
    code?: string
    details?: Record<string, unknown> | string | number | null
  }
}

export interface ScrapingConfig {
  sourceId: string
  rateLimitMs: number
  maxRetries: number
  timeoutMs: number
  selectors: Record<string, string>
  headers?: Record<string, string>
  userAgent?: string
  requiresFullBrowserAutomation: boolean
}

export interface DataQualityMetrics {
  completeness: number
  accuracy: number
  consistency: number
  duplicateRate: number
  errorRate: number
  lastUpdated: Date
}

// ============================================================================
// ADDITIONAL DOMAIN TYPES
// ============================================================================

export interface LaptopData extends Laptop {
  specifications?: LaptopSpecifications
  compatibility?: {
    scores: LLMCompatibilityScore[]
    averageScore: number
    bestModels: LLMModel[]
  }
  pricing?: {
    currentPrice: number
    currency: string
    priceHistory: PricePoint[]
    bestDeal?: LaptopListing
  }
  availability?: AvailabilityInfo
  reviews?: {
    averageRating: number
    totalReviews: number
    categories: {
      performance: number
      design: number
      value: number
      battery: number
      display: number
    }
  }
  metadata?: {
    popularity: number
    trendingScore: number
    lastUpdated: Date
  }
}

export interface LaptopWithCompatibility {
  laptop: Laptop
  compatibility?: LLMCompatibilityScore[]
  averageCompatibilityScore?: number
  bestCompatibleModels?: LLMModel[]
}

export interface LaptopSearchResult extends LaptopWithCompatibility {
  relevanceScore?: number
  priceHistory?: PricePoint[]
  availability?: AvailabilityInfo
}

export interface PricePoint {
  price: number
  currency: string
  source: string
  timestamp: Date
  url?: string
}

export interface AvailabilityInfo {
  inStock: boolean
  stockLevel?: 'High' | 'Medium' | 'Low' | 'Out of Stock'
  estimatedDelivery?: Date
  source: string
  lastChecked: Date
}

export interface LLMModelWithRequirements extends LLMModel {
  requirements: {
    minRamGb: number
    recommendedRamGb: number
    minStorageGb: number
    minCpuCores: number
    requiresGpu: boolean
    minGpuVramGb?: number
  }
  performance: {
    tokensPerSecondEstimate: number
    maxContextLength: number
    quantizationOptions: string[]
  }
}

export interface ScrapingJobResult {
  jobId: string
  status: ScrapingStatus
  startedAt: Date
  completedAt?: Date
  itemsProcessed: number
  itemsSuccessful: number
  itemsFailed: number
  errors: string[]
  data?: ScrapedLaptopData[]
}

export interface UserPreferences {
  budget?: {
    min: number
    max: number
    currency: string
  }
  useCases: string[]
  preferredBrands: string[]
  requiredFeatures: string[]
  dealBreakers: string[]
  llmModelsOfInterest: string[]
}

export interface RecommendationRequest {
  userPreferences: UserPreferences
  filters?: SearchFilters
  maxResults?: number
  includeAlternatives?: boolean
}

export interface LaptopRecommendation {
  laptop: LaptopWithCompatibility
  score: number
  reasoning: string[]
  pros: string[]
  cons: string[]
  alternativeOptions?: LaptopWithCompatibility[]
}

// ============================================================================
// LAPTOP DEALS AND OFFERS SYSTEM
// ============================================================================

export interface LaptopListing {
  id: number
  laptopId: number
  sourceId: number
  price: number
  url: string
  inStock: boolean
  shippingCost?: number
  rating?: number
  reviewsCount?: number
  listingDate: Date
  freeShipping: boolean
  estimatedDeliveryDays?: number
  hasWarranty: boolean
  warrantyMonths?: number
  processed: boolean
  source: {
    id: number
    name: string
    url: string
    isActive: boolean
  }
  priceHistory?: PriceHistoryEntry[]
}

export interface PriceHistoryEntry {
  id: number
  listingId: number
  price: number
  recordedAt: Date
}

export interface LaptopDeal {
  id: string
  laptop: Laptop
  listing: LaptopListing
  dealType: 'price-drop' | 'best-price' | 'back-in-stock' | 'new-listing' | 'limited-time'
  originalPrice?: number
  currentPrice: number
  discountAmount?: number
  discountPercentage?: number
  savings?: number
  dealScore: number // 0-100 score based on price history, availability, etc.
  isHotDeal: boolean
  expiresAt?: Date
  lastUpdated: Date
  priceHistory: PriceHistoryEntry[]
  availability: {
    inStock: boolean
    stockLevel?: 'low' | 'medium' | 'high'
    lastChecked: Date
  }
  shipping: {
    cost: number
    isFree: boolean
    estimatedDays?: number
  }
  dealMetrics: {
    priceRank: number // 1 = best price among all sources
    historicalLow: boolean
    priceTrend: 'rising' | 'falling' | 'stable'
    demandLevel: 'low' | 'medium' | 'high'
  }
}

export interface DealFilters {
  dealTypes?: Array<'price-drop' | 'best-price' | 'back-in-stock' | 'new-listing' | 'limited-time'>
  minDiscount?: number
  maxPrice?: number
  onlyHotDeals?: boolean
  inStockOnly?: boolean
  freeShippingOnly?: boolean
  sources?: string[]
  brands?: string[]
  minDealScore?: number
}

export interface DealsSearchResult {
  deals: LaptopDeal[]
  total: number
  page: number
  limit: number
  hasMore: boolean
  summary: {
    totalDeals: number
    hotDeals: number
    averageDiscount: number
    topSavings: number
    newDealsToday: number
  }
}

export interface DealAlert {
  id: string
  userId?: string
  laptopId?: number
  filters: DealFilters
  targetPrice?: number
  targetDiscount?: number
  isActive: boolean
  createdAt: Date
  lastTriggered?: Date
  triggerCount: number
}

export interface DealNotification {
  id: string
  dealId: string
  alertId: string
  deal: LaptopDeal
  message: string
  type: 'price-drop' | 'target-reached' | 'back-in-stock' | 'hot-deal'
  sentAt: Date
  isRead: boolean
}
