// Zod validation schemas for LaptopLLM Finder

import { z } from 'zod'

// ============================================================================
// BASIC SCHEMAS
// ============================================================================

export const PaginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
})

export const SortSchema = z.object({
  sortBy: z.enum(['price', 'compatibility', 'performance', 'newest', 'popularity', 'rating']).optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
})

// ============================================================================
// LAPTOP SPECIFICATIONS SCHEMAS
// ============================================================================

export const CPUSpecSchema = z.object({
  model: z.string().min(1),
  cores: z.number().int().min(1).max(128),
  threads: z.number().int().min(1).max(256),
  baseClockGhz: z.number().min(0.1).max(10),
  boostClockGhz: z.number().min(0.1).max(10).optional(),
  architecture: z.string().min(1),
})

export const RAMSpecSchema = z.object({
  sizeGb: z.number().int().min(1).max(1024),
  type: z.string().min(1),
  speed: z.number().int().min(800).max(10000),
  expandable: z.boolean(),
  maxSupportedGb: z.number().int().min(1).max(1024).optional(),
})

export const GPUSpecSchema = z.object({
  model: z.string().min(1),
  vramGb: z.number().min(0).max(128),
  memoryType: z.string().min(1),
  isDiscrete: z.boolean(),
}).optional()

export const StorageSpecSchema = z.object({
  type: z.string().min(1),
  capacityGb: z.number().int().min(1).max(100000),
  interface: z.string().min(1),
  readSpeedMbps: z.number().min(1).max(50000).optional(),
  writeSpeedMbps: z.number().min(1).max(50000).optional(),
})

export const DisplaySpecSchema = z.object({
  sizeInches: z.number().min(10).max(50),
  resolution: z.object({
    width: z.number().int().min(800).max(10000),
    height: z.number().int().min(600).max(10000),
    name: z.string().min(1),
  }),
  refreshRate: z.number().int().min(30).max(500).optional(),
  panelType: z.string().optional(),
  isTouchscreen: z.boolean(),
})

export const PhysicalSpecSchema = z.object({
  weightKg: z.number().min(0.1).max(10),
  dimensions: z.object({
    heightMm: z.number().min(1).max(1000),
    widthMm: z.number().min(1).max(1000),
    depthMm: z.number().min(1).max(1000),
  }),
  material: z.string().optional(),
  color: z.string().optional(),
})

export const LaptopSpecificationsSchema = z.object({
  cpu: CPUSpecSchema,
  ram: RAMSpecSchema,
  gpu: GPUSpecSchema,
  storage: z.array(StorageSpecSchema).min(1),
  display: DisplaySpecSchema,
  physical: PhysicalSpecSchema,
})

// ============================================================================
// SEARCH AND FILTER SCHEMAS
// ============================================================================

export const SearchFiltersSchema = z.object({
  brands: z.array(z.string()).optional(),
  priceRange: z.object({
    min: z.number().min(0),
    max: z.number().min(0),
  }).refine(data => data.min <= data.max, {
    message: "Min price must be less than or equal to max price"
  }).optional(),
  ramRange: z.object({
    min: z.number().int().min(1),
    max: z.number().int().min(1),
  }).refine(data => data.min <= data.max, {
    message: "Min RAM must be less than or equal to max RAM"
  }).optional(),
  storageRange: z.object({
    min: z.number().int().min(1),
    max: z.number().int().min(1),
  }).refine(data => data.min <= data.max, {
    message: "Min storage must be less than or equal to max storage"
  }).optional(),
  cpuBrands: z.array(z.string()).optional(),
  gpuBrands: z.array(z.string()).optional(),
  displaySizes: z.array(z.number().min(10).max(50)).optional(),
  llmCompatibility: z.object({
    modelId: z.number().int().min(1),
    minScore: z.number().min(0).max(100),
  }).optional(),
}).merge(SortSchema)

export const GetLaptopsRequestSchema = PaginationSchema.merge(z.object({
  filters: SearchFiltersSchema.optional(),
  includeCompatibility: z.boolean().default(false),
  includePriceHistory: z.boolean().default(false),
}))

export const SearchLaptopsRequestSchema = PaginationSchema.merge(z.object({
  query: z.string().min(1).max(200),
  filters: SearchFiltersSchema.optional(),
  includeCompatibility: z.boolean().default(false),
  fuzzySearch: z.boolean().default(true),
}))

// ============================================================================
// COMPATIBILITY SCHEMAS
// ============================================================================

export const LLMCompatibilityScoreSchema = z.object({
  laptopId: z.number().int().min(1),
  llmId: z.number().int().min(1),
  overallScore: z.number().min(0).max(100),
  canRunOffline: z.boolean(),
  estimatedTokensPerSecond: z.number().min(0).optional(),
  maxContextLength: z.number().int().min(1).optional(),
  recommendedBatchSize: z.number().int().min(1).optional(),
  estimatedMemoryUsageGb: z.number().min(0).optional(),
  qualitativeAssessment: z.string().optional(),
  breakdown: z.object({
    cpuScore: z.number().min(0).max(100),
    ramScore: z.number().min(0).max(100),
    gpuScore: z.number().min(0).max(100),
    storageScore: z.number().min(0).max(100),
  }),
})

export const CalculateCompatibilityRequestSchema = z.object({
  laptopSpecs: z.object({
    ramGb: z.number().int().min(1).max(1024),
    cpuCores: z.number().int().min(1).max(128),
    cpuArchitecture: z.string().min(1),
    gpuVramGb: z.number().min(0).max(128).optional(),
    storageGb: z.number().int().min(1).max(100000),
    storageType: z.string().min(1),
  }),
  llmModelId: z.number().int().min(1).optional(),
})

// ============================================================================
// SCRAPING SCHEMAS
// ============================================================================

export const ScrapingConfigSchema = z.object({
  sourceId: z.string().min(1),
  rateLimitMs: z.number().int().min(100).max(60000),
  maxRetries: z.number().int().min(0).max(10),
  timeoutMs: z.number().int().min(1000).max(300000),
  selectors: z.record(z.string()),
  headers: z.record(z.string()).optional(),
  userAgent: z.string().optional(),
  requiresFullBrowserAutomation: z.boolean(),
})

export const CreateScrapingJobRequestSchema = z.object({
  sourceIds: z.array(z.string().min(1)).min(1),
  priority: z.enum(['low', 'normal', 'high']).default('normal'),
  scheduledFor: z.date().optional(),
  config: z.object({
    maxPages: z.number().int().min(1).max(10000).optional(),
    rateLimitMs: z.number().int().min(100).max(60000).optional(),
    timeoutMs: z.number().int().min(1000).max(300000).optional(),
  }).optional(),
})

export const ScrapedLaptopDataSchema = z.object({
  title: z.string().min(1),
  brand: z.string().optional(),
  model: z.string().optional(),
  price: z.number().min(0),
  currency: z.string().length(3),
  url: z.string().url(),
  imageUrls: z.array(z.string().url()),
  description: z.string().optional(),
  specifications: LaptopSpecificationsSchema.partial().optional(),
  sourceId: z.string().min(1),
  scrapedAt: z.date(),
})

// ============================================================================
// USER PREFERENCES SCHEMAS
// ============================================================================

export const UserPreferencesSchema = z.object({
  budget: z.object({
    min: z.number().min(0),
    max: z.number().min(0),
    currency: z.string().length(3),
  }).refine(data => data.min <= data.max, {
    message: "Min budget must be less than or equal to max budget"
  }).optional(),
  useCases: z.array(z.string().min(1)),
  preferredBrands: z.array(z.string().min(1)),
  requiredFeatures: z.array(z.string().min(1)),
  dealBreakers: z.array(z.string().min(1)),
  llmModelsOfInterest: z.array(z.string().min(1)),
})

export const GetRecommendationsRequestSchema = z.object({
  preferences: UserPreferencesSchema,
  filters: SearchFiltersSchema.optional(),
  maxResults: z.number().int().min(1).max(50).default(10),
  includeAlternatives: z.boolean().default(true),
})

// ============================================================================
// API RESPONSE SCHEMAS
// ============================================================================

export const ApiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) => z.object({
  success: z.boolean(),
  data: dataSchema.optional(),
  error: z.object({
    message: z.string(),
    code: z.string().optional(),
    details: z.any().optional(),
  }).optional(),
})

export const SearchResultsSchema = <T extends z.ZodTypeAny>(itemSchema: T) => z.object({
  data: z.array(itemSchema),
  pagination: z.object({
    page: z.number().int().min(1),
    limit: z.number().int().min(1),
    total: z.number().int().min(0),
    totalPages: z.number().int().min(0),
    hasNext: z.boolean(),
    hasPrev: z.boolean(),
  }),
})

// ============================================================================
// EXPORT TYPES FROM SCHEMAS
// ============================================================================

export type PaginationParams = z.infer<typeof PaginationSchema>
export type SortParams = z.infer<typeof SortSchema>
export type SearchFilters = z.infer<typeof SearchFiltersSchema>
export type GetLaptopsRequest = z.infer<typeof GetLaptopsRequestSchema>
export type SearchLaptopsRequest = z.infer<typeof SearchLaptopsRequestSchema>
export type LLMCompatibilityScore = z.infer<typeof LLMCompatibilityScoreSchema>
export type CalculateCompatibilityRequest = z.infer<typeof CalculateCompatibilityRequestSchema>
export type ScrapingConfig = z.infer<typeof ScrapingConfigSchema>
export type CreateScrapingJobRequest = z.infer<typeof CreateScrapingJobRequestSchema>
export type ScrapedLaptopData = z.infer<typeof ScrapedLaptopDataSchema>
export type UserPreferences = z.infer<typeof UserPreferencesSchema>
export type GetRecommendationsRequest = z.infer<typeof GetRecommendationsRequestSchema>
