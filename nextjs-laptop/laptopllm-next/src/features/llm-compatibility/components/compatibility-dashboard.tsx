'use client'

/**
 * LLM Compatibility Dashboard
 * Main dashboard for exploring laptop-LLM compatibility
 */

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { 
  Search, 
  Filter, 
  TrendingUp, 
  Cpu, 
  MemoryStick, 
  Zap,
  Target,
  BarChart3,
  Settings,
  Info,
  Star,
  Award,
  Laptop
} from 'lucide-react'
import { CompatibilityScoreCard } from './compatibility-score-card'
import { ModelRecommendations } from './model-recommendations'
import { 
  useCompatibilityStats,
  useAvailableModels,
  useModelCategories,
  useModelRecommendations
} from '../hooks/use-compatibility'

interface CompatibilityDashboardProps {
  className?: string
}

export function CompatibilityDashboard({ className }: CompatibilityDashboardProps) {
  const [selectedLaptop, setSelectedLaptop] = useState<string>('')
  const [selectedModel, setSelectedModel] = useState<string>('')
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState('overview')

  // Data hooks
  const { data: stats, isLoading: statsLoading } = useCompatibilityStats()
  const { data: models, isLoading: modelsLoading } = useAvailableModels()
  const { data: categories } = useModelCategories()
  const { data: laptopRecommendations } = useModelRecommendations(selectedModel)

  const handleLaptopSelect = (laptopId: string) => {
    setSelectedLaptop(laptopId)
  }

  const handleModelSelect = (modelId: string) => {
    setSelectedModel(modelId)
  }

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">LLM Compatibility Engine</h1>
            <p className="text-muted-foreground">
              Discover the best laptop-LLM combinations for your needs
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button variant="outline" size="sm">
              <BarChart3 className="h-4 w-4 mr-2" />
              Analytics
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Models</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalModels}</div>
                <p className="text-xs text-muted-foreground">
                  Available LLM models
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Compatibility Scores</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalCompatibilityScores}</div>
                <p className="text-xs text-muted-foreground">
                  Calculated combinations
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Score</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.overallAverageScore}/100</div>
                <p className="text-xs text-muted-foreground">
                  Overall compatibility
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Excellent Matches</CardTitle>
                <Star className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.distribution.excellent}</div>
                <p className="text-xs text-muted-foreground">
                  80+ compatibility score
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="explore">Explore</TabsTrigger>
            <TabsTrigger value="compare">Compare</TabsTrigger>
            <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Top Performing Models */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Award className="h-5 w-5 mr-2" />
                  Top Performing Models
                </CardTitle>
                <CardDescription>
                  Models with the highest average compatibility scores
                </CardDescription>
              </CardHeader>
              <CardContent>
                {statsLoading ? (
                  <div className="space-y-3">
                    {[1, 2, 3, 4, 5].map(i => (
                      <div key={i} className="animate-pulse">
                        <div className="h-16 bg-gray-200 rounded"></div>
                      </div>
                    ))}
                  </div>
                ) : stats?.modelStats ? (
                  <div className="space-y-3">
                    {stats.modelStats.slice(0, 5).map((model, index) => (
                      <div key={model.modelId} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <Badge variant="outline" className="w-8 h-8 rounded-full flex items-center justify-center">
                            {index + 1}
                          </Badge>
                          <div>
                            <div className="font-medium">{model.modelName}</div>
                            <div className="text-sm text-muted-foreground">
                              {model.parameters}B parameters • {model.scoreCount} scores
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {model.requiresGpu && (
                            <Badge variant="secondary" className="text-xs">
                              <Zap className="h-3 w-3 mr-1" />
                              GPU
                            </Badge>
                          )}
                          <Badge variant="default" className="text-sm">
                            {model.averageScore || 0}/100
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      No compatibility data available yet. Start by calculating some compatibility scores.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>

            {/* Compatibility Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Compatibility Distribution</CardTitle>
                <CardDescription>
                  How models perform across different compatibility ranges
                </CardDescription>
              </CardHeader>
              <CardContent>
                {stats && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{stats.distribution.excellent}</div>
                      <div className="text-sm text-muted-foreground">Excellent (80+)</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{stats.distribution.good}</div>
                      <div className="text-sm text-muted-foreground">Good (60-79)</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600">{stats.distribution.acceptable}</div>
                      <div className="text-sm text-muted-foreground">Acceptable (40-59)</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{stats.distribution.poor}</div>
                      <div className="text-sm text-muted-foreground">Poor (40)</div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Explore Tab */}
          <TabsContent value="explore" className="space-y-6">
            {/* Search and Filters */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Search className="h-5 w-5 mr-2" />
                  Explore Models
                </CardTitle>
                <CardDescription>
                  Search and filter LLM models to find the best match for your needs
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Search Models</label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search by name..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Model Size</label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Any size" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="small">Small (7B)</SelectItem>
                        <SelectItem value="medium">Medium (7-30B)</SelectItem>
                        <SelectItem value="large">Large (30-70B)</SelectItem>
                        <SelectItem value="ultra">Ultra Large (70B+)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">GPU Requirement</label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Any" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="required">GPU Required</SelectItem>
                        <SelectItem value="optional">GPU Optional</SelectItem>
                        <SelectItem value="cpu-only">CPU Only</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Framework</label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Any framework" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pytorch">PyTorch</SelectItem>
                        <SelectItem value="tensorflow">TensorFlow</SelectItem>
                        <SelectItem value="onnx">ONNX</SelectItem>
                        <SelectItem value="llamacpp">llama.cpp</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Models Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {modelsLoading ? (
                Array.from({ length: 6 }).map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="h-20 bg-gray-200 rounded"></div>
                    </CardContent>
                  </Card>
                ))
              ) : models ? (
                models
                  .filter(model => 
                    !searchTerm || 
                    model.name.toLowerCase().includes(searchTerm.toLowerCase())
                  )
                  .slice(0, 12)
                  .map(model => (
                    <Card key={model.id} className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">{model.name}</CardTitle>
                        <CardDescription className="flex items-center space-x-2">
                          <span>{model.parameters_billions}B parameters</span>
                          {model.requires_gpu && (
                            <Badge variant="secondary" className="text-xs">
                              <Zap className="h-3 w-3 mr-1" />
                              GPU
                            </Badge>
                          )}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Min RAM:</span>
                            <span>{model.min_ram_gb || 'N/A'}GB</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>Min VRAM:</span>
                            <span>{model.min_vram_gb || 'N/A'}GB</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>Avg Score:</span>
                            <Badge variant="outline">
                              {(model as any).averageCompatibilityScore || 'N/A'}/100
                            </Badge>
                          </div>
                        </div>
                        <Button 
                          className="w-full mt-3" 
                          size="sm"
                          onClick={() => handleModelSelect(model.id.toString())}
                        >
                          Select Model
                        </Button>
                      </CardContent>
                    </Card>
                  ))
              ) : (
                <div className="col-span-full text-center py-8">
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      No models available. Please check your connection or try again later.
                    </AlertDescription>
                  </Alert>
                </div>
              )}
            </div>
          </TabsContent>

          {/* Compare Tab */}
          <TabsContent value="compare" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Laptop className="h-5 w-5 mr-2" />
                  Laptop Comparison
                </CardTitle>
                <CardDescription>
                  Compare how different laptops perform with the same LLM model
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Laptop comparison feature will be implemented in a future update.
                    Select a model from the Explore tab to see laptop recommendations.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Recommendations Tab */}
          <TabsContent value="recommendations" className="space-y-6">
            {selectedModel ? (
              laptopRecommendations && (
                <ModelRecommendations
                  laptopId={selectedLaptop}
                  laptopName="Selected Laptop"
                  recommendations={laptopRecommendations}
                  onModelSelect={handleModelSelect}
                />
              )
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Target className="h-5 w-5 mr-2" />
                    Get Recommendations
                  </CardTitle>
                  <CardDescription>
                    Select a model from the Explore tab to see personalized recommendations
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      Choose an LLM model to get tailored laptop recommendations based on compatibility scores and performance metrics.
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}


