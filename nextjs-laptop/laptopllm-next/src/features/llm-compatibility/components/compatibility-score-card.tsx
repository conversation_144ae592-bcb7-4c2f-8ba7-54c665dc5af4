'use client'

/**
 * Compatibility Score Card Component
 * Displays detailed compatibility scores and metrics for laptop-LLM combinations
 */

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Cpu, 
  MemoryStick, 
  HardDrive, 
  Zap, 
  Thermometer,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Info,
  Lightbulb,
  Target,
  Clock,
  Battery,
  Gauge
} from 'lucide-react'
import type { DetailedCompatibilityScore } from '@/lib/llm/enhanced-compatibility.service'



interface CompatibilityScoreCardProps {
  score: DetailedCompatibilityScore
  laptopName: string
  modelName: string
  className?: string
}

export function CompatibilityScoreCard({ 
  score, 
  laptopName, 
  modelName, 
  className 
}: CompatibilityScoreCardProps) {
  const getScoreColor = (value: number) => {
    if (value >= 80) return 'text-green-600'
    if (value >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreBadgeVariant = (value: number) => {
    if (value >= 80) return 'default'
    if (value >= 60) return 'secondary'
    return 'destructive'
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'excellent': return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'good': return <CheckCircle className="h-5 w-5 text-blue-600" />
      case 'acceptable': return <Info className="h-5 w-5 text-yellow-600" />
      case 'limited': return <AlertTriangle className="h-5 w-5 text-orange-600" />
      case 'unsuitable': return <AlertTriangle className="h-5 w-5 text-red-600" />
      default: return <Info className="h-5 w-5" />
    }
  }

  const formatTokensPerSecond = (tokens: number) => {
    if (tokens >= 1000) return `${(tokens / 1000).toFixed(1)}K`
    return tokens.toString()
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Compatibility Analysis</CardTitle>
            <CardDescription>
              {laptopName} × {modelName}
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            {getCategoryIcon(score.assessment.category)}
            <Badge variant={getScoreBadgeVariant(score.overall)} className="text-lg px-3 py-1">
              {score.overall}/100
            </Badge>
          </div>
        </div>
        
        {/* Overall Assessment */}
        <Alert className="mt-4">
          <div className="flex items-center space-x-2">
            {getCategoryIcon(score.assessment.category)}
            <div>
              <div className="font-medium capitalize">
                {score.assessment.category} Compatibility
              </div>
              <AlertDescription className="mt-1">
                Confidence: {Math.round(score.assessment.confidence * 100)}%
              </AlertDescription>
            </div>
          </div>
        </Alert>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="components">Components</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="recommendations">Tips</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-4">
            {/* Component Scores */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Cpu className="h-4 w-4" />
                  <span className="text-sm font-medium">CPU</span>
                </div>
                <Progress value={score.components.cpu.score} className="h-2" />
                <div className="flex justify-between text-xs">
                  <span className={getScoreColor(score.components.cpu.score)}>
                    {score.components.cpu.score}/100
                  </span>
                  {score.components.cpu.bottleneck && (
                    <Badge variant="outline" className="text-xs">Bottleneck</Badge>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <MemoryStick className="h-4 w-4" />
                  <span className="text-sm font-medium">Memory</span>
                </div>
                <Progress value={score.components.memory.score} className="h-2" />
                <div className="flex justify-between text-xs">
                  <span className={getScoreColor(score.components.memory.score)}>
                    {score.components.memory.score}/100
                  </span>
                  {score.components.memory.bottleneck && (
                    <Badge variant="outline" className="text-xs">Bottleneck</Badge>
                  )}
                </div>
              </div>

              {score.components.gpu && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Zap className="h-4 w-4" />
                    <span className="text-sm font-medium">GPU</span>
                  </div>
                  <Progress value={score.components.gpu.score} className="h-2" />
                  <div className="flex justify-between text-xs">
                    <span className={getScoreColor(score.components.gpu.score)}>
                      {score.components.gpu.score}/100
                    </span>
                    {score.components.gpu.bottleneck && (
                      <Badge variant="outline" className="text-xs">Bottleneck</Badge>
                    )}
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <HardDrive className="h-4 w-4" />
                  <span className="text-sm font-medium">Storage</span>
                </div>
                <Progress value={score.components.storage.score} className="h-2" />
                <div className="flex justify-between text-xs">
                  <span className={getScoreColor(score.components.storage.score)}>
                    {score.components.storage.score}/100
                  </span>
                  {score.components.storage.bottleneck && (
                    <Badge variant="outline" className="text-xs">Bottleneck</Badge>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Thermometer className="h-4 w-4" />
                  <span className="text-sm font-medium">Thermal</span>
                </div>
                <Progress value={score.components.thermal.score} className="h-2" />
                <div className="flex justify-between text-xs">
                  <span className={getScoreColor(score.components.thermal.score)}>
                    {score.components.thermal.score}/100
                  </span>
                  <Badge 
                    variant={score.components.thermal.throttlingRisk === 'low' ? 'default' : 
                            score.components.thermal.throttlingRisk === 'medium' ? 'secondary' : 'destructive'}
                    className="text-xs"
                  >
                    {score.components.thermal.throttlingRisk} risk
                  </Badge>
                </div>
              </div>
            </div>

            <Separator />

            {/* Key Metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Speed</span>
                </div>
                <div className="text-lg font-bold">
                  {formatTokensPerSecond(score.performance.tokensPerSecond.optimal)}
                </div>
                <div className="text-xs text-muted-foreground">tokens/sec</div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <MemoryStick className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Memory</span>
                </div>
                <div className="text-lg font-bold">
                  {score.performance.memoryUsage.inference.toFixed(1)}
                </div>
                <div className="text-xs text-muted-foreground">GB used</div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <Zap className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Power</span>
                </div>
                <div className="text-lg font-bold">
                  {score.performance.powerConsumption.load.toFixed(0)}
                </div>
                <div className="text-xs text-muted-foreground">W load</div>
              </div>

              {score.performance.batteryLife && (
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1 mb-1">
                    <Battery className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Battery</span>
                  </div>
                  <div className="text-lg font-bold">
                    {score.performance.batteryLife.inference.toFixed(1)}
                  </div>
                  <div className="text-xs text-muted-foreground">hours</div>
                </div>
              )}
            </div>

            {/* Use Case Suitability */}
            <Separator />
            <div>
              <h4 className="text-sm font-medium mb-3 flex items-center">
                <Target className="h-4 w-4 mr-2" />
                Use Case Suitability
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {Object.entries(score.useCaseSuitability).map(([useCase, data]) => (
                  <div key={useCase} className="text-center">
                    <div className="text-xs font-medium capitalize mb-1">{useCase}</div>
                    <Badge variant={getScoreBadgeVariant(data.score)} className="text-xs">
                      {data.score}/100
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Components Tab */}
          <TabsContent value="components" className="space-y-4">
            {Object.entries(score.components).map(([component, data]) => {
              if (!data || component === 'gpu' && !score.components.gpu) return null
              
              return (
                <Card key={component}>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center space-x-2">
                      {component === 'cpu' && <Cpu className="h-4 w-4" />}
                      {component === 'memory' && <MemoryStick className="h-4 w-4" />}
                      {component === 'gpu' && <Zap className="h-4 w-4" />}
                      {component === 'storage' && <HardDrive className="h-4 w-4" />}
                      {component === 'thermal' && <Thermometer className="h-4 w-4" />}
                      <span className="capitalize">{component}</span>
                      <Badge variant={getScoreBadgeVariant(data.score)}>
                        {data.score}/100
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {/* Factor breakdown */}
                    <div className="space-y-2 mb-4">
                      {Object.entries(data.factors).map(([factor, value]) => (
                        <div key={factor} className="flex items-center justify-between">
                          <span className="text-sm capitalize">{factor.replace(/([A-Z])/g, ' $1')}</span>
                          <div className="flex items-center space-x-2">
                            <Progress value={(value as number) * 100} className="w-20 h-2" />
                            <span className="text-xs w-12 text-right">
                              {Math.round((value as number) * 100)}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Recommendations */}
                    {data.recommendations && data.recommendations.length > 0 && (
                      <div className="space-y-1">
                        <div className="text-sm font-medium flex items-center">
                          <Lightbulb className="h-3 w-3 mr-1" />
                          Recommendations
                        </div>
                        {data.recommendations.map((rec, index) => (
                          <div key={index} className="text-xs text-muted-foreground pl-4">
                            • {rec}
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              )
            })}
          </TabsContent>

          {/* Performance Tab */}
          <TabsContent value="performance" className="space-y-4">
            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center">
                  <Gauge className="h-4 w-4 mr-2" />
                  Performance Predictions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Tokens per Second */}
                <div>
                  <h5 className="text-sm font-medium mb-2">Inference Speed (tokens/sec)</h5>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {Object.entries(score.performance.tokensPerSecond).map(([quant, tokens]) => (
                      <div key={quant} className="text-center">
                        <div className="text-xs font-medium uppercase">{quant}</div>
                        <div className="text-lg font-bold">
                          {formatTokensPerSecond(tokens as number)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* Memory Usage */}
                <div>
                  <h5 className="text-sm font-medium mb-2">Memory Usage (GB)</h5>
                  <div className="grid grid-cols-3 gap-3">
                    <div className="text-center">
                      <div className="text-xs font-medium">Model</div>
                      <div className="text-lg font-bold">
                        {score.performance.memoryUsage.model.toFixed(1)}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-xs font-medium">Inference</div>
                      <div className="text-lg font-bold">
                        {score.performance.memoryUsage.inference.toFixed(1)}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-xs font-medium">Peak</div>
                      <div className="text-lg font-bold">
                        {score.performance.memoryUsage.peak.toFixed(1)}
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Power and Thermal */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h5 className="text-sm font-medium mb-2">Power Consumption (W)</h5>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>Idle:</span>
                        <span>{score.performance.powerConsumption.idle.toFixed(0)}W</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Load:</span>
                        <span>{score.performance.powerConsumption.load.toFixed(0)}W</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Peak:</span>
                        <span>{score.performance.powerConsumption.peak.toFixed(0)}W</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium mb-2">Thermal Load (%)</h5>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>Sustained:</span>
                        <span>{score.performance.thermalLoad.sustained.toFixed(0)}%</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Peak:</span>
                        <span>{score.performance.thermalLoad.peak.toFixed(0)}%</span>
                      </div>
                    </div>
                  </div>
                </div>

                {score.performance.batteryLife && (
                  <>
                    <Separator />
                    <div>
                      <h5 className="text-sm font-medium mb-2">Battery Life (hours)</h5>
                      <div className="grid grid-cols-2 gap-3">
                        <div className="text-center">
                          <div className="text-xs font-medium">Inference</div>
                          <div className="text-lg font-bold">
                            {score.performance.batteryLife.inference.toFixed(1)}h
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-xs font-medium">Idle</div>
                          <div className="text-lg font-bold">
                            {score.performance.batteryLife.idle.toFixed(1)}h
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Optimizations */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Recommended Optimizations
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <div>
                    <div className="text-xs font-medium">Quantization</div>
                    <Badge variant="outline">{score.optimizations.quantization}</Badge>
                  </div>
                  <div>
                    <div className="text-xs font-medium">Batch Size</div>
                    <Badge variant="outline">{score.optimizations.batchSize}</Badge>
                  </div>
                  <div>
                    <div className="text-xs font-medium">Context Length</div>
                    <Badge variant="outline">{score.optimizations.contextLength}</Badge>
                  </div>
                  <div>
                    <div className="text-xs font-medium">CPU Threads</div>
                    <Badge variant="outline">{score.optimizations.parallelization.cpuThreads}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Recommendations Tab */}
          <TabsContent value="recommendations" className="space-y-4">
            {/* Key Strengths */}
            {score.assessment.keyStrengths.length > 0 && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <div>
                  <div className="font-medium">Key Strengths</div>
                  <AlertDescription className="mt-1">
                    <ul className="list-disc list-inside space-y-1">
                      {score.assessment.keyStrengths.map((strength, index) => (
                        <li key={index} className="text-sm">{strength}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </div>
              </Alert>
            )}

            {/* Primary Limitations */}
            {score.assessment.primaryLimitations.length > 0 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <div>
                  <div className="font-medium">Primary Limitations</div>
                  <AlertDescription className="mt-1">
                    <ul className="list-disc list-inside space-y-1">
                      {score.assessment.primaryLimitations.map((limitation, index) => (
                        <li key={index} className="text-sm">{limitation}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </div>
              </Alert>
            )}

            {/* Upgrade Recommendations */}
            {score.assessment.upgradeRecommendations.length > 0 && (
              <Alert>
                <Lightbulb className="h-4 w-4" />
                <div>
                  <div className="font-medium">Upgrade Recommendations</div>
                  <AlertDescription className="mt-1">
                    <ul className="list-disc list-inside space-y-1">
                      {score.assessment.upgradeRecommendations.map((rec, index) => (
                        <li key={index} className="text-sm">{rec}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </div>
              </Alert>
            )}

            {/* Memory Optimizations */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Memory Optimizations</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1">
                  {score.optimizations.memoryOptimizations.map((opt, index) => (
                    <li key={index} className="text-sm flex items-start">
                      <span className="mr-2">•</span>
                      {opt}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Thermal Optimizations */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Thermal Management</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1">
                  {score.optimizations.thermalOptimizations.map((opt, index) => (
                    <li key={index} className="text-sm flex items-start">
                      <span className="mr-2">•</span>
                      {opt}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
