'use client'

/**
 * Model Recommendations Component
 * Shows recommended LLM models for a specific laptop with compatibility scores
 */

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { 
  Star, 
  TrendingUp, 
  Zap, 
  MemoryStick, 
  Clock, 
  Filter,
  Search,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  Info
} from 'lucide-react'
import type { ModelRecommendation, CompatibilityFilter } from '@/features/llm-compatibility/types'

interface ModelRecommendationsProps {
  laptopId: string
  laptopName: string
  recommendations: ModelRecommendation[]
  isLoading?: boolean
  onModelSelect?: (modelId: string) => void
  className?: string
}

export function ModelRecommendations({ 
  laptopId,
  laptopName, 
  recommendations, 
  isLoading = false,
  onModelSelect,
  className 
}: ModelRecommendationsProps) {
  const [filters, setFilters] = useState<CompatibilityFilter>({})
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<'score' | 'performance' | 'memory'>('score')
  const [showFilters, setShowFilters] = useState(false)
  const [expandedModels, setExpandedModels] = useState<Set<string>>(new Set())

  // Filter and sort recommendations
  const filteredRecommendations = recommendations
    .filter(rec => {
      // Search filter
      if (searchTerm && !rec.model.name.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false
      }
      
      // Score filter
      if (filters.minScore && rec.score.overall < filters.minScore) {
        return false
      }
      if (filters.maxScore && rec.score.overall > filters.maxScore) {
        return false
      }
      
      // Memory filter
      if (filters.memoryRequirements?.min && rec.performance.memoryUsage < filters.memoryRequirements.min) {
        return false
      }
      if (filters.memoryRequirements?.max && rec.performance.memoryUsage > filters.memoryRequirements.max) {
        return false
      }
      
      // Framework filter
      if (filters.frameworks && filters.frameworks.length > 0) {
        // Assuming model has framework property
        return filters.frameworks.includes((rec.model as any).framework)
      }
      
      return true
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'score':
          return b.score.overall - a.score.overall
        case 'performance':
          return b.performance.tokensPerSecond - a.performance.tokensPerSecond
        case 'memory':
          return a.performance.memoryUsage - b.performance.memoryUsage
        default:
          return 0
      }
    })

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'optimal': return 'bg-green-100 text-green-800 border-green-200'
      case 'good': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'acceptable': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'not-recommended': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'optimal': return <Star className="h-4 w-4" />
      case 'good': return <TrendingUp className="h-4 w-4" />
      case 'acceptable': return <Info className="h-4 w-4" />
      default: return null
    }
  }

  const toggleModelExpansion = (modelId: string) => {
    const newExpanded = new Set(expandedModels)
    if (newExpanded.has(modelId)) {
      newExpanded.delete(modelId)
    } else {
      newExpanded.add(modelId)
    }
    setExpandedModels(newExpanded)
  }

  const formatMemory = (gb: number) => {
    if (gb >= 1000) return `${(gb / 1000).toFixed(1)}TB`
    return `${gb.toFixed(1)}GB`
  }

  const formatTokensPerSecond = (tokens: number) => {
    if (tokens >= 1000) return `${(tokens / 1000).toFixed(1)}K`
    return tokens.toString()
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Model Recommendations</CardTitle>
          <CardDescription>Loading recommendations for {laptopName}...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="animate-pulse">
                <div className="h-20 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Model Recommendations</CardTitle>
            <CardDescription>
              Best LLM models for {laptopName} ({filteredRecommendations.length} models)
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
            {showFilters ? <ChevronUp className="h-4 w-4 ml-2" /> : <ChevronDown className="h-4 w-4 ml-2" />}
          </Button>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="mt-4 p-4 border rounded-lg space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Search */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Search Models</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Sort By */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Sort By</label>
                <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="score">Compatibility Score</SelectItem>
                    <SelectItem value="performance">Performance</SelectItem>
                    <SelectItem value="memory">Memory Usage</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Min Score */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Min Score</label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  placeholder="0"
                  value={filters.minScore || ''}
                  onChange={(e) => setFilters(prev => ({ 
                    ...prev, 
                    minScore: e.target.value ? parseInt(e.target.value) : undefined 
                  }))}
                />
              </div>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        {filteredRecommendations.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Info className="h-8 w-8 mx-auto mb-2" />
            <p>No models match your current filters</p>
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-2"
              onClick={() => {
                setFilters({})
                setSearchTerm('')
              }}
            >
              Clear Filters
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredRecommendations.map((recommendation) => {
              const isExpanded = expandedModels.has(recommendation.model.id.toString())
              
              return (
                <Card key={recommendation.model.id} className="border-l-4 border-l-blue-500">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getCategoryIcon(recommendation.category)}
                        <div>
                          <CardTitle className="text-base">{recommendation.model.name}</CardTitle>
                          <CardDescription className="flex items-center space-x-2">
                            <span>{recommendation.model.parameters_billions}B parameters</span>
                            {recommendation.model.quantization_bits && (
                              <>
                                <span>•</span>
                                <span>{recommendation.model.quantization_bits}-bit</span>
                              </>
                            )}
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={getCategoryColor(recommendation.category)}>
                          {recommendation.category}
                        </Badge>
                        <Badge variant="outline" className="text-lg">
                          {recommendation.score.overall}/100
                        </Badge>
                      </div>
                    </div>

                    {/* Quick Metrics */}
                    <div className="grid grid-cols-3 gap-4 mt-3">
                      <div className="text-center">
                        <div className="flex items-center justify-center space-x-1 mb-1">
                          <Clock className="h-3 w-3 text-muted-foreground" />
                          <span className="text-xs font-medium">Speed</span>
                        </div>
                        <div className="text-sm font-bold">
                          {formatTokensPerSecond(recommendation.performance.tokensPerSecond)}
                        </div>
                        <div className="text-xs text-muted-foreground">tokens/sec</div>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center justify-center space-x-1 mb-1">
                          <MemoryStick className="h-3 w-3 text-muted-foreground" />
                          <span className="text-xs font-medium">Memory</span>
                        </div>
                        <div className="text-sm font-bold">
                          {formatMemory(recommendation.performance.memoryUsage)}
                        </div>
                        <div className="text-xs text-muted-foreground">required</div>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center justify-center space-x-1 mb-1">
                          <Zap className="h-3 w-3 text-muted-foreground" />
                          <span className="text-xs font-medium">Confidence</span>
                        </div>
                        <div className="text-sm font-bold">
                          {Math.round(recommendation.confidence * 100)}%
                        </div>
                        <div className="text-xs text-muted-foreground">score</div>
                      </div>
                    </div>

                    {/* Compatibility Score Bar */}
                    <div className="mt-3">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs font-medium">Compatibility Score</span>
                        <span className="text-xs">{recommendation.score.overall}/100</span>
                      </div>
                      <Progress value={recommendation.score.overall} className="h-2" />
                    </div>
                  </CardHeader>

                  {/* Expandable Details */}
                  {isExpanded && (
                    <CardContent className="pt-0">
                      <Separator className="mb-4" />
                      
                      {/* Detailed Scores */}
                      <div className="space-y-3 mb-4">
                        <h5 className="text-sm font-medium">Component Scores</h5>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                          <div>
                            <div className="text-xs text-muted-foreground">CPU</div>
                            <Progress value={recommendation.score.breakdown.cpuScore} className="h-1 mt-1" />
                            <div className="text-xs mt-1">{recommendation.score.breakdown.cpuScore}/100</div>
                          </div>
                          <div>
                            <div className="text-xs text-muted-foreground">Memory</div>
                            <Progress value={recommendation.score.breakdown.ramScore} className="h-1 mt-1" />
                            <div className="text-xs mt-1">{recommendation.score.breakdown.ramScore}/100</div>
                          </div>
                          <div>
                            <div className="text-xs text-muted-foreground">GPU</div>
                            <Progress value={recommendation.score.breakdown.gpuScore} className="h-1 mt-1" />
                            <div className="text-xs mt-1">{recommendation.score.breakdown.gpuScore}/100</div>
                          </div>
                          <div>
                            <div className="text-xs text-muted-foreground">Storage</div>
                            <Progress value={recommendation.score.breakdown.storageScore} className="h-1 mt-1" />
                            <div className="text-xs mt-1">{recommendation.score.breakdown.storageScore}/100</div>
                          </div>
                        </div>
                      </div>

                      {/* Reasoning */}
                      {recommendation.reasoning.length > 0 && (
                        <div className="mb-4">
                          <h5 className="text-sm font-medium mb-2">Why This Model?</h5>
                          <ul className="space-y-1">
                            {recommendation.reasoning.map((reason, index) => (
                              <li key={index} className="text-sm text-muted-foreground flex items-start">
                                <span className="mr-2">•</span>
                                {reason}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {/* Performance Details */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h5 className="text-sm font-medium mb-2">Performance</h5>
                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span>Tokens/sec:</span>
                              <span>{formatTokensPerSecond(recommendation.performance.tokensPerSecond)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Memory:</span>
                              <span>{formatMemory(recommendation.performance.memoryUsage)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Context:</span>
                              <span>{recommendation.performance.maxContextLength?.toLocaleString() || 'N/A'}</span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h5 className="text-sm font-medium mb-2">Requirements</h5>
                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span>Min RAM:</span>
                              <span>{recommendation.model.min_ram_gb || 'N/A'}GB</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Min VRAM:</span>
                              <span>{recommendation.model.min_vram_gb || 'N/A'}GB</span>
                            </div>
                            <div className="flex justify-between">
                              <span>GPU Required:</span>
                              <span>{recommendation.model.requires_gpu ? 'Yes' : 'No'}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  )}

                  {/* Action Buttons */}
                  <CardContent className="pt-0">
                    <div className="flex items-center justify-between">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleModelExpansion(recommendation.model.id.toString())}
                      >
                        {isExpanded ? (
                          <>
                            <ChevronUp className="h-4 w-4 mr-2" />
                            Show Less
                          </>
                        ) : (
                          <>
                            <ChevronDown className="h-4 w-4 mr-2" />
                            Show Details
                          </>
                        )}
                      </Button>

                      <div className="flex items-center space-x-2">
                        {recommendation.model.model_card_url && (
                          <Button variant="outline" size="sm" asChild>
                            <a 
                              href={recommendation.model.model_card_url} 
                              target="_blank" 
                              rel="noopener noreferrer"
                            >
                              <ExternalLink className="h-4 w-4 mr-2" />
                              Model Card
                            </a>
                          </Button>
                        )}
                        
                        {onModelSelect && (
                          <Button 
                            size="sm"
                            onClick={() => onModelSelect(recommendation.model.id.toString())}
                          >
                            Select Model
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export { ModelRecommendations }
