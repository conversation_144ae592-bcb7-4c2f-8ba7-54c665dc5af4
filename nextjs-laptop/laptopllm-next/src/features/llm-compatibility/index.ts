/**
 * LLM Compatibility feature exports
 * Centralized exports for the LLM compatibility feature module
 */

// Types
export type * from './types'

// Services
export { compatibilityApiService } from './services/compatibility-api'

// Hooks
export {
  useCompatibilityScore,
  useCompatibilityAnalysis,
  useModelRecommendations,
  useCompatibilityReport,
  usePerformanceEstimation,
  useLaptopComparison,
  useModelComparison,
  useAvailableModels,
  useModelCategories,
  useCompatibilityStats,
  useOptimizationSuggestions,
  useCompatibilityTrends,
  useCompatibilityFeedback,
  useBatchCompatibility,
  useCompatibilityPrefetch
} from './hooks/use-compatibility'

// Components (to be created)
export * from './components'

// Utils (to be created)
export * from './utils'
