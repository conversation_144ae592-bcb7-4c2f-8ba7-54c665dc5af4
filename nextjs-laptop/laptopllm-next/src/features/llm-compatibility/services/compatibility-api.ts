import type { 
  LaptopData, 
  LLMModel, 
  CompatibilityScore, 
  PerformanceMetrics,
  CompatibilityAnalysis,
  ModelRecommendation,
  CompatibilityReport,
  CompatibilityFilter,
  CompatibilityComparison
} from '../types'

/**
 * API service for LLM compatibility operations
 */
export class CompatibilityApiService {
  private baseUrl = '/api/compatibility'

  /**
   * Calculate compatibility score between laptop and model
   */
  async calculateCompatibility(
    laptopId: string, 
    modelId: string
  ): Promise<CompatibilityScore> {
    const response = await fetch(`${this.baseUrl}/calculate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ laptopId, modelId }),
    })

    if (!response.ok) {
      throw new Error(`Failed to calculate compatibility: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get detailed compatibility analysis
   */
  async getCompatibilityAnalysis(
    laptopId: string, 
    modelId: string
  ): Promise<CompatibilityAnalysis> {
    const response = await fetch(`${this.baseUrl}/analysis`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ laptopId, modelId }),
    })

    if (!response.ok) {
      throw new Error(`Failed to get compatibility analysis: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get model recommendations for a laptop
   */
  async getModelRecommendations(
    laptopId: string,
    filters?: CompatibilityFilter
  ): Promise<ModelRecommendation[]> {
    const searchParams = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.set(`filter.${key}`, JSON.stringify(value))
        }
      })
    }

    const url = `${this.baseUrl}/recommendations/${laptopId}${searchParams.toString() ? `?${searchParams}` : ''}`
    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`Failed to get model recommendations: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get comprehensive compatibility report
   */
  async getCompatibilityReport(laptopId: string): Promise<CompatibilityReport> {
    const response = await fetch(`${this.baseUrl}/report/${laptopId}`)

    if (!response.ok) {
      throw new Error(`Failed to get compatibility report: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Estimate performance metrics
   */
  async estimatePerformance(
    laptopId: string, 
    modelId: string
  ): Promise<PerformanceMetrics> {
    const response = await fetch(`${this.baseUrl}/performance`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ laptopId, modelId }),
    })

    if (!response.ok) {
      throw new Error(`Failed to estimate performance: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Compare compatibility across multiple laptops for a model
   */
  async compareLaptopsForModel(
    laptopIds: string[], 
    modelId: string
  ): Promise<CompatibilityComparison> {
    const response = await fetch(`${this.baseUrl}/compare-laptops`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ laptopIds, modelId }),
    })

    if (!response.ok) {
      throw new Error(`Failed to compare laptops: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Compare compatibility across multiple models for a laptop
   */
  async compareModelsForLaptop(
    laptopId: string, 
    modelIds: string[]
  ): Promise<{
    laptop: LaptopData
    models: LLMModel[]
    scores: CompatibilityScore[]
    recommendations: ModelRecommendation[]
  }> {
    const response = await fetch(`${this.baseUrl}/compare-models`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ laptopId, modelIds }),
    })

    if (!response.ok) {
      throw new Error(`Failed to compare models: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get all available LLM models
   */
  async getAvailableModels(filters?: {
    category?: string
    framework?: string
    minSize?: string
    maxSize?: string
  }): Promise<LLMModel[]> {
    const searchParams = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value) searchParams.set(key, value)
      })
    }

    const url = `${this.baseUrl}/models${searchParams.toString() ? `?${searchParams}` : ''}`
    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`Failed to get available models: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get model categories
   */
  async getModelCategories(): Promise<Array<{
    id: string
    name: string
    description: string
    modelCount: number
  }>> {
    const response = await fetch(`${this.baseUrl}/model-categories`)

    if (!response.ok) {
      throw new Error(`Failed to get model categories: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get compatibility statistics
   */
  async getCompatibilityStats(): Promise<{
    totalAnalyses: number
    averageScore: number
    topModels: Array<{
      model: LLMModel
      averageScore: number
      analysisCount: number
    }>
    topLaptops: Array<{
      laptop: LaptopData
      averageScore: number
      analysisCount: number
    }>
    scoreDistribution: Record<string, number>
  }> {
    const response = await fetch(`${this.baseUrl}/stats`)

    if (!response.ok) {
      throw new Error(`Failed to get compatibility stats: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get optimization suggestions
   */
  async getOptimizationSuggestions(
    laptopId: string, 
    modelId: string
  ): Promise<Array<{
    type: 'hardware' | 'software' | 'configuration'
    priority: 'high' | 'medium' | 'low'
    title: string
    description: string
    impact: {
      performance: number
      efficiency: number
    }
  }>> {
    const response = await fetch(`${this.baseUrl}/optimizations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ laptopId, modelId }),
    })

    if (!response.ok) {
      throw new Error(`Failed to get optimization suggestions: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Submit compatibility feedback
   */
  async submitFeedback(data: {
    laptopId: string
    modelId: string
    actualPerformance?: Partial<PerformanceMetrics>
    userRating: number
    comments?: string
  }): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${this.baseUrl}/feedback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Failed to submit feedback: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get compatibility trends
   */
  async getCompatibilityTrends(
    period: 'week' | 'month' | 'quarter' | 'year' = 'month'
  ): Promise<Array<{
    date: string
    averageScore: number
    analysisCount: number
    topModel: string
    topLaptop: string
  }>> {
    const response = await fetch(`${this.baseUrl}/trends?period=${period}`)

    if (!response.ok) {
      throw new Error(`Failed to get compatibility trends: ${response.statusText}`)
    }

    return response.json()
  }
}

// Export singleton instance
export const compatibilityApiService = new CompatibilityApiService()
