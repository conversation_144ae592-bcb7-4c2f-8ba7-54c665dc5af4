import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useCallback } from 'react'
import { compatibilityApiService } from '../services/compatibility-api'
import type { 
  CompatibilityScore, 
  PerformanceMetrics,
  CompatibilityAnalysis,
  ModelRecommendation,
  CompatibilityReport,
  CompatibilityFilter
} from '../types'

/**
 * Hook for calculating compatibility between laptop and model
 */
export function useCompatibilityScore(laptopId: string, modelId: string) {
  return useQuery<CompatibilityScore>({
    queryKey: ['compatibility', 'score', laptopId, modelId],
    queryFn: () => compatibilityApiService.calculateCompatibility(laptopId, modelId),
    enabled: !!laptopId && !!modelId,
    staleTime: 30 * 60 * 1000, // 30 minutes - compatibility scores are stable
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
    retry: 2,
  })
}

/**
 * Hook for detailed compatibility analysis
 */
export function useCompatibilityAnalysis(laptopId: string, modelId: string) {
  return useQuery<CompatibilityAnalysis>({
    queryKey: ['compatibility', 'analysis', laptopId, modelId],
    queryFn: () => compatibilityApiService.getCompatibilityAnalysis(laptopId, modelId),
    enabled: !!laptopId && !!modelId,
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  })
}

/**
 * Hook for model recommendations
 */
export function useModelRecommendations(
  laptopId: string, 
  filters?: CompatibilityFilter
) {
  return useQuery<ModelRecommendation[]>({
    queryKey: ['compatibility', 'recommendations', laptopId, filters],
    queryFn: () => compatibilityApiService.getModelRecommendations(laptopId, filters),
    enabled: !!laptopId,
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  })
}

/**
 * Hook for comprehensive compatibility report
 */
export function useCompatibilityReport(laptopId: string) {
  return useQuery<CompatibilityReport>({
    queryKey: ['compatibility', 'report', laptopId],
    queryFn: () => compatibilityApiService.getCompatibilityReport(laptopId),
    enabled: !!laptopId,
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  })
}

/**
 * Hook for performance estimation
 */
export function usePerformanceEstimation(laptopId: string, modelId: string) {
  return useQuery<PerformanceMetrics>({
    queryKey: ['compatibility', 'performance', laptopId, modelId],
    queryFn: () => compatibilityApiService.estimatePerformance(laptopId, modelId),
    enabled: !!laptopId && !!modelId,
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  })
}

/**
 * Hook for comparing laptops for a specific model
 */
export function useLaptopComparison(laptopIds: string[], modelId: string) {
  return useQuery({
    queryKey: ['compatibility', 'compare-laptops', laptopIds.sort(), modelId],
    queryFn: () => compatibilityApiService.compareLaptopsForModel(laptopIds, modelId),
    enabled: laptopIds.length >= 2 && !!modelId,
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  })
}

/**
 * Hook for comparing models for a specific laptop
 */
export function useModelComparison(laptopId: string, modelIds: string[]) {
  return useQuery({
    queryKey: ['compatibility', 'compare-models', laptopId, modelIds.sort()],
    queryFn: () => compatibilityApiService.compareModelsForLaptop(laptopId, modelIds),
    enabled: !!laptopId && modelIds.length >= 2,
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  })
}

/**
 * Hook for available LLM models
 */
export function useAvailableModels(filters?: {
  category?: string
  framework?: string
  minSize?: string
  maxSize?: string
}) {
  return useQuery({
    queryKey: ['compatibility', 'models', filters],
    queryFn: () => compatibilityApiService.getAvailableModels(filters),
    staleTime: 60 * 60 * 1000, // 1 hour - model list is relatively stable
    gcTime: 4 * 60 * 60 * 1000, // 4 hours
  })
}

/**
 * Hook for model categories
 */
export function useModelCategories() {
  return useQuery({
    queryKey: ['compatibility', 'model-categories'],
    queryFn: () => compatibilityApiService.getModelCategories(),
    staleTime: 2 * 60 * 60 * 1000, // 2 hours
    gcTime: 8 * 60 * 60 * 1000, // 8 hours
  })
}

/**
 * Hook for compatibility statistics
 */
export function useCompatibilityStats() {
  return useQuery({
    queryKey: ['compatibility', 'stats'],
    queryFn: () => compatibilityApiService.getCompatibilityStats(),
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  })
}

/**
 * Hook for optimization suggestions
 */
export function useOptimizationSuggestions(laptopId: string, modelId: string) {
  return useQuery({
    queryKey: ['compatibility', 'optimizations', laptopId, modelId],
    queryFn: () => compatibilityApiService.getOptimizationSuggestions(laptopId, modelId),
    enabled: !!laptopId && !!modelId,
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 4 * 60 * 60 * 1000, // 4 hours
  })
}

/**
 * Hook for compatibility trends
 */
export function useCompatibilityTrends(period: 'week' | 'month' | 'quarter' | 'year' = 'month') {
  return useQuery({
    queryKey: ['compatibility', 'trends', period],
    queryFn: () => compatibilityApiService.getCompatibilityTrends(period),
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 4 * 60 * 60 * 1000, // 4 hours
  })
}

/**
 * Hook for submitting compatibility feedback
 */
export function useCompatibilityFeedback() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: compatibilityApiService.submitFeedback,
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['compatibility', 'score', variables.laptopId, variables.modelId]
      })
      queryClient.invalidateQueries({
        queryKey: ['compatibility', 'analysis', variables.laptopId, variables.modelId]
      })
      queryClient.invalidateQueries({
        queryKey: ['compatibility', 'stats']
      })
    },
  })
}

/**
 * Hook for batch compatibility calculations
 */
export function useBatchCompatibility() {
  const queryClient = useQueryClient()

  const calculateBatch = useCallback(async (
    laptopIds: string[], 
    modelIds: string[]
  ) => {
    const results = []
    
    for (const laptopId of laptopIds) {
      for (const modelId of modelIds) {
        try {
          const score = await compatibilityApiService.calculateCompatibility(laptopId, modelId)
          results.push({
            laptopId,
            modelId,
            score,
            success: true,
          })
          
          // Cache the result
          queryClient.setQueryData(
            ['compatibility', 'score', laptopId, modelId],
            score
          )
        } catch (error) {
          results.push({
            laptopId,
            modelId,
            error: error instanceof Error ? error.message : 'Unknown error',
            success: false,
          })
        }
      }
    }
    
    return results
  }, [queryClient])

  return { calculateBatch }
}

/**
 * Hook for prefetching compatibility data
 */
export function useCompatibilityPrefetch() {
  const queryClient = useQueryClient()

  const prefetchCompatibility = useCallback((laptopId: string, modelId: string) => {
    // Prefetch compatibility score
    queryClient.prefetchQuery({
      queryKey: ['compatibility', 'score', laptopId, modelId],
      queryFn: () => compatibilityApiService.calculateCompatibility(laptopId, modelId),
      staleTime: 30 * 60 * 1000,
    })

    // Prefetch performance estimation
    queryClient.prefetchQuery({
      queryKey: ['compatibility', 'performance', laptopId, modelId],
      queryFn: () => compatibilityApiService.estimatePerformance(laptopId, modelId),
      staleTime: 30 * 60 * 1000,
    })
  }, [queryClient])

  const prefetchRecommendations = useCallback((laptopId: string) => {
    queryClient.prefetchQuery({
      queryKey: ['compatibility', 'recommendations', laptopId, undefined],
      queryFn: () => compatibilityApiService.getModelRecommendations(laptopId),
      staleTime: 15 * 60 * 1000,
    })
  }, [queryClient])

  return {
    prefetchCompatibility,
    prefetchRecommendations,
  }
}
