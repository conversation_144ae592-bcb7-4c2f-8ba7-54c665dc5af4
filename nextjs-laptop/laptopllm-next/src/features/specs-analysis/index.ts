/**
 * Specifications Analysis Module
 * Automated technical specification analysis and LLM compatibility prediction
 */

// Types
export type {
  SpecificationAnalysis,
  ExtractedSpecifications,
  ExtractedCPU,
  ExtractedMemory,
  ExtractedStorage,
  ExtractedGPU,
  ExtractedDisplay,
  ExtractedConnectivity,
  ExtractedPhysical,
  ExtractedBattery,
  ExtractedThermal,
  AnalysisConfidence,
  AnalysisMetadata,
  PerformancePrediction,
  LLMCompatibilityPrediction,
  GeneralPerformancePrediction,
  PowerEfficiencyPrediction,
  ThermalPerformancePrediction,
  RecommendedLLMModel,
  ExtractionPattern,
  RegexPattern,
  NormalizationRule,
  AnalysisConfig,
  SpecsAnalysisResult,
  ComponentAnalysisResult
} from './types'

// Services
export { SpecsAnalysisService } from './services/specs-analysis.service'
export { CPUAnalyzer } from './services/analyzers/cpu-analyzer'
export { MemoryAnalyzer } from './services/analyzers/memory-analyzer'
export { StorageAnalyzer } from './services/analyzers/storage-analyzer'
export { SpecsNormalizer } from './services/specs-normalizer'
export { PerformancePredictor } from './services/performance-predictor'

// Hooks
export {
  useSpecsAnalysis,
  useSpecsAnalysisResult,
  useBatchSpecsAnalysis,
  usePerformancePrediction,
  useAnalysisConfig,
  useAnalysisStats,
  useAnalysisProgress,
  useAnalysisComparison,
  useAnalysisValidation
} from './hooks/use-specs-analysis'

// Components
export { SpecsAnalyzer } from './components/specs-analyzer'
export { SpecsAnalysisResult } from './components/specs-analysis-result'

// Utils and Constants
export const ANALYSIS_VERSION = '1.0.0'

export const DEFAULT_ANALYSIS_CONFIG = {
  extractionMethods: {
    regex: true,
    nlp: false,
    structured: true,
    hybrid: true
  },
  confidenceThresholds: {
    minimum: 30,
    warning: 60,
    good: 80
  },
  performance: {
    timeout: 30000,
    maxRetries: 3,
    cacheResults: true
  },
  validation: {
    strictMode: false,
    requireMinimumSpecs: true,
    validateBenchmarks: false
  }
} as const

export const SUPPORTED_CPU_MANUFACTURERS = [
  'Intel',
  'AMD', 
  'Apple',
  'Qualcomm'
] as const

export const SUPPORTED_MEMORY_TYPES = [
  'DDR4',
  'DDR5',
  'LPDDR4',
  'LPDDR5'
] as const

export const SUPPORTED_STORAGE_TYPES = [
  'NVMe SSD',
  'SATA SSD', 
  'HDD',
  'eMMC'
] as const

export const SUPPORTED_GPU_MANUFACTURERS = [
  'NVIDIA',
  'AMD',
  'Intel',
  'Apple'
] as const

// Utility functions
export const createAnalysisId = () => `analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

export const formatConfidence = (confidence: number): string => {
  if (confidence >= 80) return 'High'
  if (confidence >= 60) return 'Medium'
  if (confidence >= 40) return 'Low'
  return 'Very Low'
}

export const getConfidenceColor = (confidence: number): string => {
  if (confidence >= 80) return 'green'
  if (confidence >= 60) return 'yellow'
  if (confidence >= 40) return 'orange'
  return 'red'
}

export const formatProcessingTime = (ms: number): string => {
  if (ms < 1000) return `${ms}ms`
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
  return `${(ms / 60000).toFixed(1)}m`
}

export const formatMemorySize = (gb: number): string => {
  if (gb >= 1024) return `${(gb / 1024).toFixed(1)} TB`
  return `${gb} GB`
}

export const formatStorageSize = (gb: number): string => {
  if (gb >= 1024) return `${(gb / 1024).toFixed(1)} TB`
  return `${gb} GB`
}

export const formatFrequency = (ghz: number): string => {
  if (ghz >= 1) return `${ghz.toFixed(1)} GHz`
  return `${(ghz * 1000).toFixed(0)} MHz`
}

export const formatSpeed = (mbps: number): string => {
  if (mbps >= 1000) return `${(mbps / 1000).toFixed(1)} GB/s`
  return `${mbps} MB/s`
}

// Validation utilities
export const validateCPUSpecs = (cpu: ExtractedCPU): boolean => {
  return !!(
    cpu.manufacturer &&
    cpu.model &&
    cpu.cores > 0 &&
    cpu.threads >= cpu.cores &&
    cpu.baseFrequency > 0 &&
    cpu.tdp > 0
  )
}

export const validateMemorySpecs = (memory: ExtractedMemory): boolean => {
  return !!(
    memory.capacity > 0 &&
    memory.type &&
    memory.speed > 0 &&
    memory.channels > 0
  )
}

export const validateStorageSpecs = (storage: ExtractedStorage[]): boolean => {
  return storage.length > 0 && storage.every(s => 
    s.type && s.capacity > 0 && s.interface
  )
}

// Analysis helpers
export const calculateOverallScore = (
  cpuScore: number,
  memoryScore: number,
  storageScore: number,
  gpuScore: number = 0
): number => {
  const weights = {
    cpu: 0.35,
    memory: 0.30,
    storage: 0.20,
    gpu: 0.15
  }

  return Math.round(
    cpuScore * weights.cpu +
    memoryScore * weights.memory +
    storageScore * weights.storage +
    gpuScore * weights.gpu
  )
}

export const estimateLLMPerformance = (
  specs: ExtractedSpecifications,
  modelSize: '7B' | '13B' | '70B'
): number => {
  const cpuFactor = specs.cpu.cores * specs.cpu.baseFrequency
  const memoryFactor = specs.memory.capacity / 8
  const gpuFactor = specs.gpu?.type === 'Discrete' ? 1.5 : 1

  let baseTPS = cpuFactor * memoryFactor * gpuFactor * 0.1

  // Adjust for model size
  switch (modelSize) {
    case '70B':
      baseTPS *= 0.1
      break
    case '13B':
      baseTPS *= 0.3
      break
    case '7B':
      baseTPS *= 0.6
      break
  }

  return Math.max(1, Math.round(baseTPS))
}

// Error handling
export class SpecsAnalysisError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message)
    this.name = 'SpecsAnalysisError'
  }
}

export const createAnalysisError = (
  code: string,
  message: string,
  details?: any
): SpecsAnalysisError => {
  return new SpecsAnalysisError(message, code, details)
}

// Common error codes
export const ANALYSIS_ERROR_CODES = {
  INVALID_INPUT: 'INVALID_INPUT',
  EXTRACTION_FAILED: 'EXTRACTION_FAILED',
  NORMALIZATION_FAILED: 'NORMALIZATION_FAILED',
  PREDICTION_FAILED: 'PREDICTION_FAILED',
  TIMEOUT: 'TIMEOUT',
  INSUFFICIENT_DATA: 'INSUFFICIENT_DATA'
} as const
