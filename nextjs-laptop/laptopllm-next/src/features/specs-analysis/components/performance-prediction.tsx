/**
 * Performance Prediction Component
 * Displays LLM compatibility and performance predictions
 */

'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Brain, 
  Zap, 
  Battery, 
  Thermometer, 
  TrendingUp, 
  Clock, 
  HardDrive,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react'
import type { PerformancePrediction } from '../types'

interface PerformancePredictionProps {
  predictions?: PerformancePrediction
}

export function PerformancePrediction({ predictions }: PerformancePredictionProps) {
  if (!predictions) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-muted-foreground">No performance predictions available</p>
        </CardContent>
      </Card>
    )
  }

  const { llmCompatibility, generalPerformance, powerEfficiency, thermalPerformance } = predictions

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return 'default'
    if (score >= 60) return 'secondary'
    return 'destructive'
  }

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'Low': return 'text-green-600'
      case 'Medium': return 'text-yellow-600'
      case 'High': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <div className="space-y-6">
      {/* LLM Compatibility */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            LLM Compatibility & Performance
          </CardTitle>
          <CardDescription>
            Predicted performance for Large Language Model workloads
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Overall Score */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Overall LLM Compatibility</span>
              <Badge variant={getScoreBadgeVariant(llmCompatibility.overallScore)}>
                {llmCompatibility.overallScore}/100
              </Badge>
            </div>
            <Progress value={llmCompatibility.overallScore} className="w-full" />
          </div>

          {/* Performance Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-3">Estimated Tokens/Second</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Small Models (7B)</span>
                  <span className="font-medium">{llmCompatibility.performance.tokensPerSecond.small}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Medium Models (13B)</span>
                  <span className="font-medium">{llmCompatibility.performance.tokensPerSecond.medium}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Large Models (30B+)</span>
                  <span className="font-medium">{llmCompatibility.performance.tokensPerSecond.large}</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-3">Max Context Length</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Small Models</span>
                  <span className="font-medium">{llmCompatibility.performance.maxContextLength.small.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Medium Models</span>
                  <span className="font-medium">{llmCompatibility.performance.maxContextLength.medium.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Large Models</span>
                  <span className="font-medium">{llmCompatibility.performance.maxContextLength.large.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-2">Concurrent Sessions</h4>
              <p className="text-2xl font-bold">{llmCompatibility.performance.concurrentSessions}</p>
            </div>
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-2">Memory Efficiency</h4>
              <p className="text-2xl font-bold">{Math.round(llmCompatibility.performance.memoryEfficiency * 100)}%</p>
            </div>
          </div>

          {/* Recommended Models */}
          {llmCompatibility.recommendedModels.length > 0 && (
            <div className="mt-6">
              <h4 className="font-medium text-sm text-muted-foreground mb-3">Recommended Models</h4>
              <div className="space-y-3">
                {llmCompatibility.recommendedModels.slice(0, 3).map((model, index) => (
                  <div key={index} className="border rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h5 className="font-medium">{model.name}</h5>
                        <p className="text-sm text-muted-foreground">{model.size} • {model.quantization}</p>
                      </div>
                      <Badge variant="outline">{model.confidence}% confidence</Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Performance:</span>
                        <span className="ml-2 font-medium">{model.estimatedPerformance} tokens/sec</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Memory:</span>
                        <span className="ml-2 font-medium">{model.memoryUsage} GB</span>
                      </div>
                    </div>
                    {model.notes.length > 0 && (
                      <div className="mt-2">
                        <ul className="text-xs text-muted-foreground space-y-1">
                          {model.notes.map((note, noteIndex) => (
                            <li key={noteIndex} className="flex items-start gap-1">
                              <span>•</span>
                              <span>{note}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Limitations */}
          {llmCompatibility.limitations.length > 0 && (
            <div className="mt-6">
              <h4 className="font-medium text-sm text-muted-foreground mb-2">Limitations</h4>
              <div className="space-y-1">
                {llmCompatibility.limitations.map((limitation, index) => (
                  <Alert key={index} variant="default" className="py-2">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription className="text-sm">
                      {limitation}
                    </AlertDescription>
                  </Alert>
                ))}
              </div>
            </div>
          )}

          {/* Optimizations */}
          {llmCompatibility.optimizations.length > 0 && (
            <div className="mt-6">
              <h4 className="font-medium text-sm text-muted-foreground mb-2">Optimization Suggestions</h4>
              <div className="space-y-1">
                {llmCompatibility.optimizations.map((optimization, index) => (
                  <Alert key={index} variant="default" className="py-2">
                    <Info className="h-4 w-4" />
                    <AlertDescription className="text-sm">
                      {optimization}
                    </AlertDescription>
                  </Alert>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* General Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            General Performance
          </CardTitle>
          <CardDescription>
            Overall system performance across different workloads
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
            <div className="text-center">
              <div className={`text-2xl font-bold ${getScoreColor(generalPerformance.cpuScore)}`}>
                {generalPerformance.cpuScore}
              </div>
              <div className="text-sm text-muted-foreground">CPU</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${getScoreColor(generalPerformance.gpuScore)}`}>
                {generalPerformance.gpuScore}
              </div>
              <div className="text-sm text-muted-foreground">GPU</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${getScoreColor(generalPerformance.memoryScore)}`}>
                {generalPerformance.memoryScore}
              </div>
              <div className="text-sm text-muted-foreground">Memory</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${getScoreColor(generalPerformance.storageScore)}`}>
                {generalPerformance.storageScore}
              </div>
              <div className="text-sm text-muted-foreground">Storage</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${getScoreColor(generalPerformance.overallScore)}`}>
                {generalPerformance.overallScore}
              </div>
              <div className="text-sm text-muted-foreground">Overall</div>
            </div>
          </div>

          <Separator className="my-4" />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-3">Benchmark Estimates</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Cinebench R23</span>
                  <span className="font-medium">{generalPerformance.benchmarkEstimates.cinebench.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Geekbench 5</span>
                  <span className="font-medium">{generalPerformance.benchmarkEstimates.geekbench.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">PCMark 10</span>
                  <span className="font-medium">{generalPerformance.benchmarkEstimates.pcmark.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Gaming Score</span>
                  <span className="font-medium">{generalPerformance.benchmarkEstimates.gaming.toLocaleString()}</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-3">Recommended Use Cases</h4>
              <div className="space-y-1">
                {generalPerformance.useCases.map((useCase, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">{useCase}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Power Efficiency */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Battery className="h-5 w-5" />
            Power Efficiency
          </CardTitle>
          <CardDescription>
            Battery life and power consumption estimates
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-3">Estimated Battery Life</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Office Work</span>
                  <span className="font-medium">{powerEfficiency.batteryLife.office}h</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Video Playback</span>
                  <span className="font-medium">{powerEfficiency.batteryLife.video}h</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Gaming</span>
                  <span className="font-medium">{powerEfficiency.batteryLife.gaming}h</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">LLM Inference</span>
                  <span className="font-medium">{powerEfficiency.batteryLife.llmInference}h</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-3">Power Consumption</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Idle</span>
                  <span className="font-medium">{powerEfficiency.powerConsumption.idle}W</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Typical Use</span>
                  <span className="font-medium">{powerEfficiency.powerConsumption.typical}W</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Peak Load</span>
                  <span className="font-medium">{powerEfficiency.powerConsumption.peak}W</span>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Efficiency Rating</span>
              <Badge variant={getScoreBadgeVariant(powerEfficiency.efficiencyRating)}>
                {powerEfficiency.efficiencyRating}/100
              </Badge>
            </div>
            <Progress value={powerEfficiency.efficiencyRating} className="w-full" />
          </div>
        </CardContent>
      </Card>

      {/* Thermal Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Thermometer className="h-5 w-5" />
            Thermal Performance
          </CardTitle>
          <CardDescription>
            Cooling efficiency and thermal management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-3">Thermal Metrics</h4>
              <div className="space-y-3">
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm">Thermal Rating</span>
                    <Badge variant={getScoreBadgeVariant(thermalPerformance.thermalRating)}>
                      {thermalPerformance.thermalRating}/100
                    </Badge>
                  </div>
                  <Progress value={thermalPerformance.thermalRating} className="w-full" />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm">Sustained Performance</span>
                    <span className="font-medium">{thermalPerformance.sustainedPerformance}%</span>
                  </div>
                  <Progress value={thermalPerformance.sustainedPerformance} className="w-full" />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm">Cooling Adequacy</span>
                    <span className="font-medium">{thermalPerformance.coolingAdequacy}%</span>
                  </div>
                  <Progress value={thermalPerformance.coolingAdequacy} className="w-full" />
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-3">Thermal Characteristics</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Throttling Risk</span>
                  <Badge variant={thermalPerformance.throttlingRisk === 'Low' ? 'default' : 
                                 thermalPerformance.throttlingRisk === 'Medium' ? 'secondary' : 'destructive'}>
                    {thermalPerformance.throttlingRisk}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Idle Noise</span>
                  <span className="font-medium">{thermalPerformance.noiseLevel.idle} dB</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Load Noise</span>
                  <span className="font-medium">{thermalPerformance.noiseLevel.load} dB</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
