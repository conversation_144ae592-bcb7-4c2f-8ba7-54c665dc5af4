/**
 * Specifications Analysis Result Component
 * Displays detailed extracted specifications
 */

'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Cpu, HardDrive, Monitor, Zap, Thermometer, Wifi } from 'lucide-react'
import type { SpecificationAnalysis } from '../types'

interface SpecsAnalysisResultProps {
  analysis?: SpecificationAnalysis
}

export function SpecsAnalysisResult({ analysis }: SpecsAnalysisResultProps) {
  if (!analysis) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-muted-foreground">No analysis results available</p>
        </CardContent>
      </Card>
    )
  }

  const { extractedSpecs, confidence } = analysis

  const getConfidenceColor = (conf: number) => {
    if (conf >= 80) return 'text-green-600'
    if (conf >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const formatFrequency = (freq: number) => `${freq.toFixed(1)} GHz`
  const formatMemory = (mem: number) => `${mem} GB`
  const formatStorage = (storage: number) => storage >= 1024 ? `${(storage / 1024).toFixed(1)} TB` : `${storage} GB`

  return (
    <div className="space-y-6">
      {/* CPU Specifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cpu className="h-5 w-5" />
            CPU Specifications
            <Badge variant="outline" className={getConfidenceColor(confidence.cpu)}>
              {confidence.cpu}% confidence
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Processor</h4>
              <p className="font-semibold">{extractedSpecs.cpu.manufacturer} {extractedSpecs.cpu.family}</p>
              <p className="text-sm">{extractedSpecs.cpu.model}</p>
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Architecture</h4>
              <p>{extractedSpecs.cpu.architecture || 'Unknown'}</p>
              <p className="text-sm text-muted-foreground">{extractedSpecs.cpu.generation}</p>
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Cores & Threads</h4>
              <p>{extractedSpecs.cpu.cores} cores, {extractedSpecs.cpu.threads} threads</p>
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Frequency</h4>
              <p>Base: {formatFrequency(extractedSpecs.cpu.baseFrequency)}</p>
              {extractedSpecs.cpu.boostFrequency && (
                <p className="text-sm text-muted-foreground">
                  Boost: {formatFrequency(extractedSpecs.cpu.boostFrequency)}
                </p>
              )}
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">TDP</h4>
              <p>{extractedSpecs.cpu.tdp}W</p>
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Cache</h4>
              {extractedSpecs.cpu.cache.l3 && <p>L3: {extractedSpecs.cpu.cache.l3} MB</p>}
              {extractedSpecs.cpu.cache.l2 && <p className="text-sm text-muted-foreground">L2: {extractedSpecs.cpu.cache.l2} KB</p>}
            </div>
          </div>
          
          {extractedSpecs.cpu.features && extractedSpecs.cpu.features.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium text-sm text-muted-foreground mb-2">Features</h4>
              <div className="flex flex-wrap gap-1">
                {extractedSpecs.cpu.features.map((feature, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {feature}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Memory Specifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HardDrive className="h-5 w-5" />
            Memory Specifications
            <Badge variant="outline" className={getConfidenceColor(confidence.memory)}>
              {confidence.memory}% confidence
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Capacity</h4>
              <p className="font-semibold">{formatMemory(extractedSpecs.memory.capacity)}</p>
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Type & Speed</h4>
              <p>{extractedSpecs.memory.type}</p>
              <p className="text-sm text-muted-foreground">{extractedSpecs.memory.speed} MHz</p>
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Configuration</h4>
              <p>{extractedSpecs.memory.channels}-channel</p>
              <p className="text-sm text-muted-foreground">
                {extractedSpecs.memory.slots.occupied}/{extractedSpecs.memory.slots.total} slots used
              </p>
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Form Factor</h4>
              <p>{extractedSpecs.memory.formFactor}</p>
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Upgradeability</h4>
              <Badge variant={extractedSpecs.memory.isUpgradeable ? "default" : "secondary"}>
                {extractedSpecs.memory.isUpgradeable ? "Upgradeable" : "Non-upgradeable"}
              </Badge>
              <p className="text-sm text-muted-foreground mt-1">
                Max: {formatMemory(extractedSpecs.memory.maxSupported)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Storage Specifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HardDrive className="h-5 w-5" />
            Storage Specifications
            <Badge variant="outline" className={getConfidenceColor(confidence.storage)}>
              {confidence.storage}% confidence
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {extractedSpecs.storage.map((storage, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium">Storage Device {index + 1}</h4>
                  <Badge variant="outline">{storage.type}</Badge>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <h5 className="font-medium text-sm text-muted-foreground">Capacity</h5>
                    <p className="font-semibold">{formatStorage(storage.capacity)}</p>
                  </div>
                  
                  <div>
                    <h5 className="font-medium text-sm text-muted-foreground">Interface</h5>
                    <p>{storage.interface}</p>
                  </div>
                  
                  <div>
                    <h5 className="font-medium text-sm text-muted-foreground">Form Factor</h5>
                    <p>{storage.formFactor}</p>
                  </div>
                  
                  <div>
                    <h5 className="font-medium text-sm text-muted-foreground">Performance</h5>
                    {storage.speed.read && <p className="text-sm">Read: {storage.speed.read} MB/s</p>}
                    {storage.speed.write && <p className="text-sm">Write: {storage.speed.write} MB/s</p>}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* GPU Specifications */}
      {extractedSpecs.gpu && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="h-5 w-5" />
              GPU Specifications
              <Badge variant="outline" className={getConfidenceColor(confidence.gpu)}>
                {confidence.gpu}% confidence
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Graphics Card</h4>
                <p className="font-semibold">{extractedSpecs.gpu.manufacturer} {extractedSpecs.gpu.model}</p>
                <Badge variant="outline" className="mt-1">{extractedSpecs.gpu.type}</Badge>
              </div>
              
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Architecture</h4>
                <p>{extractedSpecs.gpu.architecture || 'Unknown'}</p>
              </div>
              
              {extractedSpecs.gpu.vram && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">VRAM</h4>
                  <p>{extractedSpecs.gpu.vram.capacity} GB {extractedSpecs.gpu.vram.type}</p>
                </div>
              )}
              
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Compute Units</h4>
                <p>{extractedSpecs.gpu.computeUnits}</p>
              </div>
              
              {extractedSpecs.gpu.tdp && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">TDP</h4>
                  <p>{extractedSpecs.gpu.tdp}W</p>
                </div>
              )}
            </div>
            
            {extractedSpecs.gpu.features && extractedSpecs.gpu.features.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium text-sm text-muted-foreground mb-2">Features</h4>
                <div className="flex flex-wrap gap-1">
                  {extractedSpecs.gpu.features.map((feature, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Display Specifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            Display Specifications
            <Badge variant="outline" className={getConfidenceColor(confidence.display)}>
              {confidence.display}% confidence
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Size & Resolution</h4>
              <p className="font-semibold">{extractedSpecs.display.size}" {extractedSpecs.display.resolution.name}</p>
              <p className="text-sm text-muted-foreground">
                {extractedSpecs.display.resolution.width} × {extractedSpecs.display.resolution.height}
              </p>
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Panel Type</h4>
              <p>{extractedSpecs.display.panelType}</p>
              <p className="text-sm text-muted-foreground">{extractedSpecs.display.refreshRate} Hz</p>
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Brightness</h4>
              <p>{extractedSpecs.display.brightness} nits</p>
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Aspect Ratio</h4>
              <p>{extractedSpecs.display.aspectRatio}</p>
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Color Gamut</h4>
              {extractedSpecs.display.colorGamut.srgb && (
                <p className="text-sm">sRGB: {extractedSpecs.display.colorGamut.srgb}%</p>
              )}
              {extractedSpecs.display.colorGamut.dciP3 && (
                <p className="text-sm">DCI-P3: {extractedSpecs.display.colorGamut.dciP3}%</p>
              )}
            </div>
          </div>
          
          {extractedSpecs.display.features && extractedSpecs.display.features.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium text-sm text-muted-foreground mb-2">Features</h4>
              <div className="flex flex-wrap gap-1">
                {extractedSpecs.display.features.map((feature, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {feature}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Analysis Metadata */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Analysis Metadata
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Extraction Method</h4>
              <Badge variant="outline">{analysis.analysisMetadata.extractionMethod}</Badge>
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Source Quality</h4>
              <Badge variant={analysis.analysisMetadata.sourceQuality === 'high' ? 'default' : 'secondary'}>
                {analysis.analysisMetadata.sourceQuality}
              </Badge>
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Processing Time</h4>
              <p>{analysis.analysisMetadata.processingTime}ms</p>
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Version</h4>
              <p>{analysis.analysisMetadata.version}</p>
            </div>
          </div>
          
          {analysis.analysisMetadata.suggestions && analysis.analysisMetadata.suggestions.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium text-sm text-muted-foreground mb-2">Suggestions</h4>
              <ul className="text-sm space-y-1">
                {analysis.analysisMetadata.suggestions.map((suggestion, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-muted-foreground">•</span>
                    <span>{suggestion}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
