/**
 * Analysis Configuration Component
 * Allows users to configure analysis settings
 */

'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { Settings, Clock, Shield, Zap } from 'lucide-react'
import type { AnalysisConfig } from '../types'

interface AnalysisConfigProps {
  config: Partial<AnalysisConfig>
  onChange: (config: Partial<AnalysisConfig>) => void
}

export function AnalysisConfig({ config, onChange }: AnalysisConfigProps) {
  const updateExtractionMethods = (method: keyof AnalysisConfig['extractionMethods'], enabled: boolean) => {
    onChange({
      ...config,
      extractionMethods: {
        ...config.extractionMethods,
        [method]: enabled
      }
    })
  }

  const updateConfidenceThresholds = (threshold: keyof AnalysisConfig['confidenceThresholds'], value: number) => {
    onChange({
      ...config,
      confidenceThresholds: {
        ...config.confidenceThresholds,
        [threshold]: value
      }
    })
  }

  const updatePerformance = (setting: keyof AnalysisConfig['performance'], value: number | boolean) => {
    onChange({
      ...config,
      performance: {
        ...config.performance,
        [setting]: value
      }
    })
  }

  const updateValidation = (setting: keyof AnalysisConfig['validation'], value: boolean) => {
    onChange({
      ...config,
      validation: {
        ...config.validation,
        [setting]: value
      }
    })
  }

  return (
    <div className="space-y-6">
      {/* Extraction Methods */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Extraction Methods
          </CardTitle>
          <CardDescription>
            Configure which extraction methods to use for analyzing specifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="regex-extraction">Regular Expression Extraction</Label>
              <p className="text-sm text-muted-foreground">
                Use pattern matching to extract specifications from text
              </p>
            </div>
            <Switch
              id="regex-extraction"
              checked={config.extractionMethods?.regex ?? true}
              onCheckedChange={(checked) => updateExtractionMethods('regex', checked)}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="nlp-extraction">Natural Language Processing</Label>
              <p className="text-sm text-muted-foreground">
                Use AI to understand and extract specifications (experimental)
              </p>
            </div>
            <Switch
              id="nlp-extraction"
              checked={config.extractionMethods?.nlp ?? false}
              onCheckedChange={(checked) => updateExtractionMethods('nlp', checked)}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="structured-extraction">Structured Data Processing</Label>
              <p className="text-sm text-muted-foreground">
                Process pre-structured specification data (JSON, XML)
              </p>
            </div>
            <Switch
              id="structured-extraction"
              checked={config.extractionMethods?.structured ?? true}
              onCheckedChange={(checked) => updateExtractionMethods('structured', checked)}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="hybrid-extraction">Hybrid Analysis</Label>
              <p className="text-sm text-muted-foreground">
                Combine multiple methods for best results
              </p>
            </div>
            <Switch
              id="hybrid-extraction"
              checked={config.extractionMethods?.hybrid ?? true}
              onCheckedChange={(checked) => updateExtractionMethods('hybrid', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Confidence Thresholds */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Confidence Thresholds
          </CardTitle>
          <CardDescription>
            Set confidence levels for analysis quality assessment
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="minimum-confidence">Minimum Confidence</Label>
              <span className="text-sm text-muted-foreground">
                {config.confidenceThresholds?.minimum ?? 30}%
              </span>
            </div>
            <Slider
              id="minimum-confidence"
              min={0}
              max={100}
              step={5}
              value={[config.confidenceThresholds?.minimum ?? 30]}
              onValueChange={([value]) => updateConfidenceThresholds('minimum', value)}
              className="w-full"
            />
            <p className="text-xs text-muted-foreground">
              Results below this threshold will be flagged as unreliable
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="warning-confidence">Warning Threshold</Label>
              <span className="text-sm text-muted-foreground">
                {config.confidenceThresholds?.warning ?? 60}%
              </span>
            </div>
            <Slider
              id="warning-confidence"
              min={0}
              max={100}
              step={5}
              value={[config.confidenceThresholds?.warning ?? 60]}
              onValueChange={([value]) => updateConfidenceThresholds('warning', value)}
              className="w-full"
            />
            <p className="text-xs text-muted-foreground">
              Results below this threshold will show warnings
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="good-confidence">Good Quality Threshold</Label>
              <span className="text-sm text-muted-foreground">
                {config.confidenceThresholds?.good ?? 80}%
              </span>
            </div>
            <Slider
              id="good-confidence"
              min={0}
              max={100}
              step={5}
              value={[config.confidenceThresholds?.good ?? 80]}
              onValueChange={([value]) => updateConfidenceThresholds('good', value)}
              className="w-full"
            />
            <p className="text-xs text-muted-foreground">
              Results above this threshold are considered high quality
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Performance Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Performance Settings
          </CardTitle>
          <CardDescription>
            Configure analysis performance and timeout settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="timeout">Analysis Timeout (seconds)</Label>
            <Input
              id="timeout"
              type="number"
              min={5}
              max={300}
              value={(config.performance?.timeout ?? 30000) / 1000}
              onChange={(e) => updatePerformance('timeout', parseInt(e.target.value) * 1000)}
              className="w-full"
            />
            <p className="text-xs text-muted-foreground">
              Maximum time to wait for analysis completion
            </p>
          </div>

          <Separator />

          <div className="space-y-2">
            <Label htmlFor="max-retries">Maximum Retries</Label>
            <Input
              id="max-retries"
              type="number"
              min={0}
              max={10}
              value={config.performance?.maxRetries ?? 3}
              onChange={(e) => updatePerformance('maxRetries', parseInt(e.target.value))}
              className="w-full"
            />
            <p className="text-xs text-muted-foreground">
              Number of retry attempts on analysis failure
            </p>
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="cache-results">Cache Results</Label>
              <p className="text-sm text-muted-foreground">
                Cache analysis results to improve performance
              </p>
            </div>
            <Switch
              id="cache-results"
              checked={config.performance?.cacheResults ?? true}
              onCheckedChange={(checked) => updatePerformance('cacheResults', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Validation Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Validation Settings
          </CardTitle>
          <CardDescription>
            Configure validation and quality control settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="strict-mode">Strict Mode</Label>
              <p className="text-sm text-muted-foreground">
                Apply stricter validation rules for higher accuracy
              </p>
            </div>
            <Switch
              id="strict-mode"
              checked={config.validation?.strictMode ?? false}
              onCheckedChange={(checked) => updateValidation('strictMode', checked)}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="require-minimum-specs">Require Minimum Specifications</Label>
              <p className="text-sm text-muted-foreground">
                Ensure essential specifications (CPU, memory, storage) are present
              </p>
            </div>
            <Switch
              id="require-minimum-specs"
              checked={config.validation?.requireMinimumSpecs ?? true}
              onCheckedChange={(checked) => updateValidation('requireMinimumSpecs', checked)}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="validate-benchmarks">Validate Benchmark Scores</Label>
              <p className="text-sm text-muted-foreground">
                Cross-check extracted benchmark scores for consistency
              </p>
            </div>
            <Switch
              id="validate-benchmarks"
              checked={config.validation?.validateBenchmarks ?? false}
              onCheckedChange={(checked) => updateValidation('validateBenchmarks', checked)}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
