/**
 * Specifications Analyzer Component
 * Main component for analyzing laptop specifications
 */

'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CheckCircle, AlertTriangle, Info } from 'lucide-react'
import { useSpecsAnalysis, useAnalysisProgress } from '../hooks/use-specs-analysis'
import { SpecsAnalysisResult } from './specs-analysis-result'
import { PerformancePrediction } from './performance-prediction'
import { AnalysisConfig } from './analysis-config'
import type { AnalysisConfig as AnalysisConfigType } from '../types'

interface SpecsAnalyzerProps {
  laptopId?: string
  initialText?: string
  onAnalysisComplete?: (result: any) => void
}

export function SpecsAnalyzer({
  laptopId = 'temp-' + Date.now(),
  initialText = '',
  onAnalysisComplete
}: SpecsAnalyzerProps) {
  const [rawText, setRawText] = useState(initialText)
  const [structuredData, setStructuredData] = useState('')
  const [config, setConfig] = useState<Partial<AnalysisConfigType>>({})
  
  const { analyzeSpecs, isAnalyzing, analysisError, analysisResult } = useSpecsAnalysis()
  const { progress, updateProgress, resetProgress } = useAnalysisProgress()

  const handleAnalyze = async () => {
    if (!rawText.trim()) {
      return
    }

    resetProgress()
    updateProgress('extracting', 25, 'Extracting specifications...')

    try {
      let parsedStructuredData
      if (structuredData.trim()) {
        try {
          parsedStructuredData = JSON.parse(structuredData)
        } catch (e) {
          console.warn('Invalid JSON in structured data, ignoring')
        }
      }

      updateProgress('normalizing', 50, 'Normalizing data...')
      
      const result = await analyzeSpecs(
        laptopId,
        rawText,
        parsedStructuredData,
        config
      )

      updateProgress('predicting', 75, 'Generating predictions...')
      
      setTimeout(() => {
        updateProgress('complete', 100, 'Analysis complete!')
        onAnalysisComplete?.(result)
      }, 500)

    } catch (error) {
      updateProgress('idle', 0, 'Analysis failed')
      console.error('Analysis failed:', error)
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'bg-green-500'
    if (confidence >= 60) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const getConfidenceBadgeVariant = (confidence: number) => {
    if (confidence >= 80) return 'default'
    if (confidence >= 60) return 'secondary'
    return 'destructive'
  }

  return (
    <div className="space-y-6">
      {/* Input Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Specification Analysis
          </CardTitle>
          <CardDescription>
            Analyze laptop specifications from text or structured data to extract detailed hardware information and performance predictions.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="text" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="text">Raw Text</TabsTrigger>
              <TabsTrigger value="structured">Structured Data</TabsTrigger>
              <TabsTrigger value="config">Configuration</TabsTrigger>
            </TabsList>
            
            <TabsContent value="text" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="raw-text">Specification Text</Label>
                <Textarea
                  id="raw-text"
                  placeholder="Paste laptop specifications here... (e.g., Intel Core i7-13700H, 16GB DDR5, 512GB NVMe SSD, NVIDIA RTX 4060)"
                  value={rawText}
                  onChange={(e) => setRawText(e.target.value)}
                  rows={8}
                  className="min-h-[200px]"
                />
                <p className="text-sm text-muted-foreground">
                  Enter any text containing laptop specifications. The analyzer will extract CPU, memory, storage, GPU, and other hardware details.
                </p>
              </div>
            </TabsContent>
            
            <TabsContent value="structured" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="structured-data">Structured Data (JSON)</Label>
                <Textarea
                  id="structured-data"
                  placeholder='{"cpu": {"model": "i7-13700H"}, "memory": {"capacity": 16, "type": "DDR5"}}'
                  value={structuredData}
                  onChange={(e) => setStructuredData(e.target.value)}
                  rows={8}
                  className="min-h-[200px] font-mono"
                />
                <p className="text-sm text-muted-foreground">
                  Optional: Provide structured data in JSON format for more accurate analysis.
                </p>
              </div>
            </TabsContent>
            
            <TabsContent value="config">
              <AnalysisConfig
                config={config}
                onChange={setConfig}
              />
            </TabsContent>
          </Tabs>

          {/* Analysis Progress */}
          {progress.stage !== 'idle' && (
            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{progress.message}</span>
                <span className="text-sm text-muted-foreground">{progress.percentage}%</span>
              </div>
              <Progress value={progress.percentage} className="w-full" />
            </div>
          )}

          {/* Error Display */}
          {analysisError && (
            <Alert className="mt-4" variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Analysis failed: {analysisError.message}
              </AlertDescription>
            </Alert>
          )}

          {/* Action Button */}
          <div className="mt-6 flex justify-end">
            <Button
              onClick={handleAnalyze}
              disabled={!rawText.trim() || isAnalyzing}
              className="min-w-[120px]"
            >
              {isAnalyzing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Analyzing...
                </>
              ) : (
                'Analyze Specs'
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results Section */}
      {analysisResult && (
        <div className="space-y-6">
          {/* Analysis Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                Analysis Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {analysisResult.analysis?.confidence.overall || 0}%
                  </div>
                  <div className="text-sm text-muted-foreground">Overall Confidence</div>
                  <Badge 
                    variant={getConfidenceBadgeVariant(analysisResult.analysis?.confidence.overall || 0)}
                    className="mt-1"
                  >
                    {analysisResult.analysis?.confidence.overall >= 80 ? 'High' :
                     analysisResult.analysis?.confidence.overall >= 60 ? 'Medium' : 'Low'}
                  </Badge>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {analysisResult.analysis?.analysisMetadata.processingTime || 0}ms
                  </div>
                  <div className="text-sm text-muted-foreground">Processing Time</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {analysisResult.warnings?.length || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Warnings</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {analysisResult.analysis?.analysisMetadata.extractionMethod || 'N/A'}
                  </div>
                  <div className="text-sm text-muted-foreground">Method</div>
                </div>
              </div>

              {/* Confidence Breakdown */}
              {analysisResult.analysis?.confidence && (
                <div className="mt-6">
                  <h4 className="text-sm font-medium mb-3">Confidence by Component</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {Object.entries(analysisResult.analysis.confidence)
                      .filter(([key]) => key !== 'overall')
                      .map(([component, confidence]) => (
                        <div key={component} className="flex items-center justify-between p-2 bg-muted rounded">
                          <span className="text-sm capitalize">{component}</span>
                          <div className="flex items-center gap-2">
                            <div className={`w-2 h-2 rounded-full ${getConfidenceColor(confidence)}`} />
                            <span className="text-sm font-medium">{confidence}%</span>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              )}

              {/* Warnings */}
              {analysisResult.warnings && analysisResult.warnings.length > 0 && (
                <div className="mt-6">
                  <h4 className="text-sm font-medium mb-2">Warnings</h4>
                  <div className="space-y-1">
                    {analysisResult.warnings.map((warning, index) => (
                      <Alert key={index} variant="default" className="py-2">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription className="text-sm">
                          {warning}
                        </AlertDescription>
                      </Alert>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Detailed Results */}
          <Tabs defaultValue="specs" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="specs">Extracted Specifications</TabsTrigger>
              <TabsTrigger value="predictions">Performance Predictions</TabsTrigger>
            </TabsList>
            
            <TabsContent value="specs">
              <SpecsAnalysisResult analysis={analysisResult.analysis} />
            </TabsContent>
            
            <TabsContent value="predictions">
              <PerformancePrediction predictions={analysisResult.predictions} />
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  )
}
