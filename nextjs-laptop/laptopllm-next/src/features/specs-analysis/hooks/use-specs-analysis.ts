'use client'
/**
 * Specifications Analysis Hooks
 * React hooks for specification analysis functionality
 */

import { useState, useCallback, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import type {
  SpecificationAnalysis,
  SpecsAnalysisResult,
  AnalysisConfig,
  PerformancePrediction
} from '../types'
import { SpecsAnalysisService } from '../services/specs-analysis.service'

// Initialize service
const specsAnalysisService = new SpecsAnalysisService()

/**
 * Hook for analyzing laptop specifications
 */
export function useSpecsAnalysis() {
  const queryClient = useQueryClient()

  const analyzeMutation = useMutation({
    mutationFn: async ({
      laptopId,
      rawText,
      structuredData,
      config
    }: {
      laptopId: string
      rawText: string
      structuredData?: any
      config?: Partial<AnalysisConfig>
    }) => {
      return specsAnalysisService.analyzeSpecifications(
        laptopId,
        rawText,
        structuredData,
        config
      )
    },
    onSuccess: (data, variables) => {
      // Cache the analysis result
      queryClient.setQueryData(
        ['specs-analysis', variables.laptopId],
        data
      )
      
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['laptop-compatibility', variables.laptopId]
      })
    }
  })

  const analyzeSpecs = useCallback(
    (
      laptopId: string,
      rawText: string,
      structuredData?: any,
      config?: Partial<AnalysisConfig>
    ) => {
      return analyzeMutation.mutateAsync({
        laptopId,
        rawText,
        structuredData,
        config
      })
    },
    [analyzeMutation]
  )

  return {
    analyzeSpecs,
    isAnalyzing: analyzeMutation.isPending,
    analysisError: analyzeMutation.error,
    analysisResult: analyzeMutation.data
  }
}

/**
 * Hook for getting cached analysis results
 */
export function useSpecsAnalysisResult(laptopId: string) {
  return useQuery({
    queryKey: ['specs-analysis', laptopId],
    queryFn: async () => {
      // This would typically fetch from an API or database
      // For now, return null if not cached
      return null
    },
    enabled: !!laptopId,
    staleTime: 1000 * 60 * 30, // 30 minutes
    gcTime: 1000 * 60 * 60 * 2 // 2 hours
  })
}

/**
 * Hook for batch analysis of multiple laptops
 */
export function useBatchSpecsAnalysis() {
  const [progress, setProgress] = useState(0)
  const [currentLaptop, setCurrentLaptop] = useState<string>('')
  const [results, setResults] = useState<Map<string, SpecsAnalysisResult>>(new Map())

  const analyzeBatch = useCallback(
    async (
      laptops: Array<{
        id: string
        rawText: string
        structuredData?: any
      }>,
      config?: Partial<AnalysisConfig>
    ) => {
      setProgress(0)
      setResults(new Map())

      for (let i = 0; i < laptops.length; i++) {
        const laptop = laptops[i]
        setCurrentLaptop(laptop.id)
        setProgress((i / laptops.length) * 100)

        try {
          const result = await specsAnalysisService.analyzeSpecifications(
            laptop.id,
            laptop.rawText,
            laptop.structuredData,
            config
          )

          setResults(prev => new Map(prev).set(laptop.id, result))
        } catch (error) {
          console.error(`Failed to analyze laptop ${laptop.id}:`, error)
          setResults(prev => new Map(prev).set(laptop.id, {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            warnings: ['Analysis failed']
          }))
        }
      }

      setProgress(100)
      setCurrentLaptop('')
    },
    []
  )

  return {
    analyzeBatch,
    progress,
    currentLaptop,
    results: Array.from(results.entries()).map(([id, result]) => ({ id, result }))
  }
}

/**
 * Hook for performance predictions
 */
export function usePerformancePrediction(analysisResult?: SpecsAnalysisResult) {
  const [prediction, setPrediction] = useState<PerformancePrediction | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (analysisResult?.analysis?.extractedSpecs) {
      setIsLoading(true)
      
      // Simulate async prediction (in real app, this might be a separate API call)
      setTimeout(() => {
        setPrediction(analysisResult.predictions || null)
        setIsLoading(false)
      }, 500)
    }
  }, [analysisResult])

  return {
    prediction,
    isLoading
  }
}

/**
 * Hook for analysis configuration management
 */
export function useAnalysisConfig() {
  const [config, setConfig] = useState<AnalysisConfig>({
    extractionMethods: {
      regex: true,
      nlp: false,
      structured: true,
      hybrid: true
    },
    confidenceThresholds: {
      minimum: 30,
      warning: 60,
      good: 80
    },
    performance: {
      timeout: 30000,
      maxRetries: 3,
      cacheResults: true
    },
    validation: {
      strictMode: false,
      requireMinimumSpecs: true,
      validateBenchmarks: false
    }
  })

  const updateConfig = useCallback((updates: Partial<AnalysisConfig>) => {
    setConfig(prev => ({
      ...prev,
      ...updates,
      extractionMethods: {
        ...prev.extractionMethods,
        ...updates.extractionMethods
      },
      confidenceThresholds: {
        ...prev.confidenceThresholds,
        ...updates.confidenceThresholds
      },
      performance: {
        ...prev.performance,
        ...updates.performance
      },
      validation: {
        ...prev.validation,
        ...updates.validation
      }
    }))
  }, [])

  const resetConfig = useCallback(() => {
    setConfig({
      extractionMethods: {
        regex: true,
        nlp: false,
        structured: true,
        hybrid: true
      },
      confidenceThresholds: {
        minimum: 30,
        warning: 60,
        good: 80
      },
      performance: {
        timeout: 30000,
        maxRetries: 3,
        cacheResults: true
      },
      validation: {
        strictMode: false,
        requireMinimumSpecs: true,
        validateBenchmarks: false
      }
    })
  }, [])

  return {
    config,
    updateConfig,
    resetConfig
  }
}

/**
 * Hook for analysis statistics
 */
export function useAnalysisStats() {
  return useQuery({
    queryKey: ['specs-analysis-stats'],
    queryFn: async () => {
      return specsAnalysisService.getAnalysisStats()
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchInterval: 1000 * 60 * 5 // Refetch every 5 minutes
  })
}

/**
 * Hook for real-time analysis progress
 */
export function useAnalysisProgress() {
  const [progress, setProgress] = useState({
    stage: 'idle' as 'idle' | 'extracting' | 'normalizing' | 'predicting' | 'complete',
    percentage: 0,
    message: ''
  })

  const updateProgress = useCallback((
    stage: typeof progress.stage,
    percentage: number,
    message: string
  ) => {
    setProgress({ stage, percentage, message })
  }, [])

  const resetProgress = useCallback(() => {
    setProgress({
      stage: 'idle',
      percentage: 0,
      message: ''
    })
  }, [])

  return {
    progress,
    updateProgress,
    resetProgress
  }
}

/**
 * Hook for comparing analysis results
 */
export function useAnalysisComparison() {
  const [comparisons, setComparisons] = useState<SpecificationAnalysis[]>([])

  const addToComparison = useCallback((analysis: SpecificationAnalysis) => {
    setComparisons(prev => {
      if (prev.find(a => a.id === analysis.id)) {
        return prev // Already in comparison
      }
      return [...prev, analysis].slice(-5) // Keep max 5 items
    })
  }, [])

  const removeFromComparison = useCallback((analysisId: string) => {
    setComparisons(prev => prev.filter(a => a.id !== analysisId))
  }, [])

  const clearComparison = useCallback(() => {
    setComparisons([])
  }, [])

  const compareSpecs = useCallback(() => {
    if (comparisons.length < 2) return null

    // Generate comparison data
    const comparison = {
      laptops: comparisons.map(analysis => ({
        id: analysis.laptopId,
        specs: analysis.extractedSpecs,
        confidence: analysis.confidence
      })),
      differences: {
        cpu: comparisons.map(a => a.extractedSpecs.cpu),
        memory: comparisons.map(a => a.extractedSpecs.memory),
        storage: comparisons.map(a => a.extractedSpecs.storage),
        gpu: comparisons.map(a => a.extractedSpecs.gpu).filter(Boolean)
      }
    }

    return comparison
  }, [comparisons])

  return {
    comparisons,
    addToComparison,
    removeFromComparison,
    clearComparison,
    compareSpecs,
    canCompare: comparisons.length >= 2
  }
}

/**
 * Hook for analysis validation
 */
export function useAnalysisValidation() {
  const validateAnalysis = useCallback((analysis: SpecificationAnalysis) => {
    const issues: string[] = []
    const warnings: string[] = []

    // Check confidence levels
    if (analysis.confidence.overall < 50) {
      issues.push('Overall confidence is very low')
    } else if (analysis.confidence.overall < 70) {
      warnings.push('Overall confidence could be improved')
    }

    // Check for missing critical specs
    if (!analysis.extractedSpecs.cpu.model) {
      issues.push('CPU model not identified')
    }

    if (analysis.extractedSpecs.memory.capacity < 8) {
      warnings.push('Memory capacity seems low for modern laptops')
    }

    if (!analysis.extractedSpecs.storage || analysis.extractedSpecs.storage.length === 0) {
      issues.push('No storage devices identified')
    }

    return {
      isValid: issues.length === 0,
      issues,
      warnings,
      score: Math.max(0, 100 - (issues.length * 20) - (warnings.length * 5))
    }
  }, [])

  return {
    validateAnalysis
  }
}