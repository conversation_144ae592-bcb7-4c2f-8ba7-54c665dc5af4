/**
 * Specifications Normalizer
 * Normalizes and validates extracted specifications
 */

import type { ExtractedSpecifications, NormalizationRule, ExtractedCPU, ExtractedMemory, ExtractedStorage, ExtractedGPU, ExtractedDisplay } from '../types'

export class SpecsNormalizer {
  private readonly normalizationRules: NormalizationRule[] = [
    {
      id: 'cpu-manufacturer',
      component: 'cpu',
      field: 'manufacturer',
      rules: {
        aliases: {
          'intel': 'Intel',
          'amd': 'AMD',
          'apple': 'Apple',
          'qualcomm': 'Qualcomm'
        },
        ranges: { min: 0, max: 0 },
        defaults: 'Unknown',
        validation: (value) => typeof value === 'string' && value.length > 0
      }
    },
    {
      id: 'memory-type',
      component: 'memory',
      field: 'type',
      rules: {
        aliases: {
          'ddr4': 'DDR4',
          'ddr5': 'DDR5',
          'lpddr4': 'LPDDR4',
          'lpddr5': 'LPDDR5'
        },
        ranges: { min: 0, max: 0 },
        defaults: 'DDR4',
        validation: (value) => ['DDR4', 'DDR5', 'LPDDR4', 'LPDDR5', 'Unknown'].includes(value)
      }
    }
  ]

  /**
   * Normalize extracted specifications
   */
  async normalize(specs: ExtractedSpecifications): Promise<ExtractedSpecifications> {
    const normalized = { ...specs }

    // Normalize CPU specifications
    if (normalized.cpu) {
      normalized.cpu = this.normalizeCPU(normalized.cpu)
    }

    // Normalize memory specifications
    if (normalized.memory) {
      normalized.memory = this.normalizeMemory(normalized.memory)
    }

    // Normalize storage specifications
    if (normalized.storage) {
      normalized.storage = normalized.storage.map(storage => this.normalizeStorage(storage))
    }

    // Normalize GPU specifications
    if (normalized.gpu) {
      normalized.gpu = this.normalizeGPU(normalized.gpu)
    }

    // Normalize display specifications
    if (normalized.display) {
      normalized.display = this.normalizeDisplay(normalized.display)
    }

    // Cross-validate specifications
    this.crossValidate(normalized)

    return normalized
  }

  /**
   * Normalize CPU specifications
   */
  private normalizeCPU(cpu: ExtractedCPU): ExtractedCPU {
    const normalized = { ...cpu }

    // Normalize manufacturer
    if (normalized.manufacturer) {
      normalized.manufacturer = this.applyAliases(normalized.manufacturer, {
        'intel': 'Intel',
        'amd': 'AMD',
        'apple': 'Apple',
        'qualcomm': 'Qualcomm'
      })
    }

    // Validate core count
    if (normalized.cores < 1 || normalized.cores > 64) {
      normalized.cores = 4
    }

    // Validate thread count
    if (normalized.threads < normalized.cores) {
      normalized.threads = normalized.cores * 2
    }

    // Validate frequencies
    if (normalized.baseFrequency < 0.5 || normalized.baseFrequency > 6) {
      normalized.baseFrequency = 2.5
    }

    if (normalized.boostFrequency && normalized.boostFrequency <= normalized.baseFrequency) {
      normalized.boostFrequency = normalized.baseFrequency + 1
    }

    // Validate TDP
    if (normalized.tdp < 5 || normalized.tdp > 200) {
      normalized.tdp = 15
    }

    return normalized
  }

  /**
   * Normalize memory specifications
   */
  private normalizeMemory(memory: ExtractedMemory): ExtractedMemory {
    const normalized = { ...memory }

    // Normalize type
    if (normalized.type) {
      normalized.type = this.applyAliases(normalized.type.toUpperCase(), {
        'DDR4': 'DDR4',
        'DDR5': 'DDR5',
        'LPDDR4': 'LPDDR4',
        'LPDDR5': 'LPDDR5',
        'LPDDR4X': 'LPDDR4',
        'LPDDR5X': 'LPDDR5'
      })
    }

    // Validate capacity
    if (normalized.capacity < 2 || normalized.capacity > 256) {
      normalized.capacity = 8
    }

    // Validate speed based on type
    if (normalized.type === 'DDR5') {
      if (normalized.speed < 4800 || normalized.speed > 8000) {
        normalized.speed = 4800
      }
    } else if (normalized.type === 'DDR4') {
      if (normalized.speed < 2133 || normalized.speed > 4000) {
        normalized.speed = 2400
      }
    }

    // Validate channels
    if (normalized.channels < 1 || normalized.channels > 4) {
      normalized.channels = 2
    }

    return normalized
  }

  /**
   * Normalize storage specifications
   */
  private normalizeStorage(storage: ExtractedStorage): ExtractedStorage {
    const normalized = { ...storage }

    // Normalize type
    if (normalized.type) {
      normalized.type = this.applyAliases(normalized.type, {
        'nvme': 'NVMe SSD',
        'ssd': 'SATA SSD',
        'hdd': 'HDD',
        'emmc': 'eMMC'
      })
    }

    // Validate capacity
    if (normalized.capacity < 32 || normalized.capacity > 8192) {
      normalized.capacity = 512
    }

    // Normalize interface based on type
    if (normalized.type === 'NVMe SSD' && !normalized.interface.includes('PCIe')) {
      normalized.interface = 'PCIe 4.0'
    } else if (normalized.type === 'SATA SSD' && !normalized.interface.includes('SATA')) {
      normalized.interface = 'SATA III'
    }

    // Validate speeds
    if (normalized.speed.read > 10000) normalized.speed.read = 10000
    if (normalized.speed.write > 10000) normalized.speed.write = 10000

    return normalized
  }

  /**
   * Normalize GPU specifications
   */
  private normalizeGPU(gpu: ExtractedGPU): ExtractedGPU {
    const normalized = { ...gpu }

    // Normalize manufacturer
    if (normalized.manufacturer) {
      normalized.manufacturer = this.applyAliases(normalized.manufacturer, {
        'nvidia': 'NVIDIA',
        'amd': 'AMD',
        'intel': 'Intel',
        'apple': 'Apple'
      })
    }

    // Normalize type
    if (normalized.type) {
      normalized.type = this.applyAliases(normalized.type, {
        'integrated': 'Integrated',
        'discrete': 'Discrete',
        'hybrid': 'Hybrid'
      })
    }

    // Validate VRAM capacity
    if (normalized.vram && normalized.vram.capacity) {
      if (normalized.vram.capacity < 1 || normalized.vram.capacity > 24) {
        normalized.vram.capacity = 4
      }
    }

    return normalized
  }

  /**
   * Normalize display specifications
   */
  private normalizeDisplay(display: ExtractedDisplay): ExtractedDisplay {
    const normalized = { ...display }

    // Validate size
    if (normalized.size < 10 || normalized.size > 20) {
      normalized.size = 15.6
    }

    // Normalize resolution name
    if (normalized.resolution) {
      const { width, height } = normalized.resolution
      if (width === 1920 && height === 1080) {
        normalized.resolution.name = 'Full HD'
      } else if (width === 2560 && height === 1440) {
        normalized.resolution.name = 'QHD'
      } else if (width === 3840 && height === 2160) {
        normalized.resolution.name = '4K UHD'
      }
    }

    // Validate refresh rate
    if (normalized.refreshRate < 60 || normalized.refreshRate > 240) {
      normalized.refreshRate = 60
    }

    // Validate brightness
    if (normalized.brightness < 200 || normalized.brightness > 1000) {
      normalized.brightness = 300
    }

    return normalized
  }

  /**
   * Cross-validate specifications for consistency
   */
  private crossValidate(specs: ExtractedSpecifications): void {
    // Validate CPU-GPU compatibility
    if (specs.cpu && specs.gpu) {
      // If CPU has integrated graphics, ensure GPU type is consistent
      if (specs.cpu.integratedGraphics && specs.gpu.type === 'Discrete') {
        // This is fine - laptop can have both integrated and discrete
      }
    }

    // Validate memory-CPU compatibility
    if (specs.cpu && specs.memory) {
      // Check if memory type is compatible with CPU generation
      if (specs.cpu.manufacturer === 'Intel' && specs.cpu.generation?.includes('12th')) {
        // 12th gen Intel supports both DDR4 and DDR5
      } else if (specs.cpu.manufacturer === 'AMD' && specs.cpu.generation?.includes('7000')) {
        // 7000 series AMD primarily uses DDR5
        if (specs.memory.type === 'DDR4') {
          // This might be unusual but not impossible
        }
      }
    }

    // Validate storage-performance expectations
    if (specs.storage && specs.storage.length > 0) {
      const primaryStorage = specs.storage[0]
      if (primaryStorage.type === 'HDD' && primaryStorage.capacity < 500) {
        // Small HDD is unusual, might be an error
        primaryStorage.capacity = 1024 // 1TB default for HDD
      }
    }

    // Validate display-GPU compatibility
    if (specs.display && specs.gpu) {
      const { width, height } = specs.display.resolution
      const pixelCount = width * height
      
      if (pixelCount > 2073600 && specs.gpu.type === 'Integrated') {
        // High resolution with integrated graphics might struggle
        // This is just a warning, not an error
      }
    }
  }

  /**
   * Apply alias mappings to normalize values
   */
  private applyAliases(value: string, aliases: Record<string, string>): string {
    const lowerValue = value.toLowerCase().trim()
    return aliases[lowerValue] || value
  }

  /**
   * Validate value against rules
   */
  private validateValue(value: unknown, rule: NormalizationRule['rules']): boolean {
    if (!rule.validation(value)) {
      return false
    }

    if (typeof value === 'number') {
      return value >= rule.ranges.min && value <= rule.ranges.max
    }

    return true
  }

  /**
   * Get confidence score for normalized specifications
   */
  getConfidenceScore(original: ExtractedSpecifications, normalized: ExtractedSpecifications): number {
    let totalChanges = 0
    let totalFields = 0

    // Compare CPU fields
    if (original.cpu && normalized.cpu) {
      const cpuChanges = this.countChanges(original.cpu, normalized.cpu)
      totalChanges += cpuChanges.changes
      totalFields += cpuChanges.total
    }

    // Compare memory fields
    if (original.memory && normalized.memory) {
      const memoryChanges = this.countChanges(original.memory, normalized.memory)
      totalChanges += memoryChanges.changes
      totalFields += memoryChanges.total
    }

    // Calculate confidence (fewer changes = higher confidence)
    if (totalFields === 0) return 100
    const changeRatio = totalChanges / totalFields
    return Math.max(0, Math.round((1 - changeRatio) * 100))
  }

  /**
   * Count changes between original and normalized objects
   */
  private countChanges(original: Record<string, unknown>, normalized: Record<string, unknown>): { changes: number; total: number } {
    let changes = 0
    let total = 0

    for (const key in original) {
      if (original.hasOwnProperty(key)) {
        total++
        if (original[key] !== normalized[key]) {
          changes++
        }
      }
    }

    return { changes, total }
  }
}
