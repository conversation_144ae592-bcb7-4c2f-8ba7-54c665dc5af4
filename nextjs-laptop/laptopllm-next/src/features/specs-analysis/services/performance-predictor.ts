/**
 * Performance Predictor
 * Predicts laptop performance for LLM workloads and general computing
 */

import type {
  ExtractedSpecifications,
  ExtractedGPU,
  ExtractedStorage,
  PerformancePrediction,
  LLMCompatibilityPrediction,
  GeneralPerformancePrediction,
  PowerEfficiencyPrediction,
  ThermalPerformancePrediction,
  RecommendedLLMModel
} from '../types'

interface LLMModel {
  name: string;
  size: string;
  memoryReq: number;
  computeReq: string;
}

export class PerformancePredictor {
  private readonly llmModels = [
    { name: 'Llama 2 7B', size: '7B', memoryReq: 8, computeReq: 'medium' },
    { name: 'Llama 2 13B', size: '13B', memoryReq: 16, computeReq: 'high' },
    { name: 'Llama 2 70B', size: '70B', memoryReq: 64, computeReq: 'extreme' },
    { name: 'Mistral 7B', size: '7B', memoryReq: 8, computeReq: 'medium' },
    { name: 'CodeLlama 7B', size: '7B', memoryReq: 8, computeReq: 'medium' },
    { name: 'CodeLlama 13B', size: '13B', memoryReq: 16, computeReq: 'high' },
    { name: 'Phi-2', size: '2.7B', memoryReq: 4, computeReq: 'low' },
    { name: 'Gemma 7B', size: '7B', memoryReq: 8, computeReq: 'medium' }
  ]

  /**
   * Predict performance for given specifications
   */
  async predict(specs: ExtractedSpecifications): Promise<PerformancePrediction> {
    const llmCompatibility = this.predictLLMCompatibility(specs)
    const generalPerformance = this.predictGeneralPerformance(specs)
    const powerEfficiency = this.predictPowerEfficiency(specs)
    const thermalPerformance = this.predictThermalPerformance(specs)

    return {
      llmCompatibility,
      generalPerformance,
      powerEfficiency,
      thermalPerformance
    }
  }

  /**
   * Predict LLM compatibility and performance
   */
  private predictLLMCompatibility(specs: ExtractedSpecifications): LLMCompatibilityPrediction {
    const cpuScore = this.calculateCPUScore(specs.cpu)
    const memoryScore = this.calculateMemoryScore(specs.memory)
    const gpuScore = specs.gpu ? this.calculateGPUScore(specs.gpu) : 0
    const storageScore = this.calculateStorageScore(specs.storage)

    // Weighted overall score for LLM workloads
    const overallScore = Math.round(
      cpuScore * 0.35 +
      memoryScore * 0.40 +
      gpuScore * 0.15 +
      storageScore * 0.10
    )

    const recommendedModels = this.getRecommendedModels(specs)
    const limitations = this.identifyLimitations(specs)
    const optimizations = this.suggestOptimizations(specs)

    const performance = {
      tokensPerSecond: {
        small: this.estimateTokensPerSecond(specs, '7B'),
        medium: this.estimateTokensPerSecond(specs, '13B'),
        large: this.estimateTokensPerSecond(specs, '70B')
      },
      maxContextLength: {
        small: this.estimateMaxContext(specs, '7B'),
        medium: this.estimateMaxContext(specs, '13B'),
        large: this.estimateMaxContext(specs, '70B')
      },
      concurrentSessions: this.estimateConcurrentSessions(specs),
      memoryEfficiency: this.calculateMemoryEfficiency(specs)
    }

    return {
      overallScore,
      recommendedModels,
      limitations,
      optimizations,
      performance
    }
  }

  /**
   * Calculate CPU score for LLM workloads
   */
  private calculateCPUScore(cpu: ExtractedSpecifications['cpu']): number {
    let score = 0

    // Base score from core count and frequency
    score += Math.min(cpu.cores * 8, 64) // Max 64 points from cores
    score += Math.min(cpu.baseFrequency * 10, 30) // Max 30 points from frequency

    // Bonus for modern architectures
    if (cpu.manufacturer === 'Apple' && cpu.model?.includes('M')) {
      score += 20 // Apple Silicon bonus
    } else if (cpu.manufacturer === 'Intel' && cpu.generation?.includes('13th')) {
      score += 15 // Latest Intel bonus
    } else if (cpu.manufacturer === 'AMD' && cpu.generation?.includes('7000')) {
      score += 15 // Latest AMD bonus
    }

    // Bonus for high thread count
    if (cpu.threads >= 16) score += 10

    return Math.min(score, 100)
  }

  /**
   * Calculate memory score for LLM workloads
   */
  private calculateMemoryScore(memory: ExtractedSpecifications['memory']): number {
    let score = 0

    // Base score from capacity (most important for LLMs)
    if (memory.capacity >= 64) score += 50
    else if (memory.capacity >= 32) score += 40
    else if (memory.capacity >= 16) score += 30
    else if (memory.capacity >= 8) score += 20
    else score += 10

    // Bonus for memory type and speed
    if (memory.type === 'DDR5') score += 15
    else if (memory.type === 'LPDDR5') score += 12
    else if (memory.type === 'DDR4') score += 8

    // Bonus for high speed
    if (memory.speed >= 5600) score += 10
    else if (memory.speed >= 4800) score += 8
    else if (memory.speed >= 3200) score += 5

    // Bonus for dual channel
    if (memory.channels >= 2) score += 10

    // Penalty for non-upgradeable memory with low capacity
    if (!memory.isUpgradeable && memory.capacity < 16) score -= 15

    return Math.min(score, 100)
  }

  /**
   * Calculate GPU score for LLM workloads
   */
  private calculateGPUScore(gpu: ExtractedGPU): number {
    let score = 0

    if (gpu.type === 'Discrete') {
      // Discrete GPU scoring
      if (gpu.vram?.capacity >= 12) score += 40
      else if (gpu.vram?.capacity >= 8) score += 30
      else if (gpu.vram?.capacity >= 6) score += 20
      else score += 10

      // Bonus for modern architectures
      if (gpu.manufacturer === 'NVIDIA') {
        if (gpu.architecture?.includes('Ada Lovelace')) score += 20
        else if (gpu.architecture?.includes('Ampere')) score += 15
      } else if (gpu.manufacturer === 'AMD') {
        if (gpu.architecture?.includes('RDNA 3')) score += 18
        else if (gpu.architecture?.includes('RDNA 2')) score += 12
      }
    } else {
      // Integrated GPU scoring
      if (gpu.manufacturer === 'Apple') score += 25 // Apple Silicon integrated
      else if (gpu.manufacturer === 'AMD') score += 15 // AMD APU
      else if (gpu.manufacturer === 'Intel') score += 10 // Intel Xe
    }

    return Math.min(score, 100)
  }

  /**
   * Calculate storage score for LLM workloads
   */
  private calculateStorageScore(storage: ExtractedStorage[]): number {
    if (!storage || storage.length === 0) return 20

    const primaryStorage = storage[0]
    let score = 0

    // Base score from type
    if (primaryStorage.type === 'NVMe SSD') score += 40
    else if (primaryStorage.type === 'SATA SSD') score += 30
    else if (primaryStorage.type === 'HDD') score += 10

    // Bonus for capacity
    if (primaryStorage.capacity >= 1024) score += 30
    else if (primaryStorage.capacity >= 512) score += 20
    else if (primaryStorage.capacity >= 256) score += 10

    // Bonus for fast interface
    if (primaryStorage.interface === 'PCIe 4.0') score += 20
    else if (primaryStorage.interface === 'PCIe 3.0') score += 15

    // Bonus for high speeds
    if (primaryStorage.speed?.read >= 5000) score += 10
    else if (primaryStorage.speed?.read >= 3000) score += 5

    return Math.min(score, 100)
  }

  /**
   * Get recommended LLM models based on specifications
   */
  private getRecommendedModels(specs: ExtractedSpecifications): RecommendedLLMModel[] {
    const recommendations: RecommendedLLMModel[] = []
    const availableMemory = specs.memory.capacity

    for (const model of this.llmModels) {
      if (availableMemory >= model.memoryReq) {
        const performance = this.estimateTokensPerSecond(specs, model.size)
        const memoryUsage = this.estimateMemoryUsage(model.size)
        const confidence = this.calculateModelConfidence(specs, model)

        recommendations.push({
          name: model.name,
          size: model.size,
          quantization: this.suggestQuantization(specs, model),
          estimatedPerformance: performance,
          memoryUsage,
          confidence,
          notes: this.generateModelNotes(specs, model)
        })
      }
    }

    return recommendations.sort((a, b) => b.confidence - a.confidence).slice(0, 5)
  }

  /**
   * Estimate tokens per second for a model size
   */
  private estimateTokensPerSecond(specs: ExtractedSpecifications, modelSize: string): number {
    const cpuFactor = specs.cpu.cores * specs.cpu.baseFrequency * 0.5
    const memoryFactor = specs.memory.speed / 1000
    const gpuFactor = specs.gpu ? (specs.gpu.type === 'Discrete' ? 2 : 1) : 1

    let baseTPS = cpuFactor * memoryFactor * gpuFactor

    // Adjust for model size
    if (modelSize.includes('70B')) baseTPS *= 0.1
    else if (modelSize.includes('13B')) baseTPS *= 0.3
    else if (modelSize.includes('7B')) baseTPS *= 0.6
    else if (modelSize.includes('2.7B')) baseTPS *= 1.0

    return Math.round(Math.max(baseTPS, 1))
  }

  /**
   * Estimate maximum context length
   */
  private estimateMaxContext(specs: ExtractedSpecifications, modelSize: string): number {
    const availableMemory = specs.memory.capacity * 0.8 // 80% usable

    let baseContext = 2048
    if (modelSize.includes('70B')) baseContext = Math.min(availableMemory * 50, 4096)
    else if (modelSize.includes('13B')) baseContext = Math.min(availableMemory * 100, 8192)
    else if (modelSize.includes('7B')) baseContext = Math.min(availableMemory * 200, 16384)
    else if (modelSize.includes('2.7B')) baseContext = Math.min(availableMemory * 400, 32768)

    return Math.round(baseContext)
  }

  /**
   * Estimate concurrent sessions
   */
  private estimateConcurrentSessions(specs: ExtractedSpecifications): number {
    const memoryFactor = Math.floor(specs.memory.capacity / 8)
    const cpuFactor = Math.floor(specs.cpu.cores / 4)
    return Math.max(1, Math.min(memoryFactor, cpuFactor))
  }

  /**
   * Calculate memory efficiency
   */
  private calculateMemoryEfficiency(specs: ExtractedSpecifications): number {
    let efficiency = 0.5

    if (specs.memory.type === 'DDR5' || specs.memory.type === 'LPDDR5') efficiency += 0.2
    if (specs.memory.channels >= 2) efficiency += 0.1
    if (specs.memory.speed >= 4800) efficiency += 0.1
    if (specs.cpu.manufacturer === 'Apple') efficiency += 0.1

    return Math.min(efficiency, 1.0)
  }

  /**
   * Predict general performance
   */
  private predictGeneralPerformance(specs: ExtractedSpecifications): GeneralPerformancePrediction {
    const cpuScore = this.calculateCPUScore(specs.cpu)
    const gpuScore = specs.gpu ? this.calculateGPUScore(specs.gpu) : 30
    const memoryScore = this.calculateMemoryScore(specs.memory)
    const storageScore = this.calculateStorageScore(specs.storage)

    const overallScore = Math.round((cpuScore + gpuScore + memoryScore + storageScore) / 4)

    return {
      cpuScore,
      gpuScore,
      memoryScore,
      storageScore,
      overallScore,
      useCases: this.determineUseCases(overallScore),
      benchmarkEstimates: {
        cinebench: Math.round(cpuScore * 100),
        geekbench: Math.round(cpuScore * 80),
        pcmark: Math.round(overallScore * 50),
        gaming: Math.round(gpuScore * 30)
      }
    }
  }

  /**
   * Predict power efficiency
   */
  private predictPowerEfficiency(specs: ExtractedSpecifications): PowerEfficiencyPrediction {
    const basePower = specs.cpu.tdp + (specs.gpu?.tdp || 0)
    
    return {
      batteryLife: {
        office: Math.round(60 / basePower * 10),
        video: Math.round(60 / basePower * 8),
        gaming: Math.round(60 / basePower * 3),
        llmInference: Math.round(60 / basePower * 5)
      },
      powerConsumption: {
        idle: Math.round(basePower * 0.1),
        typical: Math.round(basePower * 0.6),
        peak: basePower
      },
      efficiencyRating: Math.round(100 - (basePower / 2))
    }
  }

  /**
   * Predict thermal performance
   */
  private predictThermalPerformance(specs: ExtractedSpecifications): ThermalPerformancePrediction {
    const totalTDP = specs.cpu.tdp + (specs.gpu?.tdp || 0)
    const thermalRating = Math.max(0, 100 - totalTDP)

    return {
      thermalRating,
      sustainedPerformance: Math.max(60, 100 - totalTDP / 2),
      throttlingRisk: totalTDP > 80 ? 'High' : totalTDP > 50 ? 'Medium' : 'Low',
      coolingAdequacy: thermalRating,
      noiseLevel: {
        idle: Math.round(totalTDP * 0.2 + 25),
        load: Math.round(totalTDP * 0.4 + 35)
      }
    }
  }

  /**
   * Helper methods
   */
  private identifyLimitations(specs: ExtractedSpecifications): string[] {
    const limitations: string[] = []

    if (specs.memory.capacity < 16) {
      limitations.push('Limited memory capacity may restrict large model usage')
    }

    if (!specs.gpu || specs.gpu.type === 'Integrated') {
      limitations.push('No discrete GPU limits acceleration capabilities')
    }

    if (specs.storage[0]?.type === 'HDD') {
      limitations.push('HDD storage may cause slow model loading times')
    }

    return limitations
  }

  private suggestOptimizations(specs: ExtractedSpecifications): string[] {
    const optimizations: string[] = []

    if (specs.memory.isUpgradeable && specs.memory.capacity < 32) {
      optimizations.push('Consider upgrading memory to 32GB or more')
    }

    if (specs.storage[0]?.type !== 'NVMe SSD') {
      optimizations.push('Upgrade to NVMe SSD for faster model loading')
    }

    optimizations.push('Use quantized models to reduce memory usage')
    optimizations.push('Enable CPU optimizations like AVX2/AVX-512')

    return optimizations
  }

  private estimateMemoryUsage(modelSize: string): number {
    if (modelSize.includes('70B')) return 40
    if (modelSize.includes('13B')) return 8
    if (modelSize.includes('7B')) return 4
    if (modelSize.includes('2.7B')) return 2
    return 4
  }

  private calculateModelConfidence(specs: ExtractedSpecifications, model: LLMModel): number {
    let confidence = 50

    if (specs.memory.capacity >= model.memoryReq * 2) confidence += 30
    else if (specs.memory.capacity >= model.memoryReq * 1.5) confidence += 20
    else if (specs.memory.capacity >= model.memoryReq) confidence += 10

    if (specs.cpu.cores >= 8) confidence += 10
    if (specs.gpu && specs.gpu.type === 'Discrete') confidence += 10

    return Math.min(confidence, 100)
  }

  private suggestQuantization(specs: ExtractedSpecifications, model: LLMModel): string {
    if (specs.memory.capacity >= model.memoryReq * 2) return 'FP16'
    if (specs.memory.capacity >= model.memoryReq * 1.5) return 'INT8'
    return 'INT4'
  }

  private generateModelNotes(specs: ExtractedSpecifications, model: LLMModel): string[] {
    const notes: string[] = []

    if (specs.memory.capacity < model.memoryReq * 1.5) {
      notes.push('Consider using quantized version')
    }

    if (model.computeReq === 'extreme' && specs.cpu.cores < 16) {
      notes.push('May require significant processing time')
    }

    return notes
  }

  private determineUseCases(score: number): string[] {
    const useCases: string[] = []

    if (score >= 80) {
      useCases.push('Professional AI/ML development', 'Large model inference', 'Content creation')
    } else if (score >= 60) {
      useCases.push('Medium model inference', 'Programming assistance', 'Research')
    } else if (score >= 40) {
      useCases.push('Small model inference', 'Basic AI tasks', 'Learning')
    } else {
      useCases.push('Very basic AI tasks', 'Educational use')
    }

    return useCases
  }
}
