/**
 * Specifications Analysis Service
 * Automated technical specification analysis and LLM compatibility prediction
 */

import { BaseService } from '@/lib/services/base.service'
import type {
  SpecificationAnalysis,
  ExtractedSpecifications,
  AnalysisConfidence,
  AnalysisMetadata,
  SpecsAnalysisResult,
  AnalysisConfig,
  PerformancePrediction
} from '../types'
import { CPUAnalyzer } from './analyzers/cpu-analyzer'
import { MemoryAnalyzer } from './analyzers/memory-analyzer'
import { StorageAnalyzer } from './analyzers/storage-analyzer'
// import { GPUAnalyzer } from './analyzers/gpu-analyzer'
// import { DisplayAnalyzer } from './analyzers/display-analyzer'
// import { ConnectivityAnalyzer } from './analyzers/connectivity-analyzer'
// import { PhysicalAnalyzer } from './analyzers/physical-analyzer'
// import { BatteryAnalyzer } from './analyzers/battery-analyzer'
// import { ThermalAnalyzer } from './analyzers/thermal-analyzer'
// import { PerformancePredictor } from './performance-predictor'
// import { SpecsNormalizer } from './specs-normalizer'
import { v4 as uuidv4 } from 'uuid'

export class SpecsAnalysisService extends BaseService {
  private readonly cpuAnalyzer: CPUAnalyzer
  private readonly memoryAnalyzer: MemoryAnalyzer
  private readonly storageAnalyzer: StorageAnalyzer
  // private readonly gpuAnalyzer: GPUAnalyzer
  // private readonly displayAnalyzer: DisplayAnalyzer
  // private readonly connectivityAnalyzer: ConnectivityAnalyzer
  // private readonly physicalAnalyzer: PhysicalAnalyzer
  // private readonly batteryAnalyzer: BatteryAnalyzer
  // private readonly thermalAnalyzer: ThermalAnalyzer
  private readonly performancePredictor: PerformancePredictor
  private readonly specsNormalizer: SpecsNormalizer

  private readonly defaultConfig: AnalysisConfig = {
    extractionMethods: {
      regex: true,
      nlp: false,
      structured: true,
      hybrid: true
    },
    confidenceThresholds: {
      minimum: 30,
      warning: 60,
      good: 80
    },
    performance: {
      timeout: 30000,
      maxRetries: 3,
      cacheResults: true
    },
    validation: {
      strictMode: false,
      requireMinimumSpecs: true,
      validateBenchmarks: false
    }
  }

  constructor() {
    super()

    // Initialize component analyzers
    this.cpuAnalyzer = new CPUAnalyzer()
    this.memoryAnalyzer = new MemoryAnalyzer()
    this.storageAnalyzer = new StorageAnalyzer()
    // this.gpuAnalyzer = new GPUAnalyzer()
    // this.displayAnalyzer = new DisplayAnalyzer()
    // this.connectivityAnalyzer = new ConnectivityAnalyzer()
    // this.physicalAnalyzer = new PhysicalAnalyzer()
    // this.batteryAnalyzer = new BatteryAnalyzer()
    // this.thermalAnalyzer = new ThermalAnalyzer()

    // Initialize prediction and normalization services
    this.performancePredictor = new PerformancePredictor()
    this.specsNormalizer = new SpecsNormalizer()
  }

  /**
   * Analyze laptop specifications from raw text or structured data
   */
  async analyzeSpecifications(
    laptopId: string,
    rawText: string,
    structuredData?: any,
    config: Partial<AnalysisConfig> = {}
  ): Promise<SpecsAnalysisResult> {
    const startTime = Date.now()
    const analysisConfig = { ...this.defaultConfig, ...config }

    try {
      await this.logActivity('info', 'Starting specification analysis', laptopId)

      // Extract specifications from raw text and structured data
      const extractedSpecs = await this.extractSpecifications(
        rawText,
        structuredData,
        analysisConfig
      )

      // Calculate confidence scores
      const confidence = this.calculateConfidence(extractedSpecs)

      // Normalize and validate extracted specifications
      const normalizedSpecs = await this.specsNormalizer.normalize(extractedSpecs)

      // Create analysis metadata
      const metadata: AnalysisMetadata = {
        extractionMethod: this.determineExtractionMethod(analysisConfig),
        sourceQuality: this.assessSourceQuality(rawText, structuredData),
        processingTime: Date.now() - startTime,
        warnings: this.generateWarnings(extractedSpecs, confidence),
        suggestions: this.generateSuggestions(extractedSpecs, confidence),
        version: '1.0.0'
      }

      // Create specification analysis result
      const analysis: SpecificationAnalysis = {
        id: uuidv4(),
        laptopId,
        rawText,
        extractedSpecs: normalizedSpecs,
        confidence,
        analysisMetadata: metadata,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // Generate performance predictions
      const predictions = await this.performancePredictor.predict(normalizedSpecs)

      await this.logActivity('info', 'Specification analysis completed', laptopId, {
        confidence: confidence.overall,
        processingTime: metadata.processingTime
      })

      return {
        success: true,
        analysis,
        predictions,
        warnings: metadata.warnings
      }

    } catch (error) {
      await this.logActivity('error', `Specification analysis failed: ${error}`, laptopId)
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        warnings: ['Analysis failed due to unexpected error']
      }
    }
  }

  /**
   * Extract specifications using multiple analysis methods
   */
  private async extractSpecifications(
    rawText: string,
    structuredData?: any,
    config: AnalysisConfig = this.defaultConfig
  ): Promise<ExtractedSpecifications> {
    const specs: Partial<ExtractedSpecifications> = {}

    // Extract CPU specifications
    const cpuResult = await this.cpuAnalyzer.analyze(rawText, structuredData?.cpu)
    specs.cpu = cpuResult

    // Extract memory specifications
    const memoryResult = await this.memoryAnalyzer.analyze(rawText, structuredData?.memory)
    specs.memory = memoryResult

    // Extract storage specifications
    const storageResult = await this.storageAnalyzer.analyze(rawText, structuredData?.storage)
    specs.storage = storageResult

    // TODO: Extract GPU specifications (optional)
    // specs.gpu = await this.gpuAnalyzer.analyze(rawText, structuredData?.gpu)

    // TODO: Extract display specifications
    // specs.display = await this.displayAnalyzer.analyze(rawText, structuredData?.display)

    // Provide default display specs for now
    specs.display = {
      size: 15.6,
      resolution: { width: 1920, height: 1080, name: 'Full HD' },
      panelType: 'IPS',
      refreshRate: 60,
      brightness: 300,
      aspectRatio: '16:9',
      coating: 'Matte',
      colorGamut: { srgb: 100 },
      features: []
    }

    // TODO: Extract connectivity specifications
    // specs.connectivity = await this.connectivityAnalyzer.analyze(rawText, structuredData?.connectivity)

    // Provide default connectivity specs for now
    specs.connectivity = {
      wifi: { standard: 'Wi-Fi 6', speed: 1200, bands: ['2.4GHz', '5GHz'] },
      bluetooth: { version: '5.0' },
      ethernet: { speed: 1000, type: 'RJ45' },
      ports: {
        usb: {
          usbA: 2,
          usbC: 1,
          thunderbolt: 0,
          usb2: 0,
          usb3: 2,
          usb4: 0
        },
        display: {
          hdmi: 1,
          displayPort: 0,
          vga: 0,
          dvi: 0
        },
        audio: {
          headphone: 1,
          microphone: 0,
          combo: 1
        },
        other: {
          sdCard: true,
          ethernet: true,
          kensington: true
        }
      }
    }

    // TODO: Extract physical specifications
    // specs.physical = await this.physicalAnalyzer.analyze(rawText, structuredData?.physical)

    // Provide default physical specs for now
    specs.physical = {
      dimensions: { width: 360, depth: 240, height: 20 },
      weight: 2.0,
      material: { lid: 'Plastic', base: 'Plastic', keyboard: 'Plastic' },
      color: 'Black',
      build: {
        hingeType: 'Standard',
        keyboardType: 'Scissor',
        trackpadSize: 'Medium',
        webcamResolution: '720p',
        speakers: 'Stereo'
      }
    }

    // TODO: Extract battery specifications (optional)
    // specs.battery = await this.batteryAnalyzer.analyze(rawText, structuredData?.battery)

    // Provide default battery specs for now
    specs.battery = {
      capacity: 50,
      cells: 3,
      type: 'Li-ion',
      removable: false,
      fastCharging: { supported: true, wattage: 65 },
      estimatedLife: { typical: 8, video: 6, web: 7, idle: 10 }
    }

    // TODO: Extract thermal specifications
    // specs.thermal = await this.thermalAnalyzer.analyze(rawText, structuredData?.thermal)

    // Provide default thermal specs for now
    specs.thermal = {
      coolingType: 'Active',
      fans: 2,
      heatPipes: 2,
      thermalDesign: 'Dual Fan',
      maxTdp: 45,
      thermalThrottling: {
        cpuThreshold: 85,
        gpuThreshold: 80
      },
      noiseLevel: { idle: 25, load: 40 }
    }

    return specs as ExtractedSpecifications
  }

  /**
   * Calculate confidence scores for extracted specifications
   */
  private calculateConfidence(specs: ExtractedSpecifications): AnalysisConfidence {
    const cpuConfidence = this.calculateComponentConfidence(specs.cpu)
    const memoryConfidence = this.calculateComponentConfidence(specs.memory)
    const storageConfidence = this.calculateComponentConfidence(specs.storage)
    const gpuConfidence = specs.gpu ? this.calculateComponentConfidence(specs.gpu) : 0
    const displayConfidence = this.calculateComponentConfidence(specs.display)
    const connectivityConfidence = this.calculateComponentConfidence(specs.connectivity)
    const physicalConfidence = this.calculateComponentConfidence(specs.physical)
    const batteryConfidence = specs.battery ? this.calculateComponentConfidence(specs.battery) : 0
    const thermalConfidence = this.calculateComponentConfidence(specs.thermal)

    // Calculate weighted overall confidence
    const weights = {
      cpu: 0.25,
      memory: 0.20,
      storage: 0.15,
      gpu: 0.15,
      display: 0.10,
      connectivity: 0.05,
      physical: 0.05,
      battery: 0.03,
      thermal: 0.02
    }

    const overall = Math.round(
      cpuConfidence * weights.cpu +
      memoryConfidence * weights.memory +
      storageConfidence * weights.storage +
      gpuConfidence * weights.gpu +
      displayConfidence * weights.display +
      connectivityConfidence * weights.connectivity +
      physicalConfidence * weights.physical +
      batteryConfidence * weights.battery +
      thermalConfidence * weights.thermal
    )

    return {
      overall,
      cpu: cpuConfidence,
      memory: memoryConfidence,
      storage: storageConfidence,
      gpu: gpuConfidence,
      display: displayConfidence,
      connectivity: connectivityConfidence,
      physical: physicalConfidence,
      battery: batteryConfidence,
      thermal: thermalConfidence
    }
  }

  /**
   * Calculate confidence for a specific component
   */
  private calculateComponentConfidence(component: unknown): number {
    if (!component) return 0

    let score = 0
    let maxScore = 0

    // Count filled vs empty fields
    for (const [key, value] of Object.entries(component)) {
      maxScore += 10
      
      if (value !== null && value !== undefined && value !== '' && value !== 'Unknown') {
        if (typeof value === 'object' && !Array.isArray(value)) {
          // Nested object - check if it has meaningful data
          const hasData = Object.values(value).some(v => 
            v !== null && v !== undefined && v !== '' && v !== 'Unknown'
          )
          if (hasData) score += 10
        } else if (Array.isArray(value)) {
          // Array - check if it has items
          if (value.length > 0) score += 10
        } else {
          // Primitive value
          score += 10
        }
      }
    }

    return maxScore > 0 ? Math.round((score / maxScore) * 100) : 0
  }

  /**
   * Determine the primary extraction method used
   */
  private determineExtractionMethod(config: AnalysisConfig): AnalysisMetadata['extractionMethod'] {
    if (config.extractionMethods.hybrid) return 'hybrid'
    if (config.extractionMethods.structured) return 'structured'
    if (config.extractionMethods.nlp) return 'nlp'
    return 'regex'
  }

  /**
   * Assess the quality of source data
   */
  private assessSourceQuality(rawText: string, structuredData?: any): AnalysisMetadata['sourceQuality'] {
    let score = 0

    // Check raw text quality
    if (rawText && rawText.length > 100) score += 1
    if (rawText && rawText.length > 500) score += 1
    if (rawText && /specification|spec|cpu|processor|memory|ram|storage|gpu|graphics/i.test(rawText)) score += 1

    // Check structured data quality
    if (structuredData) {
      score += 1
      const keys = Object.keys(structuredData)
      if (keys.length > 3) score += 1
      if (keys.some(key => ['cpu', 'memory', 'storage', 'gpu'].includes(key.toLowerCase()))) score += 1
    }

    if (score >= 5) return 'high'
    if (score >= 3) return 'medium'
    return 'low'
  }

  /**
   * Generate warnings based on analysis results
   */
  private generateWarnings(specs: ExtractedSpecifications, confidence: AnalysisConfidence): string[] {
    const warnings: string[] = []

    if (confidence.overall < this.defaultConfig.confidenceThresholds.warning) {
      warnings.push(`Low overall confidence (${confidence.overall}%) - results may be inaccurate`)
    }

    if (confidence.cpu < this.defaultConfig.confidenceThresholds.minimum) {
      warnings.push('CPU specifications could not be reliably extracted')
    }

    if (confidence.memory < this.defaultConfig.confidenceThresholds.minimum) {
      warnings.push('Memory specifications could not be reliably extracted')
    }

    if (!specs.gpu || confidence.gpu < this.defaultConfig.confidenceThresholds.minimum) {
      warnings.push('GPU specifications missing or unreliable - may impact LLM performance predictions')
    }

    return warnings
  }

  /**
   * Generate suggestions for improving analysis
   */
  private generateSuggestions(specs: ExtractedSpecifications, confidence: AnalysisConfidence): string[] {
    const suggestions: string[] = []

    if (confidence.overall < this.defaultConfig.confidenceThresholds.good) {
      suggestions.push('Consider providing more detailed specification data for better analysis')
    }

    if (!specs.gpu) {
      suggestions.push('GPU information would improve LLM compatibility predictions')
    }

    if (!specs.battery) {
      suggestions.push('Battery specifications would enable power efficiency analysis')
    }

    return suggestions
  }

  /**
   * Get analysis statistics
   */
  async getAnalysisStats(): Promise<{
    totalAnalyses: number
    averageConfidence: number
    successRate: number
    commonIssues: string[]
  }> {
    // This would typically query a database
    // For now, return mock data
    return {
      totalAnalyses: 0,
      averageConfidence: 0,
      successRate: 0,
      commonIssues: []
    }
  }

  /**
   * Validate analysis configuration
   */
  validateConfig(config: Partial<AnalysisConfig>): boolean {
    // Validate extraction methods
    if (config.extractionMethods) {
      const methods = Object.values(config.extractionMethods)
      if (!methods.some(enabled => enabled)) {
        throw new Error('At least one extraction method must be enabled')
      }
    }

    // Validate confidence thresholds
    if (config.confidenceThresholds) {
      const { minimum, warning, good } = config.confidenceThresholds
      if (minimum < 0 || minimum > 100 || warning < 0 || warning > 100 || good < 0 || good > 100) {
        throw new Error('Confidence thresholds must be between 0 and 100')
      }
      if (minimum > warning || warning > good) {
        throw new Error('Confidence thresholds must be in ascending order')
      }
    }

    return true
  }
}
