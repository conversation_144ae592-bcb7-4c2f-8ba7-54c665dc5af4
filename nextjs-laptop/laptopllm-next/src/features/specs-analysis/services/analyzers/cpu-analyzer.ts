/**
 * CPU Analyzer
 * Extracts and analyzes CPU specifications from text and structured data
 */

import type { ExtractedCPU, ComponentAnalysisResult } from '../../types'

export class CPUAnalyzer {
  private readonly cpuPatterns = {
    // Intel patterns
    intel: {
      coreI: /Intel\s+Core\s+(i[3579])-?(\d{4,5}[A-Z]*)/gi,
      xeon: /Intel\s+Xeon\s+([A-Z0-9-]+)/gi,
      celeron: /Intel\s+Celeron\s+([A-Z0-9-]+)/gi,
      pentium: /Intel\s+Pentium\s+([A-Z0-9-]+)/gi,
      atom: /Intel\s+Atom\s+([A-Z0-9-]+)/gi
    },
    // AMD patterns
    amd: {
      ryzen: /AMD\s+Ryzen\s+([3579])\s+(\d{4}[A-Z]*)/gi,
      threadripper: /AMD\s+Ryzen\s+Threadripper\s+([A-Z0-9]+)/gi,
      epyc: /AMD\s+EPYC\s+([A-Z0-9-]+)/gi,
      athlon: /AMD\s+Athlon\s+([A-Z0-9-]+)/gi,
      fx: /AMD\s+FX-?(\d{4})/gi
    },
    // Apple patterns
    apple: {
      m1: /Apple\s+M1(\s+Pro|\s+Max|\s+Ultra)?/gi,
      m2: /Apple\s+M2(\s+Pro|\s+Max|\s+Ultra)?/gi,
      m3: /Apple\s+M3(\s+Pro|\s+Max|\s+Ultra)?/gi
    },
    // Qualcomm patterns
    qualcomm: {
      snapdragon: /Qualcomm\s+Snapdragon\s+([0-9X]+)/gi
    },
    // General patterns
    general: {
      cores: /(\d+)[-\s]*(core|cores)/gi,
      threads: /(\d+)[-\s]*(thread|threads)/gi,
      frequency: /(\d+\.?\d*)\s*(GHz|MHz)/gi,
      cache: /(\d+)\s*(MB|KB)\s*(cache|L[123])/gi,
      tdp: /(\d+)\s*W\s*(TDP|tdp)/gi
    }
  }

  private readonly cpuDatabase = {
    intel: {
      generations: {
        '13': 'Raptor Lake',
        '12': 'Alder Lake',
        '11': 'Tiger Lake',
        '10': 'Ice Lake',
        '9': 'Coffee Lake',
        '8': 'Kaby Lake',
        '7': 'Skylake',
        '6': 'Skylake'
      },
      suffixes: {
        'H': { type: 'High Performance', tdp: 45 },
        'HX': { type: 'Extreme Performance', tdp: 55 },
        'HK': { type: 'Unlocked High Performance', tdp: 45 },
        'U': { type: 'Ultra Low Power', tdp: 15 },
        'P': { type: 'Performance', tdp: 28 },
        'G': { type: 'Graphics', tdp: 15 },
        'K': { type: 'Unlocked', tdp: 125 },
        'F': { type: 'No Graphics', tdp: 65 },
        'T': { type: 'Low Power', tdp: 35 }
      }
    },
    amd: {
      generations: {
        '7': 'Zen 4',
        '6': 'Zen 3+',
        '5': 'Zen 3',
        '4': 'Zen 2',
        '3': 'Zen+',
        '2': 'Zen'
      },
      suffixes: {
        'H': { type: 'High Performance', tdp: 45 },
        'HS': { type: 'High Performance Slim', tdp: 35 },
        'HX': { type: 'Extreme Performance', tdp: 55 },
        'U': { type: 'Ultra Low Power', tdp: 15 },
        'G': { type: 'Graphics', tdp: 15 },
        'X': { type: 'Extreme', tdp: 105 }
      }
    },
    apple: {
      m1: { cores: 8, architecture: 'Apple Silicon', process: 'TSMC 5nm' },
      m1_pro: { cores: 10, architecture: 'Apple Silicon', process: 'TSMC 5nm' },
      m1_max: { cores: 10, architecture: 'Apple Silicon', process: 'TSMC 5nm' },
      m1_ultra: { cores: 20, architecture: 'Apple Silicon', process: 'TSMC 5nm' },
      m2: { cores: 8, architecture: 'Apple Silicon', process: 'TSMC 5nm' },
      m2_pro: { cores: 12, architecture: 'Apple Silicon', process: 'TSMC 5nm' },
      m2_max: { cores: 12, architecture: 'Apple Silicon', process: 'TSMC 5nm' },
      m2_ultra: { cores: 24, architecture: 'Apple Silicon', process: 'TSMC 5nm' },
      m3: { cores: 8, architecture: 'Apple Silicon', process: 'TSMC 3nm' },
      m3_pro: { cores: 12, architecture: 'Apple Silicon', process: 'TSMC 3nm' },
      m3_max: { cores: 16, architecture: 'Apple Silicon', process: 'TSMC 3nm' }
    }
  }

  /**
   * Analyze CPU specifications from text and structured data
   */
  async analyze(rawText: string, structuredData?: Record<string, unknown>): Promise<ExtractedCPU> {
    const cpu: Partial<ExtractedCPU> = {
      manufacturer: 'Unknown',
      family: '',
      model: '',
      generation: '',
      cores: 4,
      threads: 8,
      baseFrequency: 2.5,
      cache: {},
      architecture: '',
      process: '',
      tdp: 15,
      features: []
    }

    // Try structured data first
    if (structuredData) {
      this.extractFromStructuredData(cpu, structuredData)
    }

    // Extract from raw text
    this.extractFromRawText(cpu, rawText)

    // Enhance with database information
    this.enhanceWithDatabase(cpu)

    // Validate and set defaults
    this.validateAndSetDefaults(cpu)

    return cpu as ExtractedCPU
  }

  /**
   * Extract CPU information from structured data
   */
  private extractFromStructuredData(cpu: Partial<ExtractedCPU>, data: Record<string, unknown>): void {
    if (data.manufacturer) cpu.manufacturer = this.normalizeManufacturer(data.manufacturer)
    if (data.model) cpu.model = data.model
    if (data.family) cpu.family = data.family
    if (data.cores) cpu.cores = parseInt(data.cores)
    if (data.threads) cpu.threads = parseInt(data.threads)
    if (data.baseFrequency) cpu.baseFrequency = parseFloat(data.baseFrequency)
    if (data.boostFrequency) cpu.boostFrequency = parseFloat(data.boostFrequency)
    if (data.tdp) cpu.tdp = parseInt(data.tdp)
    if (data.architecture) cpu.architecture = data.architecture
    if (data.cache) cpu.cache = data.cache
  }

  /**
   * Extract CPU information from raw text
   */
  private extractFromRawText(cpu: Partial<ExtractedCPU>, text: string): void {
    // Detect manufacturer and extract model
    this.detectIntelCPU(cpu, text)
    this.detectAMDCPU(cpu, text)
    this.detectAppleCPU(cpu, text)
    this.detectQualcommCPU(cpu, text)

    // Extract general specifications
    this.extractCores(cpu, text)
    this.extractThreads(cpu, text)
    this.extractFrequency(cpu, text)
    this.extractCache(cpu, text)
    this.extractTDP(cpu, text)
    this.extractFeatures(cpu, text)
  }

  /**
   * Detect Intel CPU
   */
  private detectIntelCPU(cpu: Partial<ExtractedCPU>, text: string): void {
    // Core i series
    const coreMatch = text.match(this.cpuPatterns.intel.coreI)
    if (coreMatch) {
      cpu.manufacturer = 'Intel'
      const fullMatch = coreMatch[0]
      const parts = fullMatch.match(/Core\s+(i[3579])-?(\d{4,5}[A-Z]*)/)
      if (parts) {
        cpu.family = `Core ${parts[1]}`
        cpu.model = parts[2]
        cpu.generation = this.getIntelGeneration(parts[2])
      }
      return
    }

    // Other Intel processors
    const xeonMatch = text.match(this.cpuPatterns.intel.xeon)
    if (xeonMatch) {
      cpu.manufacturer = 'Intel'
      cpu.family = 'Xeon'
      cpu.model = xeonMatch[1]
      return
    }

    const celeronMatch = text.match(this.cpuPatterns.intel.celeron)
    if (celeronMatch) {
      cpu.manufacturer = 'Intel'
      cpu.family = 'Celeron'
      cpu.model = celeronMatch[1]
      return
    }
  }

  /**
   * Detect AMD CPU
   */
  private detectAMDCPU(cpu: Partial<ExtractedCPU>, text: string): void {
    // Ryzen series
    const ryzenMatch = text.match(this.cpuPatterns.amd.ryzen)
    if (ryzenMatch) {
      cpu.manufacturer = 'AMD'
      const fullMatch = ryzenMatch[0]
      const parts = fullMatch.match(/Ryzen\s+([3579])\s+(\d{4}[A-Z]*)/)
      if (parts) {
        cpu.family = `Ryzen ${parts[1]}`
        cpu.model = parts[2]
        cpu.generation = this.getAMDGeneration(parts[2])
      }
      return
    }

    // Threadripper
    const threadripperMatch = text.match(this.cpuPatterns.amd.threadripper)
    if (threadripperMatch) {
      cpu.manufacturer = 'AMD'
      cpu.family = 'Ryzen Threadripper'
      cpu.model = threadripperMatch[1]
      return
    }
  }

  /**
   * Detect Apple CPU
   */
  private detectAppleCPU(cpu: Partial<ExtractedCPU>, text: string): void {
    const appleMatch = text.match(this.cpuPatterns.apple.m1) || 
                      text.match(this.cpuPatterns.apple.m2) ||
                      text.match(this.cpuPatterns.apple.m3)
    
    if (appleMatch) {
      cpu.manufacturer = 'Apple'
      const model = appleMatch[0].replace('Apple ', '')
      cpu.family = model.split(' ')[0] // M1, M2, M3
      cpu.model = model
      cpu.architecture = 'Apple Silicon'
    }
  }

  /**
   * Detect Qualcomm CPU
   */
  private detectQualcommCPU(cpu: Partial<ExtractedCPU>, text: string): void {
    const qualcommMatch = text.match(this.cpuPatterns.qualcomm.snapdragon)
    if (qualcommMatch) {
      cpu.manufacturer = 'Qualcomm'
      cpu.family = 'Snapdragon'
      cpu.model = qualcommMatch[1]
      cpu.architecture = 'ARM64'
    }
  }

  /**
   * Extract core count
   */
  private extractCores(cpu: Partial<ExtractedCPU>, text: string): void {
    const coreMatch = text.match(this.cpuPatterns.general.cores)
    if (coreMatch) {
      cpu.cores = parseInt(coreMatch[1])
    }
  }

  /**
   * Extract thread count
   */
  private extractThreads(cpu: Partial<ExtractedCPU>, text: string): void {
    const threadMatch = text.match(this.cpuPatterns.general.threads)
    if (threadMatch) {
      cpu.threads = parseInt(threadMatch[1])
    } else if (cpu.cores && !cpu.threads) {
      // Estimate threads (most modern CPUs have 2 threads per core)
      cpu.threads = cpu.cores * 2
    }
  }

  /**
   * Extract frequency information
   */
  private extractFrequency(cpu: Partial<ExtractedCPU>, text: string): void {
    const freqMatches = Array.from(text.matchAll(this.cpuPatterns.general.frequency))
    
    if (freqMatches.length > 0) {
      const frequencies = freqMatches.map(match => {
        const value = parseFloat(match[1])
        const unit = match[2].toLowerCase()
        return unit === 'mhz' ? value / 1000 : value
      }).sort((a, b) => a - b)

      cpu.baseFrequency = frequencies[0]
      if (frequencies.length > 1) {
        cpu.boostFrequency = frequencies[frequencies.length - 1]
      }
    }
  }

  /**
   * Extract cache information
   */
  private extractCache(cpu: Partial<ExtractedCPU>, text: string): void {
    const cacheMatches = Array.from(text.matchAll(this.cpuPatterns.general.cache))
    
    if (!cpu.cache) cpu.cache = {}

    for (const match of cacheMatches) {
      const size = parseInt(match[1])
      const unit = match[2].toLowerCase()
      const type = match[3].toLowerCase()

      const sizeInKB = unit === 'mb' ? size * 1024 : size

      if (type.includes('l3')) {
        cpu.cache.l3 = unit === 'mb' ? size : size / 1024
      } else if (type.includes('l2')) {
        cpu.cache.l2 = sizeInKB
      } else if (type.includes('l1')) {
        cpu.cache.l1 = sizeInKB
      }
    }
  }

  /**
   * Extract TDP information
   */
  private extractTDP(cpu: Partial<ExtractedCPU>, text: string): void {
    const tdpMatch = text.match(this.cpuPatterns.general.tdp)
    if (tdpMatch) {
      cpu.tdp = parseInt(tdpMatch[1])
    }
  }

  /**
   * Extract CPU features
   */
  private extractFeatures(cpu: Partial<ExtractedCPU>, text: string): void {
    const features: string[] = []

    if (/hyper[-\s]?threading/i.test(text)) features.push('Hyper-Threading')
    if (/turbo\s+boost/i.test(text)) features.push('Turbo Boost')
    if (/precision\s+boost/i.test(text)) features.push('Precision Boost')
    if (/avx/i.test(text)) features.push('AVX')
    if (/sse/i.test(text)) features.push('SSE')
    if (/virtualization/i.test(text)) features.push('Virtualization')

    cpu.features = features
  }

  /**
   * Enhance CPU data with database information
   */
  private enhanceWithDatabase(cpu: Partial<ExtractedCPU>): void {
    if (cpu.manufacturer === 'Intel' && cpu.model) {
      this.enhanceIntelCPU(cpu)
    } else if (cpu.manufacturer === 'AMD' && cpu.model) {
      this.enhanceAMDCPU(cpu)
    } else if (cpu.manufacturer === 'Apple' && cpu.model) {
      this.enhanceAppleCPU(cpu)
    }
  }

  /**
   * Enhance Intel CPU with database info
   */
  private enhanceIntelCPU(cpu: Partial<ExtractedCPU>): void {
    if (!cpu.model) return

    const generation = this.getIntelGeneration(cpu.model)
    if (generation) {
      cpu.generation = `${generation}th Gen`
      cpu.architecture = this.cpuDatabase.intel.generations[generation] || 'Unknown'
    }

    const suffix = cpu.model.slice(-1)
    const suffixInfo = this.cpuDatabase.intel.suffixes[suffix]
    if (suffixInfo && !cpu.tdp) {
      cpu.tdp = suffixInfo.tdp
    }
  }

  /**
   * Enhance AMD CPU with database info
   */
  private enhanceAMDCPU(cpu: Partial<ExtractedCPU>): void {
    if (!cpu.model) return

    const generation = this.getAMDGeneration(cpu.model)
    if (generation) {
      cpu.generation = `${generation}000 Series`
      cpu.architecture = this.cpuDatabase.amd.generations[generation] || 'Unknown'
    }

    const suffix = cpu.model.slice(-2)
    const suffixInfo = this.cpuDatabase.amd.suffixes[suffix]
    if (suffixInfo && !cpu.tdp) {
      cpu.tdp = suffixInfo.tdp
    }
  }

  /**
   * Enhance Apple CPU with database info
   */
  private enhanceAppleCPU(cpu: Partial<ExtractedCPU>): void {
    if (!cpu.model) return

    const modelKey = cpu.model.toLowerCase().replace(/\s+/g, '_')
    const cpuInfo = this.cpuDatabase.apple[modelKey as keyof typeof this.cpuDatabase.apple]
    
    if (cpuInfo) {
      cpu.cores = cpuInfo.cores
      cpu.architecture = cpuInfo.architecture
      cpu.process = cpuInfo.process
      cpu.threads = cpuInfo.cores // Apple Silicon doesn't use SMT
    }
  }

  /**
   * Get Intel generation from model number
   */
  private getIntelGeneration(model: string): string {
    const match = model.match(/^(\d{1,2})/)
    return match ? match[1] : ''
  }

  /**
   * Get AMD generation from model number
   */
  private getAMDGeneration(model: string): string {
    const match = model.match(/^(\d)/)
    return match ? match[1] : ''
  }

  /**
   * Normalize manufacturer name
   */
  private normalizeManufacturer(manufacturer: string): ExtractedCPU['manufacturer'] {
    const normalized = manufacturer.toLowerCase().trim()
    if (normalized.includes('intel')) return 'Intel'
    if (normalized.includes('amd')) return 'AMD'
    if (normalized.includes('apple')) return 'Apple'
    if (normalized.includes('qualcomm')) return 'Qualcomm'
    return 'Unknown'
  }

  /**
   * Validate and set default values
   */
  private validateAndSetDefaults(cpu: Partial<ExtractedCPU>): void {
    // Set reasonable defaults
    if (!cpu.cores || cpu.cores < 1) cpu.cores = 4
    if (!cpu.threads || cpu.threads < cpu.cores) cpu.threads = cpu.cores * 2
    if (!cpu.baseFrequency || cpu.baseFrequency < 0.5) cpu.baseFrequency = 2.5
    if (!cpu.tdp || cpu.tdp < 5) cpu.tdp = 15
    if (!cpu.features) cpu.features = []
    if (!cpu.cache) cpu.cache = {}

    // Validate ranges
    if (cpu.cores > 64) cpu.cores = 64
    if (cpu.threads > 128) cpu.threads = 128
    if (cpu.baseFrequency > 6) cpu.baseFrequency = 6
    if (cpu.boostFrequency && cpu.boostFrequency > 8) cpu.boostFrequency = 8
    if (cpu.tdp > 200) cpu.tdp = 200
  }
}
