/**
 * Memory Analyzer
 * Extracts and analyzes memory specifications from text and structured data
 */

import type { ExtractedMemory } from '../../types'

export class MemoryAnalyzer {
  private readonly memoryPatterns = {
    capacity: [
      /(\d+)\s*GB\s*(RAM|Memory|DDR\d+)/gi,
      /(\d+)\s*GB\s*(LPDDR\d+)/gi,
      /Memory:\s*(\d+)\s*GB/gi,
      /RAM:\s*(\d+)\s*GB/gi
    ],
    type: [
      /(DDR4|DDR5|LPDDR4|LPDDR5|LPDDR4X|LPDDR5X)/gi,
      /(\d+)\s*GB\s*(DDR\d+|LPDDR\d+)/gi
    ],
    speed: [
      /(\d{4,5})\s*MHz/gi,
      /(DDR\d+)-(\d{4,5})/gi,
      /(\d+)\s*GB\s*DDR\d+[/-](\d{4,5})/gi
    ],
    slots: [
      /(\d+)\s*x\s*(\d+)\s*GB/gi,
      /(\d+)\s*slot/gi,
      /(\d+)\s*DIMM/gi,
      /(\d+)\s*SO-DIMM/gi
    ],
    upgradeable: [
      /upgradeable/gi,
      /expandable/gi,
      /user[- ]replaceable/gi,
      /soldered/gi,
      /non[- ]upgradeable/gi,
      /fixed/gi
    ],
    formFactor: [
      /SO-DIMM/gi,
      /DIMM/gi,
      /soldered/gi,
      /on[- ]board/gi
    ]
  }

  private readonly memoryDatabase = {
    ddr4: {
      standardSpeeds: [2133, 2400, 2666, 2933, 3200, 3600, 4000],
      voltage: 1.2,
      maxCapacity: 128
    },
    ddr5: {
      standardSpeeds: [4800, 5200, 5600, 6000, 6400, 6800, 7200],
      voltage: 1.1,
      maxCapacity: 256
    },
    lpddr4: {
      standardSpeeds: [3200, 3733, 4266],
      voltage: 1.1,
      maxCapacity: 32
    },
    lpddr5: {
      standardSpeeds: [5500, 6000, 6400, 7500],
      voltage: 1.05,
      maxCapacity: 64
    }
  }

  /**
   * Analyze memory specifications from text and structured data
   */
  async analyze(rawText: string, structuredData?: Record<string, unknown>): Promise<ExtractedMemory> {
    const memory: Partial<ExtractedMemory> = {
      capacity: 8,
      type: 'Unknown',
      speed: 2400,
      channels: 2,
      slots: {
        total: 2,
        occupied: 1,
        maxPerSlot: 32
      },
      isUpgradeable: true,
      maxSupported: 64,
      formFactor: 'Unknown'
    }

    // Try structured data first
    if (structuredData) {
      this.extractFromStructuredData(memory, structuredData)
    }

    // Extract from raw text
    this.extractFromRawText(memory, rawText)

    // Enhance with database information
    this.enhanceWithDatabase(memory)

    // Validate and set defaults
    this.validateAndSetDefaults(memory)

    return memory as ExtractedMemory
  }

  /**
   * Extract memory information from structured data
   */
  private extractFromStructuredData(memory: Partial<ExtractedMemory>, data: Record<string, unknown>): void {
    if (data.capacity || data.size) {
      memory.capacity = parseInt(data.capacity || data.size)
    }
    
    if (data.type) {
      memory.type = this.normalizeMemoryType(data.type)
    }
    
    if (data.speed) {
      memory.speed = parseInt(data.speed)
    }
    
    if (data.channels) {
      memory.channels = parseInt(data.channels)
    }
    
    if (data.slots) {
      if (typeof data.slots === 'object') {
        memory.slots = {
          total: data.slots.total || 2,
          occupied: data.slots.occupied || 1,
          maxPerSlot: data.slots.maxPerSlot || 32
        }
      } else {
        memory.slots = {
          total: parseInt(data.slots),
          occupied: 1,
          maxPerSlot: 32
        }
      }
    }
    
    if (data.upgradeable !== undefined) {
      memory.isUpgradeable = Boolean(data.upgradeable)
    }
    
    if (data.maxSupported) {
      memory.maxSupported = parseInt(data.maxSupported)
    }
    
    if (data.formFactor) {
      memory.formFactor = this.normalizeFormFactor(data.formFactor)
    }
  }

  /**
   * Extract memory information from raw text
   */
  private extractFromRawText(memory: Partial<ExtractedMemory>, text: string): void {
    this.extractCapacity(memory, text)
    this.extractType(memory, text)
    this.extractSpeed(memory, text)
    this.extractSlots(memory, text)
    this.extractUpgradeability(memory, text)
    this.extractFormFactor(memory, text)
  }

  /**
   * Extract memory capacity
   */
  private extractCapacity(memory: Partial<ExtractedMemory>, text: string): void {
    for (const pattern of this.memoryPatterns.capacity) {
      const matches = Array.from(text.matchAll(pattern))
      if (matches.length > 0) {
        const capacities = matches.map(match => parseInt(match[1]))
        memory.capacity = Math.max(...capacities)
        break
      }
    }
  }

  /**
   * Extract memory type
   */
  private extractType(memory: Partial<ExtractedMemory>, text: string): void {
    for (const pattern of this.memoryPatterns.type) {
      const match = text.match(pattern)
      if (match) {
        const type = match[1] || match[2]
        if (type) {
          memory.type = this.normalizeMemoryType(type)
          break
        }
      }
    }
  }

  /**
   * Extract memory speed
   */
  private extractSpeed(memory: Partial<ExtractedMemory>, text: string): void {
    for (const pattern of this.memoryPatterns.speed) {
      const matches = Array.from(text.matchAll(pattern))
      if (matches.length > 0) {
        const speeds = matches.map(match => {
          // Handle different pattern groups
          if (match[2]) return parseInt(match[2]) // DDR4-3200 format
          return parseInt(match[1]) // Direct MHz format
        }).filter(speed => speed >= 1000 && speed <= 10000)

        if (speeds.length > 0) {
          memory.speed = Math.max(...speeds)
          break
        }
      }
    }
  }

  /**
   * Extract slot configuration
   */
  private extractSlots(memory: Partial<ExtractedMemory>, text: string): void {
    // Look for slot configuration patterns like "2x8GB" or "1x16GB"
    const slotConfigMatch = text.match(/(\d+)\s*x\s*(\d+)\s*GB/i)
    if (slotConfigMatch) {
      const slots = parseInt(slotConfigMatch[1])
      const capacityPerSlot = parseInt(slotConfigMatch[2])
      
      memory.slots = {
        total: Math.max(slots, 2),
        occupied: slots,
        maxPerSlot: Math.max(capacityPerSlot, 16)
      }
      
      if (!memory.capacity) {
        memory.capacity = slots * capacityPerSlot
      }
      return
    }

    // Look for general slot mentions
    for (const pattern of this.memoryPatterns.slots) {
      const match = text.match(pattern)
      if (match) {
        const slotCount = parseInt(match[1])
        if (slotCount >= 1 && slotCount <= 4) {
          if (!memory.slots) memory.slots = { total: 2, occupied: 1, maxPerSlot: 32 }
          memory.slots.total = slotCount
          break
        }
      }
    }
  }

  /**
   * Extract upgradeability information
   */
  private extractUpgradeability(memory: Partial<ExtractedMemory>, text: string): void {
    const upgradeableKeywords = ['upgradeable', 'expandable', 'user replaceable', 'user-replaceable']
    const nonUpgradeableKeywords = ['soldered', 'non-upgradeable', 'non upgradeable', 'fixed', 'on-board', 'onboard']

    const lowerText = text.toLowerCase()

    // Check for non-upgradeable indicators first (more specific)
    for (const keyword of nonUpgradeableKeywords) {
      if (lowerText.includes(keyword)) {
        memory.isUpgradeable = false
        memory.formFactor = 'Soldered'
        return
      }
    }

    // Check for upgradeable indicators
    for (const keyword of upgradeableKeywords) {
      if (lowerText.includes(keyword)) {
        memory.isUpgradeable = true
        return
      }
    }
  }

  /**
   * Extract form factor
   */
  private extractFormFactor(memory: Partial<ExtractedMemory>, text: string): void {
    const lowerText = text.toLowerCase()

    if (lowerText.includes('so-dimm') || lowerText.includes('sodimm')) {
      memory.formFactor = 'SO-DIMM'
    } else if (lowerText.includes('dimm')) {
      memory.formFactor = 'DIMM'
    } else if (lowerText.includes('soldered') || lowerText.includes('on-board') || lowerText.includes('onboard')) {
      memory.formFactor = 'Soldered'
      memory.isUpgradeable = false
    }
  }

  /**
   * Enhance memory data with database information
   */
  private enhanceWithDatabase(memory: Partial<ExtractedMemory>): void {
    if (!memory.type || memory.type === 'Unknown') return

    const typeKey = memory.type.toLowerCase().replace(/[^a-z0-9]/g, '') as keyof typeof this.memoryDatabase
    const dbInfo = this.memoryDatabase[typeKey]

    if (dbInfo) {
      // Set voltage if not specified
      if (!memory.voltage) {
        memory.voltage = dbInfo.voltage
      }

      // Validate speed against standard speeds
      if (memory.speed) {
        const closestSpeed = this.findClosestStandardSpeed(memory.speed, dbInfo.standardSpeeds)
        if (Math.abs(memory.speed - closestSpeed) > 200) {
          memory.speed = closestSpeed
        }
      }

      // Set max supported capacity based on memory type
      if (!memory.maxSupported || memory.maxSupported > dbInfo.maxCapacity) {
        memory.maxSupported = dbInfo.maxCapacity
      }

      // Infer channels based on capacity and type
      if (!memory.channels || memory.channels === 1) {
        memory.channels = this.inferChannels(memory.capacity || 8, memory.type)
      }
    }
  }

  /**
   * Find closest standard memory speed
   */
  private findClosestStandardSpeed(speed: number, standardSpeeds: number[]): number {
    return standardSpeeds.reduce((closest, current) => {
      return Math.abs(current - speed) < Math.abs(closest - speed) ? current : closest
    })
  }

  /**
   * Infer memory channels based on capacity and type
   */
  private inferChannels(capacity: number, type: string): number {
    // LPDDR is typically single channel per module but dual channel overall
    if (type.includes('LPDDR')) {
      return capacity >= 16 ? 4 : 2
    }

    // DDR typically uses dual channel for most configurations
    if (capacity >= 32) return 2 // Likely 2x16GB or similar
    if (capacity >= 16) return 2 // Likely 2x8GB or 1x16GB
    if (capacity >= 8) return 2  // Likely 2x4GB or 1x8GB
    return 1 // Single channel for smaller capacities
  }

  /**
   * Normalize memory type
   */
  private normalizeMemoryType(type: string): ExtractedMemory['type'] {
    const normalized = type.toUpperCase().replace(/[^A-Z0-9]/g, '')
    
    if (normalized.includes('DDR5')) return 'DDR5'
    if (normalized.includes('DDR4')) return 'DDR4'
    if (normalized.includes('LPDDR5X')) return 'LPDDR5X'
    if (normalized.includes('LPDDR5')) return 'LPDDR5'
    if (normalized.includes('LPDDR4X')) return 'LPDDR4X'
    if (normalized.includes('LPDDR4')) return 'LPDDR4'
    
    return 'Unknown'
  }

  /**
   * Normalize form factor
   */
  private normalizeFormFactor(formFactor: string): ExtractedMemory['formFactor'] {
    const normalized = formFactor.toLowerCase().replace(/[^a-z]/g, '')
    
    if (normalized.includes('sodimm')) return 'SO-DIMM'
    if (normalized.includes('dimm')) return 'DIMM'
    if (normalized.includes('soldered') || normalized.includes('onboard')) return 'Soldered'
    
    return 'Unknown'
  }

  /**
   * Validate and set default values
   */
  private validateAndSetDefaults(memory: Partial<ExtractedMemory>): void {
    // Validate capacity
    if (!memory.capacity || memory.capacity < 2) memory.capacity = 8
    if (memory.capacity > 256) memory.capacity = 256

    // Validate speed
    if (!memory.speed || memory.speed < 1000) {
      memory.speed = memory.type === 'DDR5' ? 4800 : 2400
    }
    if (memory.speed > 8000) memory.speed = 8000

    // Validate channels
    if (!memory.channels || memory.channels < 1) memory.channels = 2
    if (memory.channels > 4) memory.channels = 4

    // Validate slots
    if (!memory.slots) {
      memory.slots = {
        total: 2,
        occupied: memory.capacity <= 8 ? 1 : 2,
        maxPerSlot: 32
      }
    }

    // Ensure occupied slots doesn't exceed total
    if (memory.slots.occupied > memory.slots.total) {
      memory.slots.occupied = memory.slots.total
    }

    // Validate max supported
    if (!memory.maxSupported || memory.maxSupported < memory.capacity) {
      memory.maxSupported = Math.max(memory.capacity, 64)
    }

    // Set form factor default
    if (!memory.formFactor || memory.formFactor === 'Unknown') {
      memory.formFactor = 'SO-DIMM' // Most laptops use SO-DIMM
    }

    // Set upgradeability based on form factor
    if (memory.formFactor === 'Soldered') {
      memory.isUpgradeable = false
    }

    // Set timing defaults if not specified
    if (!memory.timing) {
      memory.timing = this.getDefaultTiming(memory.type, memory.speed)
    }
  }

  /**
   * Get default memory timing based on type and speed
   */
  private getDefaultTiming(type: string, speed: number): ExtractedMemory['timing'] {
    if (type === 'DDR4') {
      if (speed >= 3200) return { cas: 16, trcd: 18, trp: 18, tras: 36 }
      if (speed >= 2666) return { cas: 15, trcd: 17, trp: 17, tras: 35 }
      return { cas: 15, trcd: 15, trp: 15, tras: 35 }
    }
    
    if (type === 'DDR5') {
      if (speed >= 5600) return { cas: 36, trcd: 36, trp: 36, tras: 76 }
      if (speed >= 4800) return { cas: 34, trcd: 34, trp: 34, tras: 74 }
      return { cas: 32, trcd: 32, trp: 32, tras: 72 }
    }

    return undefined
  }
}
