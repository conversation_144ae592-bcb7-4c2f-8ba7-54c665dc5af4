/**
 * Storage Analyzer
 * Extracts and analyzes storage specifications from text and structured data
 */

import type { ExtractedStorage } from '../../types'

export class StorageAnalyzer {
  private readonly storagePatterns = {
    capacity: [
      /(\d+)\s*(GB|TB)\s*(SSD|HDD|NVMe|eMMC|Storage)/gi,
      /(\d+)\s*(GB|TB)\s*(PCIe|SATA)/gi,
      /Storage:\s*(\d+)\s*(GB|TB)/gi
    ],
    type: [
      /(NVMe|PCIe)\s*(SSD)?/gi,
      /(SATA)\s*(SSD|III)/gi,
      /(HDD|Hard\s*Drive)/gi,
      /(eMMC)/gi,
      /(M\.2)/gi
    ],
    interface: [
      /(PCIe\s*4\.0|PCIe\s*Gen\s*4)/gi,
      /(PCIe\s*3\.0|PCIe\s*Gen\s*3)/gi,
      /(SATA\s*III|SATA\s*3)/gi,
      /(SATA\s*II|SATA\s*2)/gi
    ],
    formFactor: [
      /(M\.2\s*2280)/gi,
      /(M\.2\s*2242)/gi,
      /(2\.5["\s]*inch|2\.5")/gi,
      /(mSATA)/gi
    ],
    speed: [
      /(\d+,?\d*)\s*MB\/s\s*(read|write)?/gi,
      /(\d+,?\d*)\s*IOPS/gi
    ]
  }

  /**
   * Analyze storage specifications from text and structured data
   */
  async analyze(rawText: string, structuredData?: Record<string, unknown>): Promise<ExtractedStorage[]> {
    const storageDevices: ExtractedStorage[] = []

    // Try structured data first
    if (structuredData) {
      if (Array.isArray(structuredData)) {
        for (const device of structuredData) {
          storageDevices.push(this.extractFromStructuredData(device))
        }
      } else {
        storageDevices.push(this.extractFromStructuredData(structuredData))
      }
    }

    // Extract from raw text
    const textDevices = this.extractFromRawText(rawText)
    storageDevices.push(...textDevices)

    // If no devices found, create a default one
    if (storageDevices.length === 0) {
      storageDevices.push(this.createDefaultStorage())
    }

    // Enhance and validate each device
    return storageDevices.map(device => this.enhanceAndValidate(device))
  }

  /**
   * Extract storage from structured data
   */
  private extractFromStructuredData(data: Record<string, unknown>): ExtractedStorage {
    return {
      type: this.normalizeStorageType(data.type || 'NVMe SSD'),
      capacity: this.parseCapacity(data.capacity || data.size || '512GB'),
      interface: this.normalizeInterface(data.interface || 'PCIe 4.0'),
      formFactor: this.normalizeFormFactor(data.formFactor || 'M.2 2280'),
      speed: {
        read: data.readSpeed || data.speed?.read,
        write: data.writeSpeed || data.speed?.write,
        iops: data.iops
      },
      isRemovable: data.removable !== false,
      additionalSlots: data.additionalSlots || 0,
      manufacturer: data.manufacturer,
      model: data.model
    }
  }

  /**
   * Extract storage devices from raw text
   */
  private extractFromRawText(text: string): ExtractedStorage[] {
    const devices: ExtractedStorage[] = []
    
    // Find all capacity mentions
    const capacityMatches = this.findAllMatches(text, this.storagePatterns.capacity)
    
    for (const match of capacityMatches) {
      const device = this.createDefaultStorage()
      
      // Extract capacity
      const capacity = parseInt(match[1])
      const unit = match[2].toUpperCase()
      device.capacity = unit === 'TB' ? capacity * 1024 : capacity
      
      // Extract type from the same match
      const typeHint = match[3]
      if (typeHint) {
        device.type = this.normalizeStorageType(typeHint)
      }
      
      // Look for additional details in surrounding text
      this.extractAdditionalDetails(device, text, match.index || 0)
      
      devices.push(device)
    }

    return devices
  }

  /**
   * Extract additional storage details from surrounding text
   */
  private extractAdditionalDetails(device: ExtractedStorage, text: string, position: number): void {
    // Look in a window around the match
    const start = Math.max(0, position - 100)
    const end = Math.min(text.length, position + 100)
    const context = text.slice(start, end)

    // Extract interface
    const interfaceMatch = context.match(this.storagePatterns.interface[0]) ||
                          context.match(this.storagePatterns.interface[1]) ||
                          context.match(this.storagePatterns.interface[2])
    if (interfaceMatch) {
      device.interface = this.normalizeInterface(interfaceMatch[0])
    }

    // Extract form factor
    const formFactorMatch = context.match(this.storagePatterns.formFactor[0]) ||
                           context.match(this.storagePatterns.formFactor[1]) ||
                           context.match(this.storagePatterns.formFactor[2])
    if (formFactorMatch) {
      device.formFactor = this.normalizeFormFactor(formFactorMatch[0])
    }

    // Extract speed
    const speedMatches = this.findAllMatches(context, this.storagePatterns.speed)
    for (const speedMatch of speedMatches) {
      const speed = parseInt(speedMatch[1].replace(',', ''))
      const type = speedMatch[2]?.toLowerCase()
      
      if (speedMatch[0].includes('IOPS')) {
        if (!device.speed.iops) device.speed.iops = {}
        device.speed.iops.read = speed
      } else if (type === 'read') {
        device.speed.read = speed
      } else if (type === 'write') {
        device.speed.write = speed
      } else {
        // Assume read speed if not specified
        device.speed.read = speed
      }
    }
  }

  /**
   * Create default storage device
   */
  private createDefaultStorage(): ExtractedStorage {
    return {
      type: 'NVMe SSD',
      capacity: 512,
      interface: 'PCIe 4.0',
      formFactor: 'M.2 2280',
      speed: {},
      isRemovable: true,
      additionalSlots: 0
    }
  }

  /**
   * Enhance and validate storage device
   */
  private enhanceAndValidate(device: ExtractedStorage): ExtractedStorage {
    // Set default speeds based on type and interface
    if (!device.speed.read || !device.speed.write) {
      const defaultSpeeds = this.getDefaultSpeeds(device.type, device.interface)
      if (!device.speed.read) device.speed.read = defaultSpeeds.read
      if (!device.speed.write) device.speed.write = defaultSpeeds.write
    }

    // Validate capacity
    if (device.capacity < 32) device.capacity = 256
    if (device.capacity > 8192) device.capacity = 8192

    // Validate speeds
    if (device.speed.read && device.speed.read > 10000) device.speed.read = 10000
    if (device.speed.write && device.speed.write > 10000) device.speed.write = 10000

    // Set form factor based on type
    if (device.formFactor === 'Unknown') {
      if (device.type === 'NVMe SSD') device.formFactor = 'M.2 2280'
      else if (device.type === 'SATA SSD') device.formFactor = '2.5"'
      else if (device.type === 'HDD') device.formFactor = '2.5"'
    }

    return device
  }

  /**
   * Get default speeds for storage type and interface
   */
  private getDefaultSpeeds(type: string, storageInterface: string): { read: number; write: number } {
    if (type === 'NVMe SSD') {
      if (storageInterface === 'PCIe 4.0') return { read: 7000, write: 6000 }
      if (storageInterface === 'PCIe 3.0') return { read: 3500, write: 3000 }
    }
    
    if (type === 'SATA SSD') {
      return { read: 550, write: 520 }
    }
    
    if (type === 'HDD') {
      return { read: 120, write: 120 }
    }
    
    if (type === 'eMMC') {
      return { read: 300, write: 150 }
    }

    return { read: 500, write: 500 }
  }

  /**
   * Parse capacity string to GB
   */
  private parseCapacity(capacityStr: string): number {
    const match = capacityStr.match(/(\d+)\s*(GB|TB)/i)
    if (match) {
      const value = parseInt(match[1])
      const unit = match[2].toUpperCase()
      return unit === 'TB' ? value * 1024 : value
    }
    return 512 // Default
  }

  /**
   * Normalize storage type
   */
  private normalizeStorageType(type: string): ExtractedStorage['type'] {
    const normalized = type.toLowerCase()
    
    if (normalized.includes('nvme') || normalized.includes('pcie')) return 'NVMe SSD'
    if (normalized.includes('sata') && normalized.includes('ssd')) return 'SATA SSD'
    if (normalized.includes('ssd')) return 'NVMe SSD' // Default SSD to NVMe
    if (normalized.includes('hdd') || normalized.includes('hard')) return 'HDD'
    if (normalized.includes('emmc')) return 'eMMC'
    
    return 'Unknown'
  }

  /**
   * Normalize interface
   */
  private normalizeInterface(storageInterface: string): ExtractedStorage['interface'] {
    const normalized = storageInterface.toLowerCase()
    
    if (normalized.includes('pcie 4') || normalized.includes('gen 4')) return 'PCIe 4.0'
    if (normalized.includes('pcie 3') || normalized.includes('gen 3')) return 'PCIe 3.0'
    if (normalized.includes('sata iii') || normalized.includes('sata 3')) return 'SATA III'
    if (normalized.includes('sata')) return 'SATA III'
    if (normalized.includes('emmc')) return 'eMMC'
    
    return 'Unknown'
  }

  /**
   * Normalize form factor
   */
  private normalizeFormFactor(formFactor: string): ExtractedStorage['formFactor'] {
    const normalized = formFactor.toLowerCase()
    
    if (normalized.includes('m.2 2280') || normalized.includes('m2 2280')) return 'M.2 2280'
    if (normalized.includes('m.2 2242') || normalized.includes('m2 2242')) return 'M.2 2242'
    if (normalized.includes('m.2') || normalized.includes('m2')) return 'M.2 2280'
    if (normalized.includes('2.5') || normalized.includes('2.5"')) return '2.5"'
    if (normalized.includes('msata')) return 'mSATA'
    
    return 'Unknown'
  }

  /**
   * Find all regex matches with their positions
   */
  private findAllMatches(text: string, patterns: RegExp[]): Array<RegExpMatchArray & { index: number }> {
    const matches: Array<RegExpMatchArray & { index: number }> = []
    
    for (const pattern of patterns) {
      let match
      const regex = new RegExp(pattern.source, pattern.flags)
      
      while ((match = regex.exec(text)) !== null) {
        matches.push(match as RegExpMatchArray & { index: number })
        if (!pattern.global) break
      }
    }
    
    return matches
  }
}
