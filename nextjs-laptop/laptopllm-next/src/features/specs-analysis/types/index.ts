/**
 * Types for Specifications Analysis Module
 * Automated technical specification analysis and LLM compatibility prediction
 */

import type { LaptopSpecifications } from '@/shared/types'

// ============================================================================
// CORE ANALYSIS TYPES
// ============================================================================

export interface SpecificationAnalysis {
  id: string
  laptopId: string
  rawText: string
  extractedSpecs: ExtractedSpecifications
  confidence: AnalysisConfidence
  analysisMetadata: AnalysisMetadata
  createdAt: Date
  updatedAt: Date
}

export interface ExtractedSpecifications {
  cpu: ExtractedCPU
  memory: ExtractedMemory
  storage: ExtractedStorage[]
  gpu?: ExtractedGPU
  display: ExtractedDisplay
  connectivity: ExtractedConnectivity
  physical: ExtractedPhysical
  battery?: ExtractedBattery
  thermal: ExtractedThermal
}

export interface AnalysisConfidence {
  overall: number // 0-100
  cpu: number
  memory: number
  storage: number
  gpu: number
  display: number
  connectivity: number
  physical: number
  battery: number
  thermal: number
}

export interface AnalysisMetadata {
  extractionMethod: 'regex' | 'nlp' | 'structured' | 'hybrid'
  sourceQuality: 'high' | 'medium' | 'low'
  processingTime: number
  warnings: string[]
  suggestions: string[]
  version: string
}

// ============================================================================
// EXTRACTED COMPONENT SPECIFICATIONS
// ============================================================================

export interface ExtractedCPU {
  manufacturer: 'Intel' | 'AMD' | 'Apple' | 'Qualcomm' | 'Unknown'
  family: string // e.g., "Core i7", "Ryzen 7", "M2"
  model: string // e.g., "13700H", "7840HS", "M2 Pro"
  generation: string // e.g., "13th Gen", "7000 Series", "M2"
  cores: number
  threads: number
  baseFrequency: number // GHz
  boostFrequency?: number // GHz
  cache: {
    l1?: number // KB
    l2?: number // KB
    l3?: number // MB
  }
  architecture: string // e.g., "Raptor Lake", "Zen 4", "Apple Silicon"
  process: string // e.g., "Intel 7", "TSMC 4nm"
  tdp: number // Watts
  integratedGraphics?: string
  features: string[] // e.g., ["Hyper-Threading", "Turbo Boost"]
  benchmarkScores?: {
    cinebenchR23Single?: number
    cinebenchR23Multi?: number
    geekbench5Single?: number
    geekbench5Multi?: number
    passmarkCpu?: number
  }
}

export interface ExtractedMemory {
  capacity: number // GB
  type: 'DDR4' | 'DDR5' | 'LPDDR4' | 'LPDDR5' | 'Unknown'
  speed: number // MHz
  channels: number // 1, 2, 4
  slots: {
    total: number
    occupied: number
    maxPerSlot: number
  }
  isUpgradeable: boolean
  maxSupported: number // GB
  timing?: {
    cas?: number
    trcd?: number
    trp?: number
    tras?: number
  }
  voltage?: number // V
  formFactor: 'SO-DIMM' | 'DIMM' | 'Soldered' | 'Unknown'
}

export interface ExtractedStorage {
  type: 'NVMe SSD' | 'SATA SSD' | 'HDD' | 'eMMC' | 'Unknown'
  capacity: number // GB
  interface: 'PCIe 4.0' | 'PCIe 3.0' | 'SATA III' | 'eMMC' | 'Unknown'
  formFactor: 'M.2 2280' | 'M.2 2242' | '2.5"' | 'mSATA' | 'Unknown'
  speed: {
    read?: number // MB/s
    write?: number // MB/s
    iops?: {
      read?: number
      write?: number
    }
  }
  isRemovable: boolean
  additionalSlots: number
  manufacturer?: string
  model?: string
}

export interface ExtractedGPU {
  type: 'Integrated' | 'Discrete' | 'Hybrid'
  manufacturer: 'NVIDIA' | 'AMD' | 'Intel' | 'Apple' | 'Unknown'
  model: string
  architecture: string // e.g., "Ada Lovelace", "RDNA 3", "Xe"
  vram: {
    capacity: number // GB
    type: 'GDDR6' | 'GDDR6X' | 'HBM2' | 'Shared' | 'Unknown'
    bandwidth: number // GB/s
  }
  computeUnits: number
  baseClock?: number // MHz
  boostClock?: number // MHz
  tdp?: number // Watts
  features: string[] // e.g., ["Ray Tracing", "DLSS", "FSR"]
  benchmarkScores?: {
    timespy?: number
    firestrike?: number
    port_royal?: number
    blender?: number
  }
}

export interface ExtractedDisplay {
  size: number // inches
  resolution: {
    width: number
    height: number
    name: string // e.g., "Full HD", "4K UHD"
  }
  aspectRatio: string // e.g., "16:9", "16:10"
  refreshRate: number // Hz
  panelType: 'IPS' | 'OLED' | 'TN' | 'VA' | 'Mini-LED' | 'Unknown'
  brightness: number // nits
  colorGamut: {
    srgb?: number // %
    adobeRgb?: number // %
    dciP3?: number // %
  }
  features: string[] // e.g., ["Touch", "HDR", "G-Sync", "FreeSync"]
  coating: 'Glossy' | 'Matte' | 'Anti-glare' | 'Unknown'
}

export interface ExtractedConnectivity {
  wifi: {
    standard: 'Wi-Fi 6E' | 'Wi-Fi 6' | 'Wi-Fi 5' | 'Wi-Fi 4' | 'Unknown'
    speed: number // Mbps
    bands: string[] // e.g., ["2.4GHz", "5GHz", "6GHz"]
  }
  bluetooth: {
    version: string // e.g., "5.3", "5.2"
  }
  ethernet?: {
    speed: number // Mbps
    type: 'RJ45' | 'USB-C' | 'Thunderbolt'
  }
  ports: {
    usb: {
      usbA: number
      usbC: number
      thunderbolt: number
      usb2: number
      usb3: number
      usb4: number
    }
    display: {
      hdmi: number
      displayPort: number
      vga: number
      dvi: number
    }
    audio: {
      headphone: number
      microphone: number
      combo: number
    }
    other: {
      sdCard: boolean
      ethernet: boolean
      kensington: boolean
    }
  }
}

export interface ExtractedPhysical {
  dimensions: {
    width: number // mm
    depth: number // mm
    height: number // mm
  }
  weight: number // kg
  material: {
    lid: string // e.g., "Aluminum", "Plastic", "Carbon Fiber"
    base: string
    keyboard: string
  }
  color: string
  build: {
    hingeType: string
    keyboardType: 'Membrane' | 'Mechanical' | 'Scissor' | 'Butterfly' | 'Unknown'
    trackpadSize: string // e.g., "Large", "Medium", "Small"
    webcamResolution: string // e.g., "1080p", "720p"
    speakers: string // e.g., "Stereo", "Quad", "Dolby Atmos"
  }
}

export interface ExtractedBattery {
  capacity: number // Wh
  cells: number
  type: 'Li-ion' | 'Li-Po' | 'Unknown'
  removable: boolean
  fastCharging: {
    supported: boolean
    wattage?: number // W
    technology?: string // e.g., "USB-C PD", "Proprietary"
  }
  estimatedLife: {
    typical: number // hours
    video: number // hours
    web: number // hours
    idle: number // hours
  }
}

export interface ExtractedThermal {
  coolingType: 'Active' | 'Passive' | 'Hybrid'
  fans: number
  heatPipes: number
  thermalDesign: string // e.g., "Dual Fan", "Vapor Chamber"
  maxTdp: number // Watts
  thermalThrottling: {
    cpuThreshold: number // °C
    gpuThreshold: number // °C
  }
  noiseLevel: {
    idle: number // dB
    load: number // dB
  }
}

// ============================================================================
// ANALYSIS ALGORITHMS AND PREDICTION
// ============================================================================

export interface PerformancePrediction {
  llmCompatibility: LLMCompatibilityPrediction
  generalPerformance: GeneralPerformancePrediction
  powerEfficiency: PowerEfficiencyPrediction
  thermalPerformance: ThermalPerformancePrediction
}

export interface LLMCompatibilityPrediction {
  overallScore: number // 0-100
  recommendedModels: RecommendedLLMModel[]
  limitations: string[]
  optimizations: string[]
  performance: {
    tokensPerSecond: {
      small: number // 7B models
      medium: number // 13B models
      large: number // 30B+ models
    }
    maxContextLength: {
      small: number
      medium: number
      large: number
    }
    concurrentSessions: number
    memoryEfficiency: number // 0-1
  }
}

export interface RecommendedLLMModel {
  name: string
  size: string
  quantization: string
  estimatedPerformance: number // tokens/sec
  memoryUsage: number // GB
  confidence: number // 0-100
  notes: string[]
}

export interface GeneralPerformancePrediction {
  cpuScore: number // 0-100
  gpuScore: number // 0-100
  memoryScore: number // 0-100
  storageScore: number // 0-100
  overallScore: number // 0-100
  useCases: string[]
  benchmarkEstimates: {
    cinebench: number
    geekbench: number
    pcmark: number
    gaming: number
  }
}

export interface PowerEfficiencyPrediction {
  batteryLife: {
    office: number // hours
    video: number // hours
    gaming: number // hours
    llmInference: number // hours
  }
  powerConsumption: {
    idle: number // W
    typical: number // W
    peak: number // W
  }
  efficiencyRating: number // 0-100
}

export interface ThermalPerformancePrediction {
  thermalRating: number // 0-100
  sustainedPerformance: number // % of peak
  throttlingRisk: 'Low' | 'Medium' | 'High'
  coolingAdequacy: number // 0-100
  noiseLevel: {
    idle: number // dB
    load: number // dB
  }
}

// ============================================================================
// EXTRACTION PATTERNS AND RULES
// ============================================================================

export interface ExtractionPattern {
  id: string
  name: string
  description: string
  component: keyof ExtractedSpecifications
  patterns: RegexPattern[]
  priority: number
  confidence: number
  version: string
}

export interface RegexPattern {
  pattern: RegExp
  groups: string[]
  transform?: (match: RegExpMatchArray) => any
  validation?: (value: any) => boolean
}

export interface NormalizationRule {
  id: string
  component: keyof ExtractedSpecifications
  field: string
  rules: {
    aliases: Record<string, string>
    ranges: { min: number; max: number }
    defaults: any
    validation: (value: any) => boolean
  }
}

// ============================================================================
// ANALYSIS CONFIGURATION
// ============================================================================

export interface AnalysisConfig {
  extractionMethods: {
    regex: boolean
    nlp: boolean
    structured: boolean
    hybrid: boolean
  }
  confidenceThresholds: {
    minimum: number
    warning: number
    good: number
  }
  performance: {
    timeout: number // ms
    maxRetries: number
    cacheResults: boolean
  }
  validation: {
    strictMode: boolean
    requireMinimumSpecs: boolean
    validateBenchmarks: boolean
  }
}

// ============================================================================
// EXPORT TYPES
// ============================================================================

export type SpecsAnalysisResult = {
  success: boolean
  analysis?: SpecificationAnalysis
  predictions?: PerformancePrediction
  error?: string
  warnings: string[]
}

export type ComponentAnalysisResult<T> = {
  extracted: T
  confidence: number
  method: string
  warnings: string[]
}
