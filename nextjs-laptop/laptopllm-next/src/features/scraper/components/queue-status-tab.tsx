'use client'

/**
 * Queue Status Tab Component
 * Real-time monitoring of BullMQ queue status and performance
 */

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Activity, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Pause,
  Play,
  Users,
  Database,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react'
import { type QueueStats } from '@/hooks/use-scraping'

interface QueueStatusTabProps {
  queueStats: QueueStats[]
  getStatusColor: (status: string) => string
}

export function QueueStatusTab({ queueStats, getStatusColor }: QueueStatusTabProps) {
  const getTotalJobs = (queue: QueueStats) => {
    return queue.waiting + queue.active + queue.completed + queue.failed + queue.delayed
  }

  const getSuccessRate = (queue: QueueStats) => {
    const total = queue.completed + queue.failed
    if (total === 0) return 0
    return (queue.completed / total) * 100
  }

  const getQueueHealthStatus = (queue: QueueStats) => {
    const successRate = getSuccessRate(queue)
    const totalJobs = getTotalJobs(queue)
    
    if (queue.paused) return 'paused'
    if (successRate < 70 && totalJobs > 10) return 'critical'
    if (successRate < 85 && totalJobs > 5) return 'warning'
    if (queue.active > 0) return 'active'
    if (queue.waiting > 0) return 'pending'
    return 'idle'
  }

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-blue-600'
      case 'pending': return 'text-yellow-600'
      case 'idle': return 'text-gray-600'
      case 'paused': return 'text-orange-600'
      case 'warning': return 'text-yellow-600'
      case 'critical': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Activity className="h-4 w-4 text-blue-600" />
      case 'pending': return <Clock className="h-4 w-4 text-yellow-600" />
      case 'idle': return <Minus className="h-4 w-4 text-gray-600" />
      case 'paused': return <Pause className="h-4 w-4 text-orange-600" />
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'critical': return <XCircle className="h-4 w-4 text-red-600" />
      default: return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  if (!queueStats || queueStats.length === 0) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No queue statistics available. Make sure the queue system is running.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      {/* Queue Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Queues</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{queueStats.length}</div>
            <p className="text-xs text-muted-foreground">
              {queueStats.filter(q => !q.paused).length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
            <Play className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {queueStats.reduce((sum, q) => sum + q.active, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Currently processing
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Waiting Jobs</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {queueStats.reduce((sum, q) => sum + q.waiting, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              In queue
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {queueStats.length > 0 ? (
                queueStats.reduce((sum, q) => {
                  const total = q.completed + q.failed
                  return sum + (total > 0 ? (q.completed / total) * 100 : 0)
                }, 0) / queueStats.length
              ).toFixed(1) : '0'}%
            </div>
            <p className="text-xs text-muted-foreground">
              Average across queues
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Individual Queue Status */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Queue Details</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {queueStats.map((queue) => {
            const healthStatus = getQueueHealthStatus(queue)
            const successRate = getSuccessRate(queue)
            const totalJobs = getTotalJobs(queue)
            
            return (
              <Card key={queue.name}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">{queue.name}</CardTitle>
                    <div className="flex items-center space-x-2">
                      {getHealthStatusIcon(healthStatus)}
                      <Badge 
                        variant={queue.paused ? 'secondary' : 'default'}
                        className={getHealthStatusColor(healthStatus)}
                      >
                        {healthStatus.toUpperCase()}
                      </Badge>
                    </div>
                  </div>
                  <CardDescription>
                    {totalJobs} total jobs • {successRate.toFixed(1)}% success rate
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Job Distribution */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="flex items-center">
                          <Play className="h-3 w-3 mr-1 text-blue-600" />
                          Active
                        </span>
                        <span className="font-medium">{queue.active}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="flex items-center">
                          <Clock className="h-3 w-3 mr-1 text-yellow-600" />
                          Waiting
                        </span>
                        <span className="font-medium">{queue.waiting}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="flex items-center">
                          <CheckCircle className="h-3 w-3 mr-1 text-green-600" />
                          Completed
                        </span>
                        <span className="font-medium">{queue.completed}</span>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="flex items-center">
                          <XCircle className="h-3 w-3 mr-1 text-red-600" />
                          Failed
                        </span>
                        <span className="font-medium">{queue.failed}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="flex items-center">
                          <Clock className="h-3 w-3 mr-1 text-orange-600" />
                          Delayed
                        </span>
                        <span className="font-medium">{queue.delayed}</span>
                      </div>
                      {queue.paused && (
                        <div className="flex items-center justify-between text-sm">
                          <span className="flex items-center">
                            <Pause className="h-3 w-3 mr-1 text-orange-600" />
                            Paused
                          </span>
                          <span className="font-medium text-orange-600">Yes</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Success Rate Progress */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Success Rate</span>
                      <span className="font-medium">{successRate.toFixed(1)}%</span>
                    </div>
                    <Progress 
                      value={successRate} 
                      className="h-2"
                    />
                  </div>

                  {/* Queue Load Indicator */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Queue Load</span>
                      <span className="font-medium">
                        {queue.active + queue.waiting} / {totalJobs}
                      </span>
                    </div>
                    <Progress 
                      value={totalJobs > 0 ? ((queue.active + queue.waiting) / totalJobs) * 100 : 0} 
                      className="h-2"
                    />
                  </div>

                  {/* Alerts */}
                  {healthStatus === 'critical' && (
                    <Alert variant="destructive">
                      <XCircle className="h-4 w-4" />
                      <AlertDescription>
                        High failure rate detected. Check job configurations and error logs.
                      </AlertDescription>
                    </Alert>
                  )}
                  
                  {healthStatus === 'warning' && (
                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        Moderate failure rate. Monitor job performance closely.
                      </AlertDescription>
                    </Alert>
                  )}
                  
                  {queue.paused && (
                    <Alert>
                      <Pause className="h-4 w-4" />
                      <AlertDescription>
                        Queue is paused. Jobs will not be processed until resumed.
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </div>
  )
}
