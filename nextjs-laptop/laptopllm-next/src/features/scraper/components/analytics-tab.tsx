'use client'

/**
 * Analytics Tab Component
 * Performance analytics and insights for scraping operations
 */

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  PieChart, 
  Clock, 
  Zap,
  Target,
  AlertTriangle,
  CheckCircle,
  Activity,
  Database,
  Users,
  Calendar
} from 'lucide-react'
import { type DashboardData, type JobStats } from '@/hooks/use-scraping'

interface AnalyticsTabProps {
  dashboardData?: DashboardData
  jobStats?: JobStats
}

export function AnalyticsTab({ dashboardData, jobStats }: AnalyticsTabProps) {
  if (!dashboardData || !jobStats) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Analytics data is not available. Please check if the scraping system is running.
        </AlertDescription>
      </Alert>
    )
  }

  const { summary } = dashboardData

  // Calculate performance metrics
  const performanceMetrics = {
    efficiency: summary.overallSuccessRate,
    throughputTrend: summary.throughputPerHour > 50 ? 'up' : summary.throughputPerHour > 20 ? 'stable' : 'down',
    avgProcessingSeconds: summary.averageProcessingTime / 1000,
    totalProcessed: summary.totalCompletedJobs + summary.totalFailedJobs,
    errorRate: summary.totalFailedJobs / (summary.totalCompletedJobs + summary.totalFailedJobs) * 100 || 0
  }

  // Status distribution for pie chart visualization
  const statusDistribution = [
    { name: 'Completed', value: summary.totalCompletedJobs, color: 'bg-green-500' },
    { name: 'Failed', value: summary.totalFailedJobs, color: 'bg-red-500' },
    { name: 'Active', value: summary.totalActiveJobs, color: 'bg-blue-500' },
    { name: 'Waiting', value: summary.totalWaitingJobs, color: 'bg-yellow-500' }
  ]

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'down': return <TrendingDown className="h-4 w-4 text-red-600" />
      default: return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-600'
      case 'down': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getPerformanceRating = (successRate: number) => {
    if (successRate >= 95) return { rating: 'Excellent', color: 'text-green-600', badge: 'default' }
    if (successRate >= 85) return { rating: 'Good', color: 'text-blue-600', badge: 'default' }
    if (successRate >= 70) return { rating: 'Fair', color: 'text-yellow-600', badge: 'secondary' }
    return { rating: 'Poor', color: 'text-red-600', badge: 'destructive' }
  }

  const performance = getPerformanceRating(performanceMetrics.efficiency)

  return (
    <div className="space-y-6">
      {/* Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overall Performance</CardTitle>
            <Target className={`h-4 w-4 ${performance.color}`} />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${performance.color}`}>
              {performance.rating}
            </div>
            <p className="text-xs text-muted-foreground">
              {performanceMetrics.efficiency.toFixed(1)}% success rate
            </p>
            <Badge variant={performance.badge as any} className="mt-2">
              {performanceMetrics.efficiency.toFixed(1)}%
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Throughput</CardTitle>
            {getTrendIcon(performanceMetrics.throughputTrend)}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getTrendColor(performanceMetrics.throughputTrend)}`}>
              {summary.throughputPerHour}
            </div>
            <p className="text-xs text-muted-foreground">
              jobs per hour
            </p>
            <Badge variant="outline" className="mt-2">
              {performanceMetrics.throughputTrend.toUpperCase()}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Processing Time</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {performanceMetrics.avgProcessingSeconds.toFixed(1)}s
            </div>
            <p className="text-xs text-muted-foreground">
              per job
            </p>
            <Badge variant="outline" className="mt-2">
              {performanceMetrics.avgProcessingSeconds < 30 ? 'FAST' : 
               performanceMetrics.avgProcessingSeconds < 60 ? 'NORMAL' : 'SLOW'}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <AlertTriangle className={`h-4 w-4 ${performanceMetrics.errorRate > 15 ? 'text-red-600' : 
              performanceMetrics.errorRate > 5 ? 'text-yellow-600' : 'text-green-600'}`} />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${performanceMetrics.errorRate > 15 ? 'text-red-600' : 
              performanceMetrics.errorRate > 5 ? 'text-yellow-600' : 'text-green-600'}`}>
              {performanceMetrics.errorRate.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              of total jobs
            </p>
            <Badge variant={performanceMetrics.errorRate > 15 ? 'destructive' : 
              performanceMetrics.errorRate > 5 ? 'secondary' : 'default'} className="mt-2">
              {performanceMetrics.errorRate > 15 ? 'HIGH' : 
               performanceMetrics.errorRate > 5 ? 'MODERATE' : 'LOW'}
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Job Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChart className="h-5 w-5 mr-2" />
              Job Status Distribution
            </CardTitle>
            <CardDescription>
              Current distribution of jobs across different statuses
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {statusDistribution.map((status) => {
              const total = statusDistribution.reduce((sum, s) => sum + s.value, 0)
              const percentage = total > 0 ? (status.value / total) * 100 : 0
              
              return (
                <div key={status.name} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${status.color}`} />
                      <span className="text-sm font-medium">{status.name}</span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {status.value} ({percentage.toFixed(1)}%)
                    </div>
                  </div>
                  <Progress value={percentage} className="h-2" />
                </div>
              )
            })}
          </CardContent>
        </Card>

        {/* System Health Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              System Health Metrics
            </CardTitle>
            <CardDescription>
              Key performance indicators for system health
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Queue Health</span>
                <Badge className={`${summary.healthStatus === 'healthy' ? 'bg-green-500' : 
                  summary.healthStatus === 'warning' ? 'bg-yellow-500' : 'bg-red-500'}`}>
                  {summary.healthStatus.toUpperCase()}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Active Queues</span>
                <span className="text-sm">{summary.totalQueues}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Total Jobs Processed</span>
                <span className="text-sm">{performanceMetrics.totalProcessed.toLocaleString()}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Success Rate Trend</span>
                <div className="flex items-center space-x-1">
                  {summary.overallSuccessRate >= 90 ? 
                    <TrendingUp className="h-4 w-4 text-green-600" /> :
                    summary.overallSuccessRate >= 70 ?
                    <Activity className="h-4 w-4 text-yellow-600" /> :
                    <TrendingDown className="h-4 w-4 text-red-600" />
                  }
                  <span className="text-sm">{summary.overallSuccessRate.toFixed(1)}%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Performance Insights */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Performance Insights
            </CardTitle>
            <CardDescription>
              Automated insights and recommendations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {/* Performance Insights */}
            {performanceMetrics.efficiency >= 95 && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Excellent performance! Your scraping system is operating at peak efficiency.
                </AlertDescription>
              </Alert>
            )}
            
            {performanceMetrics.errorRate > 15 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  High error rate detected. Consider reviewing job configurations and target websites.
                </AlertDescription>
              </Alert>
            )}
            
            {summary.throughputPerHour < 20 && (
              <Alert>
                <Clock className="h-4 w-4" />
                <AlertDescription>
                  Low throughput detected. Consider increasing worker concurrency or optimizing job processing.
                </AlertDescription>
              </Alert>
            )}
            
            {performanceMetrics.avgProcessingSeconds > 60 && (
              <Alert>
                <Zap className="h-4 w-4" />
                <AlertDescription>
                  Jobs are taking longer than expected. Review timeout settings and target website performance.
                </AlertDescription>
              </Alert>
            )}
            
            {summary.totalWaitingJobs > 100 && (
              <Alert>
                <Users className="h-4 w-4" />
                <AlertDescription>
                  Large queue backlog detected. Consider adding more workers or increasing processing capacity.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Job Statistics Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              Job Statistics
            </CardTitle>
            <CardDescription>
              Detailed breakdown of job processing statistics
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="text-sm font-medium">Total Jobs</div>
                <div className="text-2xl font-bold">{jobStats.total.toLocaleString()}</div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium">Success Rate</div>
                <div className="text-2xl font-bold text-green-600">
                  {jobStats.successRate.toFixed(1)}%
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium">Avg Processing</div>
                <div className="text-2xl font-bold text-orange-600">
                  {(jobStats.averageProcessingTime / 1000).toFixed(1)}s
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium">Daily Throughput</div>
                <div className="text-2xl font-bold text-blue-600">
                  {jobStats.throughput.perDay}
                </div>
              </div>
            </div>
            
            {/* Status Breakdown */}
            <div className="space-y-2">
              <div className="text-sm font-medium">Status Breakdown</div>
              <div className="grid grid-cols-2 gap-2 text-xs">
                {Object.entries(jobStats.byStatus).map(([status, count]) => (
                  <div key={status} className="flex justify-between">
                    <span className="capitalize">{status}:</span>
                    <span className="font-medium">{count}</span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
