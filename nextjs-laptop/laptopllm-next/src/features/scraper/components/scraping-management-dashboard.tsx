'use client'

/**
 * Comprehensive Scraping Management Dashboard
 * Advanced dashboard for monitoring, controlling, and managing scraping operations
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  Activity, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Play, 
  Pause, 
  RefreshCw,
  TrendingUp,
  Database,
  Server,
  Settings,
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  Trash2,
  RotateCcw,
  Eye,
  BarChart3,
  Users,
  Zap
} from 'lucide-react'
import {
  useEnhancedScrapingDashboard,
  useEnhancedScrapingJobs,
  useStartEnhancedScrapingJob,
  useStartBatchScrapingJobs,
  useCancelEnhancedScrapingJob,
  useRetryEnhancedScrapingJob,
  type DashboardData,
  type EnhancedScrapingJob,
  type ScrapingJobRequest,
  type BatchJobRequest
} from '@/hooks/use-scraping'
import { JobManagementTab } from './job-management-tab'
import { QueueStatusTab } from './queue-status-tab'
import { AnalyticsTab } from './analytics-tab'
import { CreateJobForm } from './create-job-form'

interface JobFilters {
  status?: string
  priority?: string
  type?: string
  batchId?: string
  search?: string
}

export function ScrapingManagementDashboard() {
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [filters, setFilters] = useState<JobFilters>({})
  const [selectedJobs, setSelectedJobs] = useState<string[]>([])
  const [showCreateJobDialog, setShowCreateJobDialog] = useState(false)
  const [showBatchJobDialog, setShowBatchJobDialog] = useState(false)
  const [page, setPage] = useState(1)
  const limit = 20

  // Queries
  const { 
    data: dashboardData, 
    isLoading: isDashboardLoading, 
    error: dashboardError,
    refetch: refetchDashboard
  } = useEnhancedScrapingDashboard()

  const { 
    data: jobsData, 
    isLoading: isJobsLoading, 
    error: jobsError,
    refetch: refetchJobs
  } = useEnhancedScrapingJobs({
    page,
    limit,
    ...filters
  })

  // Mutations
  const startJobMutation = useStartEnhancedScrapingJob()
  const startBatchMutation = useStartBatchScrapingJobs()
  const cancelJobMutation = useCancelEnhancedScrapingJob()
  const retryJobMutation = useRetryEnhancedScrapingJob()

  // Auto-refresh effect
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      refetchDashboard()
      refetchJobs()
    }, 5000)

    return () => clearInterval(interval)
  }, [autoRefresh, refetchDashboard, refetchJobs])

  const handleRefresh = () => {
    refetchDashboard()
    refetchJobs()
  }

  const handleJobAction = async (action: 'cancel' | 'retry', jobId: string) => {
    try {
      if (action === 'cancel') {
        await cancelJobMutation.mutateAsync(jobId)
      } else if (action === 'retry') {
        await retryJobMutation.mutateAsync(jobId)
      }
    } catch (error) {
      console.error(`Failed to ${action} job:`, error)
    }
  }

  const handleBulkAction = async (action: 'cancel' | 'retry') => {
    try {
      await Promise.all(
        selectedJobs.map(jobId => 
          action === 'cancel' 
            ? cancelJobMutation.mutateAsync(jobId)
            : retryJobMutation.mutateAsync(jobId)
        )
      )
      setSelectedJobs([])
    } catch (error) {
      console.error(`Failed to ${action} jobs:`, error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500'
      case 'running': return 'bg-blue-500'
      case 'pending': return 'bg-yellow-500'
      case 'failed': return 'bg-red-500'
      case 'cancelled': return 'bg-gray-500'
      case 'paused': return 'bg-orange-500'
      default: return 'bg-gray-400'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'destructive'
      case 'high': return 'default'
      case 'normal': return 'secondary'
      case 'low': return 'outline'
      default: return 'secondary'
    }
  }

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600'
      case 'warning': return 'text-yellow-600'
      case 'critical': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  if (isDashboardLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading scraping dashboard...</span>
      </div>
    )
  }

  if (dashboardError) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load dashboard data: {dashboardError.message}
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Scraping Management Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor, control, and manage all scraping operations in real-time
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            {autoRefresh ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            {autoRefresh ? 'Pause' : 'Resume'}
          </Button>
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
          <Dialog open={showCreateJobDialog} onOpenChange={setShowCreateJobDialog}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                New Job
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Scraping Job</DialogTitle>
                <DialogDescription>
                  Start a new scraping job with custom configuration
                </DialogDescription>
              </DialogHeader>
              <CreateJobForm
                onSubmit={(data) => {
                  startJobMutation.mutate(data)
                  setShowCreateJobDialog(false)
                }}
                isLoading={startJobMutation.isPending}
              />
            </DialogContent>
          </Dialog>
          <Dialog open={showBatchJobDialog} onOpenChange={setShowBatchJobDialog}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Batch Jobs
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Batch Jobs</DialogTitle>
                <DialogDescription>
                  Start multiple scraping jobs simultaneously
                </DialogDescription>
              </DialogHeader>
              <BatchJobForm 
                onSubmit={(data) => {
                  startBatchMutation.mutate(data)
                  setShowBatchJobDialog(false)
                }}
                isLoading={startBatchMutation.isPending}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* System Health Overview */}
      {dashboardData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">System Health</CardTitle>
              <Activity className={`h-4 w-4 ${getHealthStatusColor(dashboardData.summary.healthStatus)}`} />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getHealthStatusColor(dashboardData.summary.healthStatus)}`}>
                {dashboardData.summary.healthStatus.toUpperCase()}
              </div>
              <p className="text-xs text-muted-foreground">
                {dashboardData.summary.totalQueues} queues active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
              <Zap className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardData.summary.totalActiveJobs}</div>
              <p className="text-xs text-muted-foreground">
                {dashboardData.summary.totalWaitingJobs} waiting
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {dashboardData.summary.overallSuccessRate.toFixed(1)}%
              </div>
              <p className="text-xs text-muted-foreground">
                {dashboardData.summary.totalCompletedJobs} completed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Throughput</CardTitle>
              <BarChart3 className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardData.summary.throughputPerHour}</div>
              <p className="text-xs text-muted-foreground">
                jobs per hour
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Processing</CardTitle>
              <Clock className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {(dashboardData.summary.averageProcessingTime / 1000).toFixed(1)}s
              </div>
              <p className="text-xs text-muted-foreground">
                per job
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs defaultValue="jobs" className="space-y-4">
        <TabsList>
          <TabsTrigger value="jobs">Job Management</TabsTrigger>
          <TabsTrigger value="queues">Queue Status</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="jobs" className="space-y-4">
          <JobManagementTab 
            jobsData={jobsData}
            isLoading={isJobsLoading}
            error={jobsError}
            filters={filters}
            onFiltersChange={setFilters}
            selectedJobs={selectedJobs}
            onSelectedJobsChange={setSelectedJobs}
            onJobAction={handleJobAction}
            onBulkAction={handleBulkAction}
            page={page}
            onPageChange={setPage}
            getStatusColor={getStatusColor}
            getPriorityColor={getPriorityColor}
          />
        </TabsContent>

        <TabsContent value="queues" className="space-y-4">
          <QueueStatusTab 
            queueStats={dashboardData?.queueStats || []}
            getStatusColor={getStatusColor}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <AnalyticsTab 
            dashboardData={dashboardData}
            jobStats={dashboardData?.jobStats}
          />
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <SettingsTab />
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Batch Job Form Component (simplified for now)
function BatchJobForm({ onSubmit, isLoading }: { onSubmit: (data: BatchJobRequest) => void, isLoading: boolean }) {
  const [sourceIds, setSourceIds] = useState<string[]>([''])
  const [priority, setPriority] = useState<'low' | 'normal' | 'high' | 'critical'>('normal')
  const [staggerDelay, setStaggerDelay] = useState(5000)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const validSourceIds = sourceIds.filter(id => id.trim() !== '')

    if (validSourceIds.length === 0) return

    onSubmit({
      sourceIds: validSourceIds,
      priority,
      staggerDelay,
      config: {
        batchSize: 10,
        maxConcurrent: 3,
        delayBetweenBatches: 1000,
        saveToDatabase: true,
        timeout: 30000,
        retryAttempts: 3
      }
    })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label>Source IDs</Label>
        {sourceIds.map((sourceId, index) => (
          <div key={index} className="flex items-center space-x-2">
            <Input
              placeholder="e.g., amazon-laptops, bestbuy-deals"
              value={sourceId}
              onChange={(e) => {
                const newSourceIds = [...sourceIds]
                newSourceIds[index] = e.target.value
                setSourceIds(newSourceIds)
              }}
            />
            {sourceIds.length > 1 && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setSourceIds(sourceIds.filter((_, i) => i !== index))}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        ))}
        <Button
          type="button"
          variant="outline"
          onClick={() => setSourceIds([...sourceIds, ''])}
          className="w-full"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Source ID
        </Button>
      </div>

      <div className="space-y-2">
        <Label>Priority</Label>
        <Select value={priority} onValueChange={(value: 'low' | 'normal' | 'high' | 'critical') => setPriority(value)}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="low">Low</SelectItem>
            <SelectItem value="normal">Normal</SelectItem>
            <SelectItem value="high">High</SelectItem>
            <SelectItem value="critical">Critical</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Stagger Delay (ms)</Label>
        <Input
          type="number"
          min="0"
          max="60000"
          value={staggerDelay}
          onChange={(e) => setStaggerDelay(parseInt(e.target.value))}
        />
      </div>

      <Button type="submit" disabled={isLoading} className="w-full">
        {isLoading ? 'Creating Jobs...' : 'Create Batch Jobs'}
      </Button>
    </form>
  )
}

// Settings Tab Component (simplified for now)
function SettingsTab() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Scraping Configuration</CardTitle>
          <CardDescription>
            Global settings for scraping operations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <Settings className="h-4 w-4" />
            <AlertDescription>
              Settings management will be implemented in a future update.
              Current settings are managed through environment variables and configuration files.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  )
}
