'use client'

/**
 * Create Job Form Component
 * Form for creating new scraping jobs with advanced configuration
 */

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { 
  Plus, 
  Trash2, 
  AlertTriangle, 
  Info,
  Settings,
  Globe,
  Clock,
  Zap
} from 'lucide-react'
import { type ScrapingJobRequest } from '@/hooks/use-scraping'

interface CreateJobFormProps {
  onSubmit: (data: ScrapingJobRequest) => void
  isLoading: boolean
}

export function CreateJobForm({ onSubmit, isLoading }: CreateJobFormProps) {
  const [formData, setFormData] = useState<ScrapingJobRequest>({
    priority: 'normal',
    config: {
      batchSize: 10,
      maxConcurrent: 3,
      delayBetweenBatches: 1000,
      enableScreenshots: false,
      saveToDatabase: true,
      timeout: 30000,
      retryAttempts: 3
    }
  })
  
  const [urls, setUrls] = useState<string[]>([''])
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleUrlChange = (index: number, value: string) => {
    const newUrls = [...urls]
    newUrls[index] = value
    setUrls(newUrls)
    
    // Clear URL-specific errors
    if (errors[`url_${index}`]) {
      const newErrors = { ...errors }
      delete newErrors[`url_${index}`]
      setErrors(newErrors)
    }
  }

  const addUrl = () => {
    setUrls([...urls, ''])
  }

  const removeUrl = (index: number) => {
    if (urls.length > 1) {
      const newUrls = urls.filter((_, i) => i !== index)
      setUrls(newUrls)
      
      // Clear related errors
      const newErrors = { ...errors }
      delete newErrors[`url_${index}`]
      setErrors(newErrors)
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    // Validate URLs
    const validUrls = urls.filter(url => url.trim() !== '')
    if (validUrls.length === 0) {
      newErrors.urls = 'At least one URL is required'
    } else {
      validUrls.forEach((url, index) => {
        try {
          new URL(url.trim())
        } catch {
          newErrors[`url_${index}`] = 'Invalid URL format'
        }
      })
    }

    // Validate source ID if provided
    if (formData.sourceId && formData.sourceId.trim().length < 3) {
      newErrors.sourceId = 'Source ID must be at least 3 characters'
    }

    // Validate config values
    if (formData.config) {
      if (formData.config.batchSize && (formData.config.batchSize < 1 || formData.config.batchSize > 100)) {
        newErrors.batchSize = 'Batch size must be between 1 and 100'
      }
      
      if (formData.config.maxConcurrent && (formData.config.maxConcurrent < 1 || formData.config.maxConcurrent > 20)) {
        newErrors.maxConcurrent = 'Max concurrent must be between 1 and 20'
      }
      
      if (formData.config.timeout && (formData.config.timeout < 5000 || formData.config.timeout > 300000)) {
        newErrors.timeout = 'Timeout must be between 5 and 300 seconds'
      }
      
      if (formData.config.retryAttempts && (formData.config.retryAttempts < 0 || formData.config.retryAttempts > 10)) {
        newErrors.retryAttempts = 'Retry attempts must be between 0 and 10'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    const validUrls = urls.filter(url => url.trim() !== '').map(url => url.trim())
    
    const jobRequest: ScrapingJobRequest = {
      ...formData,
      urls: validUrls,
      config: {
        ...formData.config,
        timeout: formData.config?.timeout ? formData.config.timeout * 1000 : 30000 // Convert to milliseconds
      }
    }

    onSubmit(jobRequest)
  }

  const updateConfig = (key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      config: {
        ...prev.config,
        [key]: value
      }
    }))
    
    // Clear related errors
    if (errors[key]) {
      const newErrors = { ...errors }
      delete newErrors[key]
      setErrors(newErrors)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-base">
            <Settings className="h-4 w-4 mr-2" />
            Basic Configuration
          </CardTitle>
          <CardDescription>
            Configure the basic parameters for your scraping job
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Source ID */}
          <div className="space-y-2">
            <Label htmlFor="sourceId">Source ID (Optional)</Label>
            <Input
              id="sourceId"
              placeholder="e.g., amazon-laptops, bestbuy-deals"
              value={formData.sourceId || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, sourceId: e.target.value }))}
            />
            {errors.sourceId && (
              <p className="text-sm text-red-600">{errors.sourceId}</p>
            )}
            <p className="text-xs text-muted-foreground">
              Identifier for grouping related scraping jobs
            </p>
          </div>

          {/* Priority */}
          <div className="space-y-2">
            <Label htmlFor="priority">Priority</Label>
            <Select
              value={formData.priority}
              onValueChange={(value) => setFormData(prev => ({ 
                ...prev, 
                priority: value as 'low' | 'normal' | 'high' | 'critical' 
              }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">Low</Badge>
                    <span>Background processing</span>
                  </div>
                </SelectItem>
                <SelectItem value="normal">
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">Normal</Badge>
                    <span>Standard processing</span>
                  </div>
                </SelectItem>
                <SelectItem value="high">
                  <div className="flex items-center space-x-2">
                    <Badge variant="default">High</Badge>
                    <span>Priority processing</span>
                  </div>
                </SelectItem>
                <SelectItem value="critical">
                  <div className="flex items-center space-x-2">
                    <Badge variant="destructive">Critical</Badge>
                    <span>Immediate processing</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Delay */}
          <div className="space-y-2">
            <Label htmlFor="delay">Start Delay (seconds)</Label>
            <Input
              id="delay"
              type="number"
              min="0"
              max="3600"
              placeholder="0"
              value={formData.delay ? formData.delay / 1000 : ''}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                delay: e.target.value ? parseInt(e.target.value) * 1000 : undefined 
              }))}
            />
            <p className="text-xs text-muted-foreground">
              Delay before starting the job (0 for immediate start)
            </p>
          </div>
        </CardContent>
      </Card>

      {/* URLs Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-base">
            <Globe className="h-4 w-4 mr-2" />
            Target URLs
          </CardTitle>
          <CardDescription>
            Add the URLs you want to scrape
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {urls.map((url, index) => (
            <div key={index} className="flex items-center space-x-2">
              <div className="flex-1 space-y-1">
                <Input
                  placeholder="https://example.com/laptops"
                  value={url}
                  onChange={(e) => handleUrlChange(index, e.target.value)}
                />
                {errors[`url_${index}`] && (
                  <p className="text-sm text-red-600">{errors[`url_${index}`]}</p>
                )}
              </div>
              {urls.length > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeUrl(index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          ))}
          
          {errors.urls && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{errors.urls}</AlertDescription>
            </Alert>
          )}
          
          <Button
            type="button"
            variant="outline"
            onClick={addUrl}
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Another URL
          </Button>
        </CardContent>
      </Card>

      {/* Advanced Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-base">
            <Zap className="h-4 w-4 mr-2" />
            Advanced Configuration
          </CardTitle>
          <CardDescription>
            Fine-tune the scraping behavior and performance
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Batch Size */}
            <div className="space-y-2">
              <Label htmlFor="batchSize">Batch Size</Label>
              <Input
                id="batchSize"
                type="number"
                min="1"
                max="100"
                value={formData.config?.batchSize || 10}
                onChange={(e) => updateConfig('batchSize', parseInt(e.target.value))}
              />
              {errors.batchSize && (
                <p className="text-sm text-red-600">{errors.batchSize}</p>
              )}
              <p className="text-xs text-muted-foreground">
                Number of URLs to process in each batch
              </p>
            </div>

            {/* Max Concurrent */}
            <div className="space-y-2">
              <Label htmlFor="maxConcurrent">Max Concurrent</Label>
              <Input
                id="maxConcurrent"
                type="number"
                min="1"
                max="20"
                value={formData.config?.maxConcurrent || 3}
                onChange={(e) => updateConfig('maxConcurrent', parseInt(e.target.value))}
              />
              {errors.maxConcurrent && (
                <p className="text-sm text-red-600">{errors.maxConcurrent}</p>
              )}
              <p className="text-xs text-muted-foreground">
                Maximum concurrent requests
              </p>
            </div>

            {/* Timeout */}
            <div className="space-y-2">
              <Label htmlFor="timeout">Timeout (seconds)</Label>
              <Input
                id="timeout"
                type="number"
                min="5"
                max="300"
                value={formData.config?.timeout ? formData.config.timeout / 1000 : 30}
                onChange={(e) => updateConfig('timeout', parseInt(e.target.value))}
              />
              {errors.timeout && (
                <p className="text-sm text-red-600">{errors.timeout}</p>
              )}
              <p className="text-xs text-muted-foreground">
                Request timeout in seconds
              </p>
            </div>

            {/* Retry Attempts */}
            <div className="space-y-2">
              <Label htmlFor="retryAttempts">Retry Attempts</Label>
              <Input
                id="retryAttempts"
                type="number"
                min="0"
                max="10"
                value={formData.config?.retryAttempts || 3}
                onChange={(e) => updateConfig('retryAttempts', parseInt(e.target.value))}
              />
              {errors.retryAttempts && (
                <p className="text-sm text-red-600">{errors.retryAttempts}</p>
              )}
              <p className="text-xs text-muted-foreground">
                Number of retry attempts on failure
              </p>
            </div>
          </div>

          <Separator />

          {/* Delay Between Batches */}
          <div className="space-y-2">
            <Label htmlFor="delayBetweenBatches">Delay Between Batches (ms)</Label>
            <Input
              id="delayBetweenBatches"
              type="number"
              min="0"
              max="60000"
              value={formData.config?.delayBetweenBatches || 1000}
              onChange={(e) => updateConfig('delayBetweenBatches', parseInt(e.target.value))}
            />
            <p className="text-xs text-muted-foreground">
              Delay between processing batches to avoid rate limiting
            </p>
          </div>

          <Separator />

          {/* Feature Toggles */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="enableScreenshots">Enable Screenshots</Label>
                <p className="text-xs text-muted-foreground">
                  Capture screenshots of scraped pages
                </p>
              </div>
              <Switch
                id="enableScreenshots"
                checked={formData.config?.enableScreenshots || false}
                onCheckedChange={(checked) => updateConfig('enableScreenshots', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="saveToDatabase">Save to Database</Label>
                <p className="text-xs text-muted-foreground">
                  Automatically save scraped data to database
                </p>
              </div>
              <Switch
                id="saveToDatabase"
                checked={formData.config?.saveToDatabase !== false}
                onCheckedChange={(checked) => updateConfig('saveToDatabase', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end space-x-2">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Creating Job...
            </>
          ) : (
            <>
              <Plus className="h-4 w-4 mr-2" />
              Create Scraping Job
            </>
          )}
        </Button>
      </div>

      {/* Info Alert */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          The job will be queued and processed according to its priority. 
          You can monitor progress in the Job Management tab.
        </AlertDescription>
      </Alert>
    </form>
  )
}
