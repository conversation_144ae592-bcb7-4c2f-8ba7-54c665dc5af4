'use client'

/**
 * Job Management Tab Component
 * Comprehensive job listing, filtering, and management interface
 */

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Search, 
  Filter, 
  Download, 
  Trash2, 
  RotateCcw, 
  Eye, 
  Play,
  Pause,
  Square,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import { type EnhancedScrapingJob } from '@/hooks/use-scraping'

interface JobFilters {
  status?: string
  priority?: string
  type?: string
  batchId?: string
  search?: string
}

interface JobManagementTabProps {
  jobsData?: {
    jobs: EnhancedScrapingJob[]
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
      hasMore: boolean
    }
  }
  isLoading: boolean
  error: Error | null
  filters: JobFilters
  onFiltersChange: (filters: JobFilters) => void
  selectedJobs: string[]
  onSelectedJobsChange: (jobIds: string[]) => void
  onJobAction: (action: 'cancel' | 'retry', jobId: string) => void
  onBulkAction: (action: 'cancel' | 'retry') => void
  page: number
  onPageChange: (page: number) => void
  getStatusColor: (status: string) => string
  getPriorityColor: (priority: string) => string
}

export function JobManagementTab({
  jobsData,
  isLoading,
  error,
  filters,
  onFiltersChange,
  selectedJobs,
  onSelectedJobsChange,
  onJobAction,
  onBulkAction,
  page,
  onPageChange,
  getStatusColor,
  getPriorityColor
}: JobManagementTabProps) {
  const [showJobDetails, setShowJobDetails] = useState<string | null>(null)

  const handleSelectAll = (checked: boolean) => {
    if (checked && jobsData?.jobs) {
      onSelectedJobsChange(jobsData.jobs.map(job => job.id))
    } else {
      onSelectedJobsChange([])
    }
  }

  const handleSelectJob = (jobId: string, checked: boolean) => {
    if (checked) {
      onSelectedJobsChange([...selectedJobs, jobId])
    } else {
      onSelectedJobsChange(selectedJobs.filter(id => id !== jobId))
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'running': return <Play className="h-4 w-4 text-blue-600" />
      case 'pending': return <Clock className="h-4 w-4 text-yellow-600" />
      case 'failed': return <XCircle className="h-4 w-4 text-red-600" />
      case 'cancelled': return <Square className="h-4 w-4 text-gray-600" />
      case 'paused': return <Pause className="h-4 w-4 text-orange-600" />
      default: return <AlertTriangle className="h-4 w-4 text-gray-600" />
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const formatDuration = (startTime?: string, endTime?: string) => {
    if (!startTime) return '-'
    const start = new Date(startTime)
    const end = endTime ? new Date(endTime) : new Date()
    const duration = end.getTime() - start.getTime()
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Failed to load jobs: {error.message}
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-4">
      {/* Filters and Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Job Filters & Actions</CardTitle>
          <CardDescription>
            Filter and manage scraping jobs
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search jobs..."
                  value={filters.search || ''}
                  onChange={(e) => onFiltersChange({ ...filters, search: e.target.value })}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <Select
                value={filters.status || 'all'}
                onValueChange={(value) => onFiltersChange({ 
                  ...filters, 
                  status: value === 'all' ? undefined : value 
                })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="running">Running</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="paused">Paused</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Priority</Label>
              <Select
                value={filters.priority || 'all'}
                onValueChange={(value) => onFiltersChange({ 
                  ...filters, 
                  priority: value === 'all' ? undefined : value 
                })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All priorities" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="normal">Normal</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Type</Label>
              <Select
                value={filters.type || 'all'}
                onValueChange={(value) => onFiltersChange({ 
                  ...filters, 
                  type: value === 'all' ? undefined : value 
                })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="laptop-scraping">Laptop Scraping</SelectItem>
                  <SelectItem value="price-monitoring">Price Monitoring</SelectItem>
                  <SelectItem value="spec-extraction">Spec Extraction</SelectItem>
                  <SelectItem value="discovery">URL Discovery</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Batch ID</Label>
              <Input
                placeholder="Batch ID..."
                value={filters.batchId || ''}
                onChange={(e) => onFiltersChange({ ...filters, batchId: e.target.value })}
              />
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedJobs.length > 0 && (
            <div className="flex items-center space-x-2 p-3 bg-muted rounded-lg">
              <span className="text-sm font-medium">
                {selectedJobs.length} job(s) selected
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkAction('cancel')}
              >
                <Square className="h-4 w-4 mr-1" />
                Cancel Selected
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkAction('retry')}
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                Retry Selected
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onSelectedJobsChange([])}
              >
                Clear Selection
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Jobs Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Jobs</CardTitle>
          <CardDescription>
            {jobsData?.pagination.total || 0} total jobs
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2">Loading jobs...</span>
            </div>
          ) : (
            <div className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedJobs.length === jobsData?.jobs.length && jobsData?.jobs.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead>Job ID</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {jobsData?.jobs.map((job) => (
                    <TableRow key={job.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedJobs.includes(job.id)}
                          onCheckedChange={(checked) => handleSelectJob(job.id, checked as boolean)}
                        />
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {job.id.slice(0, 8)}...
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{job.type}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(job.status)}
                          <Badge className={getStatusColor(job.status)}>
                            {job.status}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getPriorityColor(job.priority) as any}>
                          {job.priority}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <Progress value={job.progress} className="w-20" />
                          <span className="text-xs text-muted-foreground">
                            {job.progress}%
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="text-sm">
                        {formatDuration(job.startedAt, job.completedAt)}
                      </TableCell>
                      <TableCell className="text-sm">
                        {formatDate(job.createdAt)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl">
                              <DialogHeader>
                                <DialogTitle>Job Details</DialogTitle>
                                <DialogDescription>
                                  Detailed information for job {job.id}
                                </DialogDescription>
                              </DialogHeader>
                              <JobDetailsView job={job} />
                            </DialogContent>
                          </Dialog>
                          
                          {(job.status === 'running' || job.status === 'pending') && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onJobAction('cancel', job.id)}
                            >
                              <Square className="h-4 w-4" />
                            </Button>
                          )}
                          
                          {(job.status === 'failed' || job.status === 'cancelled') && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onJobAction('retry', job.id)}
                            >
                              <RotateCcw className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {jobsData?.pagination && jobsData.pagination.pages > 1 && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing {((page - 1) * jobsData.pagination.limit) + 1} to{' '}
                    {Math.min(page * jobsData.pagination.limit, jobsData.pagination.total)} of{' '}
                    {jobsData.pagination.total} jobs
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onPageChange(page - 1)}
                      disabled={page <= 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    <span className="text-sm">
                      Page {page} of {jobsData.pagination.pages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onPageChange(page + 1)}
                      disabled={page >= jobsData.pagination.pages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// Job Details Component
function JobDetailsView({ job }: { job: EnhancedScrapingJob }) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label className="text-sm font-medium">Job ID</Label>
          <p className="font-mono text-sm">{job.id}</p>
        </div>
        <div>
          <Label className="text-sm font-medium">Type</Label>
          <p className="text-sm">{job.type}</p>
        </div>
        <div>
          <Label className="text-sm font-medium">Status</Label>
          <p className="text-sm">{job.status}</p>
        </div>
        <div>
          <Label className="text-sm font-medium">Priority</Label>
          <p className="text-sm">{job.priority}</p>
        </div>
        <div>
          <Label className="text-sm font-medium">Progress</Label>
          <p className="text-sm">{job.progress}%</p>
        </div>
        <div>
          <Label className="text-sm font-medium">Attempts</Label>
          <p className="text-sm">{job.attempts} / {job.maxAttempts}</p>
        </div>
      </div>
      
      {job.batchId && (
        <div>
          <Label className="text-sm font-medium">Batch ID</Label>
          <p className="font-mono text-sm">{job.batchId}</p>
        </div>
      )}
      
      {job.sourceId && (
        <div>
          <Label className="text-sm font-medium">Source ID</Label>
          <p className="font-mono text-sm">{job.sourceId}</p>
        </div>
      )}
      
      {job.error && (
        <div>
          <Label className="text-sm font-medium">Error</Label>
          <p className="text-sm text-red-600">{job.error}</p>
        </div>
      )}
      
      {job.config && (
        <div>
          <Label className="text-sm font-medium">Configuration</Label>
          <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-32">
            {JSON.stringify(job.config, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}
