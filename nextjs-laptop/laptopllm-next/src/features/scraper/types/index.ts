/**
 * Scraper feature specific types
 */

export interface ScrapingJob {
  id: string
  url: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  type: 'single' | 'batch' | 'scheduled'
  config: ScrapingConfig
  result?: ScrapingResult
  error?: string
  createdAt: Date
  startedAt?: Date
  completedAt?: Date
  retryCount: number
  maxRetries: number
  nextRetryAt?: Date
}

export interface ScrapingConfig {
  url: string
  selectors?: Record<string, string>
  waitFor?: number
  timeout?: number
  userAgent?: string
  headers?: Record<string, string>
  proxy?: string
  javascript?: boolean
  screenshots?: boolean
  followRedirects?: boolean
  maxDepth?: number
  respectRobots?: boolean
  rateLimit?: {
    requests: number
    period: number // in milliseconds
  }
}

export interface ScrapingResult {
  success: boolean
  data?: Record<string, unknown> | string | number | null
  error?: string
  scraper: string
  duration: number
  timestamp: Date
  metadata?: {
    statusCode?: number
    contentType?: string
    contentLength?: number
    redirects?: string[]
    screenshots?: string[]
  }
}

export interface ScrapingHealth {
  healthy: boolean
  scrapers: {
    firecrawl: boolean
    puppeteer: boolean
  }
  timestamp: Date
  metrics?: {
    totalRequests: number
    successRate: number
    averageResponseTime: number
    errorRate: number
  }
}

export interface ScrapingQueue {
  pending: ScrapingJob[]
  running: ScrapingJob[]
  completed: ScrapingJob[]
  failed: ScrapingJob[]
  total: number
  stats: {
    successRate: number
    averageProcessingTime: number
    queueLength: number
    workersActive: number
  }
}

export interface ScrapingSchedule {
  id: string
  name: string
  description?: string
  config: ScrapingConfig
  schedule: {
    type: 'interval' | 'cron' | 'once'
    value: string // cron expression or interval in ms
    timezone?: string
  }
  enabled: boolean
  lastRun?: Date
  nextRun?: Date
  runCount: number
  successCount: number
  failureCount: number
  createdAt: Date
  updatedAt: Date
}

export interface ScrapingMetrics {
  period: 'hour' | 'day' | 'week' | 'month'
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  averageResponseTime: number
  dataPointsCollected: number
  bytesTransferred: number
  uniqueUrls: number
  scraperUsage: {
    firecrawl: number
    puppeteer: number
  }
  errorBreakdown: Record<string, number>
  performanceByHour: Array<{
    hour: number
    requests: number
    successRate: number
    avgResponseTime: number
  }>
}

export interface ScrapingTarget {
  id: string
  name: string
  baseUrl: string
  category: 'laptop-store' | 'marketplace' | 'manufacturer' | 'review-site'
  config: ScrapingConfig
  selectors: {
    productList: string
    productTitle: string
    productPrice: string
    productImage: string
    productSpecs: Record<string, string>
    pagination?: string
    availability?: string
  }
  enabled: boolean
  lastScraped?: Date
  successRate: number
  averageResponseTime: number
  dataQuality: number
  priority: number
}

export interface ScrapingRule {
  id: string
  name: string
  description: string
  conditions: Array<{
    field: string
    operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'regex'
    value: string
  }>
  actions: Array<{
    type: 'transform' | 'validate' | 'filter' | 'enrich'
    config: Record<string, unknown>
  }>
  enabled: boolean
  priority: number
}

export interface ScrapingAlert {
  id: string
  type: 'error' | 'warning' | 'info' | 'success'
  title: string
  message: string
  source: string
  timestamp: Date
  acknowledged: boolean
  severity: 'low' | 'medium' | 'high' | 'critical'
  metadata?: Record<string, unknown>
}

export interface ScrapingBatch {
  id: string
  name: string
  description?: string
  urls: string[]
  config: ScrapingConfig
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  progress: {
    total: number
    completed: number
    failed: number
    percentage: number
  }
  results: ScrapingResult[]
  createdAt: Date
  startedAt?: Date
  completedAt?: Date
  estimatedCompletion?: Date
}

export interface ScrapingWorker {
  id: string
  name: string
  status: 'idle' | 'busy' | 'error' | 'offline'
  currentJob?: string
  capabilities: string[]
  performance: {
    jobsCompleted: number
    averageJobTime: number
    successRate: number
    lastActivity: Date
  }
  resources: {
    cpuUsage: number
    memoryUsage: number
    diskUsage: number
  }
}

export interface ScrapingTemplate {
  id: string
  name: string
  description: string
  category: string
  config: ScrapingConfig
  selectors: Record<string, string>
  rules: ScrapingRule[]
  tags: string[]
  usageCount: number
  rating: number
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

export interface DataValidationRule {
  field: string
  type: 'required' | 'format' | 'range' | 'custom'
  config: {
    pattern?: string
    min?: number
    max?: number
    customValidator?: string
  }
  errorMessage: string
}

export interface ScrapingReport {
  id: string
  period: {
    start: Date
    end: Date
  }
  summary: {
    totalJobs: number
    successfulJobs: number
    failedJobs: number
    dataPointsCollected: number
    uniqueProducts: number
    averageJobTime: number
  }
  performance: {
    scraperComparison: Array<{
      scraper: string
      jobs: number
      successRate: number
      avgResponseTime: number
    }>
    targetPerformance: Array<{
      target: string
      jobs: number
      successRate: number
      dataQuality: number
    }>
  }
  issues: {
    errors: ScrapingAlert[]
    warnings: ScrapingAlert[]
    recommendations: string[]
  }
  trends: {
    dailyVolume: Array<{
      date: string
      jobs: number
      success: number
      failed: number
    }>
    performanceMetrics: Array<{
      date: string
      avgResponseTime: number
      successRate: number
    }>
  }
  generatedAt: Date
}

// Export shared types from lib
export type {
  ScrapingConfig as LibScrapingConfig,
  ScrapingResult as LibScrapingResult,
  ScrapingHealth as LibScrapingHealth,
} from '@/lib/scraping/types'
