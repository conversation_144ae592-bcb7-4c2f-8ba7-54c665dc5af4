import type { 
  LaptopData, 
  LaptopSearchParams, 
  LaptopSearchResult,
  LaptopFilters,
  LaptopRecommendation,
  LaptopAvailability,
  LaptopPriceHistory 
} from '../types'

/**
 * Service for laptop data management and operations
 */
export class LaptopService {
  private baseUrl = '/api/laptops'

  /**
   * Search laptops with filters and pagination
   */
  async searchLaptops(params: LaptopSearchParams): Promise<LaptopSearchResult> {
    const searchParams = new URLSearchParams()
    
    if (params.query) searchParams.set('q', params.query)
    if (params.sortBy) searchParams.set('sortBy', params.sortBy)
    if (params.sortOrder) searchParams.set('sortOrder', params.sortOrder)
    if (params.page) searchParams.set('page', params.page.toString())
    if (params.limit) searchParams.set('limit', params.limit.toString())
    
    // Add filters
    if (params.filters) {
      Object.entries(params.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.set(`filter.${key}`, JSON.stringify(value))
        }
      })
    }

    const response = await fetch(`${this.baseUrl}/search?${searchParams}`)
    if (!response.ok) {
      throw new Error(`Search failed: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get laptop by ID
   */
  async getLaptopById(id: string): Promise<LaptopData> {
    const response = await fetch(`${this.baseUrl}/${id}`)
    if (!response.ok) {
      if (response.status === 404) {
        throw new Error('Laptop not found')
      }
      throw new Error(`Failed to fetch laptop: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get multiple laptops by IDs
   */
  async getLaptopsByIds(ids: string[]): Promise<LaptopData[]> {
    const response = await fetch(`${this.baseUrl}/batch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ids }),
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch laptops: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get all available laptop categories
   */
  async getCategories(): Promise<Array<{ value: string; label: string; count: number }>> {
    const response = await fetch(`${this.baseUrl}/categories`)
    if (!response.ok) {
      throw new Error(`Failed to fetch categories: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get all available brands
   */
  async getBrands(): Promise<Array<{ value: string; label: string; count: number }>> {
    const response = await fetch(`${this.baseUrl}/brands`)
    if (!response.ok) {
      throw new Error(`Failed to fetch brands: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get price range for filtering
   */
  async getPriceRange(): Promise<{ min: number; max: number }> {
    const response = await fetch(`${this.baseUrl}/price-range`)
    if (!response.ok) {
      throw new Error(`Failed to fetch price range: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get laptop recommendations based on criteria
   */
  async getRecommendations(criteria: {
    budget?: number
    usage?: string[]
    preferences?: string[]
  }): Promise<LaptopRecommendation[]> {
    const response = await fetch(`${this.baseUrl}/recommendations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(criteria),
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch recommendations: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get similar laptops
   */
  async getSimilarLaptops(laptopId: string, limit = 5): Promise<LaptopData[]> {
    const response = await fetch(`${this.baseUrl}/${laptopId}/similar?limit=${limit}`)
    if (!response.ok) {
      throw new Error(`Failed to fetch similar laptops: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get laptop availability and pricing
   */
  async getLaptopAvailability(laptopId: string): Promise<LaptopAvailability> {
    const response = await fetch(`${this.baseUrl}/${laptopId}/availability`)
    if (!response.ok) {
      throw new Error(`Failed to fetch availability: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get laptop price history
   */
  async getLaptopPriceHistory(laptopId: string, days = 30): Promise<LaptopPriceHistory> {
    const response = await fetch(`${this.baseUrl}/${laptopId}/price-history?days=${days}`)
    if (!response.ok) {
      throw new Error(`Failed to fetch price history: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Refresh laptop data (trigger scraping)
   */
  async refreshLaptopData(laptopId?: string): Promise<{ success: boolean; message: string }> {
    const url = laptopId ? `${this.baseUrl}/${laptopId}/refresh` : `${this.baseUrl}/refresh`
    
    const response = await fetch(url, {
      method: 'POST',
    })

    if (!response.ok) {
      throw new Error(`Failed to refresh data: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get laptop statistics
   */
  async getLaptopStats(): Promise<{
    total: number
    categories: Record<string, number>
    brands: Record<string, number>
    priceRanges: Record<string, number>
    lastUpdated: Date
  }> {
    const response = await fetch(`${this.baseUrl}/stats`)
    if (!response.ok) {
      throw new Error(`Failed to fetch stats: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Compare laptops specifications
   */
  async compareLaptops(laptopIds: string[]): Promise<{
    laptops: LaptopData[]
    comparison: Array<{
      category: string
      specs: Array<{
        field: string
        label: string
        values: Array<{
          laptopId: string
          value: string | number
          score?: number
        }>
        winner?: string
      }>
    }>
  }> {
    const response = await fetch(`${this.baseUrl}/compare`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ laptopIds }),
    })

    if (!response.ok) {
      throw new Error(`Failed to compare laptops: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get trending laptops
   */
  async getTrendingLaptops(limit = 10): Promise<LaptopData[]> {
    const response = await fetch(`${this.baseUrl}/trending?limit=${limit}`)
    if (!response.ok) {
      throw new Error(`Failed to fetch trending laptops: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get featured laptops
   */
  async getFeaturedLaptops(category?: string): Promise<LaptopData[]> {
    const url = category 
      ? `${this.baseUrl}/featured?category=${category}`
      : `${this.baseUrl}/featured`
      
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`Failed to fetch featured laptops: ${response.statusText}`)
    }

    return response.json()
  }
}

// Export singleton instance
export const laptopService = new LaptopService()
