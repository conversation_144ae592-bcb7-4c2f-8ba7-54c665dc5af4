/**
 * Laptop Deals Service
 * Handles business logic for laptop deals, price tracking, and deal analysis
 */

// Define a type for the metrics returned by calculateDealMetrics
interface DealMetrics {
  priceRank: number;
  historicalLow: boolean;
  priceTrend: 'rising' | 'falling' | 'stable';
  demandLevel: 'low' | 'medium' | 'high';
}

// Define a more specific type for the listing object from prisma
// This is a simplified representation based on the includes
interface PrismaLaptopListingWithIncludes {
  id: number;
  laptop_id: number;
  source_id: number;
  price: number;
  url: string;
  in_stock: boolean;
  shipping_cost: number | null;
  rating: number | null;
  reviews_count: number | null;
  listing_date: Date;
  free_shipping: boolean;
  estimated_delivery_days: number | null;
  processed: boolean;
  sources: { id: number; name: string };
  price_history: PriceHistoryEntry[];
  laptops: {
    id: number;
    title: string;
    brands: { id: number; name: string };
    laptop_cpus: { cpus: { model: string } }[];
    laptop_gpus: { gpus: { model: string } }[];
    laptop_ram: { ram_configurations: { ram_type: string; speed: number } }[];
  };
}



import { prisma, Prisma } from '@/lib/prisma'
import type {
  LaptopDeal,
  LaptopListing,
  DealFilters,
  DealsSearchResult,
  PriceHistoryEntry,
  DealAlert,
  DealNotification
} from '@/shared/types'

export class DealsService {
  /**
   * Get current deals with filtering and pagination
   */
  async getDeals(filters: DealFilters = {}, page = 1, limit = 20): Promise<DealsSearchResult> {
    try {
      const offset = (page - 1) * limit

      // Build where clause for filtering
      const whereClause: Prisma.LaptopListingsWhereInput = {
        processed: true,
        laptops: {
          is_available: true
        }
      }

      if (filters.inStockOnly) {
        whereClause.in_stock = true
      }

      if (filters.freeShippingOnly) {
        whereClause.free_shipping = true
      }

      if (filters.maxPrice) {
        whereClause.price = {
          lte: filters.maxPrice
        }
      }

      if (filters.sources?.length) {
        whereClause.sources = {
          name: {
            in: filters.sources
          }
        }
      }

      if (filters.brands?.length) {
        whereClause.laptops = {
          ...whereClause.laptops,
          brands: {
            name: {
              in: filters.brands
            }
          }
        }
      }

      // Get listings with related data
      const listings = await prisma.laptop_listings.findMany({
        where: whereClause,
        include: {
          laptops: {
            include: {
              brands: true,
              laptop_cpus: {
                include: {
                  cpus: true
                }
              },
              laptop_gpus: {
                include: {
                  gpus: true
                }
              },
              laptop_ram: {
                include: {
                  ram_configurations: true
                }
              }
            }
          },
          sources: true,
          price_history: {
            orderBy: {
              recorded_at: 'desc'
            },
            take: 30 // Last 30 price points
          }
        },
        orderBy: [
          { price: 'asc' },
          { listing_date: 'desc' }
        ],
        skip: offset,
        take: limit
      })

      // Get total count
      const total = await prisma.laptop_listings.count({
        where: whereClause
      })

      // Transform listings to deals
      const deals = await Promise.all(
        listings.map(listing => this.transformListingToDeal(listing))
      )

      // Filter deals based on deal-specific criteria
      const filteredDeals = this.filterDeals(deals, filters)

      // Calculate summary statistics
      const summary = this.calculateDealsSummary(filteredDeals)

      return {
        deals: filteredDeals,
        total,
        page,
        limit,
        hasMore: offset + limit < total,
        summary
      }
    } catch (error) {
      console.error('Error fetching deals:', error)
      throw new Error('Failed to fetch deals')
    }
  }

  /**
   * Get deals for a specific laptop
   */
  async getLaptopDeals(laptopId: number): Promise<LaptopDeal[]> {
    try {
      const listings = await prisma.laptop_listings.findMany({
        where: {
          laptop_id: laptopId,
          processed: true
        },
        include: {
          laptops: {
            include: {
              brands: true
            }
          },
          sources: true,
          price_history: {
            orderBy: {
              recorded_at: 'desc'
            },
            take: 30
          }
        },
        orderBy: {
          price: 'asc'
        }
      })

      return Promise.all(
        listings.map(listing => this.transformListingToDeal(listing))
      )
    } catch (error) {
      console.error('Error fetching laptop deals:', error)
      throw new Error('Failed to fetch laptop deals')
    }
  }

  /**
   * Get hot deals (best deals based on scoring algorithm)
   */
  async getHotDeals(limit = 10): Promise<LaptopDeal[]> {
    const deals = await this.getDeals({}, 1, 100)
    
    // Sort by deal score and return top deals
    return deals.deals
      .filter(deal => deal.isHotDeal)
      .sort((a, b) => b.dealScore - a.dealScore)
      .slice(0, limit)
  }

  /**
   * Transform a database listing to a deal object
   */
  private async transformListingToDeal(listing: PrismaLaptopListingWithIncludes): Promise<LaptopDeal> {
    const priceHistory = listing.price_history || []
    const currentPrice = listing.price
    
    // Calculate deal metrics
    const dealMetrics = this.calculateDealMetrics(listing, priceHistory)
    const dealType = this.determineDealType(listing, priceHistory)
    const dealScore = this.calculateDealScore(listing, priceHistory, dealMetrics)

    return {
      id: `deal_${listing.id}`,
      laptop: listing.laptops,
      listing: {
        id: listing.id,
        laptopId: listing.laptop_id,
        sourceId: listing.source_id,
        price: listing.price,
        url: listing.url,
        inStock: listing.in_stock,
        shippingCost: listing.shipping_cost,
        rating: listing.rating,
        reviewsCount: listing.reviews_count,
        listingDate: listing.listing_date,
        freeShipping: listing.free_shipping,
        estimatedDeliveryDays: listing.estimated_delivery_days,
        hasWarranty: listing.has_warranty,
        warrantyMonths: listing.warranty_months,
        processed: listing.processed,
        source: listing.sources,
        priceHistory: priceHistory.map((ph: PriceHistoryEntry) => ({
          id: ph.id,
          listingId: ph.listing_id,
          price: ph.price,
          recordedAt: ph.recorded_at
        }))
      },
      dealType,
      currentPrice,
      originalPrice: this.getOriginalPrice(priceHistory),
      discountAmount: this.calculateDiscountAmount(listing, priceHistory),
      discountPercentage: this.calculateDiscountPercentage(listing, priceHistory),
      savings: this.calculateSavings(listing, priceHistory),
      dealScore,
      isHotDeal: dealScore >= 75, // Threshold for hot deals
      lastUpdated: new Date(),
      priceHistory: priceHistory.map((ph: PriceHistoryEntry) => ({
        id: ph.id,
        listingId: ph.listing_id,
        price: ph.price,
        recordedAt: ph.recorded_at
      })),
      availability: {
        inStock: listing.in_stock,
        stockLevel: this.determineStockLevel(listing),
        lastChecked: new Date()
      },
      shipping: {
        cost: listing.shipping_cost || 0,
        isFree: listing.free_shipping,
        estimatedDays: listing.estimated_delivery_days
      },
      dealMetrics
    }
  }

  /**
   * Calculate deal metrics for scoring
   */
  private calculateDealMetrics(listing: PrismaLaptopListingWithIncludes, priceHistory: PriceHistoryEntry[]) {
    const prices = priceHistory.map(ph => ph.price).filter(p => p > 0)
    const currentPrice = listing.price

    const priceRank = 1 // Will be calculated against other sources
    let historicalLow = false
    let priceTrend: 'rising' | 'falling' | 'stable' = 'stable'

    if (prices.length > 0) {
      const minPrice = Math.min(...prices)
      const maxPrice = Math.max(...prices)
      
      historicalLow = currentPrice <= minPrice
      
      // Calculate trend from last 7 days
      const recentPrices = prices.slice(0, 7)
      if (recentPrices.length >= 2) {
        const oldAvg = recentPrices.slice(-3).reduce((a, b) => a + b, 0) / 3
        const newAvg = recentPrices.slice(0, 3).reduce((a, b) => a + b, 0) / 3
        
        if (newAvg < oldAvg * 0.95) priceTrend = 'falling'
        else if (newAvg > oldAvg * 1.05) priceTrend = 'rising'
      }
    }

    return {
      priceRank,
      historicalLow,
      priceTrend,
      demandLevel: 'medium' as const // Will be enhanced with actual demand data
    }
  }

  /**
   * Determine the type of deal
   */
  private determineDealType(listing: PrismaLaptopListingWithIncludes, priceHistory: PriceHistoryEntry[]): LaptopDeal['dealType'] {
    if (priceHistory.length === 0) return 'new-listing'
    
    const currentPrice = listing.price
    const recentPrices = priceHistory.slice(0, 5).map(ph => ph.price)
    
    if (recentPrices.length > 0) {
      const avgRecentPrice = recentPrices.reduce((a, b) => a + b, 0) / recentPrices.length
      
      if (currentPrice < avgRecentPrice * 0.9) return 'price-drop'
      if (!listing.in_stock && priceHistory.some((ph: PriceHistoryEntry) => !ph.in_stock)) return 'back-in-stock'
    }
    
    // Check if it's the best price among sources (simplified)
    return 'best-price'
  }

  /**
   * Calculate overall deal score (0-100)
   */
  private calculateDealScore(listing: PrismaLaptopListingWithIncludes, priceHistory: PriceHistoryEntry[], metrics: DealMetrics): number {
    let score = 50 // Base score
    
    // Price competitiveness (30 points)
    if (metrics.historicalLow) score += 30
    else if (metrics.priceTrend === 'falling') score += 20
    else if (metrics.priceTrend === 'rising') score -= 10
    
    // Availability (20 points)
    if (listing.in_stock) score += 20
    
    // Shipping (10 points)
    if (listing.free_shipping) score += 10
    
    // Rating and reviews (10 points)
    if (listing.rating && listing.rating >= 4.0) score += 10
    else if (listing.rating && listing.rating >= 3.5) score += 5
    
    // Warranty (5 points)
    if (listing.has_warranty) score += 5
    
    // Delivery speed (5 points)
    if (listing.estimated_delivery_days && listing.estimated_delivery_days <= 2) score += 5
    
    return Math.max(0, Math.min(100, score))
  }

  // Helper methods for calculations
  private getOriginalPrice(priceHistory: PriceHistoryEntry[]): number | undefined {
    if (priceHistory.length === 0) return undefined
    return Math.max(...priceHistory.map(ph => ph.price))
  }

  private calculateDiscountAmount(listing: PrismaLaptopListingWithIncludes, priceHistory: PriceHistoryEntry[]): number | undefined {
    const originalPrice = this.getOriginalPrice(priceHistory)
    if (!originalPrice) return undefined
    return originalPrice - listing.price
  }

  private calculateDiscountPercentage(listing: PrismaLaptopListingWithIncludes, priceHistory: PriceHistoryEntry[]): number | undefined {
    const originalPrice = this.getOriginalPrice(priceHistory)
    if (!originalPrice) return undefined
    return ((originalPrice - listing.price) / originalPrice) * 100
  }

  private calculateSavings(listing: PrismaLaptopListingWithIncludes, priceHistory: PriceHistoryEntry[]): number | undefined {
    return this.calculateDiscountAmount(listing, priceHistory)
  }

  private determineStockLevel(listing: PrismaLaptopListingWithIncludes): 'low' | 'medium' | 'high' | undefined {
    // This would be enhanced with actual stock data
    return listing.in_stock ? 'medium' : undefined
  }

  private filterDeals(deals: LaptopDeal[], filters: DealFilters): LaptopDeal[] {
    return deals.filter(deal => {
      if (filters.dealTypes?.length && !filters.dealTypes.includes(deal.dealType)) {
        return false
      }
      
      if (filters.minDiscount && (!deal.discountPercentage || deal.discountPercentage < filters.minDiscount)) {
        return false
      }
      
      if (filters.onlyHotDeals && !deal.isHotDeal) {
        return false
      }
      
      if (filters.minDealScore && deal.dealScore < filters.minDealScore) {
        return false
      }
      
      return true
    })
  }

  private calculateDealsSummary(deals: LaptopDeal[]) {
    const totalDeals = deals.length
    const hotDeals = deals.filter(d => d.isHotDeal).length
    const discounts = deals.map(d => d.discountPercentage).filter(d => d !== undefined) as number[]
    const savings = deals.map(d => d.savings).filter(s => s !== undefined) as number[]
    
    return {
      totalDeals,
      hotDeals,
      averageDiscount: discounts.length > 0 ? discounts.reduce((a, b) => a + b, 0) / discounts.length : 0,
      topSavings: savings.length > 0 ? Math.max(...savings) : 0,
      newDealsToday: deals.filter(d => {
        const today = new Date()
        const dealDate = new Date(d.listing.listingDate)
        return dealDate.toDateString() === today.toDateString()
      }).length
    }
  }
}

export const dealsService = new DealsService()
