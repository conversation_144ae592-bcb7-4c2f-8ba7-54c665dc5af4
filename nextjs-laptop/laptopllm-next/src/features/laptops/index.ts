/**
 * Laptops feature exports
 * Centralized exports for the laptops feature module
 */

// Types
export type * from './types'

// Services
export { laptopService } from './services/laptop-service'
export { dealsService } from './services/deals-service'

// Hooks
export { 
  useLaptopSearch,
  useLaptopSearchSuggestions,
  usePopularSearches 
} from './hooks/use-laptop-search'

export {
  useLaptopDetails,
  useSimilarLaptops,
  useLaptopAvailability,
  useLaptopPriceHistory,
  useLaptopsByIds,
  useLaptopComparison,
  useTrendingLaptops,
  useFeaturedLaptops,
  useLaptopStats
} from './hooks/use-laptop-details'

export {
  useDeals,
  useLaptopDeals,
  useHotDeals,
  useDealAlerts,
  useDealNotifications,
  useDealComparison,
  useDealPriceHistory,
  useDealStats
} from './hooks/use-deals'

// Components
export { LaptopCard } from './components/laptop-card'
export { DealsDashboard } from './components/deals-dashboard'
export { DealCard } from './components/deal-card'
export { DealsSummary } from './components/deals-summary'
export { DealsFilters } from './components/deals-filters'
export { HotDealsCarousel } from './components/hot-deals-carousel'

// Utils (to be created)
export * from './utils'
