/**
 * Laptop feature utility functions
 */

import type { LaptopData, LaptopFilters, LaptopSpecComparison } from '../types'

/**
 * Format laptop price with currency
 */
export function formatPrice(price: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price)
}

/**
 * Format laptop specifications for display
 */
export function formatSpecs(laptop: LaptopData): Record<string, string> {
  const specs = laptop.specifications
  
  return {
    processor: `${specs.cpu.brand} ${specs.cpu.model}`,
    memory: `${specs.memory.size}GB ${specs.memory.type}`,
    storage: `${specs.storage.capacity}GB ${specs.storage.type}`,
    graphics: `${specs.gpu.brand} ${specs.gpu.model}`,
    display: `${specs.display.size}" ${specs.display.resolution}`,
    weight: `${laptop.features.weight}kg`,
    battery: `${laptop.features.batteryLife}h`,
  }
}

/**
 * Generate laptop slug for URLs
 */
export function generateLaptopSlug(laptop: LaptopData): string {
  const brand = laptop.brand.toLowerCase().replace(/[^a-z0-9]/g, '-')
  const model = laptop.model.toLowerCase().replace(/[^a-z0-9]/g, '-')
  return `${brand}-${model}`.replace(/-+/g, '-').replace(/^-|-$/g, '')
}

/**
 * Check if laptop matches filters
 */
export function matchesFilters(laptop: LaptopData, filters: LaptopFilters): boolean {
  // Category filter
  if (filters.category && filters.category.length > 0) {
    if (!filters.category.includes(laptop.features.category)) {
      return false
    }
  }

  // Price range filter
  if (filters.priceRange) {
    if (laptop.price < filters.priceRange.min || laptop.price > filters.priceRange.max) {
      return false
    }
  }

  // Brand filter
  if (filters.brands && filters.brands.length > 0) {
    if (!filters.brands.includes(laptop.brand)) {
      return false
    }
  }

  // CPU brand filter
  if (filters.cpuBrands && filters.cpuBrands.length > 0) {
    if (!filters.cpuBrands.includes(laptop.specifications.cpu.brand)) {
      return false
    }
  }

  // GPU brand filter
  if (filters.gpuBrands && filters.gpuBrands.length > 0) {
    if (!filters.gpuBrands.includes(laptop.specifications.gpu.brand)) {
      return false
    }
  }

  // Memory size filter
  if (filters.memorySize && filters.memorySize.length > 0) {
    if (!filters.memorySize.includes(laptop.specifications.memory.size)) {
      return false
    }
  }

  // Storage type filter
  if (filters.storageType && filters.storageType.length > 0) {
    if (!filters.storageType.includes(laptop.specifications.storage.type)) {
      return false
    }
  }

  // Display size filter
  if (filters.displaySize) {
    const size = laptop.specifications.display.size
    if (size < filters.displaySize.min || size > filters.displaySize.max) {
      return false
    }
  }

  // Weight filter
  if (filters.weight) {
    if (laptop.features.weight > filters.weight.max) {
      return false
    }
  }

  // Battery life filter
  if (filters.batteryLife) {
    if (laptop.features.batteryLife < filters.batteryLife.min) {
      return false
    }
  }

  // Stock filter
  if (filters.inStock !== undefined) {
    if (laptop.availability?.inStock !== filters.inStock) {
      return false
    }
  }

  return true
}

/**
 * Sort laptops by specified criteria
 */
export function sortLaptops(
  laptops: LaptopData[], 
  sortBy: string, 
  sortOrder: 'asc' | 'desc' = 'desc'
): LaptopData[] {
  const sorted = [...laptops].sort((a, b) => {
    let aValue: any
    let bValue: any

    switch (sortBy) {
      case 'price':
        aValue = a.price
        bValue = b.price
        break
      case 'name':
        aValue = a.title.toLowerCase()
        bValue = b.title.toLowerCase()
        break
      case 'brand':
        aValue = a.brand.toLowerCase()
        bValue = b.brand.toLowerCase()
        break
      case 'newest':
        aValue = a.scrapedAt
        bValue = b.scrapedAt
        break
      case 'rating':
        // Placeholder for rating - would need to be added to laptop data
        aValue = 0
        bValue = 0
        break
      default:
        return 0
    }

    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1
    return 0
  })

  return sorted
}

/**
 * Get unique values for filter options
 */
export function getFilterOptions(laptops: LaptopData[]) {
  const brands = [...new Set(laptops.map(l => l.brand))].sort()
  const categories = [...new Set(laptops.map(l => l.features.category))].sort()
  const cpuBrands = [...new Set(laptops.map(l => l.specifications.cpu.brand))].sort()
  const gpuBrands = [...new Set(laptops.map(l => l.specifications.gpu.brand))].sort()
  const memorySizes = [...new Set(laptops.map(l => l.specifications.memory.size))].sort((a, b) => a - b)
  const storageTypes = [...new Set(laptops.map(l => l.specifications.storage.type))].sort()

  const priceRange = laptops.reduce(
    (range, laptop) => ({
      min: Math.min(range.min, laptop.price),
      max: Math.max(range.max, laptop.price),
    }),
    { min: Infinity, max: -Infinity }
  )

  return {
    brands: brands.map(brand => ({ value: brand, label: brand })),
    categories: categories.map(cat => ({ value: cat, label: cat })),
    cpuBrands: cpuBrands.map(brand => ({ value: brand, label: brand })),
    gpuBrands: gpuBrands.map(brand => ({ value: brand, label: brand })),
    memorySizes: memorySizes.map(size => ({ value: size, label: `${size}GB` })),
    storageTypes: storageTypes.map(type => ({ value: type, label: type })),
    priceRange,
  }
}

/**
 * Compare laptop specifications
 */
export function compareLaptopSpecs(laptops: LaptopData[]): LaptopSpecComparison[] {
  if (laptops.length === 0) return []

  const comparisons: LaptopSpecComparison[] = [
    {
      field: 'price',
      label: 'Price',
      category: 'design',
      values: laptops.map(laptop => ({
        laptopId: laptop.url,
        value: laptop.price,
        formatted: formatPrice(laptop.price, laptop.currency),
      })),
    },
    {
      field: 'cpu',
      label: 'Processor',
      category: 'performance',
      values: laptops.map(laptop => ({
        laptopId: laptop.url,
        value: `${laptop.specifications.cpu.brand} ${laptop.specifications.cpu.model}`,
        formatted: `${laptop.specifications.cpu.brand} ${laptop.specifications.cpu.model}`,
      })),
    },
    {
      field: 'memory',
      label: 'Memory',
      category: 'performance',
      values: laptops.map(laptop => ({
        laptopId: laptop.url,
        value: laptop.specifications.memory.size,
        formatted: `${laptop.specifications.memory.size}GB ${laptop.specifications.memory.type}`,
      })),
    },
    {
      field: 'storage',
      label: 'Storage',
      category: 'storage',
      values: laptops.map(laptop => ({
        laptopId: laptop.url,
        value: laptop.specifications.storage.capacity,
        formatted: `${laptop.specifications.storage.capacity}GB ${laptop.specifications.storage.type}`,
      })),
    },
    {
      field: 'display',
      label: 'Display',
      category: 'display',
      values: laptops.map(laptop => ({
        laptopId: laptop.url,
        value: laptop.specifications.display.size,
        formatted: `${laptop.specifications.display.size}" ${laptop.specifications.display.resolution}`,
      })),
    },
    {
      field: 'weight',
      label: 'Weight',
      category: 'design',
      values: laptops.map(laptop => ({
        laptopId: laptop.url,
        value: laptop.features.weight,
        formatted: `${laptop.features.weight}kg`,
      })),
    },
  ]

  // Determine winners for numeric comparisons
  comparisons.forEach(comparison => {
    if (comparison.field === 'price' || comparison.field === 'weight') {
      // Lower is better for price and weight
      const minValue = Math.min(...comparison.values.map(v => Number(v.value)))
      const winner = comparison.values.find(v => Number(v.value) === minValue)
      comparison.winner = winner?.laptopId
    } else if (['memory', 'storage', 'display'].includes(comparison.field)) {
      // Higher is better for memory, storage, display
      const maxValue = Math.max(...comparison.values.map(v => Number(v.value)))
      const winner = comparison.values.find(v => Number(v.value) === maxValue)
      comparison.winner = winner?.laptopId
    }
  })

  return comparisons
}

/**
 * Calculate laptop score based on specifications
 */
export function calculateLaptopScore(laptop: LaptopData): number {
  const specs = laptop.specifications
  
  // CPU score (0-25 points)
  const cpuScore = Math.min(25, (specs.cpu.cores / 16) * 15 + (specs.cpu.boostFrequency / 5) * 10)
  
  // Memory score (0-25 points)
  const memoryScore = Math.min(25, (specs.memory.size / 32) * 25)
  
  // Storage score (0-20 points)
  const storageScore = specs.storage.type === 'SSD' ? 20 : 10
  
  // GPU score (0-20 points)
  let gpuScore = 5 // Base score for integrated graphics
  if (specs.gpu.brand === 'NVIDIA' && specs.gpu.model.includes('RTX')) {
    gpuScore = 20
  } else if (specs.gpu.brand === 'AMD' && specs.gpu.model.includes('RX')) {
    gpuScore = 18
  } else if (specs.gpu.memory > 0) {
    gpuScore = 15
  }
  
  // Display score (0-10 points)
  const displayScore = specs.display.refreshRate > 60 ? 10 : 5
  
  return Math.round(cpuScore + memoryScore + storageScore + gpuScore + displayScore)
}

/**
 * Get laptop category color
 */
export function getCategoryColor(category: string): string {
  const colors: Record<string, string> = {
    'Gaming': 'bg-red-100 text-red-800',
    'Business': 'bg-blue-100 text-blue-800',
    'Ultrabook': 'bg-green-100 text-green-800',
    'Workstation': 'bg-purple-100 text-purple-800',
    'Budget': 'bg-yellow-100 text-yellow-800',
    'Creator': 'bg-pink-100 text-pink-800',
  }
  
  return colors[category] || 'bg-gray-100 text-gray-800'
}
