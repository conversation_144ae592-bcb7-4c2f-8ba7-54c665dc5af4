'use client'

/**
 * Advanced Laptop Search Component
 * Comprehensive search interface with filters, sorting, and advanced options
 */

import React, { useState, useCallback, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Slider } from '@/components/ui/slider'
import { Separator } from '@/components/ui/separator'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { 
  Search, 
  Filter, 
  X, 
  ChevronDown, 
  ChevronUp,
  SlidersHorizontal,
  Zap,
  Cpu,
  MemoryStick,
  HardDrive,
  Monitor,
  DollarSign,
  Star,
  TrendingUp,
  Target
} from 'lucide-react'
import type { LaptopFilters, LaptopSearchParams } from '../types'

interface AdvancedSearchProps {
  searchParams: LaptopSearchParams
  onSearchParamsChange: (params: LaptopSearchParams) => void
  onSearch: () => void
  onReset: () => void
  isLoading?: boolean
  resultCount?: number
  className?: string
}

const LAPTOP_BRANDS = [
  'Apple', 'ASUS', 'Acer', 'Dell', 'HP', 'Lenovo', 'MSI', 'Razer', 
  'Alienware', 'Samsung', 'LG', 'Microsoft', 'Framework', 'System76'
]

const CPU_BRANDS = ['Intel', 'AMD', 'Apple']
const GPU_BRANDS = ['NVIDIA', 'AMD', 'Intel', 'Apple']
const STORAGE_TYPES = ['SSD', 'NVMe', 'HDD', 'eMMC']
const MEMORY_SIZES = [4, 8, 16, 32, 64, 128]

const SORT_OPTIONS = [
  { value: 'price', label: 'Price' },
  { value: 'rating', label: 'Rating' },
  { value: 'compatibility', label: 'LLM Compatibility' },
  { value: 'name', label: 'Name' },
  { value: 'newest', label: 'Newest' }
]

export function AdvancedSearch({
  searchParams,
  onSearchParamsChange,
  onSearch,
  onReset,
  isLoading = false,
  resultCount = 0,
  className
}: AdvancedSearchProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [activeTab, setActiveTab] = useState('basic')

  // Extract filters for easier manipulation
  const filters = searchParams.filters || {}

  const updateSearchParams = useCallback((updates: Partial<LaptopSearchParams>) => {
    onSearchParamsChange({ ...searchParams, ...updates })
  }, [searchParams, onSearchParamsChange])

  const updateFilters = useCallback((filterUpdates: Partial<LaptopFilters>) => {
    updateSearchParams({
      filters: { ...filters, ...filterUpdates },
      page: 1 // Reset to first page when filters change
    })
  }, [filters, updateSearchParams])

  const clearFilter = useCallback((filterKey: keyof LaptopFilters) => {
    const newFilters = { ...filters }
    delete newFilters[filterKey]
    updateSearchParams({ filters: newFilters, page: 1 })
  }, [filters, updateSearchParams])

  const activeFilterCount = useMemo(() => {
    return Object.keys(filters).filter(key => {
      const value = filters[key as keyof LaptopFilters]
      if (Array.isArray(value)) return value.length > 0
      if (typeof value === 'object' && value !== null) {
        return Object.values(value).some(v => v !== undefined && v !== null)
      }
      return value !== undefined && value !== null
    }).length
  }, [filters])

  const handleQueryChange = (query: string) => {
    updateSearchParams({ query, page: 1 })
  }

  const handleSortChange = (sortBy: string, sortOrder?: 'asc' | 'desc') => {
    updateSearchParams({ 
      sortBy: sortBy as any, 
      sortOrder: sortOrder || 'desc',
      page: 1 
    })
  }

  const handlePriceRangeChange = (values: number[]) => {
    updateFilters({
      priceRange: {
        min: values[0],
        max: values[1]
      }
    })
  }

  const handleDisplaySizeChange = (values: number[]) => {
    updateFilters({
      displaySize: {
        min: values[0],
        max: values[1]
      }
    })
  }

  const toggleBrand = (brand: string) => {
    const currentBrands = filters.brands || []
    const newBrands = currentBrands.includes(brand)
      ? currentBrands.filter(b => b !== brand)
      : [...currentBrands, brand]
    
    updateFilters({ brands: newBrands.length > 0 ? newBrands : undefined })
  }

  const toggleMemorySize = (size: number) => {
    const currentSizes = filters.memorySize || []
    const newSizes = currentSizes.includes(size)
      ? currentSizes.filter(s => s !== size)
      : [...currentSizes, size]
    
    updateFilters({ memorySize: newSizes.length > 0 ? newSizes : undefined })
  }

  const toggleStorageType = (type: string) => {
    const currentTypes = filters.storageType || []
    const newTypes = currentTypes.includes(type)
      ? currentTypes.filter(t => t !== type)
      : [...currentTypes, type]
    
    updateFilters({ storageType: newTypes.length > 0 ? newTypes : undefined })
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Search className="h-5 w-5 mr-2" />
              Advanced Laptop Search
            </CardTitle>
            <CardDescription>
              Find the perfect laptop with detailed filters and search options
              {resultCount > 0 && ` • ${resultCount} results found`}
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="flex items-center">
                <Filter className="h-3 w-3 mr-1" />
                {activeFilterCount} filter{activeFilterCount > 1 ? 's' : ''}
              </Badge>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <SlidersHorizontal className="h-4 w-4 mr-2" />
              {isExpanded ? 'Simple' : 'Advanced'}
              {isExpanded ? <ChevronUp className="h-4 w-4 ml-2" /> : <ChevronDown className="h-4 w-4 ml-2" />}
            </Button>
          </div>
        </div>

        {/* Main Search Bar */}
        <div className="flex space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search laptops by name, brand, or specifications..."
              value={searchParams.query || ''}
              onChange={(e) => handleQueryChange(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select
            value={`${searchParams.sortBy || 'compatibility'}-${searchParams.sortOrder || 'desc'}`}
            onValueChange={(value) => {
              const [sortBy, sortOrder] = value.split('-')
              handleSortChange(sortBy, sortOrder as 'asc' | 'desc')
            }}
          >
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {SORT_OPTIONS.map(option => (
                <React.Fragment key={option.value}>
                  <SelectItem value={`${option.value}-desc`}>
                    {option.label} (High to Low)
                  </SelectItem>
                  <SelectItem value={`${option.value}-asc`}>
                    {option.label} (Low to High)
                  </SelectItem>
                </React.Fragment>
              ))}
            </SelectContent>
          </Select>
          <Button onClick={onSearch} disabled={isLoading}>
            {isLoading ? 'Searching...' : 'Search'}
          </Button>
        </div>

        {/* Active Filters */}
        {activeFilterCount > 0 && (
          <div className="flex flex-wrap gap-2">
            {filters.brands?.map(brand => (
              <Badge key={brand} variant="secondary" className="flex items-center">
                {brand}
                <X 
                  className="h-3 w-3 ml-1 cursor-pointer" 
                  onClick={() => toggleBrand(brand)}
                />
              </Badge>
            ))}
            {filters.priceRange && (
              <Badge variant="secondary" className="flex items-center">
                <DollarSign className="h-3 w-3 mr-1" />
                ${filters.priceRange.min} - ${filters.priceRange.max}
                <X 
                  className="h-3 w-3 ml-1 cursor-pointer" 
                  onClick={() => clearFilter('priceRange')}
                />
              </Badge>
            )}
            {filters.memorySize?.map(size => (
              <Badge key={size} variant="secondary" className="flex items-center">
                <MemoryStick className="h-3 w-3 mr-1" />
                {size}GB RAM
                <X 
                  className="h-3 w-3 ml-1 cursor-pointer" 
                  onClick={() => toggleMemorySize(size)}
                />
              </Badge>
            ))}
            <Button
              variant="ghost"
              size="sm"
              onClick={onReset}
              className="h-6 px-2 text-xs"
            >
              Clear All
            </Button>
          </div>
        )}
      </CardHeader>

      {/* Advanced Filters */}
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleContent>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="basic">Basic</TabsTrigger>
                <TabsTrigger value="specs">Specifications</TabsTrigger>
                <TabsTrigger value="features">Features</TabsTrigger>
                <TabsTrigger value="llm">LLM Compatibility</TabsTrigger>
              </TabsList>

              {/* Basic Filters */}
              <TabsContent value="basic" className="space-y-6">
                {/* Price Range */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium flex items-center">
                    <DollarSign className="h-4 w-4 mr-2" />
                    Price Range
                  </Label>
                  <div className="px-3">
                    <Slider
                      value={[filters.priceRange?.min || 0, filters.priceRange?.max || 5000]}
                      onValueChange={handlePriceRangeChange}
                      max={5000}
                      min={0}
                      step={100}
                      className="w-full"
                    />
                    <div className="flex justify-between text-sm text-muted-foreground mt-1">
                      <span>${filters.priceRange?.min || 0}</span>
                      <span>${filters.priceRange?.max || 5000}</span>
                    </div>
                  </div>
                </div>

                {/* Brands */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Brands</Label>
                  <div className="grid grid-cols-3 md:grid-cols-4 gap-2">
                    {LAPTOP_BRANDS.map(brand => (
                      <div key={brand} className="flex items-center space-x-2">
                        <Checkbox
                          id={`brand-${brand}`}
                          checked={filters.brands?.includes(brand) || false}
                          onCheckedChange={() => toggleBrand(brand)}
                        />
                        <Label htmlFor={`brand-${brand}`} className="text-sm">
                          {brand}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Display Size */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium flex items-center">
                    <Monitor className="h-4 w-4 mr-2" />
                    Display Size (inches)
                  </Label>
                  <div className="px-3">
                    <Slider
                      value={[filters.displaySize?.min || 11, filters.displaySize?.max || 17]}
                      onValueChange={handleDisplaySizeChange}
                      max={17}
                      min={11}
                      step={0.1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-sm text-muted-foreground mt-1">
                      <span>{filters.displaySize?.min || 11}"</span>
                      <span>{filters.displaySize?.max || 17}"</span>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Specifications */}
              <TabsContent value="specs" className="space-y-6">
                {/* Memory */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium flex items-center">
                    <MemoryStick className="h-4 w-4 mr-2" />
                    Memory (RAM)
                  </Label>
                  <div className="grid grid-cols-3 md:grid-cols-6 gap-2">
                    {MEMORY_SIZES.map(size => (
                      <div key={size} className="flex items-center space-x-2">
                        <Checkbox
                          id={`memory-${size}`}
                          checked={filters.memorySize?.includes(size) || false}
                          onCheckedChange={() => toggleMemorySize(size)}
                        />
                        <Label htmlFor={`memory-${size}`} className="text-sm">
                          {size}GB
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Storage Type */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium flex items-center">
                    <HardDrive className="h-4 w-4 mr-2" />
                    Storage Type
                  </Label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {STORAGE_TYPES.map(type => (
                      <div key={type} className="flex items-center space-x-2">
                        <Checkbox
                          id={`storage-${type}`}
                          checked={filters.storageType?.includes(type) || false}
                          onCheckedChange={() => toggleStorageType(type)}
                        />
                        <Label htmlFor={`storage-${type}`} className="text-sm">
                          {type}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* CPU Brands */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium flex items-center">
                    <Cpu className="h-4 w-4 mr-2" />
                    CPU Brand
                  </Label>
                  <div className="grid grid-cols-3 gap-2">
                    {CPU_BRANDS.map(brand => (
                      <div key={brand} className="flex items-center space-x-2">
                        <Checkbox
                          id={`cpu-${brand}`}
                          checked={filters.cpuBrands?.includes(brand) || false}
                          onCheckedChange={() => {
                            const current = filters.cpuBrands || []
                            const updated = current.includes(brand)
                              ? current.filter(b => b !== brand)
                              : [...current, brand]
                            updateFilters({ cpuBrands: updated.length > 0 ? updated : undefined })
                          }}
                        />
                        <Label htmlFor={`cpu-${brand}`} className="text-sm">
                          {brand}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>

              {/* Features */}
              <TabsContent value="features" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Availability */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Availability</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="in-stock"
                          checked={filters.inStock || false}
                          onCheckedChange={(checked) => 
                            updateFilters({ inStock: checked ? true : undefined })
                          }
                        />
                        <Label htmlFor="in-stock" className="text-sm">
                          In Stock Only
                        </Label>
                      </div>
                    </div>
                  </div>

                  {/* Weight */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Maximum Weight (lbs)</Label>
                    <div className="px-3">
                      <Slider
                        value={[filters.weight?.max || 10]}
                        onValueChange={(values) => 
                          updateFilters({ weight: { max: values[0] } })
                        }
                        max={10}
                        min={1}
                        step={0.1}
                        className="w-full"
                      />
                      <div className="text-sm text-muted-foreground mt-1">
                        Up to {filters.weight?.max || 10} lbs
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* LLM Compatibility */}
              <TabsContent value="llm" className="space-y-6">
                <div className="text-center py-8">
                  <Zap className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-medium mb-2">LLM Compatibility Filters</h3>
                  <p className="text-muted-foreground mb-4">
                    Advanced LLM compatibility filters will be available soon.
                    Use the compatibility dashboard for detailed analysis.
                  </p>
                  <Button variant="outline" asChild>
                    <a href="/compatibility">
                      <Target className="h-4 w-4 mr-2" />
                      Open Compatibility Dashboard
                    </a>
                  </Button>
                </div>
              </TabsContent>
            </Tabs>

            <Separator className="my-6" />

            {/* Action Buttons */}
            <div className="flex justify-between">
              <Button variant="outline" onClick={onReset}>
                Reset All Filters
              </Button>
              <div className="flex space-x-2">
                <Button variant="outline" onClick={() => setIsExpanded(false)}>
                  Close Advanced
                </Button>
                <Button onClick={onSearch} disabled={isLoading}>
                  {isLoading ? 'Searching...' : `Search Laptops`}
                </Button>
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}

export { AdvancedSearch }
