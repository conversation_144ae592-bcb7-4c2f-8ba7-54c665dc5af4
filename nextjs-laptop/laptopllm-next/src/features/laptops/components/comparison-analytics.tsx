'use client'

/**
 * Comparison Analytics Component
 * Advanced analytics and insights for laptop comparisons
 */

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  TrendingUp, 
  TrendingDown, 
  Award, 
  AlertTriangle, 
  CheckCircle,
  BarChart3,
  Zap,
  DollarSign,
  Target,
  Lightbulb,
  Info
} from 'lucide-react'
import type { LaptopData } from '../types'

interface ComparisonAnalyticsProps {
  laptops: LaptopData[]
  className?: string
}

interface AnalysisInsight {
  type: 'strength' | 'weakness' | 'opportunity' | 'recommendation'
  title: string
  description: string
  laptopId?: string
  metric?: string
  value?: any
  icon: React.ReactNode
}

export function ComparisonAnalytics({ laptops, className }: ComparisonAnalyticsProps) {
  if (laptops.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="py-12">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">No Analytics Available</h3>
            <p className="text-muted-foreground">
              Add laptops to your comparison to see detailed analytics and insights.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Calculate analytics insights
  const generateInsights = (): AnalysisInsight[] => {
    const insights: AnalysisInsight[] = []

    // Price analysis
    const prices = laptops.map(l => l.price || 0).filter(p => p > 0)
    if (prices.length > 1) {
      const minPrice = Math.min(...prices)
      const maxPrice = Math.max(...prices)
      const priceDiff = ((maxPrice - minPrice) / minPrice) * 100

      if (priceDiff > 50) {
        insights.push({
          type: 'opportunity',
          title: 'Significant Price Variation',
          description: `Price difference of ${priceDiff.toFixed(0)}% between options. Consider if the premium features justify the cost.`,
          icon: <DollarSign className="h-4 w-4" />
        })
      }
    }

    // Compatibility analysis
    const compatibilityScores = laptops.map(l => l.compatibility?.averageScore || 0)
    const avgCompatibility = compatibilityScores.reduce((sum, score) => sum + score, 0) / compatibilityScores.length
    const bestCompatibility = Math.max(...compatibilityScores)
    const worstCompatibility = Math.min(...compatibilityScores)

    if (bestCompatibility - worstCompatibility > 20) {
      const bestLaptop = laptops.find(l => l.compatibility?.averageScore === bestCompatibility)
      insights.push({
        type: 'strength',
        title: 'Clear LLM Performance Leader',
        description: `${bestLaptop?.title} shows significantly better LLM compatibility (${bestCompatibility}/100 vs ${worstCompatibility}/100).`,
        laptopId: bestLaptop?.id,
        icon: <Zap className="h-4 w-4" />
      })
    }

    // Memory analysis
    const memorySizes = laptops.map(l => l.specifications?.memory?.size || 0).filter(m => m > 0)
    if (memorySizes.length > 0) {
      const maxMemory = Math.max(...memorySizes)
      const minMemory = Math.min(...memorySizes)
      
      if (maxMemory >= 32 && minMemory < 16) {
        insights.push({
          type: 'recommendation',
          title: 'Memory Considerations',
          description: 'For optimal LLM performance, consider options with 32GB+ RAM for larger models.',
          icon: <Target className="h-4 w-4" />
        })
      }
    }

    // Value proposition analysis
    laptops.forEach(laptop => {
      const valueScore = (laptop.compatibility?.averageScore || 0) / ((laptop.price || 1) / 1000)
      if (valueScore > 50) {
        insights.push({
          type: 'strength',
          title: 'Excellent Value Proposition',
          description: `${laptop.title} offers strong performance per dollar spent.`,
          laptopId: laptop.id,
          icon: <Award className="h-4 w-4" />
        })
      }
    })

    // Feature gaps analysis
    const hasGPU = laptops.some(l => l.specifications?.gpu?.type === 'dedicated')
    const hasIntegratedOnly = laptops.some(l => l.specifications?.gpu?.type === 'integrated')
    
    if (hasGPU && hasIntegratedOnly) {
      insights.push({
        type: 'opportunity',
        title: 'GPU Performance Variation',
        description: 'Mix of dedicated and integrated graphics. Dedicated GPU will significantly improve LLM inference speed.',
        icon: <TrendingUp className="h-4 w-4" />
      })
    }

    return insights.slice(0, 6) // Limit to 6 insights
  }

  const insights = generateInsights()

  // Performance matrix calculation
  const performanceMatrix = laptops.map(laptop => {
    const compatibility = laptop.compatibility?.averageScore || 0
    const memory = laptop.specifications?.memory?.size || 0
    const storage = laptop.specifications?.storage?.capacity || 0
    const price = laptop.price || 0

    return {
      laptop,
      scores: {
        performance: compatibility,
        memory: Math.min((memory / 64) * 100, 100), // Normalize to 64GB max
        storage: Math.min((storage / 2000) * 100, 100), // Normalize to 2TB max
        value: price > 0 ? Math.max(0, 100 - ((price - 500) / 50)) : 0 // Value score based on price
      }
    }
  })

  const getInsightIcon = (type: AnalysisInsight['type']) => {
    switch (type) {
      case 'strength':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'weakness':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'opportunity':
        return <TrendingUp className="h-4 w-4 text-blue-600" />
      case 'recommendation':
        return <Lightbulb className="h-4 w-4 text-yellow-600" />
      default:
        return <Info className="h-4 w-4" />
    }
  }

  const getInsightColor = (type: AnalysisInsight['type']) => {
    switch (type) {
      case 'strength':
        return 'border-green-200 bg-green-50'
      case 'weakness':
        return 'border-red-200 bg-red-50'
      case 'opportunity':
        return 'border-blue-200 bg-blue-50'
      case 'recommendation':
        return 'border-yellow-200 bg-yellow-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Key Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Lightbulb className="h-5 w-5 mr-2" />
            Key Insights
          </CardTitle>
          <CardDescription>
            AI-powered analysis of your laptop comparison
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            {insights.map((insight, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border ${getInsightColor(insight.type)}`}
              >
                <div className="flex items-start space-x-3">
                  {getInsightIcon(insight.type)}
                  <div className="flex-1">
                    <h4 className="font-medium mb-1">{insight.title}</h4>
                    <p className="text-sm text-muted-foreground">{insight.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Performance Matrix */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Performance Matrix
          </CardTitle>
          <CardDescription>
            Normalized scores across key performance dimensions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {performanceMatrix.map(({ laptop, scores }) => (
              <div key={laptop.id} className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">{laptop.title}</h4>
                  <Badge variant="outline">${laptop.price?.toLocaleString() || 'N/A'}</Badge>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <div className="flex items-center justify-between text-sm mb-1">
                      <span>LLM Performance</span>
                      <span>{scores.performance}/100</span>
                    </div>
                    <Progress value={scores.performance} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between text-sm mb-1">
                      <span>Memory</span>
                      <span>{scores.memory.toFixed(0)}/100</span>
                    </div>
                    <Progress value={scores.memory} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between text-sm mb-1">
                      <span>Storage</span>
                      <span>{scores.storage.toFixed(0)}/100</span>
                    </div>
                    <Progress value={scores.storage} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between text-sm mb-1">
                      <span>Value</span>
                      <span>{scores.value.toFixed(0)}/100</span>
                    </div>
                    <Progress value={scores.value} className="h-2" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recommendations Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="h-5 w-5 mr-2" />
            Decision Framework
          </CardTitle>
          <CardDescription>
            Choose based on your priorities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2 flex items-center">
                <Zap className="h-4 w-4 mr-2 text-blue-600" />
                Best Performance
              </h4>
              <p className="text-sm text-muted-foreground mb-2">
                For maximum LLM capability and future-proofing
              </p>
              {performanceMatrix.length > 0 && (
                <Badge variant="secondary">
                  {performanceMatrix.reduce((best, current) => 
                    current.scores.performance > best.scores.performance ? current : best
                  ).laptop.title}
                </Badge>
              )}
            </div>

            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2 flex items-center">
                <DollarSign className="h-4 w-4 mr-2 text-green-600" />
                Best Value
              </h4>
              <p className="text-sm text-muted-foreground mb-2">
                For balanced performance and cost efficiency
              </p>
              {performanceMatrix.length > 0 && (
                <Badge variant="secondary">
                  {performanceMatrix.reduce((best, current) => 
                    current.scores.value > best.scores.value ? current : best
                  ).laptop.title}
                </Badge>
              )}
            </div>

            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2 flex items-center">
                <Award className="h-4 w-4 mr-2 text-purple-600" />
                Most Balanced
              </h4>
              <p className="text-sm text-muted-foreground mb-2">
                For well-rounded performance across all areas
              </p>
              {performanceMatrix.length > 0 && (
                <Badge variant="secondary">
                  {performanceMatrix.reduce((best, current) => {
                    const bestAvg = (best.scores.performance + best.scores.memory + best.scores.storage + best.scores.value) / 4
                    const currentAvg = (current.scores.performance + current.scores.memory + current.scores.storage + current.scores.value) / 4
                    return currentAvg > bestAvg ? current : best
                  }).laptop.title}
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export { ComparisonAnalytics }
