'use client'

/**
 * Deals Dashboard Component
 * Main dashboard for viewing and managing laptop deals
 */

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { TrendingUp, TrendingDown, Zap, Package, Filter, Search } from 'lucide-react'
import { useDeals, useHotDeals, useDealStats } from '../hooks/use-deals'
import { DealCard } from './deal-card'
import { DealsFilters } from './deals-filters'
import { DealsSummary } from './deals-summary'
import { HotDealsCarousel } from './hot-deals-carousel'
import type { DealFilters } from '@/shared/types'

interface DealsDashboardProps {
  className?: string
}

export function DealsDashboard({ className }: DealsDashboardProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [activeTab, setActiveTab] = useState('all')

  // Initialize filters based on active tab
  const getFiltersForTab = (tab: string): DealFilters => {
    switch (tab) {
      case 'hot':
        return { onlyHotDeals: true }
      case 'price-drops':
        return { dealTypes: ['price-drop'] }
      case 'in-stock':
        return { dealTypes: ['back-in-stock'] }
      case 'free-shipping':
        return { freeShippingOnly: true }
      default:
        return {}
    }
  }

  const {
    deals,
    summary,
    total,
    page,
    hasMore,
    isLoading,
    isFetching,
    error,
    filters,
    updateFilters,
    clearFilters,
    nextPage,
    prevPage,
    refetch
  } = useDeals(getFiltersForTab(activeTab))

  const { data: hotDeals, isLoading: hotDealsLoading } = useHotDeals(6)
  const { data: stats } = useDealStats()

  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
    clearFilters()
    updateFilters(getFiltersForTab(tab))
  }

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    // Implement search logic here
  }

  const handleFiltersChange = (newFilters: Partial<DealFilters>) => {
    updateFilters(newFilters)
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <Card className="p-6">
          <CardHeader>
            <CardTitle className="text-red-600">Error Loading Deals</CardTitle>
            <CardDescription>
              {error instanceof Error ? error.message : 'An unexpected error occurred'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => refetch()}>Try Again</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Laptop Deals</h1>
          <p className="text-muted-foreground">
            Discover the best laptop deals with real-time price tracking
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search deals..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-9 w-64"
            />
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
        </div>
      </div>

      {/* Summary Stats */}
      {summary && <DealsSummary summary={summary} stats={stats} />}

      {/* Hot Deals Carousel */}
      {hotDeals && hotDeals.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-orange-500" />
              Hot Deals
            </CardTitle>
            <CardDescription>
              The best deals right now based on our scoring algorithm
            </CardDescription>
          </CardHeader>
          <CardContent>
            <HotDealsCarousel deals={hotDeals} isLoading={hotDealsLoading} />
          </CardContent>
        </Card>
      )}

      {/* Filters Panel */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <DealsFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              onClearFilters={clearFilters}
            />
          </CardContent>
        </Card>
      )}

      {/* Deals Tabs */}
      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">All Deals</TabsTrigger>
          <TabsTrigger value="hot">
            <Zap className="h-4 w-4 mr-1" />
            Hot
          </TabsTrigger>
          <TabsTrigger value="price-drops">
            <TrendingDown className="h-4 w-4 mr-1" />
            Price Drops
          </TabsTrigger>
          <TabsTrigger value="in-stock">
            <Package className="h-4 w-4 mr-1" />
            Back in Stock
          </TabsTrigger>
          <TabsTrigger value="free-shipping">Free Shipping</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {/* Results Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {isLoading ? 'Loading...' : `${total} deals found`}
              </span>
              {isFetching && (
                <Badge variant="secondary" className="animate-pulse">
                  Updating...
                </Badge>
              )}
            </div>
            
            <Select defaultValue="deal-score">
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="deal-score">Best Deal Score</SelectItem>
                <SelectItem value="price-low">Price: Low to High</SelectItem>
                <SelectItem value="price-high">Price: High to Low</SelectItem>
                <SelectItem value="discount">Highest Discount</SelectItem>
                <SelectItem value="newest">Newest First</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Deals Grid */}
          {isLoading ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {Array.from({ length: 6 }).map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-6">
                    <div className="space-y-3">
                      <div className="h-4 bg-muted rounded w-3/4" />
                      <div className="h-4 bg-muted rounded w-1/2" />
                      <div className="h-8 bg-muted rounded" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : deals.length > 0 ? (
            <>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {deals.map((deal) => (
                  <DealCard key={deal.id} deal={deal} />
                ))}
              </div>

              {/* Pagination */}
              {(hasMore || page > 1) && (
                <div className="flex items-center justify-center gap-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={prevPage}
                    disabled={page === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm text-muted-foreground px-4">
                    Page {page}
                  </span>
                  <Button
                    variant="outline"
                    onClick={nextPage}
                    disabled={!hasMore}
                  >
                    Next
                  </Button>
                </div>
              )}
            </>
          ) : (
            <Card className="p-12 text-center">
              <CardContent>
                <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No deals found</h3>
                <p className="text-muted-foreground mb-4">
                  Try adjusting your filters or check back later for new deals.
                </p>
                <Button onClick={clearFilters} variant="outline">
                  Clear Filters
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
