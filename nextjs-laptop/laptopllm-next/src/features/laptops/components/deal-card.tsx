'use client'

/**
 * Deal Card Component
 * Displays individual laptop deal information
 */

import React from 'react'
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  ExternalLink, 
  Zap, 
  TrendingDown, 
  Package, 
  Truck, 
  Shield,
  Star,
  Clock,
  AlertCircle
} from 'lucide-react'
import { formatCurrency, formatPercentage, formatDate } from '@/lib/utils'
import type { LaptopDeal } from '@/shared/types'

interface DealCardProps {
  deal: LaptopDeal
  className?: string
  showFullDetails?: boolean
}

export function DealCard({ deal, className, showFullDetails = false }: DealCardProps) {
  const {
    laptop,
    listing,
    dealType,
    currentPrice,
    originalPrice,
    discountAmount,
    discountPercentage,
    dealScore,
    isHotDeal,
    availability,
    shipping,
    dealMetrics
  } = deal

  const getDealTypeIcon = (type: LaptopDeal['dealType']) => {
    switch (type) {
      case 'price-drop':
        return <TrendingDown className="h-4 w-4" />
      case 'back-in-stock':
        return <Package className="h-4 w-4" />
      case 'limited-time':
        return <Clock className="h-4 w-4" />
      default:
        return <Zap className="h-4 w-4" />
    }
  }

  const getDealTypeLabel = (type: LaptopDeal['dealType']) => {
    switch (type) {
      case 'price-drop':
        return 'Price Drop'
      case 'best-price':
        return 'Best Price'
      case 'back-in-stock':
        return 'Back in Stock'
      case 'new-listing':
        return 'New Listing'
      case 'limited-time':
        return 'Limited Time'
      default:
        return 'Deal'
    }
  }

  const getDealTypeColor = (type: LaptopDeal['dealType']) => {
    switch (type) {
      case 'price-drop':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'best-price':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'back-in-stock':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'new-listing':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'limited-time':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <Card className={`relative overflow-hidden transition-all hover:shadow-lg ${className}`}>
      {/* Hot Deal Indicator */}
      {isHotDeal && (
        <div className="absolute top-2 right-2 z-10">
          <Badge className="bg-orange-500 text-white">
            <Zap className="h-3 w-3 mr-1" />
            Hot Deal
          </Badge>
        </div>
      )}

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg leading-tight truncate">
              {laptop.model_name}
            </h3>
            <p className="text-sm text-muted-foreground">
              {laptop.brands?.name}
            </p>
          </div>
          
          <Badge className={getDealTypeColor(dealType)}>
            {getDealTypeIcon(dealType)}
            <span className="ml-1">{getDealTypeLabel(dealType)}</span>
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Price Information */}
        <div className="space-y-2">
          <div className="flex items-baseline gap-2">
            <span className="text-2xl font-bold">
              {formatCurrency(currentPrice)}
            </span>
            {originalPrice && originalPrice > currentPrice && (
              <span className="text-sm text-muted-foreground line-through">
                {formatCurrency(originalPrice)}
              </span>
            )}
          </div>
          
          {discountPercentage && discountPercentage > 0 && (
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-green-600">
                {formatPercentage(discountPercentage)} OFF
              </Badge>
              {discountAmount && (
                <span className="text-sm text-green-600">
                  Save {formatCurrency(discountAmount)}
                </span>
              )}
            </div>
          )}
        </div>

        {/* Deal Score */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Deal Score</span>
            <span className={`text-sm font-bold ${getScoreColor(dealScore)}`}>
              {dealScore}/100
            </span>
          </div>
          <Progress value={dealScore} className="h-2" />
        </div>

        {/* Availability & Shipping */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Package className={`h-4 w-4 ${availability.inStock ? 'text-green-500' : 'text-red-500'}`} />
            <span className={availability.inStock ? 'text-green-600' : 'text-red-600'}>
              {availability.inStock ? 'In Stock' : 'Out of Stock'}
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <Truck className={`h-4 w-4 ${shipping.isFree ? 'text-green-500' : 'text-gray-500'}`} />
            <span>
              {shipping.isFree ? 'Free Shipping' : formatCurrency(shipping.cost)}
            </span>
          </div>
        </div>

        {/* Additional Details */}
        {showFullDetails && (
          <div className="space-y-3 pt-3 border-t">
            {/* Source & Rating */}
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Source:</span>
              <span className="font-medium">{listing.source.name}</span>
            </div>
            
            {listing.rating && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Rating:</span>
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span>{listing.rating.toFixed(1)}</span>
                  {listing.reviewsCount && (
                    <span className="text-muted-foreground">
                      ({listing.reviewsCount})
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Warranty */}
            {listing.hasWarranty && (
              <div className="flex items-center gap-2 text-sm">
                <Shield className="h-4 w-4 text-blue-500" />
                <span>
                  {listing.warrantyMonths} month warranty included
                </span>
              </div>
            )}

            {/* Deal Metrics */}
            <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
              <div>Rank: #{dealMetrics.priceRank}</div>
              <div>Trend: {dealMetrics.priceTrend}</div>
              {dealMetrics.historicalLow && (
                <div className="col-span-2 flex items-center gap-1 text-green-600">
                  <AlertCircle className="h-3 w-3" />
                  Historical Low
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-3">
        <div className="flex items-center gap-2 w-full">
          <Button asChild className="flex-1">
            <a 
              href={listing.url} 
              target="_blank" 
              rel="noopener noreferrer"
              className="flex items-center gap-2"
            >
              View Deal
              <ExternalLink className="h-4 w-4" />
            </a>
          </Button>
          
          {/* Quick Actions */}
          <Button variant="outline" size="sm">
            Compare
          </Button>
        </div>
      </CardFooter>

      {/* Last Updated */}
      <div className="absolute bottom-2 left-4 text-xs text-muted-foreground">
        Updated {formatDate(deal.lastUpdated)}
      </div>
    </Card>
  )
}
