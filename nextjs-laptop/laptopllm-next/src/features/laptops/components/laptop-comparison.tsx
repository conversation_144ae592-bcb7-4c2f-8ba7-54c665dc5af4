'use client'

/**
 * Laptop Comparison Component
 * Side-by-side comparison of multiple laptops with detailed specifications and LLM compatibility
 */

import React, { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  X, 
  Plus,
  Star,
  TrendingUp,
  TrendingDown,
  Minus,
  Cpu,
  MemoryStick,
  HardDrive,
  Monitor,
  Zap,
  DollarSign,
  Weight,
  Battery,
  Wifi,
  Award,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react'
import type { LaptopData } from '@/shared/types'
import type {
  ComparisonMetric,
  ComparisonCategory,
  ComparisonValue,
  ComparisonAnalysis
} from '@/features/laptop-comparison/types'
import { ComparisonAnalytics } from '@/features/laptop-comparison/components/comparison-analytics'

interface LaptopComparisonProps {
  laptops: LaptopData[]
  onRemoveLaptop: (laptopId: string) => void
  onAddLaptop: () => void
  maxLaptops?: number
  className?: string
}

const COMPARISON_METRICS: ComparisonMetric[] = [
  {
    key: 'price',
    label: 'Price',
    category: 'pricing',
    icon: <DollarSign className="h-4 w-4" />,
    getValue: (laptop: LaptopData): number => laptop.pricing?.currentPrice || 0,
    format: (value: unknown): string => {
      const price = value as number
      return price > 0 ? `$${price.toLocaleString()}` : 'N/A'
    },
    isNumeric: true,
    higherIsBetter: false,
    unit: 'USD',
    description: 'Current market price of the laptop',
    weight: 0.8
  },
  {
    key: 'compatibility',
    label: 'LLM Compatibility',
    category: 'compatibility',
    icon: <Zap className="h-4 w-4" />,
    getValue: (laptop: LaptopData): number => laptop.compatibility?.averageScore || 0,
    format: (value: unknown): string => {
      const score = value as number
      return `${Math.round(score)}/100`
    },
    isNumeric: true,
    higherIsBetter: true,
    unit: 'score',
    description: 'Average compatibility score with LLM models',
    weight: 1.0
  },
  {
    key: 'cpu',
    label: 'CPU',
    category: 'performance',
    icon: <Cpu className="h-4 w-4" />,
    getValue: (laptop: LaptopData): string => {
      const cpu = laptop.specifications?.cpu
      return cpu ? `${cpu.manufacturer || ''} ${cpu.model || ''}`.trim() : 'Unknown'
    },
    format: (value: unknown): string => (value as string) || 'Unknown',
    isNumeric: false,
    higherIsBetter: false,
    description: 'CPU model and manufacturer',
    weight: 0.9
  },
  {
    key: 'memory',
    label: 'Memory',
    category: 'performance',
    icon: <MemoryStick className="h-4 w-4" />,
    getValue: (laptop: LaptopData): number => {
      const memory = laptop.specifications?.memory
      return Array.isArray(memory) ? memory.reduce((total, mem) => total + (mem.capacityGb || 0), 0) : 0
    },
    format: (value: unknown): string => {
      const memoryGb = value as number
      return memoryGb > 0 ? `${memoryGb}GB` : 'N/A'
    },
    isNumeric: true,
    higherIsBetter: true,
    unit: 'GB',
    description: 'Total system memory capacity',
    weight: 0.9
  },
  {
    key: 'storage',
    label: 'Storage',
    category: 'storage',
    icon: <HardDrive className="h-4 w-4" />,
    getValue: (laptop: LaptopData): number => {
      const storage = laptop.specifications?.storage
      return Array.isArray(storage) ? storage.reduce((total, stor) => total + (stor.capacityGb || 0), 0) : 0
    },
    format: (value: unknown): string => {
      const storageGb = value as number
      const storage = laptop.specifications?.storage
      const primaryType = Array.isArray(storage) && storage.length > 0 ? storage[0].type : ''
      return storageGb > 0 ? `${storageGb}GB ${primaryType}` : 'N/A'
    },
    isNumeric: true,
    higherIsBetter: true,
    unit: 'GB',
    description: 'Total storage capacity',
    weight: 0.7
  },
  {
    key: 'display',
    label: 'Display',
    category: 'display',
    icon: <Monitor className="h-4 w-4" />,
    getValue: (laptop: LaptopData): number => laptop.specifications?.display?.sizeInches || 0,
    format: (value: unknown): string => {
      const size = value as number
      const display = laptop.specifications?.display
      const resolution = display?.resolution || ''
      return size > 0 ? `${size}" ${resolution}` : 'N/A'
    },
    isNumeric: true,
    higherIsBetter: true,
    unit: 'inches',
    description: 'Display size and resolution',
    weight: 0.6
  },
  {
    key: 'weight',
    label: 'Weight',
    category: 'design',
    icon: <Weight className="h-4 w-4" />,
    getValue: (laptop: LaptopData): number => laptop.specifications?.physical?.weightKg || 0,
    format: (value: unknown): string => {
      const weightKg = value as number
      return weightKg > 0 ? `${weightKg.toFixed(1)} kg` : 'N/A'
    },
    isNumeric: true,
    higherIsBetter: false,
    unit: 'kg',
    description: 'Physical weight of the laptop',
    weight: 0.5
  },
  {
    key: 'battery',
    label: 'Battery Life',
    category: 'battery',
    icon: <Battery className="h-4 w-4" />,
    getValue: (laptop: LaptopData): number => laptop.specifications?.battery?.capacityWh || 0,
    format: (value: unknown): string => {
      const capacityWh = value as number
      return capacityWh > 0 ? `${capacityWh}Wh` : 'N/A'
    },
    isNumeric: true,
    higherIsBetter: true,
    unit: 'Wh',
    description: 'Battery capacity in watt-hours',
    weight: 0.6
  },
  {
    key: 'rating',
    label: 'Rating',
    category: 'general',
    icon: <Star className="h-4 w-4" />,
    getValue: (laptop: LaptopData): number => laptop.reviews?.averageRating || 0,
    format: (value: unknown): string => {
      const rating = value as number
      return rating > 0 ? `${rating.toFixed(1)}/5` : 'N/A'
    },
    isNumeric: true,
    higherIsBetter: true,
    unit: 'stars',
    description: 'Average user rating',
    weight: 0.4
  }
]

export function LaptopComparison({
  laptops,
  onRemoveLaptop,
  onAddLaptop,
  maxLaptops = 4,
  className
}: LaptopComparisonProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [highlightDifferences, setHighlightDifferences] = useState(true)

  // Calculate best/worst values for highlighting
  const metricStats = useMemo(() => {
    const stats: Record<string, { best: number; worst: number; values: number[] }> = {}

    COMPARISON_METRICS.forEach(metric => {
      if (metric.isNumeric) {
        const values = laptops
          .map(laptop => {
            const value = metric.getValue(laptop)
            return typeof value === 'number' ? value : 0
          })
          .filter(v => v > 0)

        if (values.length > 0) {
          stats[metric.key] = {
            best: metric.higherIsBetter ? Math.max(...values) : Math.min(...values),
            worst: metric.higherIsBetter ? Math.min(...values) : Math.max(...values),
            values
          }
        }
      }
    })
    
    return stats
  }, [laptops])

  const getValueStyle = (metric: ComparisonMetric, value: unknown, laptop: LaptopData): string => {
    if (!highlightDifferences || !metric.isNumeric || laptops.length < 2) {
      return ''
    }

    const stats = metricStats[metric.key]
    if (!stats) return ''

    const numericValue = typeof value === 'number' ? value : 0

    if (numericValue === stats.best) {
      return 'text-green-600 font-semibold'
    } else if (numericValue === stats.worst) {
      return 'text-red-600'
    }

    return ''
  }

  const getValueIcon = (metric: ComparisonMetric, value: unknown): React.ReactNode => {
    if (!metric.isNumeric || laptops.length < 2) return null

    const stats = metricStats[metric.key]
    if (!stats) return null

    const numericValue = typeof value === 'number' ? value : 0

    if (numericValue === stats.best) {
      return <TrendingUp className="h-3 w-3 text-green-600 ml-1" />
    } else if (numericValue === stats.worst) {
      return <TrendingDown className="h-3 w-3 text-red-600 ml-1" />
    }

    return null
  }

  const renderLaptopHeader = (laptop: LaptopData, index: number) => (
    <div className="relative">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onRemoveLaptop(laptop.id)}
        className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full bg-destructive text-destructive-foreground hover:bg-destructive/90"
      >
        <X className="h-3 w-3" />
      </Button>
      
      <div className="text-center space-y-2">
        {laptop.images && laptop.images.length > 0 && (
          <img
            src={laptop.images[0]}
            alt={laptop.title}
            className="w-24 h-24 object-cover rounded-lg mx-auto"
          />
        )}
        <div>
          <h3 className="font-semibold text-sm">{laptop.title}</h3>
          <p className="text-xs text-muted-foreground">{laptop.brand}</p>
        </div>
        <div className="flex items-center justify-center space-x-2">
          <Badge variant="outline" className="text-xs">
            ${laptop.price?.toLocaleString() || 'N/A'}
          </Badge>
          {laptop.compatibility?.averageScore && (
            <Badge variant="secondary" className="text-xs">
              <Zap className="h-3 w-3 mr-1" />
              {laptop.compatibility.averageScore}/100
            </Badge>
          )}
        </div>
      </div>
    </div>
  )

  const renderComparisonTable = () => (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr>
            <th className="text-left p-4 font-medium">Specification</th>
            {laptops.map((laptop, index) => (
              <th key={laptop.id} className="p-4 min-w-48">
                {renderLaptopHeader(laptop, index)}
              </th>
            ))}
            {laptops.length < maxLaptops && (
              <th className="p-4 min-w-48">
                <Button
                  variant="dashed"
                  onClick={onAddLaptop}
                  className="w-full h-32 border-2 border-dashed"
                >
                  <Plus className="h-6 w-6 mr-2" />
                  Add Laptop
                </Button>
              </th>
            )}
          </tr>
        </thead>
        <tbody>
          {COMPARISON_METRICS.map((metric, metricIndex) => (
            <tr key={metric.key} className={metricIndex % 2 === 0 ? 'bg-muted/50' : ''}>
              <td className="p-4 font-medium">
                <div className="flex items-center">
                  {metric.icon}
                  <span className="ml-2">{metric.label}</span>
                </div>
              </td>
              {laptops.map((laptop) => {
                const value = metric.getValue(laptop)
                const formattedValue = metric.format ? metric.format(value) : value
                const style = getValueStyle(metric, value, laptop)
                const icon = getValueIcon(metric, value)
                
                return (
                  <td key={laptop.id} className="p-4">
                    <div className={`flex items-center ${style}`}>
                      <span>{formattedValue}</span>
                      {icon}
                    </div>
                  </td>
                )
              })}
              {laptops.length < maxLaptops && (
                <td className="p-4 text-muted-foreground">-</td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )

  const renderCompatibilityComparison = () => (
    <div className="space-y-6">
      {laptops.map((laptop) => (
        <Card key={laptop.id}>
          <CardHeader>
            <CardTitle className="text-lg">{laptop.title}</CardTitle>
            <CardDescription>LLM Compatibility Analysis</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Overall Score</span>
                  <span className="text-sm font-bold">
                    {laptop.compatibility?.averageScore || 0}/100
                  </span>
                </div>
                <Progress 
                  value={laptop.compatibility?.averageScore || 0} 
                  className="h-2"
                />
              </div>
              
              {laptop.compatibility?.topModels && laptop.compatibility.topModels.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2">Top Compatible Models</h4>
                  <div className="space-y-2">
                    {laptop.compatibility.topModels.map((model, index) => (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <span>{model.model}</span>
                        <Badge variant="outline">{model.score}/100</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )

  const renderPriceComparison = () => {
    const prices = laptops.map(laptop => laptop.price || 0).filter(p => p > 0)
    const minPrice = Math.min(...prices)
    const maxPrice = Math.max(...prices)
    const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length

    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Price Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-green-600">${minPrice.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Lowest Price</div>
              </div>
              <div>
                <div className="text-2xl font-bold">${Math.round(avgPrice).toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Average Price</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-red-600">${maxPrice.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Highest Price</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid gap-4">
          {laptops.map((laptop) => (
            <Card key={laptop.id}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{laptop.title}</h4>
                    <p className="text-sm text-muted-foreground">
                      {laptop.availability?.stores?.length || 0} store(s) available
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold">${laptop.price?.toLocaleString() || 'N/A'}</div>
                    {laptop.price && (
                      <div className="text-sm text-muted-foreground">
                        {laptop.price === minPrice && <Badge variant="outline" className="text-green-600">Best Price</Badge>}
                        {laptop.price === maxPrice && <Badge variant="outline" className="text-red-600">Highest Price</Badge>}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (laptops.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="py-12">
          <div className="text-center">
            <Award className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">No Laptops to Compare</h3>
            <p className="text-muted-foreground mb-4">
              Add laptops to start comparing their specifications and features.
            </p>
            <Button onClick={onAddLaptop}>
              <Plus className="h-4 w-4 mr-2" />
              Add First Laptop
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Award className="h-5 w-5 mr-2" />
                Laptop Comparison
              </CardTitle>
              <CardDescription>
                Compare {laptops.length} laptop{laptops.length > 1 ? 's' : ''} side by side
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setHighlightDifferences(!highlightDifferences)}
              >
                {highlightDifferences ? 'Hide' : 'Show'} Highlights
              </Button>
              {laptops.length < maxLaptops && (
                <Button size="sm" onClick={onAddLaptop}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Laptop
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="compatibility">LLM Compatibility</TabsTrigger>
              <TabsTrigger value="pricing">Pricing</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="detailed">Detailed Specs</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-6">
              {renderComparisonTable()}
            </TabsContent>

            <TabsContent value="compatibility" className="mt-6">
              {renderCompatibilityComparison()}
            </TabsContent>

            <TabsContent value="pricing" className="mt-6">
              {renderPriceComparison()}
            </TabsContent>

            <TabsContent value="analytics" className="mt-6">
              <ComparisonAnalytics laptops={laptops} />
            </TabsContent>

            <TabsContent value="detailed" className="mt-6">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Detailed specifications comparison will include comprehensive technical details,
                  benchmark scores, and advanced compatibility metrics.
                </AlertDescription>
              </Alert>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}

export { LaptopComparison }
