'use client'

/**
 * Deals Summary Component
 * Displays summary statistics and metrics for deals
 */

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  TrendingUp, 
  TrendingDown, 
  Zap, 
  Package, 
  DollarSign,
  Calendar,
  Target,
  Activity
} from 'lucide-react'
import { formatCurrency, formatPercentage } from '@/lib/utils'
import type { DealsSearchResult } from '@/shared/types'

interface DealsSummaryProps {
  summary: DealsSearchResult['summary']
  stats?: any // Additional stats from useDealStats
  className?: string
}

export function DealsSummary({ summary, stats, className }: DealsSummaryProps) {
  const summaryCards = [
    {
      title: 'Total Deals',
      value: summary.totalDeals.toLocaleString(),
      icon: Package,
      description: 'Active deals available',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Hot Deals',
      value: summary.hotDeals.toLocaleString(),
      icon: Zap,
      description: 'High-scoring deals',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    },
    {
      title: 'Average Discount',
      value: formatPercentage(summary.averageDiscount),
      icon: TrendingDown,
      description: 'Across all deals',
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'Top Savings',
      value: formatCurrency(summary.topSavings),
      icon: DollarSign,
      description: 'Highest single discount',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'New Today',
      value: summary.newDealsToday.toLocaleString(),
      icon: Calendar,
      description: 'Deals added today',
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50'
    }
  ]

  // Add additional stats if available
  if (stats) {
    summaryCards.push({
      title: 'Price Alerts',
      value: stats.activeAlerts?.toLocaleString() || '0',
      icon: Target,
      description: 'Active price alerts',
      color: 'text-cyan-600',
      bgColor: 'bg-cyan-50'
    })
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
        {summaryCards.map((card, index) => {
          const Icon = card.icon
          return (
            <Card key={index} className="relative overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground">
                      {card.title}
                    </p>
                    <p className="text-2xl font-bold">
                      {card.value}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {card.description}
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${card.bgColor}`}>
                    <Icon className={`h-6 w-6 ${card.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Additional Stats */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {/* Deal Distribution */}
          {stats.dealDistribution && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Deal Types
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {Object.entries(stats.dealDistribution).map(([type, count]) => (
                  <div key={type} className="flex items-center justify-between">
                    <span className="text-sm capitalize">
                      {type.replace('-', ' ')}
                    </span>
                    <Badge variant="secondary">
                      {count as number}
                    </Badge>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Top Brands */}
          {stats.topBrands && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Top Brands</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {stats.topBrands.slice(0, 5).map((brand: any, index: number) => (
                  <div key={brand.name} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">#{index + 1}</span>
                      <span className="text-sm">{brand.name}</span>
                    </div>
                    <Badge variant="outline">
                      {brand.dealCount} deals
                    </Badge>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Price Ranges */}
          {stats.priceRanges && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Price Ranges</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {Object.entries(stats.priceRanges).map(([range, count]) => (
                  <div key={range} className="flex items-center justify-between">
                    <span className="text-sm">{range}</span>
                    <Badge variant="outline">
                      {count as number}
                    </Badge>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Trending Indicators */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              Market Trends
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">Average deal score</span>
              <Badge className="bg-green-100 text-green-800">
                {stats?.averageDealScore || 75}/100
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Price trend</span>
              <Badge variant="outline" className="text-green-600">
                <TrendingDown className="h-3 w-3 mr-1" />
                Falling
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Deal velocity</span>
              <Badge variant="outline">
                {stats?.dealsPerHour || 12}/hour
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Quick Stats</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">Sources monitored</span>
              <Badge variant="outline">
                {stats?.activeSources || 15}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Last update</span>
              <Badge variant="outline">
                {stats?.lastUpdate || '2 min ago'}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Success rate</span>
              <Badge className="bg-green-100 text-green-800">
                {formatPercentage(stats?.successRate || 98)}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
