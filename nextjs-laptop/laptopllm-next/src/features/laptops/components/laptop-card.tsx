'use client'

import { memo } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Heart, ShoppingCart, Zap, Monitor, HardDrive, Cpu } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { LaptopData } from '../types'

interface LaptopCardProps {
  laptop: LaptopData
  variant?: 'default' | 'compact' | 'detailed'
  showCompatibility?: boolean
  compatibilityScore?: number
  onFavorite?: (laptopId: string) => void
  onAddToComparison?: (laptopId: string) => void
  isFavorite?: boolean
  isInComparison?: boolean
  className?: string
}

export const LaptopCard = memo<LaptopCardProps>(({
  laptop,
  variant = 'default',
  showCompatibility = false,
  compatibilityScore,
  onFavorite,
  onAddToComparison,
  isFavorite = false,
  isInComparison = false,
  className,
}) => {
  const handleFavorite = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onFavorite?.(laptop.url)
  }

  const handleAddToComparison = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onAddToComparison?.(laptop.url)
  }

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
    }).format(price)
  }

  const getCompatibilityColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-50'
    if (score >= 60) return 'text-yellow-600 bg-yellow-50'
    return 'text-red-600 bg-red-50'
  }

  const getCompatibilityLabel = (score: number) => {
    if (score >= 80) return 'Excellent'
    if (score >= 60) return 'Good'
    if (score >= 40) return 'Fair'
    return 'Poor'
  }

  if (variant === 'compact') {
    return (
      <Link href={`/laptops/${encodeURIComponent(laptop.url)}`}>
        <Card className={cn('hover:shadow-md transition-shadow cursor-pointer', className)}>
          <CardContent className="p-4">
            <div className="flex items-center space-x-4">
              <div className="relative w-16 h-16 flex-shrink-0">
                <Image
                  src={laptop.images[0] || '/placeholder-laptop.jpg'}
                  alt={laptop.title}
                  fill
                  className="object-cover rounded"
                />
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-sm truncate">{laptop.title}</h3>
                <p className="text-sm text-muted-foreground">{laptop.brand}</p>
                <p className="font-bold text-lg">{formatPrice(laptop.price, laptop.currency)}</p>
              </div>
              {showCompatibility && compatibilityScore && (
                <Badge className={getCompatibilityColor(compatibilityScore)}>
                  {compatibilityScore}%
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      </Link>
    )
  }

  return (
    <Link href={`/laptops/${encodeURIComponent(laptop.url)}`}>
      <Card className={cn('hover:shadow-lg transition-all duration-200 cursor-pointer group', className)}>
        <CardHeader className="p-0">
          <div className="relative aspect-video overflow-hidden rounded-t-lg">
            <Image
              src={laptop.images[0] || '/placeholder-laptop.jpg'}
              alt={laptop.title}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-200"
            />
            
            {/* Action buttons overlay */}
            <div className="absolute top-2 right-2 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                size="sm"
                variant="secondary"
                className="h-8 w-8 p-0"
                onClick={handleFavorite}
              >
                <Heart className={cn('h-4 w-4', isFavorite && 'fill-red-500 text-red-500')} />
              </Button>
              <Button
                size="sm"
                variant="secondary"
                className="h-8 w-8 p-0"
                onClick={handleAddToComparison}
                disabled={isInComparison}
              >
                <ShoppingCart className="h-4 w-4" />
              </Button>
            </div>

            {/* Availability badge */}
            {laptop.availability?.inStock && (
              <Badge className="absolute top-2 left-2 bg-green-500">
                In Stock
              </Badge>
            )}

            {/* Compatibility score */}
            {showCompatibility && compatibilityScore && (
              <Badge className={cn('absolute bottom-2 left-2', getCompatibilityColor(compatibilityScore))}>
                <Zap className="h-3 w-3 mr-1" />
                {getCompatibilityLabel(compatibilityScore)}
              </Badge>
            )}
          </div>
        </CardHeader>

        <CardContent className="p-4">
          <div className="space-y-2">
            <h3 className="font-semibold text-lg line-clamp-2 group-hover:text-primary transition-colors">
              {laptop.title}
            </h3>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">{laptop.brand}</span>
              <Badge variant="outline">{laptop.features.category}</Badge>
            </div>

            {variant === 'detailed' && (
              <div className="grid grid-cols-2 gap-2 text-sm text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <Cpu className="h-3 w-3" />
                  <span>{laptop.specifications.cpu.model}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Monitor className="h-3 w-3" />
                  <span>{laptop.specifications.memory.size}GB RAM</span>
                </div>
                <div className="flex items-center space-x-1">
                  <HardDrive className="h-3 w-3" />
                  <span>{laptop.specifications.storage.capacity}GB {laptop.specifications.storage.type}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Monitor className="h-3 w-3" />
                  <span>{laptop.specifications.display.size}"</span>
                </div>
              </div>
            )}
          </div>
        </CardContent>

        <CardFooter className="p-4 pt-0">
          <div className="flex items-center justify-between w-full">
            <div>
              <p className="text-2xl font-bold">{formatPrice(laptop.price, laptop.currency)}</p>
              {laptop.availability?.stores && laptop.availability.stores.length > 0 && (
                <p className="text-sm text-muted-foreground">
                  Available at {laptop.availability.stores.length} store{laptop.availability.stores.length > 1 ? 's' : ''}
                </p>
              )}
            </div>
            
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={handleAddToComparison}>
                Compare
              </Button>
              <Button size="sm">
                View Details
              </Button>
            </div>
          </div>
        </CardFooter>
      </Card>
    </Link>
  )
})

LaptopCard.displayName = 'LaptopCard'
