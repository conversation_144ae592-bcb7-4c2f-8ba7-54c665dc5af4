'use client'

/**
 * Deals Filters Component
 * Advanced filtering interface for laptop deals
 */

import React from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { X, RotateCcw } from 'lucide-react'
import type { DealFilters } from '@/shared/types'

interface DealsFiltersProps {
  filters: DealFilters
  onFiltersChange: (filters: Partial<DealFilters>) => void
  onClearFilters: () => void
  className?: string
}

export function DealsFilters({ 
  filters, 
  onFiltersChange, 
  onClearFilters, 
  className 
}: DealsFiltersProps) {
  const dealTypeOptions = [
    { value: 'price-drop', label: 'Price Drop' },
    { value: 'best-price', label: 'Best Price' },
    { value: 'back-in-stock', label: 'Back in Stock' },
    { value: 'new-listing', label: 'New Listing' },
    { value: 'limited-time', label: 'Limited Time' }
  ]

  const popularBrands = [
    'Apple', 'Dell', 'HP', 'Lenovo', 'ASUS', 'Acer', 'MSI', 'Razer', 'Microsoft', 'Samsung'
  ]

  const popularSources = [
    'Amazon', 'Best Buy', 'Newegg', 'B&H', 'Costco', 'Walmart', 'Target', 'Micro Center'
  ]

  const handleDealTypeChange = (dealType: string, checked: boolean) => {
    const currentTypes = filters.dealTypes || []
    const newTypes = checked
      ? [...currentTypes, dealType as any]
      : currentTypes.filter(type => type !== dealType)
    
    onFiltersChange({ dealTypes: newTypes.length > 0 ? newTypes : undefined })
  }

  const handleBrandChange = (brand: string, checked: boolean) => {
    const currentBrands = filters.brands || []
    const newBrands = checked
      ? [...currentBrands, brand]
      : currentBrands.filter(b => b !== brand)
    
    onFiltersChange({ brands: newBrands.length > 0 ? newBrands : undefined })
  }

  const handleSourceChange = (source: string, checked: boolean) => {
    const currentSources = filters.sources || []
    const newSources = checked
      ? [...currentSources, source]
      : currentSources.filter(s => s !== source)
    
    onFiltersChange({ sources: newSources.length > 0 ? newSources : undefined })
  }

  const handlePriceChange = (value: number[]) => {
    onFiltersChange({ maxPrice: value[0] > 0 ? value[0] : undefined })
  }

  const handleDiscountChange = (value: number[]) => {
    onFiltersChange({ minDiscount: value[0] > 0 ? value[0] : undefined })
  }

  const handleDealScoreChange = (value: number[]) => {
    onFiltersChange({ minDealScore: value[0] > 0 ? value[0] : undefined })
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.dealTypes?.length) count++
    if (filters.brands?.length) count++
    if (filters.sources?.length) count++
    if (filters.maxPrice) count++
    if (filters.minDiscount) count++
    if (filters.minDealScore) count++
    if (filters.onlyHotDeals) count++
    if (filters.inStockOnly) count++
    if (filters.freeShippingOnly) count++
    return count
  }

  const activeFiltersCount = getActiveFiltersCount()

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Filter Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="font-semibold">Filters</h3>
          {activeFiltersCount > 0 && (
            <Badge variant="secondary">
              {activeFiltersCount} active
            </Badge>
          )}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={onClearFilters}
          disabled={activeFiltersCount === 0}
        >
          <RotateCcw className="h-4 w-4 mr-2" />
          Clear All
        </Button>
      </div>

      {/* Quick Filters */}
      <div className="space-y-4">
        <Label className="text-sm font-medium">Quick Filters</Label>
        <div className="flex flex-wrap gap-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="hot-deals"
              checked={filters.onlyHotDeals || false}
              onCheckedChange={(checked) => 
                onFiltersChange({ onlyHotDeals: checked ? true : undefined })
              }
            />
            <Label htmlFor="hot-deals" className="text-sm">Hot Deals Only</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="in-stock"
              checked={filters.inStockOnly || false}
              onCheckedChange={(checked) => 
                onFiltersChange({ inStockOnly: checked ? true : undefined })
              }
            />
            <Label htmlFor="in-stock" className="text-sm">In Stock Only</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="free-shipping"
              checked={filters.freeShippingOnly || false}
              onCheckedChange={(checked) => 
                onFiltersChange({ freeShippingOnly: checked ? true : undefined })
              }
            />
            <Label htmlFor="free-shipping" className="text-sm">Free Shipping</Label>
          </div>
        </div>
      </div>

      <Separator />

      {/* Deal Types */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Deal Types</Label>
        <div className="grid grid-cols-2 gap-2">
          {dealTypeOptions.map((option) => (
            <div key={option.value} className="flex items-center space-x-2">
              <Checkbox
                id={`deal-type-${option.value}`}
                checked={filters.dealTypes?.includes(option.value as any) || false}
                onCheckedChange={(checked) => 
                  handleDealTypeChange(option.value, checked as boolean)
                }
              />
              <Label htmlFor={`deal-type-${option.value}`} className="text-sm">
                {option.label}
              </Label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Price Range */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">
          Maximum Price: ${filters.maxPrice?.toLocaleString() || '5,000'}
        </Label>
        <Slider
          value={[filters.maxPrice || 5000]}
          onValueChange={handlePriceChange}
          max={5000}
          min={0}
          step={100}
          className="w-full"
        />
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>$0</span>
          <span>$5,000+</span>
        </div>
      </div>

      <Separator />

      {/* Minimum Discount */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">
          Minimum Discount: {filters.minDiscount || 0}%
        </Label>
        <Slider
          value={[filters.minDiscount || 0]}
          onValueChange={handleDiscountChange}
          max={70}
          min={0}
          step={5}
          className="w-full"
        />
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>0%</span>
          <span>70%+</span>
        </div>
      </div>

      <Separator />

      {/* Deal Score */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">
          Minimum Deal Score: {filters.minDealScore || 0}/100
        </Label>
        <Slider
          value={[filters.minDealScore || 0]}
          onValueChange={handleDealScoreChange}
          max={100}
          min={0}
          step={5}
          className="w-full"
        />
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>0</span>
          <span>100</span>
        </div>
      </div>

      <Separator />

      {/* Brands */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Brands</Label>
        <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
          {popularBrands.map((brand) => (
            <div key={brand} className="flex items-center space-x-2">
              <Checkbox
                id={`brand-${brand}`}
                checked={filters.brands?.includes(brand) || false}
                onCheckedChange={(checked) => 
                  handleBrandChange(brand, checked as boolean)
                }
              />
              <Label htmlFor={`brand-${brand}`} className="text-sm">
                {brand}
              </Label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Sources */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Sources</Label>
        <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
          {popularSources.map((source) => (
            <div key={source} className="flex items-center space-x-2">
              <Checkbox
                id={`source-${source}`}
                checked={filters.sources?.includes(source) || false}
                onCheckedChange={(checked) => 
                  handleSourceChange(source, checked as boolean)
                }
              />
              <Label htmlFor={`source-${source}`} className="text-sm">
                {source}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Active Filters Summary */}
      {activeFiltersCount > 0 && (
        <>
          <Separator />
          <div className="space-y-2">
            <Label className="text-sm font-medium">Active Filters</Label>
            <div className="flex flex-wrap gap-1">
              {filters.dealTypes?.map((type) => (
                <Badge key={type} variant="secondary" className="text-xs">
                  {type}
                  <X 
                    className="h-3 w-3 ml-1 cursor-pointer" 
                    onClick={() => handleDealTypeChange(type, false)}
                  />
                </Badge>
              ))}
              {filters.brands?.map((brand) => (
                <Badge key={brand} variant="secondary" className="text-xs">
                  {brand}
                  <X 
                    className="h-3 w-3 ml-1 cursor-pointer" 
                    onClick={() => handleBrandChange(brand, false)}
                  />
                </Badge>
              ))}
              {filters.sources?.map((source) => (
                <Badge key={source} variant="secondary" className="text-xs">
                  {source}
                  <X 
                    className="h-3 w-3 ml-1 cursor-pointer" 
                    onClick={() => handleSourceChange(source, false)}
                  />
                </Badge>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  )
}
