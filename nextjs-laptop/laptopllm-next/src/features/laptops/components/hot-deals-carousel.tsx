'use client'

/**
 * Hot Deals Carousel Component
 * Displays a horizontal scrollable carousel of hot deals
 */

import React, { useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ChevronLeft, ChevronRight, ExternalLink, Zap, TrendingDown } from 'lucide-react'
import { formatCurrency, formatPercentage } from '@/lib/utils'
import type { LaptopDeal } from '@/shared/types'

interface HotDealsCarouselProps {
  deals: LaptopDeal[]
  isLoading?: boolean
  className?: string
}

export function HotDealsCarousel({ deals, isLoading, className }: HotDealsCarouselProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null)

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -320, behavior: 'smooth' })
    }
  }

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 320, behavior: 'smooth' })
    }
  }

  if (isLoading) {
    return (
      <div className={`relative ${className}`}>
        <div className="flex gap-4 overflow-hidden">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i} className="flex-shrink-0 w-80 animate-pulse">
              <CardContent className="p-6">
                <div className="space-y-3">
                  <div className="h-4 bg-muted rounded w-3/4" />
                  <div className="h-4 bg-muted rounded w-1/2" />
                  <div className="h-8 bg-muted rounded" />
                  <div className="h-4 bg-muted rounded w-2/3" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!deals || deals.length === 0) {
    return (
      <div className={`text-center py-8 text-muted-foreground ${className}`}>
        No hot deals available at the moment.
      </div>
    )
  }

  return (
    <div className={`relative ${className}`}>
      {/* Navigation Buttons */}
      <div className="absolute left-0 top-1/2 -translate-y-1/2 z-10">
        <Button
          variant="outline"
          size="sm"
          onClick={scrollLeft}
          className="h-8 w-8 p-0 bg-background/80 backdrop-blur-sm"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
      </div>
      
      <div className="absolute right-0 top-1/2 -translate-y-1/2 z-10">
        <Button
          variant="outline"
          size="sm"
          onClick={scrollRight}
          className="h-8 w-8 p-0 bg-background/80 backdrop-blur-sm"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Carousel Container */}
      <div
        ref={scrollContainerRef}
        className="flex gap-4 overflow-x-auto scrollbar-hide pb-2"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {deals.map((deal) => (
          <HotDealCard key={deal.id} deal={deal} />
        ))}
      </div>
    </div>
  )
}

interface HotDealCardProps {
  deal: LaptopDeal
}

function HotDealCard({ deal }: HotDealCardProps) {
  const {
    laptop,
    listing,
    currentPrice,
    originalPrice,
    discountPercentage,
    dealScore,
    availability,
    shipping
  } = deal

  return (
    <Card className="flex-shrink-0 w-80 hover:shadow-lg transition-shadow relative overflow-hidden">
      {/* Hot Deal Badge */}
      <div className="absolute top-3 right-3 z-10">
        <Badge className="bg-orange-500 text-white">
          <Zap className="h-3 w-3 mr-1" />
          Hot
        </Badge>
      </div>

      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Laptop Info */}
          <div className="space-y-1">
            <h3 className="font-semibold text-lg leading-tight line-clamp-2">
              {laptop.model_name}
            </h3>
            <p className="text-sm text-muted-foreground">
              {laptop.brands?.name}
            </p>
          </div>

          {/* Price Section */}
          <div className="space-y-2">
            <div className="flex items-baseline gap-2">
              <span className="text-2xl font-bold text-green-600">
                {formatCurrency(currentPrice)}
              </span>
              {originalPrice && originalPrice > currentPrice && (
                <span className="text-sm text-muted-foreground line-through">
                  {formatCurrency(originalPrice)}
                </span>
              )}
            </div>
            
            {discountPercentage && discountPercentage > 0 && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-green-600 bg-green-50">
                  <TrendingDown className="h-3 w-3 mr-1" />
                  {formatPercentage(discountPercentage)} OFF
                </Badge>
              </div>
            )}
          </div>

          {/* Deal Score */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Deal Score</span>
              <span className="text-sm font-bold text-orange-600">
                {dealScore}/100
              </span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div
                className="bg-orange-500 h-2 rounded-full transition-all"
                style={{ width: `${dealScore}%` }}
              />
            </div>
          </div>

          {/* Quick Info */}
          <div className="flex items-center justify-between text-sm">
            <span className={`font-medium ${availability.inStock ? 'text-green-600' : 'text-red-600'}`}>
              {availability.inStock ? 'In Stock' : 'Out of Stock'}
            </span>
            <span className={shipping.isFree ? 'text-green-600' : 'text-muted-foreground'}>
              {shipping.isFree ? 'Free Shipping' : `+${formatCurrency(shipping.cost)}`}
            </span>
          </div>

          {/* Source */}
          <div className="text-xs text-muted-foreground">
            from {listing.source.name}
          </div>

          {/* Action Button */}
          <Button asChild className="w-full" size="sm">
            <a 
              href={listing.url} 
              target="_blank" 
              rel="noopener noreferrer"
              className="flex items-center gap-2"
            >
              View Deal
              <ExternalLink className="h-3 w-3" />
            </a>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
