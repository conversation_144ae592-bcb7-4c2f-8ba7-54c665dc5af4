'use client'

/**
 * Search Results Component
 * Displays laptop search results with different view modes and pagination
 */

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { 
  Grid3X3, 
  List, 
  LayoutGrid,
  ChevronLeft, 
  ChevronRight,
  Filter,
  SortAsc,
  SortDesc,
  Search,
  Laptop,
  TrendingUp,
  Star,
  Zap,
  Info,
  RefreshCw
} from 'lucide-react'
import { LaptopCard } from './laptop-card'
import type { LaptopSearchResult, LaptopSearchParams } from '../types'

interface SearchResultsProps {
  searchResult: LaptopSearchResult
  searchParams: LaptopSearchParams
  onSearchParamsChange: (params: LaptopSearchParams) => void
  onRefresh: () => void
  isLoading?: boolean
  isRefreshing?: boolean
  className?: string
}

type ViewMode = 'grid' | 'list' | 'compact'

export function SearchResults({
  searchResult,
  searchParams,
  onSearchParamsChange,
  onRefresh,
  isLoading = false,
  isRefreshing = false,
  className
}: SearchResultsProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [selectedLaptops, setSelectedLaptops] = useState<Set<string>>(new Set())

  const { laptops, total, page, totalPages, hasMore } = searchResult

  const handlePageChange = (newPage: number) => {
    onSearchParamsChange({ ...searchParams, page: newPage })
  }

  const handleLimitChange = (newLimit: string) => {
    onSearchParamsChange({ 
      ...searchParams, 
      limit: parseInt(newLimit),
      page: 1 // Reset to first page when changing limit
    })
  }

  const handleSortChange = (sortBy: string) => {
    const currentSortOrder = searchParams.sortOrder || 'desc'
    const newSortOrder = searchParams.sortBy === sortBy && currentSortOrder === 'desc' ? 'asc' : 'desc'
    
    onSearchParamsChange({
      ...searchParams,
      sortBy: sortBy as any,
      sortOrder: newSortOrder,
      page: 1
    })
  }

  const toggleLaptopSelection = (laptopId: string) => {
    const newSelected = new Set(selectedLaptops)
    if (newSelected.has(laptopId)) {
      newSelected.delete(laptopId)
    } else {
      newSelected.add(laptopId)
    }
    setSelectedLaptops(newSelected)
  }

  const clearSelection = () => {
    setSelectedLaptops(new Set())
  }

  const getViewModeIcon = (mode: ViewMode) => {
    switch (mode) {
      case 'grid': return <Grid3X3 className="h-4 w-4" />
      case 'list': return <List className="h-4 w-4" />
      case 'compact': return <LayoutGrid className="h-4 w-4" />
    }
  }

  const getSortIcon = (sortBy: string) => {
    if (searchParams.sortBy !== sortBy) return null
    return searchParams.sortOrder === 'desc' ? 
      <SortDesc className="h-3 w-3 ml-1" /> : 
      <SortAsc className="h-3 w-3 ml-1" />
  }

  const renderPagination = () => {
    if (totalPages <= 1) return null

    const startItem = (page - 1) * (searchParams.limit || 12) + 1
    const endItem = Math.min(page * (searchParams.limit || 12), total)

    return (
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {startItem}-{endItem} of {total} results
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(page - 1)}
            disabled={page <= 1 || isLoading}
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>
          
          <div className="flex items-center space-x-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum
              if (totalPages <= 5) {
                pageNum = i + 1
              } else if (page <= 3) {
                pageNum = i + 1
              } else if (page >= totalPages - 2) {
                pageNum = totalPages - 4 + i
              } else {
                pageNum = page - 2 + i
              }
              
              return (
                <Button
                  key={pageNum}
                  variant={pageNum === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePageChange(pageNum)}
                  disabled={isLoading}
                  className="w-8 h-8 p-0"
                >
                  {pageNum}
                </Button>
              )
            })}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(page + 1)}
            disabled={page >= totalPages || isLoading}
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    )
  }

  const renderLaptopGrid = () => {
    if (laptops.length === 0) {
      return (
        <div className="text-center py-12">
          <Search className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-medium mb-2">No laptops found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search criteria or filters to find more results.
          </p>
          <Button variant="outline" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Search
          </Button>
        </div>
      )
    }

    const gridClass = viewMode === 'grid' 
      ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
      : viewMode === 'list'
      ? 'space-y-4'
      : 'grid grid-cols-1 md:grid-cols-2 gap-4'

    return (
      <div className={gridClass}>
        {laptops.map((laptop) => (
          <LaptopCard
            key={laptop.url}
            laptop={laptop}
            variant={viewMode === 'list' ? 'detailed' : viewMode === 'compact' ? 'compact' : 'default'}
            showCompatibility={true}
            compatibilityScore={85} // This would come from actual compatibility data
            onFavorite={toggleLaptopSelection}
            onAddToComparison={toggleLaptopSelection}
            isFavorite={selectedLaptops.has(laptop.url)}
            isInComparison={selectedLaptops.has(laptop.url)}
          />
        ))}
      </div>
    )
  }

  if (isLoading && laptops.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Searching for laptops...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={className}>
      {/* Results Header */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Laptop className="h-5 w-5 mr-2" />
                Search Results
                {isRefreshing && (
                  <RefreshCw className="h-4 w-4 ml-2 animate-spin" />
                )}
              </CardTitle>
              <CardDescription>
                {total > 0 ? (
                  <>
                    Found {total} laptop{total > 1 ? 's' : ''} matching your criteria
                    {searchParams.query && ` for "${searchParams.query}"`}
                  </>
                ) : (
                  'No results found'
                )}
              </CardDescription>
            </div>
            
            <div className="flex items-center space-x-2">
              {/* Results per page */}
              <Select
                value={(searchParams.limit || 12).toString()}
                onValueChange={handleLimitChange}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="12">12</SelectItem>
                  <SelectItem value="24">24</SelectItem>
                  <SelectItem value="48">48</SelectItem>
                  <SelectItem value="96">96</SelectItem>
                </SelectContent>
              </Select>
              
              {/* View mode toggle */}
              <div className="flex border rounded-md">
                {(['grid', 'list', 'compact'] as ViewMode[]).map((mode) => (
                  <Button
                    key={mode}
                    variant={viewMode === mode ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode(mode)}
                    className="rounded-none first:rounded-l-md last:rounded-r-md"
                  >
                    {getViewModeIcon(mode)}
                  </Button>
                ))}
              </div>
              
              <Button variant="outline" size="sm" onClick={onRefresh} disabled={isRefreshing}>
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>

          {/* Sort Options */}
          <div className="flex flex-wrap gap-2 mt-4">
            <span className="text-sm text-muted-foreground">Sort by:</span>
            {[
              { key: 'compatibility', label: 'LLM Compatibility' },
              { key: 'price', label: 'Price' },
              { key: 'rating', label: 'Rating' },
              { key: 'name', label: 'Name' },
              { key: 'newest', label: 'Newest' }
            ].map(({ key, label }) => (
              <Button
                key={key}
                variant={searchParams.sortBy === key ? "default" : "outline"}
                size="sm"
                onClick={() => handleSortChange(key)}
                className="flex items-center"
              >
                {label}
                {getSortIcon(key)}
              </Button>
            ))}
          </div>

          {/* Selected Items */}
          {selectedLaptops.size > 0 && (
            <Alert className="mt-4">
              <Star className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>
                  {selectedLaptops.size} laptop{selectedLaptops.size > 1 ? 's' : ''} selected
                </span>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    Compare Selected
                  </Button>
                  <Button variant="outline" size="sm" onClick={clearSelection}>
                    Clear Selection
                  </Button>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardHeader>
      </Card>

      {/* Results Grid */}
      <div className="space-y-6">
        {renderLaptopGrid()}
        
        {/* Pagination */}
        {total > 0 && (
          <Card>
            <CardContent className="py-4">
              {renderPagination()}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Load More Button (for infinite scroll alternative) */}
      {hasMore && viewMode !== 'list' && (
        <div className="text-center mt-6">
          <Button 
            variant="outline" 
            onClick={() => handlePageChange(page + 1)}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                Loading...
              </>
            ) : (
              <>
                <TrendingUp className="h-4 w-4 mr-2" />
                Load More Results
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  )
}


