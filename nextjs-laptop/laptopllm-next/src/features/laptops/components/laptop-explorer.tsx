'use client'

/**
 * Laptop Explorer Component
 * Main dashboard for advanced laptop exploration and search
 */

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Search, 
  TrendingUp, 
  Star, 
  Filter,
  Laptop,
  BarChart3,
  Target,
  Zap,
  Award,
  Info,
  Compass,
  Settings
} from 'lucide-react'
import { AdvancedSearch } from './advanced-search'
import { SearchResults } from './search-results'
import { useLaptopSearch } from '../hooks/use-laptop-search'
import type { LaptopSearchParams } from '../types'

interface LaptopExplorerProps {
  className?: string
}

export function LaptopExplorer({ className }: LaptopExplorerProps) {
  const [activeTab, setActiveTab] = useState('search')
  
  // Initialize search with default parameters
  const {
    laptops,
    total,
    page,
    totalPages,
    hasMore,
    searchParams,
    isLoading,
    isError,
    error,
    isFetching,
    hasResults,
    isEmpty,
    activeFilterCount,
    updateQuery,
    updateFilters,
    clearFilters,
    updateSort,
    changePage,
    changeLimit,
    resetSearch,
    refetch,
    prefetchNextPage
  } = useLaptopSearch({
    page: 1,
    limit: 12,
    sortBy: 'compatibility',
    sortOrder: 'desc'
  })

  const searchResult = {
    laptops,
    total,
    page,
    totalPages,
    hasMore
  }

  const handleSearchParamsChange = useCallback((newParams: LaptopSearchParams) => {
    // Update individual parts of search params
    if (newParams.query !== searchParams.query) {
      updateQuery(newParams.query || '')
    }
    
    if (newParams.filters !== searchParams.filters) {
      updateFilters(newParams.filters || {})
    }
    
    if (newParams.sortBy !== searchParams.sortBy || newParams.sortOrder !== searchParams.sortOrder) {
      updateSort(newParams.sortBy || 'compatibility', newParams.sortOrder || 'desc')
    }
    
    if (newParams.page !== searchParams.page) {
      changePage(newParams.page || 1)
    }
    
    if (newParams.limit !== searchParams.limit) {
      changeLimit(newParams.limit || 12)
    }
  }, [searchParams, updateQuery, updateFilters, updateSort, changePage, changeLimit])

  const handleSearch = useCallback(() => {
    refetch()
  }, [refetch])

  const handleReset = useCallback(() => {
    resetSearch()
  }, [resetSearch])

  // Quick stats for the overview
  const quickStats = {
    totalLaptops: total,
    activeFilters: activeFilterCount,
    avgCompatibility: 78, // This would come from actual data
    topBrands: ['Apple', 'Dell', 'HP', 'Lenovo', 'ASUS'] // This would come from actual data
  }

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center">
              <Compass className="h-8 w-8 mr-3" />
              Laptop Explorer
            </h1>
            <p className="text-muted-foreground">
              Advanced search and exploration tools for finding the perfect laptop
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Preferences
            </Button>
            <Button variant="outline" size="sm" asChild>
              <a href="/compatibility">
                <Zap className="h-4 w-4 mr-2" />
                LLM Compatibility
              </a>
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Laptops</CardTitle>
              <Laptop className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{quickStats.totalLaptops}</div>
              <p className="text-xs text-muted-foreground">
                Available in database
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Filters</CardTitle>
              <Filter className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{quickStats.activeFilters}</div>
              <p className="text-xs text-muted-foreground">
                Currently applied
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg LLM Score</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{quickStats.avgCompatibility}/100</div>
              <p className="text-xs text-muted-foreground">
                Compatibility rating
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Top Brands</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{quickStats.topBrands.length}</div>
              <p className="text-xs text-muted-foreground">
                Leading manufacturers
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="search">Advanced Search</TabsTrigger>
            <TabsTrigger value="trending">Trending</TabsTrigger>
            <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Advanced Search Tab */}
          <TabsContent value="search" className="space-y-6">
            <AdvancedSearch
              searchParams={searchParams}
              onSearchParamsChange={handleSearchParamsChange}
              onSearch={handleSearch}
              onReset={handleReset}
              isLoading={isLoading}
              resultCount={total}
            />

            {isError && (
              <Alert variant="destructive">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Error loading search results: {error?.message || 'Unknown error'}
                  <Button variant="outline" size="sm" className="ml-2" onClick={refetch}>
                    Retry
                  </Button>
                </AlertDescription>
              </Alert>
            )}

            <SearchResults
              searchResult={searchResult}
              searchParams={searchParams}
              onSearchParamsChange={handleSearchParamsChange}
              onRefresh={refetch}
              isLoading={isLoading}
              isRefreshing={isFetching}
            />
          </TabsContent>

          {/* Trending Tab */}
          <TabsContent value="trending" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Trending Laptops
                </CardTitle>
                <CardDescription>
                  Most popular and highly-rated laptops this month
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Trending laptops feature will be implemented based on user interactions, 
                    search patterns, and compatibility scores. This will include:
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Most searched laptops</li>
                      <li>Highest compatibility scores</li>
                      <li>Best price-performance ratio</li>
                      <li>Recently added models</li>
                      <li>Community favorites</li>
                    </ul>
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Recommendations Tab */}
          <TabsContent value="recommendations" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="h-5 w-5 mr-2" />
                  Personalized Recommendations
                </CardTitle>
                <CardDescription>
                  Laptop recommendations based on your preferences and usage patterns
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Personalized recommendations will be implemented to provide:
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Recommendations based on search history</li>
                      <li>LLM model compatibility preferences</li>
                      <li>Budget and performance requirements</li>
                      <li>Similar user preferences</li>
                      <li>Seasonal deals and promotions</li>
                    </ul>
                    <div className="mt-4">
                      <Button variant="outline" asChild>
                        <a href="/compatibility">
                          <Zap className="h-4 w-4 mr-2" />
                          Explore LLM Compatibility
                        </a>
                      </Button>
                    </div>
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  Market Analytics
                </CardTitle>
                <CardDescription>
                  Insights and trends in the laptop market
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Price Trends */}
                  <div>
                    <h4 className="font-medium mb-3">Price Distribution</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Budget ($0-$800)</span>
                        <Badge variant="outline">25%</Badge>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Mid-range ($800-$1500)</span>
                        <Badge variant="outline">45%</Badge>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Premium ($1500-$3000)</span>
                        <Badge variant="outline">25%</Badge>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Ultra-premium ($3000+)</span>
                        <Badge variant="outline">5%</Badge>
                      </div>
                    </div>
                  </div>

                  {/* Brand Distribution */}
                  <div>
                    <h4 className="font-medium mb-3">Brand Popularity</h4>
                    <div className="space-y-2">
                      {quickStats.topBrands.map((brand, index) => (
                        <div key={brand} className="flex justify-between text-sm">
                          <span>{brand}</span>
                          <Badge variant="outline">
                            {Math.max(25 - index * 3, 8)}%
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <Alert className="mt-6">
                  <BarChart3 className="h-4 w-4" />
                  <AlertDescription>
                    Detailed analytics dashboard with interactive charts and real-time 
                    market data will be implemented in a future update.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}


