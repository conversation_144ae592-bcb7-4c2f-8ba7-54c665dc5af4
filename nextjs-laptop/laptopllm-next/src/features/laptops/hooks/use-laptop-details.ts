import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useCallback } from 'react'
import { laptopService } from '../services/laptop-service'
import type { LaptopData, LaptopAvailability, LaptopPriceHistory } from '../types'

/**
 * Hook for laptop details
 */
export function useLaptopDetails(laptopId: string) {
  const queryClient = useQueryClient()

  const {
    data: laptop,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery<LaptopData>({
    queryKey: ['laptops', 'detail', laptopId],
    queryFn: () => laptopService.getLaptopById(laptopId),
    enabled: !!laptopId,
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
    retry: (failureCount, error: Error) => {
      // Don't retry on 404
      if (error?.message?.includes('not found')) return false
      return failureCount < 3
    },
  })

  // Prefetch related data
  const prefetchRelatedData = useCallback(() => {
    if (!laptopId) return

    // Prefetch similar laptops
    queryClient.prefetchQuery({
      queryKey: ['laptops', 'similar', laptopId],
      queryFn: () => laptopService.getSimilarLaptops(laptopId),
      staleTime: 30 * 60 * 1000, // 30 minutes
    })

    // Prefetch availability
    queryClient.prefetchQuery({
      queryKey: ['laptops', 'availability', laptopId],
      queryFn: () => laptopService.getLaptopAvailability(laptopId),
      staleTime: 5 * 60 * 1000, // 5 minutes
    })
  }, [queryClient, laptopId])

  return {
    laptop,
    isLoading,
    isError,
    error,
    refetch,
    prefetchRelatedData,
  }
}

/**
 * Hook for similar laptops
 */
export function useSimilarLaptops(laptopId: string, limit = 5) {
  return useQuery<LaptopData[]>({
    queryKey: ['laptops', 'similar', laptopId, limit],
    queryFn: () => laptopService.getSimilarLaptops(laptopId, limit),
    enabled: !!laptopId,
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  })
}

/**
 * Hook for laptop availability
 */
export function useLaptopAvailability(laptopId: string) {
  return useQuery<LaptopAvailability>({
    queryKey: ['laptops', 'availability', laptopId],
    queryFn: () => laptopService.getLaptopAvailability(laptopId),
    enabled: !!laptopId,
    staleTime: 5 * 60 * 1000, // 5 minutes - availability changes frequently
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  })
}

/**
 * Hook for laptop price history
 */
export function useLaptopPriceHistory(laptopId: string, days = 30) {
  return useQuery<LaptopPriceHistory>({
    queryKey: ['laptops', 'price-history', laptopId, days],
    queryFn: () => laptopService.getLaptopPriceHistory(laptopId, days),
    enabled: !!laptopId,
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 4 * 60 * 60 * 1000, // 4 hours
  })
}

/**
 * Hook for multiple laptops by IDs
 */
export function useLaptopsByIds(laptopIds: string[]) {
  return useQuery<LaptopData[]>({
    queryKey: ['laptops', 'batch', laptopIds.sort()],
    queryFn: () => laptopService.getLaptopsByIds(laptopIds),
    enabled: laptopIds.length > 0,
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  })
}

/**
 * Hook for laptop comparison
 */
export function useLaptopComparison(laptopIds: string[]) {
  return useQuery({
    queryKey: ['laptops', 'compare', laptopIds.sort()],
    queryFn: () => laptopService.compareLaptops(laptopIds),
    enabled: laptopIds.length >= 2,
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  })
}

/**
 * Hook for trending laptops
 */
export function useTrendingLaptops(limit = 10) {
  return useQuery<LaptopData[]>({
    queryKey: ['laptops', 'trending', limit],
    queryFn: () => laptopService.getTrendingLaptops(limit),
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  })
}

/**
 * Hook for featured laptops
 */
export function useFeaturedLaptops(category?: string) {
  return useQuery<LaptopData[]>({
    queryKey: ['laptops', 'featured', category],
    queryFn: () => laptopService.getFeaturedLaptops(category),
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 4 * 60 * 60 * 1000, // 4 hours
  })
}

/**
 * Hook for laptop statistics
 */
export function useLaptopStats() {
  return useQuery({
    queryKey: ['laptops', 'stats'],
    queryFn: () => laptopService.getLaptopStats(),
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 4 * 60 * 60 * 1000, // 4 hours
  })
}
