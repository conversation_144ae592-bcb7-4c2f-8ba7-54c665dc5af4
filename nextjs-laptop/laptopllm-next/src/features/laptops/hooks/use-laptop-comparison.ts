/**
 * Laptop Comparison Hook (Legacy Wrapper)
 * Wrapper around the enhanced laptop comparison hook for backward compatibility
 */

import { useLaptopComparison as useEnhancedLaptopComparison } from '@/features/laptop-comparison/hooks/use-laptop-comparison'
import type {
  ComparisonHookOptions,
  ComparisonStatistics
} from '@/features/laptop-comparison/types'
import type { LaptopData } from '@/shared/types'

interface UseLaptopComparisonOptions {
  maxLaptops?: number
  persistToStorage?: boolean
  storageKey?: string
}

interface ComparisonStats {
  priceRange: { min: number; max: number; avg: number }
  compatibilityRange: { min: number; max: number; avg: number }
  memoryRange: { min: number; max: number; avg: number }
  storageRange: { min: number; max: number; avg: number }
  weightRange: { min: number; max: number; avg: number }
  batteryRange: { min: number; max: number; avg: number }
  brands: string[]
  categories: string[]
}

interface UseLaptopComparisonReturn {
  comparedLaptops: LaptopData[];
  comparedLaptopIds: string[];
  isLoading: boolean;
  error: string | null;
  comparisonCount: number;
  canAddMore: boolean;
  hasComparison: boolean;
  comparisonStats: ComparisonStats | null;
  addLaptop: (laptop: LaptopData | string) => Promise<void>;
  removeLaptop: (laptopId: string) => void;
  clearComparison: () => void;
  toggleLaptop: (laptop: LaptopData | string) => Promise<void>;
  isInComparison: (laptopId: string) => boolean;
  refetch: () => Promise<void>;
  maxLaptops: number;
}

/**
 * Legacy laptop comparison hook for backward compatibility
 */
export function useLaptopComparison(options: UseLaptopComparisonOptions = {}): UseLaptopComparisonReturn {
  const {
    maxLaptops = 4,
    persistToStorage = true,
    storageKey = 'laptop-comparison'
  } = options

  // Use the enhanced hook with mapped options
  const enhancedOptions: ComparisonHookOptions = {
    maxLaptops,
    persistToStorage,
    storageKey,
    autoRefresh: false,
    refreshInterval: 30000
  }

  const {
    comparedLaptops,
    comparedLaptopIds,
    isLoading,
    error,
    comparisonCount,
    canAddMore,
    hasComparison,
    comparisonStats: enhancedStats,
    addLaptop,
    removeLaptop,
    clearComparison,
    toggleLaptop,
    isInComparison,
    refetch,
    maxLaptops: maxLaptopsFromHook
  } = useEnhancedLaptopComparison(enhancedOptions)

  // Transform enhanced stats to legacy format
  const transformStats = (stats: ComparisonStatistics | null): ComparisonStats | null => {
    if (!stats) return null

    return {
      priceRange: {
        min: stats.priceRange.min,
        max: stats.priceRange.max,
        avg: stats.priceRange.average
      },
      compatibilityRange: {
        min: stats.compatibilityRange.min,
        max: stats.compatibilityRange.max,
        avg: stats.compatibilityRange.average
      },
      memoryRange: {
        min: stats.memoryRange.min,
        max: stats.memoryRange.max,
        avg: stats.memoryRange.average
      },
      storageRange: {
        min: stats.storageRange.min,
        max: stats.storageRange.max,
        avg: stats.storageRange.average
      },
      weightRange: {
        min: stats.weightRange.min,
        max: stats.weightRange.max,
        avg: stats.weightRange.average
      },
      batteryRange: {
        min: stats.batteryRange.min,
        max: stats.batteryRange.max,
        avg: stats.batteryRange.average
      },
      brands: stats.brands,
      categories: stats.categories
    }
  }

  const comparisonStats = transformStats(enhancedStats)

  return {
    // State
    comparedLaptops,
    comparedLaptopIds,
    isLoading,
    error: error?.message || null,

    // Computed values
    comparisonCount,
    canAddMore,
    hasComparison,
    comparisonStats,

    // Actions
    addLaptop: async (laptop: LaptopData | string) => {
      try {
        await addLaptop(laptop)
      } catch (error) {
        console.error('Failed to add laptop:', error)
      }
    },
    removeLaptop,
    clearComparison,
    toggleLaptop: async (laptop: LaptopData | string) => {
      try {
        await toggleLaptop(laptop)
      } catch (error) {
        console.error('Failed to toggle laptop:', error)
      }
    },
    isInComparison,

    // Utilities
    refetch,
    maxLaptops: maxLaptopsFromHook
  }
}
