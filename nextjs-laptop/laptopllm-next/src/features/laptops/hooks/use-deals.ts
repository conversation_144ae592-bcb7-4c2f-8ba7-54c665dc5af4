'use client'

/**
 * React hooks for laptop deals management
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useState, useCallback } from 'react'
import type { 
  LaptopDeal, 
  DealFilters, 
  DealsSearchResult,
  DealAlert,
  DealNotification
} from '@/shared/types'

// API functions
const fetchDeals = async (filters: DealFilters, page: number, limit: number): Promise<DealsSearchResult> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...Object.fromEntries(
      Object.entries(filters).map(([key, value]) => [
        key,
        Array.isArray(value) ? value.join(',') : value?.toString() || ''
      ])
    )
  })

  const response = await fetch(`/api/deals?${params}`)
  if (!response.ok) {
    throw new Error('Failed to fetch deals')
  }
  return response.json()
}

const fetchLaptopDeals = async (laptopId: number): Promise<LaptopDeal[]> => {
  const response = await fetch(`/api/laptops/${laptopId}/deals`)
  if (!response.ok) {
    throw new Error('Failed to fetch laptop deals')
  }
  return response.json()
}

const fetchHotDeals = async (limit: number = 10): Promise<LaptopDeal[]> => {
  const response = await fetch(`/api/deals/hot?limit=${limit}`)
  if (!response.ok) {
    throw new Error('Failed to fetch hot deals')
  }
  return response.json()
}

const createDealAlert = async (alert: Omit<DealAlert, 'id' | 'createdAt' | 'lastTriggered' | 'triggerCount'>): Promise<DealAlert> => {
  const response = await fetch('/api/deal-alerts', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(alert),
  })
  if (!response.ok) {
    throw new Error('Failed to create deal alert')
  }
  return response.json()
}

/**
 * Hook for fetching and managing deals with filtering and pagination
 */
export function useDeals(initialFilters: DealFilters = {}, initialPage = 1, limit = 20) {
  const [filters, setFilters] = useState<DealFilters>(initialFilters)
  const [page, setPage] = useState(initialPage)

  const {
    data,
    isLoading,
    error,
    refetch,
    isFetching
  } = useQuery({
    queryKey: ['deals', filters, page, limit],
    queryFn: () => fetchDeals(filters, page, limit),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })

  const updateFilters = useCallback((newFilters: Partial<DealFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
    setPage(1) // Reset to first page when filters change
  }, [])

  const clearFilters = useCallback(() => {
    setFilters({})
    setPage(1)
  }, [])

  const nextPage = useCallback(() => {
    if (data?.hasMore) {
      setPage(prev => prev + 1)
    }
  }, [data?.hasMore])

  const prevPage = useCallback(() => {
    if (page > 1) {
      setPage(prev => prev - 1)
    }
  }, [page])

  const goToPage = useCallback((newPage: number) => {
    if (newPage >= 1) {
      setPage(newPage)
    }
  }, [])

  return {
    // Data
    deals: data?.deals || [],
    summary: data?.summary,
    total: data?.total || 0,
    page,
    hasMore: data?.hasMore || false,
    
    // State
    isLoading,
    isFetching,
    error,
    filters,
    
    // Actions
    updateFilters,
    clearFilters,
    nextPage,
    prevPage,
    goToPage,
    refetch
  }
}

/**
 * Hook for fetching deals for a specific laptop
 */
export function useLaptopDeals(laptopId: number) {
  return useQuery({
    queryKey: ['laptop-deals', laptopId],
    queryFn: () => fetchLaptopDeals(laptopId),
    enabled: !!laptopId,
    staleTime: 5 * 60 * 1000,
  })
}

/**
 * Hook for fetching hot deals
 */
export function useHotDeals(limit = 10) {
  return useQuery({
    queryKey: ['hot-deals', limit],
    queryFn: () => fetchHotDeals(limit),
    staleTime: 2 * 60 * 1000, // 2 minutes for hot deals
    refetchInterval: 5 * 60 * 1000, // Auto-refresh every 5 minutes
  })
}

/**
 * Hook for managing deal alerts
 */
export function useDealAlerts() {
  const queryClient = useQueryClient()

  const createAlert = useMutation({
    mutationFn: createDealAlert,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['deal-alerts'] })
    },
  })

  const deleteAlert = useMutation({
    mutationFn: async (alertId: string) => {
      const response = await fetch(`/api/deal-alerts/${alertId}`, {
        method: 'DELETE',
      })
      if (!response.ok) {
        throw new Error('Failed to delete deal alert')
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['deal-alerts'] })
    },
  })

  const toggleAlert = useMutation({
    mutationFn: async ({ alertId, isActive }: { alertId: string; isActive: boolean }) => {
      const response = await fetch(`/api/deal-alerts/${alertId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive }),
      })
      if (!response.ok) {
        throw new Error('Failed to update deal alert')
      }
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['deal-alerts'] })
    },
  })

  return {
    createAlert,
    deleteAlert,
    toggleAlert,
  }
}

/**
 * Hook for deal notifications
 */
export function useDealNotifications() {
  const queryClient = useQueryClient()

  const {
    data: notifications,
    isLoading,
    error
  } = useQuery({
    queryKey: ['deal-notifications'],
    queryFn: async (): Promise<DealNotification[]> => {
      const response = await fetch('/api/deal-notifications')
      if (!response.ok) {
        throw new Error('Failed to fetch notifications')
      }
      return response.json()
    },
    staleTime: 1 * 60 * 1000, // 1 minute
  })

  const markAsRead = useMutation({
    mutationFn: async (notificationId: string) => {
      const response = await fetch(`/api/deal-notifications/${notificationId}/read`, {
        method: 'PATCH',
      })
      if (!response.ok) {
        throw new Error('Failed to mark notification as read')
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['deal-notifications'] })
    },
  })

  const markAllAsRead = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/deal-notifications/read-all', {
        method: 'PATCH',
      })
      if (!response.ok) {
        throw new Error('Failed to mark all notifications as read')
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['deal-notifications'] })
    },
  })

  const unreadCount = notifications?.filter(n => !n.isRead).length || 0

  return {
    notifications: notifications || [],
    unreadCount,
    isLoading,
    error,
    markAsRead,
    markAllAsRead,
  }
}

/**
 * Hook for deal comparison and analysis
 */
export function useDealComparison(dealIds: string[]) {
  return useQuery({
    queryKey: ['deal-comparison', dealIds],
    queryFn: async () => {
      const response = await fetch('/api/deals/compare', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ dealIds }),
      })
      if (!response.ok) {
        throw new Error('Failed to compare deals')
      }
      return response.json()
    },
    enabled: dealIds.length > 1,
    staleTime: 5 * 60 * 1000,
  })
}

/**
 * Hook for deal price history and trends
 *
 */
export function useDealPriceHistory(dealId: string, days = 30) {
  return useQuery({
    queryKey: ['deal-price-history', dealId, days],
    queryFn: async () => {
      const response = await fetch(`/api/deals/${dealId}/price-history?days=${days}`)
      if (!response.ok) {
        throw new Error('Failed to fetch price history')
      }
      return response.json()
    },
    enabled: !!dealId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for deal statistics and analytics
 */
export function useDealStats() {
  return useQuery({
    queryKey: ['deal-stats'],
    queryFn: async () => {
      const response = await fetch('/api/deals/stats')
      if (!response.ok) {
        throw new Error('Failed to fetch deal stats')
      }
      return response.json()
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
    refetchInterval: 30 * 60 * 1000, // Auto-refresh every 30 minutes
  })
}