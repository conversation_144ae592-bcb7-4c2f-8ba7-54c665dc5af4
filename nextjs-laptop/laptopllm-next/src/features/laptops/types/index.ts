import type { LaptopData } from '@/shared/types'

/**
 * Laptop feature specific types
 */

export interface LaptopFilters {
  category?: string[]
  priceRange?: {
    min: number
    max: number
  }
  brands?: string[]
  cpuBrands?: string[]
  gpuBrands?: string[]
  memorySize?: number[]
  storageType?: string[]
  displaySize?: {
    min: number
    max: number
  }
  weight?: {
    max: number
  }
  batteryLife?: {
    min: number
  }
  inStock?: boolean
}

export interface LaptopSearchParams {
  query?: string
  filters?: LaptopFilters
  sortBy?: 'price' | 'rating' | 'compatibility' | 'name' | 'newest'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface LaptopSearchResult {
  laptops: LaptopData[]
  total: number
  page: number
  totalPages: number
  hasMore: boolean
}

export interface LaptopComparison {
  laptops: LaptopData[]
  maxItems: number
}

export interface LaptopFavorites {
  laptopIds: string[]
  lastUpdated: Date
}

export interface LaptopViewMode {
  type: 'grid' | 'list' | 'compact'
  itemsPerPage: number
}

export interface LaptopSortOption {
  value: string
  label: string
  field: keyof LaptopData | 'compatibility'
  order: 'asc' | 'desc'
}

export interface LaptopCategoryFilter {
  value: string
  label: string
  count?: number
}

export interface LaptopPriceRange {
  min: number
  max: number
  step: number
}

export interface LaptopSpecificationFilter {
  type: 'range' | 'select' | 'multiselect' | 'boolean'
  field: string
  label: string
  options?: Array<{
    value: string | number
    label: string
    count?: number
  }>
  range?: {
    min: number
    max: number
    step: number
  }
}

export interface LaptopRecentSearch {
  id: string
  query: string
  filters: LaptopFilters
  timestamp: Date
  resultCount: number
}

export interface LaptopRecommendation {
  laptop: LaptopData
  score: number
  reasons: string[]
  category: 'budget' | 'performance' | 'portable' | 'gaming' | 'professional'
}

export interface LaptopAvailability {
  inStock: boolean
  stores: Array<{
    name: string
    url: string
    price: number
    currency: string
    availability: 'in-stock' | 'limited' | 'out-of-stock' | 'pre-order'
    shipping?: {
      free: boolean
      estimatedDays: number
    }
  }>
  lastChecked: Date
}

export interface LaptopPriceHistory {
  laptopId: string
  prices: Array<{
    price: number
    currency: string
    store: string
    date: Date
  }>
  currentPrice: number
  lowestPrice: number
  highestPrice: number
  priceChange: {
    amount: number
    percentage: number
    trend: 'up' | 'down' | 'stable'
  }
}

export interface LaptopReview {
  id: string
  laptopId: string
  rating: number
  title: string
  content: string
  author: string
  date: Date
  verified: boolean
  helpful: number
  categories: {
    performance: number
    design: number
    value: number
    battery: number
    display: number
  }
}

export interface LaptopSpecComparison {
  field: string
  label: string
  values: Array<{
    laptopId: string
    value: string | number
    formatted: string
    score?: number
  }>
  winner?: string // laptopId of the best value
  category: 'performance' | 'display' | 'storage' | 'connectivity' | 'design'
}

// Export all types
export type {
  LaptopData,
} from '@/shared/types'
