/**
 * Enhanced Laptop Comparison Hook
 * Comprehensive hook for managing laptop comparison state and operations
 */

'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import type { 
  LaptopData,
  ComparisonConfiguration,
  ComparisonHookOptions,
  ComparisonHookReturn,
  ComparisonError,
  ComparisonErrorCode,
  ComparisonAnalysis,
  ComparisonStatistics,
  ComparisonRecommendation,
  ComparisonInsight,
  ComparisonCategory,
  ExportFormat,
  ComparisonExport
} from '../types'

// Default configuration
const DEFAULT_CONFIG: ComparisonConfiguration = {
  maxLaptops: 4,
  persistToStorage: true,
  storageKey: 'laptop-comparison',
  autoRefresh: false,
  refreshInterval: 30000,
  includeSpecs: true,
  includeCompatibility: true,
  includePricing: true,
  includeReviews: true
}

// Storage utilities with error handling
const getStoredLaptopIds = (storageKey: string): string[] => {
  try {
    if (typeof window === 'undefined') return []
    const stored = localStorage.getItem(storageKey)
    return stored ? JSON.parse(stored) : []
  } catch (error) {
    console.warn('Failed to load comparison from storage:', error)
    return []
  }
}

const setStoredLaptopIds = (storageKey: string, laptopIds: string[]): void => {
  try {
    if (typeof window === 'undefined') return
    localStorage.setItem(storageKey, JSON.stringify(laptopIds))
  } catch (error) {
    console.warn('Failed to save comparison to storage:', error)
  }
}

// Error creation utility
const createComparisonError = (
  code: ComparisonErrorCode,
  message: string,
  details?: Record<string, unknown>
): ComparisonError => ({
  code,
  message,
  details,
  timestamp: new Date()
})

/**
 * Enhanced laptop comparison hook with comprehensive functionality
 */
export function useLaptopComparison(options: ComparisonHookOptions = {}): ComparisonHookReturn {
  const config = useMemo(() => ({ ...DEFAULT_CONFIG, ...options }), [options])
  const queryClient = useQueryClient()
  
  // State management
  const [comparedLaptopIds, setComparedLaptopIds] = useState<string[]>([])
  const [error, setError] = useState<ComparisonError | null>(null)
  const [configuration, setConfiguration] = useState<ComparisonConfiguration>(config)

  // Load from storage on mount
  useEffect(() => {
    if (configuration.persistToStorage) {
      const storedIds = getStoredLaptopIds(configuration.storageKey)
      setComparedLaptopIds(storedIds)
    }
  }, [configuration.persistToStorage, configuration.storageKey])

  // Persist to storage when laptops change
  useEffect(() => {
    if (configuration.persistToStorage) {
      setStoredLaptopIds(configuration.storageKey, comparedLaptopIds)
    }
  }, [comparedLaptopIds, configuration.persistToStorage, configuration.storageKey])

  // Fetch laptop data
  const { 
    data: comparedLaptops = [], 
    isLoading, 
    refetch 
  } = useQuery({
    queryKey: ['laptops-comparison', comparedLaptopIds],
    queryFn: async (): Promise<LaptopData[]> => {
      if (comparedLaptopIds.length === 0) return []
      
      try {
        const response = await fetch('/api/laptops/batch', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            laptopIds: comparedLaptopIds,
            includeSpecs: configuration.includeSpecs,
            includeCompatibility: configuration.includeCompatibility,
            includePricing: configuration.includePricing,
            includeReviews: configuration.includeReviews
          })
        })
        
        if (!response.ok) {
          throw new Error(`Failed to fetch laptops: ${response.statusText}`)
        }
        
        const data = await response.json()
        return data.laptops || []
      } catch (error) {
        const comparisonError = createComparisonError(
          'DATA_FETCH_FAILED',
          'Failed to fetch laptop data for comparison',
          { laptopIds: comparedLaptopIds, error: String(error) }
        )
        setError(comparisonError)
        throw comparisonError
      }
    },
    enabled: comparedLaptopIds.length > 0,
    staleTime: configuration.autoRefresh ? configuration.refreshInterval : Infinity,
    refetchInterval: configuration.autoRefresh ? configuration.refreshInterval : false
  })

  // Computed values
  const comparisonCount = comparedLaptopIds.length
  const canAddMore = comparisonCount < configuration.maxLaptops
  const hasComparison = comparisonCount > 0
  const maxLaptops = configuration.maxLaptops

  // Statistics calculation
  const comparisonStats = useMemo((): ComparisonStatistics | null => {
    if (comparedLaptops.length === 0) return null

    const prices = comparedLaptops
      .map(laptop => laptop.pricing?.currentPrice)
      .filter((price): price is number => typeof price === 'number' && price > 0)

    const compatibilityScores = comparedLaptops
      .map(laptop => laptop.compatibility?.averageScore)
      .filter((score): score is number => typeof score === 'number' && score > 0)

    const calculateRange = (values: number[], unit: string) => ({
      min: Math.min(...values),
      max: Math.max(...values),
      average: values.reduce((sum, val) => sum + val, 0) / values.length,
      median: values.sort((a, b) => a - b)[Math.floor(values.length / 2)],
      standardDeviation: Math.sqrt(
        values.reduce((sum, val) => sum + Math.pow(val - (values.reduce((s, v) => s + v, 0) / values.length), 2), 0) / values.length
      ),
      unit
    })

    return {
      priceRange: prices.length > 0 ? calculateRange(prices, 'USD') : { min: 0, max: 0, average: 0, median: 0, standardDeviation: 0, unit: 'USD' },
      compatibilityRange: compatibilityScores.length > 0 ? calculateRange(compatibilityScores, 'score') : { min: 0, max: 0, average: 0, median: 0, standardDeviation: 0, unit: 'score' },
      memoryRange: { min: 0, max: 0, average: 0, median: 0, standardDeviation: 0, unit: 'GB' },
      storageRange: { min: 0, max: 0, average: 0, median: 0, standardDeviation: 0, unit: 'GB' },
      weightRange: { min: 0, max: 0, average: 0, median: 0, standardDeviation: 0, unit: 'kg' },
      batteryRange: { min: 0, max: 0, average: 0, median: 0, standardDeviation: 0, unit: 'Wh' },
      displaySizeRange: { min: 0, max: 0, average: 0, median: 0, standardDeviation: 0, unit: 'inches' },
      brands: [...new Set(comparedLaptops.map(laptop => laptop.brand).filter(Boolean))],
      categories: [...new Set(comparedLaptops.map(laptop => laptop.category).filter(Boolean))],
      averageAge: 0,
      newestLaptop: new Date(),
      oldestLaptop: new Date()
    }
  }, [comparedLaptops])

  // Analysis calculation (placeholder - would be implemented with actual analysis logic)
  const comparisonAnalysis = useMemo((): ComparisonAnalysis | null => {
    if (comparedLaptops.length === 0) return null
    
    // This would be implemented with comprehensive analysis logic
    return null
  }, [comparedLaptops])

  // Actions
  const addLaptop = useCallback(async (laptop: LaptopData | string): Promise<void> => {
    try {
      const laptopId = typeof laptop === 'string' ? laptop : laptop.id
      
      if (!canAddMore) {
        throw createComparisonError(
          'MAX_LAPTOPS_EXCEEDED',
          `Cannot add more than ${configuration.maxLaptops} laptops to comparison`,
          { maxLaptops: configuration.maxLaptops, currentCount: comparisonCount }
        )
      }
      
      if (comparedLaptopIds.includes(laptopId)) {
        return // Already in comparison
      }
      
      setComparedLaptopIds(prev => [...prev, laptopId])
      setError(null)
    } catch (error) {
      const comparisonError = error instanceof Error && 'code' in error 
        ? error as ComparisonError
        : createComparisonError('UNKNOWN_ERROR', 'Failed to add laptop to comparison')
      setError(comparisonError)
      throw comparisonError
    }
  }, [canAddMore, configuration.maxLaptops, comparisonCount, comparedLaptopIds])

  const removeLaptop = useCallback((laptopId: string): void => {
    setComparedLaptopIds(prev => prev.filter(id => id !== laptopId))
    setError(null)
  }, [])

  const clearComparison = useCallback((): void => {
    setComparedLaptopIds([])
    setError(null)
  }, [])

  const toggleLaptop = useCallback(async (laptop: LaptopData | string): Promise<void> => {
    const laptopId = typeof laptop === 'string' ? laptop : laptop.id
    
    if (comparedLaptopIds.includes(laptopId)) {
      removeLaptop(laptopId)
    } else {
      await addLaptop(laptop)
    }
  }, [comparedLaptopIds, removeLaptop, addLaptop])

  const isInComparison = useCallback((laptopId: string): boolean => {
    return comparedLaptopIds.includes(laptopId)
  }, [comparedLaptopIds])

  const reorderLaptops = useCallback((laptopIds: string[]): void => {
    // Validate that all IDs are currently in comparison
    const validIds = laptopIds.filter(id => comparedLaptopIds.includes(id))
    if (validIds.length === comparedLaptopIds.length) {
      setComparedLaptopIds(validIds)
    }
  }, [comparedLaptopIds])

  // Analysis functions (placeholders)
  const getBestLaptop = useCallback((category?: ComparisonCategory): LaptopData | null => {
    // Implementation would analyze laptops based on category
    return comparedLaptops.length > 0 ? comparedLaptops[0] : null
  }, [comparedLaptops])

  const getRecommendations = useCallback((): ComparisonRecommendation[] => {
    // Implementation would generate recommendations
    return []
  }, [])

  const getInsights = useCallback((): ComparisonInsight[] => {
    // Implementation would generate insights
    return []
  }, [])

  // Export/Share functions (placeholders)
  const exportComparison = useCallback(async (format: ExportFormat): Promise<ComparisonExport> => {
    // Implementation would export comparison data
    throw createComparisonError('EXPORT_FAILED', 'Export functionality not implemented')
  }, [])

  const shareComparison = useCallback(async (): Promise<string> => {
    // Implementation would create shareable link
    throw createComparisonError('EXPORT_FAILED', 'Share functionality not implemented')
  }, [])

  // Configuration management
  const updateConfiguration = useCallback((newConfig: Partial<ComparisonConfiguration>): void => {
    setConfiguration(prev => ({ ...prev, ...newConfig }))
  }, [])

  const resetConfiguration = useCallback((): void => {
    setConfiguration(DEFAULT_CONFIG)
  }, [])

  return {
    // State
    comparedLaptops,
    comparedLaptopIds,
    isLoading,
    error,
    
    // Computed values
    comparisonCount,
    canAddMore,
    hasComparison,
    comparisonStats,
    comparisonAnalysis,
    
    // Actions
    addLaptop,
    removeLaptop,
    clearComparison,
    toggleLaptop,
    isInComparison,
    reorderLaptops,
    
    // Analysis
    getBestLaptop,
    getRecommendations,
    getInsights,
    
    // Export/Share
    exportComparison,
    shareComparison,
    
    // Configuration
    updateConfiguration,
    resetConfiguration,
    
    // Utilities
    refetch,
    maxLaptops
  }
}
