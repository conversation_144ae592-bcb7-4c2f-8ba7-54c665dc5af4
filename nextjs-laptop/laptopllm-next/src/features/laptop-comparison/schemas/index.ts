/**
 * Zod Validation Schemas for Laptop Comparison
 * Runtime validation for comparison-related data structures
 */

import { z } from 'zod'

// Base schemas
export const ComparisonCategorySchema = z.enum([
  'pricing',
  'performance', 
  'compatibility',
  'storage',
  'display',
  'battery',
  'design',
  'connectivity',
  'general'
])

export const ComparisonValueSchema = z.union([
  z.string(),
  z.number(),
  z.boolean(),
  z.null()
])

export const ExportFormatSchema = z.enum([
  'json',
  'csv',
  'pdf',
  'xlsx'
])

export const ComparisonErrorCodeSchema = z.enum([
  'MAX_LAPTOPS_EXCEEDED',
  'LAPTOP_NOT_FOUND',
  'DATA_FETCH_FAILED',
  'INVALID_CONFIGURATION',
  'EXPORT_FAILED',
  'UNKNOWN_ERROR'
])

// Metric schema
export const ComparisonMetricSchema = z.object({
  key: z.string(),
  label: z.string(),
  category: ComparisonCategorySchema,
  icon: z.any().optional(), // React node
  getValue: z.function().args(z.any()).returns(ComparisonValueSchema),
  format: z.function().args(z.unknown()).returns(z.string()).optional(),
  isNumeric: z.boolean(),
  higherIsBetter: z.boolean().optional(),
  unit: z.string().optional(),
  description: z.string().optional(),
  weight: z.number().min(0).max(1).optional()
})

// Configuration schema
export const ComparisonConfigurationSchema = z.object({
  maxLaptops: z.number().min(1).max(10).default(4),
  persistToStorage: z.boolean().default(true),
  storageKey: z.string().default('laptop-comparison'),
  autoRefresh: z.boolean().default(false),
  refreshInterval: z.number().min(1000).default(30000),
  includeSpecs: z.boolean().default(true),
  includeCompatibility: z.boolean().default(true),
  includePricing: z.boolean().default(true),
  includeReviews: z.boolean().default(false),
  includeAvailability: z.boolean().default(false)
})

// Hook options schema
export const ComparisonHookOptionsSchema = ComparisonConfigurationSchema.partial()

// Error schema
export const ComparisonErrorSchema = z.object({
  code: ComparisonErrorCodeSchema,
  message: z.string(),
  details: z.record(z.unknown()).optional(),
  timestamp: z.date()
})

// Statistics schemas
export const StatisticalRangeSchema = z.object({
  min: z.number(),
  max: z.number(),
  average: z.number(),
  median: z.number(),
  standardDeviation: z.number(),
  unit: z.string()
})

export const ComparisonStatisticsSchema = z.object({
  priceRange: StatisticalRangeSchema,
  compatibilityRange: StatisticalRangeSchema,
  memoryRange: StatisticalRangeSchema,
  storageRange: StatisticalRangeSchema,
  weightRange: StatisticalRangeSchema,
  batteryRange: StatisticalRangeSchema,
  displaySizeRange: StatisticalRangeSchema,
  brands: z.array(z.string()),
  categories: z.array(z.string()),
  averageAge: z.number(),
  newestLaptop: z.date(),
  oldestLaptop: z.date()
})

// Insight schema
export const ComparisonInsightSchema = z.object({
  type: z.enum(['highlight', 'warning', 'recommendation', 'trend']),
  title: z.string(),
  description: z.string(),
  laptopIds: z.array(z.string()),
  metrics: z.array(z.string()),
  severity: z.enum(['positive', 'neutral', 'warning', 'critical']),
  confidence: z.number().min(0).max(100),
  actionable: z.boolean(),
  icon: z.any().optional() // React node
})

// Recommendation schema
export const ComparisonRecommendationSchema = z.object({
  type: z.enum(['best-value', 'best-performance', 'best-portability', 'best-battery', 'budget-friendly']),
  title: z.string(),
  description: z.string(),
  laptopId: z.string(),
  score: z.number().min(0).max(100),
  reasoning: z.array(z.string()),
  pros: z.array(z.string()),
  cons: z.array(z.string()),
  bestFor: z.array(z.string()),
  alternatives: z.array(z.string())
})

// Analysis schema
export const ComparisonAnalysisSchema = z.object({
  insights: z.array(ComparisonInsightSchema),
  recommendations: z.array(ComparisonRecommendationSchema),
  summary: z.object({
    totalLaptops: z.number(),
    priceSpread: z.number(),
    performanceSpread: z.number(),
    topPerformer: z.string().optional(),
    bestValue: z.string().optional(),
    mostAffordable: z.string().optional()
  }),
  trends: z.array(z.object({
    metric: z.string(),
    direction: z.enum(['increasing', 'decreasing', 'stable']),
    strength: z.number().min(0).max(1),
    description: z.string()
  })),
  correlations: z.array(z.object({
    metric1: z.string(),
    metric2: z.string(),
    correlation: z.number().min(-1).max(1),
    significance: z.enum(['strong', 'moderate', 'weak'])
  }))
})

// Export schema
export const ComparisonExportSchema = z.object({
  format: ExportFormatSchema,
  data: z.any(), // Flexible data structure
  metadata: z.object({
    exportedAt: z.date(),
    laptopCount: z.number(),
    includeAnalytics: z.boolean(),
    includeRecommendations: z.boolean()
  }),
  url: z.string().url().optional(),
  filename: z.string().optional()
})

// Filter schema
export const ComparisonFilterSchema = z.object({
  categories: z.array(ComparisonCategorySchema).optional(),
  priceRange: z.object({
    min: z.number().min(0),
    max: z.number().min(0)
  }).optional(),
  compatibilityRange: z.object({
    min: z.number().min(0).max(100),
    max: z.number().min(0).max(100)
  }).optional(),
  brands: z.array(z.string()).optional(),
  features: z.array(z.string()).optional(),
  sortBy: z.enum(['price', 'compatibility', 'rating', 'name']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
})

// Operation schema
export const ComparisonOperationSchema = z.object({
  type: z.enum(['add', 'remove', 'reorder', 'clear', 'export', 'share']),
  laptopId: z.string().optional(),
  laptopIds: z.array(z.string()).optional(),
  data: z.any().optional(),
  timestamp: z.date(),
  userId: z.string().optional()
})

// Hook return schema
export const ComparisonHookReturnSchema = z.object({
  // State
  comparedLaptops: z.array(z.any()), // LaptopData array
  comparedLaptopIds: z.array(z.string()),
  isLoading: z.boolean(),
  error: ComparisonErrorSchema.nullable(),
  
  // Computed values
  comparisonCount: z.number(),
  canAddMore: z.boolean(),
  hasComparison: z.boolean(),
  comparisonStats: ComparisonStatisticsSchema.nullable(),
  comparisonAnalysis: ComparisonAnalysisSchema.nullable(),
  
  // Actions
  addLaptop: z.function().args(z.union([z.any(), z.string()])).returns(z.promise(z.void())),
  removeLaptop: z.function().args(z.string()).returns(z.void()),
  clearComparison: z.function().returns(z.void()),
  toggleLaptop: z.function().args(z.union([z.any(), z.string()])).returns(z.promise(z.void())),
  isInComparison: z.function().args(z.string()).returns(z.boolean()),
  reorderLaptops: z.function().args(z.array(z.string())).returns(z.void()),
  
  // Analysis
  getBestLaptop: z.function().args(ComparisonCategorySchema.optional()).returns(z.any().nullable()),
  getRecommendations: z.function().returns(z.array(ComparisonRecommendationSchema)),
  getInsights: z.function().returns(z.array(ComparisonInsightSchema)),
  
  // Export/Share
  exportComparison: z.function().args(ExportFormatSchema).returns(z.promise(ComparisonExportSchema)),
  shareComparison: z.function().returns(z.promise(z.string())),
  
  // Configuration
  updateConfiguration: z.function().args(ComparisonConfigurationSchema.partial()).returns(z.void()),
  resetConfiguration: z.function().returns(z.void()),
  
  // Utilities
  refetch: z.function().returns(z.any()),
  maxLaptops: z.number()
})

// Batch laptop request schema
export const BatchLaptopRequestSchema = z.object({
  laptopIds: z.array(z.string()).min(1).max(10),
  includeSpecs: z.boolean().default(true),
  includeCompatibility: z.boolean().default(true),
  includePricing: z.boolean().default(true),
  includeReviews: z.boolean().default(false),
  includeAvailability: z.boolean().default(false)
})

// Batch laptop response schema
export const BatchLaptopResponseSchema = z.object({
  laptops: z.array(z.any()), // LaptopData array
  found: z.number(),
  requested: z.number(),
  missing: z.array(z.string()),
  errors: z.array(z.string())
})

// Validation helper functions
export const validateComparisonConfiguration = (config: unknown) => {
  return ComparisonConfigurationSchema.safeParse(config)
}

export const validateComparisonError = (error: unknown) => {
  return ComparisonErrorSchema.safeParse(error)
}

export const validateComparisonStatistics = (stats: unknown) => {
  return ComparisonStatisticsSchema.safeParse(stats)
}

export const validateComparisonAnalysis = (analysis: unknown) => {
  return ComparisonAnalysisSchema.safeParse(analysis)
}

export const validateBatchLaptopRequest = (request: unknown) => {
  return BatchLaptopRequestSchema.safeParse(request)
}

export const validateBatchLaptopResponse = (response: unknown) => {
  return BatchLaptopResponseSchema.safeParse(response)
}

// Type exports for TypeScript
export type ComparisonCategory = z.infer<typeof ComparisonCategorySchema>
export type ComparisonValue = z.infer<typeof ComparisonValueSchema>
export type ExportFormat = z.infer<typeof ExportFormatSchema>
export type ComparisonErrorCode = z.infer<typeof ComparisonErrorCodeSchema>
export type ComparisonMetric = z.infer<typeof ComparisonMetricSchema>
export type ComparisonConfiguration = z.infer<typeof ComparisonConfigurationSchema>
export type ComparisonHookOptions = z.infer<typeof ComparisonHookOptionsSchema>
export type ComparisonError = z.infer<typeof ComparisonErrorSchema>
export type StatisticalRange = z.infer<typeof StatisticalRangeSchema>
export type ComparisonStatistics = z.infer<typeof ComparisonStatisticsSchema>
export type ComparisonInsight = z.infer<typeof ComparisonInsightSchema>
export type ComparisonRecommendation = z.infer<typeof ComparisonRecommendationSchema>
export type ComparisonAnalysis = z.infer<typeof ComparisonAnalysisSchema>
export type ComparisonExport = z.infer<typeof ComparisonExportSchema>
export type ComparisonFilter = z.infer<typeof ComparisonFilterSchema>
export type ComparisonOperation = z.infer<typeof ComparisonOperationSchema>
export type ComparisonHookReturn = z.infer<typeof ComparisonHookReturnSchema>
export type BatchLaptopRequest = z.infer<typeof BatchLaptopRequestSchema>
export type BatchLaptopResponse = z.infer<typeof BatchLaptopResponseSchema>
