/**
 * Laptop Comparison Tests
 * Unit tests for laptop comparison functionality with strict TypeScript
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { renderHook, act } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import { useLaptopComparison } from '../hooks/use-laptop-comparison'
import type { LaptopData, ComparisonHookOptions } from '../types'
import { 
  validateComparisonConfiguration,
  validateComparisonError,
  validateBatchLaptopRequest,
  validateBatchLaptopResponse
} from '../schemas'

// Mock data
const mockLaptop1: LaptopData = {
  id: 'laptop-1',
  brand: 'Apple',
  model: 'MacBook Pro 16"',
  category: 'professional',
  releaseDate: new Date('2023-01-01'),
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  pricing: {
    currentPrice: 2499,
    currency: 'USD',
    priceHistory: [],
    bestDeal: undefined
  },
  compatibility: {
    scores: [],
    averageScore: 85,
    bestModels: []
  },
  specifications: {
    cpu: {
      manufacturer: 'Apple',
      model: 'M2 Pro',
      cores: 12,
      threads: 12,
      baseClockGhz: 3.5,
      maxClockGhz: 4.0,
      architecture: 'ARM',
      tdpWatts: 30,
      integratedGraphics: true
    },
    gpu: {
      manufacturer: 'Apple',
      model: 'M2 Pro GPU',
      memoryGb: 16,
      memoryType: 'Unified',
      baseClock: 1000,
      boostClock: 1200,
      tdpWatts: 30,
      architecture: 'Apple Silicon'
    },
    memory: [{
      type: 'LPDDR5',
      capacityGb: 32,
      speed: 6400,
      slots: 1,
      maxCapacityGb: 32,
      isUpgradeable: false
    }],
    storage: [{
      type: 'SSD',
      capacityGb: 1024,
      interface: 'NVMe',
      readSpeedMbps: 7400,
      writeSpeedMbps: 6500
    }],
    display: {
      sizeInches: 16.2,
      resolution: '3456x2234',
      refreshRate: 120,
      panelType: 'Mini-LED',
      brightness: 1600,
      colorGamut: 'P3',
      touchscreen: false,
      hdr: true
    },
    connectivity: {
      wifi: 'Wi-Fi 6E',
      bluetooth: 'Bluetooth 5.3',
      usb: ['USB-C', 'Thunderbolt 4'],
      hdmi: 1,
      ethernet: false,
      audioJack: true,
      sdCard: true,
      thunderbolt: 3
    },
    physical: {
      weightKg: 2.15,
      dimensions: {
        widthMm: 355,
        heightMm: 16.8,
        depthMm: 248
      },
      material: 'Aluminum',
      color: 'Space Gray'
    },
    battery: {
      capacityWh: 100,
      estimatedLifeHours: 22,
      fastCharging: true,
      chargingWatts: 140
    },
    thermal: {
      coolingType: 'Active',
      fanCount: 2,
      heatPipes: 0,
      maxTempCelsius: 85
    }
  }
}

const mockLaptop2: LaptopData = {
  id: 'laptop-2',
  brand: 'Dell',
  model: 'XPS 15',
  category: 'professional',
  releaseDate: new Date('2023-02-01'),
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  pricing: {
    currentPrice: 1899,
    currency: 'USD',
    priceHistory: [],
    bestDeal: undefined
  },
  compatibility: {
    scores: [],
    averageScore: 78,
    bestModels: []
  },
  specifications: {
    cpu: {
      manufacturer: 'Intel',
      model: 'Core i7-13700H',
      cores: 14,
      threads: 20,
      baseClockGhz: 2.4,
      maxClockGhz: 5.0,
      architecture: 'x86_64',
      tdpWatts: 45,
      integratedGraphics: true
    },
    gpu: {
      manufacturer: 'NVIDIA',
      model: 'RTX 4060',
      memoryGb: 8,
      memoryType: 'GDDR6',
      baseClock: 1830,
      boostClock: 2370,
      tdpWatts: 115,
      architecture: 'Ada Lovelace'
    },
    memory: [{
      type: 'DDR5',
      capacityGb: 16,
      speed: 4800,
      slots: 2,
      maxCapacityGb: 64,
      isUpgradeable: true
    }],
    storage: [{
      type: 'SSD',
      capacityGb: 512,
      interface: 'NVMe',
      readSpeedMbps: 7000,
      writeSpeedMbps: 6000
    }],
    display: {
      sizeInches: 15.6,
      resolution: '3840x2400',
      refreshRate: 60,
      panelType: 'OLED',
      brightness: 400,
      colorGamut: 'DCI-P3',
      touchscreen: true,
      hdr: true
    },
    connectivity: {
      wifi: 'Wi-Fi 6E',
      bluetooth: 'Bluetooth 5.2',
      usb: ['USB-A', 'USB-C'],
      hdmi: 1,
      ethernet: false,
      audioJack: true,
      sdCard: true,
      thunderbolt: 2
    },
    physical: {
      weightKg: 1.96,
      dimensions: {
        widthMm: 344,
        heightMm: 18,
        depthMm: 230
      },
      material: 'Aluminum',
      color: 'Platinum Silver'
    },
    battery: {
      capacityWh: 86,
      estimatedLifeHours: 13,
      fastCharging: true,
      chargingWatts: 130
    },
    thermal: {
      coolingType: 'Active',
      fanCount: 2,
      heatPipes: 3,
      maxTempCelsius: 90
    }
  }
}

// Mock fetch
global.fetch = jest.fn()

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })
  
  return ({ children }: { children: React.ReactNode }) =>
    React.createElement(QueryClientProvider, { client: queryClient }, children)
}

describe('Laptop Comparison Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    localStorage.clear()
  })

  it('should initialize with empty state', () => {
    const { result } = renderHook(() => useLaptopComparison(), {
      wrapper: createWrapper(),
    })

    expect(result.current.comparedLaptops).toEqual([])
    expect(result.current.comparedLaptopIds).toEqual([])
    expect(result.current.comparisonCount).toBe(0)
    expect(result.current.canAddMore).toBe(true)
    expect(result.current.hasComparison).toBe(false)
  })

  it('should add laptop to comparison', async () => {
    const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        laptops: [mockLaptop1],
        found: 1,
        requested: 1,
        missing: [],
        errors: []
      })
    } as Response)

    const { result } = renderHook(() => useLaptopComparison(), {
      wrapper: createWrapper(),
    })

    await act(async () => {
      await result.current.addLaptop(mockLaptop1)
    })

    expect(result.current.comparedLaptopIds).toContain(mockLaptop1.id)
    expect(result.current.comparisonCount).toBe(1)
    expect(result.current.hasComparison).toBe(true)
  })

  it('should remove laptop from comparison', async () => {
    const { result } = renderHook(() => useLaptopComparison(), {
      wrapper: createWrapper(),
    })

    // First add a laptop
    await act(async () => {
      await result.current.addLaptop(mockLaptop1.id)
    })

    // Then remove it
    act(() => {
      result.current.removeLaptop(mockLaptop1.id)
    })

    expect(result.current.comparedLaptopIds).not.toContain(mockLaptop1.id)
    expect(result.current.comparisonCount).toBe(0)
    expect(result.current.hasComparison).toBe(false)
  })

  it('should respect max laptops limit', async () => {
    const maxLaptops = 2
    const { result } = renderHook(() => useLaptopComparison({ maxLaptops }), {
      wrapper: createWrapper(),
    })

    // Add first laptop
    await act(async () => {
      await result.current.addLaptop(mockLaptop1.id)
    })

    // Add second laptop
    await act(async () => {
      await result.current.addLaptop(mockLaptop2.id)
    })

    expect(result.current.comparisonCount).toBe(2)
    expect(result.current.canAddMore).toBe(false)

    // Try to add third laptop - should throw error
    await expect(async () => {
      await act(async () => {
        await result.current.addLaptop('laptop-3')
      })
    }).rejects.toThrow()
  })

  it('should clear all laptops from comparison', async () => {
    const { result } = renderHook(() => useLaptopComparison(), {
      wrapper: createWrapper(),
    })

    // Add laptops
    await act(async () => {
      await result.current.addLaptop(mockLaptop1.id)
      await result.current.addLaptop(mockLaptop2.id)
    })

    expect(result.current.comparisonCount).toBe(2)

    // Clear comparison
    act(() => {
      result.current.clearComparison()
    })

    expect(result.current.comparedLaptopIds).toEqual([])
    expect(result.current.comparisonCount).toBe(0)
    expect(result.current.hasComparison).toBe(false)
  })
})

describe('Zod Schema Validation', () => {
  it('should validate comparison configuration', () => {
    const validConfig = {
      maxLaptops: 4,
      persistToStorage: true,
      storageKey: 'test-comparison',
      autoRefresh: false,
      refreshInterval: 30000
    }

    const result = validateComparisonConfiguration(validConfig)
    expect(result.success).toBe(true)
  })

  it('should reject invalid comparison configuration', () => {
    const invalidConfig = {
      maxLaptops: -1, // Invalid: negative number
      persistToStorage: 'yes', // Invalid: should be boolean
      refreshInterval: 500 // Invalid: too low
    }

    const result = validateComparisonConfiguration(invalidConfig)
    expect(result.success).toBe(false)
  })

  it('should validate batch laptop request', () => {
    const validRequest = {
      laptopIds: ['laptop-1', 'laptop-2'],
      includeSpecs: true,
      includeCompatibility: true,
      includePricing: true
    }

    const result = validateBatchLaptopRequest(validRequest)
    expect(result.success).toBe(true)
  })

  it('should reject invalid batch laptop request', () => {
    const invalidRequest = {
      laptopIds: [], // Invalid: empty array
      includeSpecs: 'yes' // Invalid: should be boolean
    }

    const result = validateBatchLaptopRequest(invalidRequest)
    expect(result.success).toBe(false)
  })

  it('should validate batch laptop response', () => {
    const validResponse = {
      laptops: [mockLaptop1, mockLaptop2],
      found: 2,
      requested: 2,
      missing: [],
      errors: []
    }

    const result = validateBatchLaptopResponse(validResponse)
    expect(result.success).toBe(true)
  })
})
