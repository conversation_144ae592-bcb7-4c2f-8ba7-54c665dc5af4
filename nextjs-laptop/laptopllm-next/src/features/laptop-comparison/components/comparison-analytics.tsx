/**
 * Enhanced Comparison Analytics Component
 * Comprehensive analytics and insights for laptop comparisons
 */

'use client'

import React, { useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  TrendingUp, 
  TrendingDown, 
  Award, 
  AlertTriangle, 
  Info,
  BarChart3,
  PieChart,
  Target,
  Lightbulb,
  Star,
  DollarSign,
  Zap,
  Cpu,
  MemoryStick,
  HardDrive,
  Battery,
  Weight
} from 'lucide-react'
import type { 
  LaptopData,
  ComparisonAnalysis,
  ComparisonStatistics,
  ComparisonRecommendation,
  ComparisonInsight,
  ComparisonCategory
} from '../types'

interface ComparisonAnalyticsProps {
  laptops: LaptopData[]
  analysis?: ComparisonAnalysis | null
  statistics?: ComparisonStatistics | null
  className?: string
}

interface MetricSummary {
  category: ComparisonCategory
  icon: React.ReactNode
  label: string
  winner: LaptopData | null
  winnerValue: string
  spread: number // How much variation there is (0-100)
  significance: 'high' | 'medium' | 'low'
}

export function ComparisonAnalytics({ 
  laptops, 
  analysis, 
  statistics, 
  className 
}: ComparisonAnalyticsProps) {
  
  // Calculate metric summaries
  const metricSummaries = useMemo((): MetricSummary[] => {
    if (laptops.length === 0) return []

    const summaries: MetricSummary[] = []

    // Price analysis
    const prices = laptops
      .map(laptop => laptop.pricing?.currentPrice)
      .filter((price): price is number => typeof price === 'number' && price > 0)
    
    if (prices.length > 0) {
      const minPrice = Math.min(...prices)
      const maxPrice = Math.max(...prices)
      const priceSpread = prices.length > 1 ? ((maxPrice - minPrice) / maxPrice) * 100 : 0
      const cheapestLaptop = laptops.find(laptop => laptop.pricing?.currentPrice === minPrice)
      
      summaries.push({
        category: 'pricing',
        icon: <DollarSign className="h-4 w-4" />,
        label: 'Best Price',
        winner: cheapestLaptop || null,
        winnerValue: `$${minPrice.toLocaleString()}`,
        spread: priceSpread,
        significance: priceSpread > 30 ? 'high' : priceSpread > 15 ? 'medium' : 'low'
      })
    }

    // Compatibility analysis
    const compatibilityScores = laptops
      .map(laptop => laptop.compatibility?.averageScore)
      .filter((score): score is number => typeof score === 'number' && score > 0)
    
    if (compatibilityScores.length > 0) {
      const maxScore = Math.max(...compatibilityScores)
      const minScore = Math.min(...compatibilityScores)
      const scoreSpread = compatibilityScores.length > 1 ? ((maxScore - minScore) / 100) * 100 : 0
      const bestLaptop = laptops.find(laptop => laptop.compatibility?.averageScore === maxScore)
      
      summaries.push({
        category: 'compatibility',
        icon: <Zap className="h-4 w-4" />,
        label: 'Best LLM Compatibility',
        winner: bestLaptop || null,
        winnerValue: `${Math.round(maxScore)}/100`,
        spread: scoreSpread,
        significance: scoreSpread > 20 ? 'high' : scoreSpread > 10 ? 'medium' : 'low'
      })
    }

    // Memory analysis
    const memoryCapacities = laptops
      .map(laptop => {
        const memory = laptop.specifications?.memory
        return Array.isArray(memory) ? memory.reduce((total, mem) => total + (mem.capacityGb || 0), 0) : 0
      })
      .filter(capacity => capacity > 0)
    
    if (memoryCapacities.length > 0) {
      const maxMemory = Math.max(...memoryCapacities)
      const minMemory = Math.min(...memoryCapacities)
      const memorySpread = memoryCapacities.length > 1 ? ((maxMemory - minMemory) / maxMemory) * 100 : 0
      const bestMemoryLaptop = laptops.find(laptop => {
        const memory = laptop.specifications?.memory
        const totalMemory = Array.isArray(memory) ? memory.reduce((total, mem) => total + (mem.capacityGb || 0), 0) : 0
        return totalMemory === maxMemory
      })
      
      summaries.push({
        category: 'performance',
        icon: <MemoryStick className="h-4 w-4" />,
        label: 'Most Memory',
        winner: bestMemoryLaptop || null,
        winnerValue: `${maxMemory}GB`,
        spread: memorySpread,
        significance: memorySpread > 50 ? 'high' : memorySpread > 25 ? 'medium' : 'low'
      })
    }

    return summaries
  }, [laptops])

  // Generate insights
  const insights = useMemo((): ComparisonInsight[] => {
    if (laptops.length === 0) return []

    const generatedInsights: ComparisonInsight[] = []

    // Price insights
    const prices = laptops
      .map(laptop => laptop.pricing?.currentPrice)
      .filter((price): price is number => typeof price === 'number' && price > 0)
    
    if (prices.length > 1) {
      const minPrice = Math.min(...prices)
      const maxPrice = Math.max(...prices)
      const priceRange = maxPrice - minPrice
      
      if (priceRange > minPrice * 0.5) { // More than 50% price difference
        generatedInsights.push({
          type: 'warning',
          title: 'Significant Price Variation',
          description: `There's a ${Math.round((priceRange / minPrice) * 100)}% price difference between the cheapest and most expensive options.`,
          laptopIds: laptops.map(laptop => laptop.id),
          metrics: ['price'],
          severity: 'warning',
          confidence: 90,
          actionable: true,
          icon: <AlertTriangle className="h-4 w-4" />
        })
      }
    }

    // Compatibility insights
    const compatibilityScores = laptops
      .map(laptop => ({ 
        id: laptop.id, 
        score: laptop.compatibility?.averageScore || 0 
      }))
      .filter(item => item.score > 0)
    
    if (compatibilityScores.length > 0) {
      const avgScore = compatibilityScores.reduce((sum, item) => sum + item.score, 0) / compatibilityScores.length
      const topPerformers = compatibilityScores.filter(item => item.score > avgScore + 10)
      
      if (topPerformers.length > 0) {
        generatedInsights.push({
          type: 'highlight',
          title: 'LLM Compatibility Leaders',
          description: `${topPerformers.length} laptop(s) significantly outperform others in LLM compatibility.`,
          laptopIds: topPerformers.map(item => item.id),
          metrics: ['compatibility'],
          severity: 'positive',
          confidence: 85,
          actionable: true,
          icon: <Star className="h-4 w-4" />
        })
      }
    }

    return generatedInsights
  }, [laptops])

  // Generate recommendations
  const recommendations = useMemo((): ComparisonRecommendation[] => {
    if (laptops.length === 0) return []

    const recs: ComparisonRecommendation[] = []

    // Best value recommendation
    const laptopsWithPriceAndScore = laptops.filter(laptop => 
      laptop.pricing?.currentPrice && laptop.compatibility?.averageScore
    )

    if (laptopsWithPriceAndScore.length > 0) {
      const bestValue = laptopsWithPriceAndScore.reduce((best, current) => {
        const bestRatio = (best.compatibility?.averageScore || 0) / (best.pricing?.currentPrice || 1)
        const currentRatio = (current.compatibility?.averageScore || 0) / (current.pricing?.currentPrice || 1)
        return currentRatio > bestRatio ? current : best
      })

      recs.push({
        type: 'best-value',
        title: 'Best Value for Money',
        description: 'Offers the best balance of LLM compatibility and price.',
        laptopId: bestValue.id,
        score: 85,
        reasoning: [
          'Highest compatibility-to-price ratio',
          'Good balance of features and cost',
          'Suitable for most LLM workloads'
        ],
        pros: [
          'Excellent value proposition',
          'Good LLM performance',
          'Reasonable price point'
        ],
        cons: [
          'May not be the absolute best in any single category'
        ],
        bestFor: [
          'Budget-conscious users',
          'General LLM development',
          'Learning and experimentation'
        ],
        alternatives: laptopsWithPriceAndScore
          .filter(laptop => laptop.id !== bestValue.id)
          .slice(0, 2)
          .map(laptop => laptop.id)
      })
    }

    // Performance leader recommendation
    const performanceLeader = laptops.reduce((best, current) => {
      const bestScore = best.compatibility?.averageScore || 0
      const currentScore = current.compatibility?.averageScore || 0
      return currentScore > bestScore ? current : best
    })

    if (performanceLeader.compatibility?.averageScore && performanceLeader.compatibility.averageScore > 70) {
      recs.push({
        type: 'best-performance',
        title: 'Performance Champion',
        description: 'Top choice for demanding LLM workloads and professional use.',
        laptopId: performanceLeader.id,
        score: 95,
        reasoning: [
          'Highest LLM compatibility score',
          'Superior hardware specifications',
          'Optimized for AI workloads'
        ],
        pros: [
          'Maximum performance',
          'Future-proof specifications',
          'Professional-grade capabilities'
        ],
        cons: [
          'Higher price point',
          'May be overkill for basic use'
        ],
        bestFor: [
          'Professional AI development',
          'Large model training',
          'Production environments'
        ],
        alternatives: []
      })
    }

    return recs
  }, [laptops])

  if (laptops.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Comparison Analytics
          </CardTitle>
          <CardDescription>
            Add laptops to see detailed comparison analytics and insights
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Metric Summaries */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Performance Overview
          </CardTitle>
          <CardDescription>
            Key metrics comparison across {laptops.length} laptops
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {metricSummaries.map((summary, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {summary.icon}
                    <span className="text-sm font-medium">{summary.label}</span>
                  </div>
                  <Badge variant={
                    summary.significance === 'high' ? 'destructive' : 
                    summary.significance === 'medium' ? 'default' : 'secondary'
                  }>
                    {summary.significance}
                  </Badge>
                </div>
                {summary.winner && (
                  <div className="text-sm">
                    <div className="font-medium">{summary.winner.brand} {summary.winner.model}</div>
                    <div className="text-muted-foreground">{summary.winnerValue}</div>
                  </div>
                )}
                <Progress value={100 - summary.spread} className="h-2" />
                <div className="text-xs text-muted-foreground">
                  {Math.round(summary.spread)}% variation across options
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Insights */}
      {insights.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5" />
              Key Insights
            </CardTitle>
            <CardDescription>
              Important observations from your comparison
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {insights.map((insight, index) => (
              <Alert key={index} className={
                insight.severity === 'positive' ? 'border-green-200 bg-green-50' :
                insight.severity === 'warning' ? 'border-yellow-200 bg-yellow-50' :
                insight.severity === 'critical' ? 'border-red-200 bg-red-50' :
                'border-blue-200 bg-blue-50'
              }>
                <div className="flex items-start gap-3">
                  {insight.icon}
                  <div className="flex-1">
                    <div className="font-medium">{insight.title}</div>
                    <AlertDescription className="mt-1">
                      {insight.description}
                    </AlertDescription>
                    <div className="mt-2 flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {insight.confidence}% confidence
                      </Badge>
                      {insight.actionable && (
                        <Badge variant="secondary" className="text-xs">
                          Actionable
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </Alert>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Recommendations
            </CardTitle>
            <CardDescription>
              Personalized suggestions based on your comparison
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {recommendations.map((rec, index) => {
              const laptop = laptops.find(l => l.id === rec.laptopId)
              if (!laptop) return null

              return (
                <div key={index} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <div className="font-medium text-lg">{rec.title}</div>
                      <div className="text-sm text-muted-foreground">{rec.description}</div>
                      <div className="mt-1 font-medium">
                        {laptop.brand} {laptop.model}
                      </div>
                    </div>
                    <Badge className="ml-2">
                      {rec.score}/100
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="font-medium text-green-600 mb-1">Pros</div>
                      <ul className="space-y-1">
                        {rec.pros.map((pro, i) => (
                          <li key={i} className="flex items-start gap-1">
                            <span className="text-green-600 mt-0.5">•</span>
                            {pro}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <div className="font-medium text-red-600 mb-1">Cons</div>
                      <ul className="space-y-1">
                        {rec.cons.map((con, i) => (
                          <li key={i} className="flex items-start gap-1">
                            <span className="text-red-600 mt-0.5">•</span>
                            {con}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <div>
                    <div className="font-medium mb-1">Best for:</div>
                    <div className="flex flex-wrap gap-1">
                      {rec.bestFor.map((use, i) => (
                        <Badge key={i} variant="outline" className="text-xs">
                          {use}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              )
            })}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
