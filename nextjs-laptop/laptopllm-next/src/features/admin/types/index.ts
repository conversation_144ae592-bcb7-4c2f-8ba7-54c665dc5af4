/**
 * Admin feature specific types
 */

export interface AdminUser {
  id: string
  email: string
  name: string
  role: 'super-admin' | 'admin' | 'moderator' | 'viewer'
  permissions: AdminPermission[]
  lastLogin?: Date
  createdAt: Date
  updatedAt: Date
  isActive: boolean
}

export interface AdminPermission {
  id: string
  name: string
  description: string
  resource: string
  actions: string[]
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical' | 'down'
  services: {
    database: ServiceStatus
    scraping: ServiceStatus
    api: ServiceStatus
    cache: ServiceStatus
    storage: ServiceStatus
  }
  metrics: {
    uptime: number
    responseTime: number
    errorRate: number
    throughput: number
  }
  lastChecked: Date
}

export interface ServiceStatus {
  status: 'healthy' | 'warning' | 'critical' | 'down'
  responseTime?: number
  errorRate?: number
  lastError?: string
  lastChecked: Date
  metrics?: Record<string, number>
}

export interface SystemMetrics {
  period: 'hour' | 'day' | 'week' | 'month'
  timestamp: Date
  performance: {
    apiRequests: number
    averageResponseTime: number
    errorRate: number
    throughput: number
  }
  resources: {
    cpuUsage: number
    memoryUsage: number
    diskUsage: number
    networkIO: number
  }
  business: {
    totalLaptops: number
    newLaptops: number
    scrapingJobs: number
    userSessions: number
    searchQueries: number
  }
  errors: {
    total: number
    byType: Record<string, number>
    critical: number
  }
}

export interface AdminDashboard {
  overview: {
    totalLaptops: number
    totalModels: number
    activeScrapingJobs: number
    systemHealth: 'healthy' | 'warning' | 'critical'
  }
  recentActivity: AdminActivity[]
  alerts: AdminAlert[]
  metrics: {
    dailyStats: Array<{
      date: string
      laptops: number
      searches: number
      comparisons: number
    }>
    performance: {
      avgResponseTime: number
      errorRate: number
      uptime: number
    }
  }
  quickActions: AdminQuickAction[]
}

export interface AdminActivity {
  id: string
  type: 'user-action' | 'system-event' | 'data-update' | 'error'
  title: string
  description: string
  user?: string
  timestamp: Date
  metadata?: Record<string, any>
  severity: 'info' | 'warning' | 'error' | 'success'
}

export interface AdminAlert {
  id: string
  type: 'system' | 'security' | 'performance' | 'data'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  message: string
  source: string
  timestamp: Date
  acknowledged: boolean
  resolvedAt?: Date
  actions?: AdminAlertAction[]
}

export interface AdminAlertAction {
  id: string
  label: string
  type: 'button' | 'link'
  action: string
  variant?: 'default' | 'destructive' | 'outline'
}

export interface AdminQuickAction {
  id: string
  label: string
  description: string
  icon: string
  action: string
  permissions?: string[]
  category: 'data' | 'system' | 'users' | 'monitoring'
}

export interface DataManagement {
  laptops: {
    total: number
    lastUpdated: Date
    sources: Array<{
      name: string
      count: number
      lastScrape: Date
      status: 'active' | 'inactive' | 'error'
    }>
    quality: {
      complete: number
      incomplete: number
      duplicates: number
      errors: number
    }
  }
  models: {
    total: number
    categories: Record<string, number>
    lastUpdated: Date
  }
  compatibility: {
    totalAnalyses: number
    averageScore: number
    lastCalculated: Date
  }
}

export interface UserManagement {
  users: AdminUser[]
  roles: AdminRole[]
  permissions: AdminPermission[]
  sessions: UserSession[]
  analytics: {
    totalUsers: number
    activeUsers: number
    newUsers: number
    userGrowth: Array<{
      date: string
      newUsers: number
      activeUsers: number
    }>
  }
}

export interface AdminRole {
  id: string
  name: string
  description: string
  permissions: string[]
  userCount: number
  isSystem: boolean
  createdAt: Date
  updatedAt: Date
}

export interface UserSession {
  id: string
  userId: string
  userEmail: string
  ipAddress: string
  userAgent: string
  startTime: Date
  lastActivity: Date
  duration: number
  pages: number
  isActive: boolean
}

export interface SystemConfiguration {
  scraping: {
    enabled: boolean
    maxConcurrentJobs: number
    defaultTimeout: number
    retryAttempts: number
    rateLimit: {
      requests: number
      period: number
    }
    targets: string[]
  }
  api: {
    rateLimit: {
      requests: number
      window: number
    }
    cors: {
      origins: string[]
      methods: string[]
    }
    cache: {
      ttl: number
      maxSize: number
    }
  }
  features: {
    registration: boolean
    guestAccess: boolean
    analytics: boolean
    monitoring: boolean
  }
  notifications: {
    email: boolean
    slack: boolean
    webhook: boolean
    alertThresholds: {
      errorRate: number
      responseTime: number
      diskUsage: number
    }
  }
}

export interface AdminLog {
  id: string
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal'
  message: string
  source: string
  timestamp: Date
  metadata?: Record<string, any>
  userId?: string
  sessionId?: string
  requestId?: string
}

export interface AdminBackup {
  id: string
  type: 'full' | 'incremental' | 'configuration'
  status: 'pending' | 'running' | 'completed' | 'failed'
  size: number
  location: string
  createdAt: Date
  completedAt?: Date
  error?: string
  metadata: {
    tables: string[]
    recordCount: number
    compression: string
  }
}

export interface AdminMaintenance {
  id: string
  type: 'scheduled' | 'emergency'
  title: string
  description: string
  status: 'planned' | 'active' | 'completed' | 'cancelled'
  startTime: Date
  endTime: Date
  estimatedDuration: number
  actualDuration?: number
  affectedServices: string[]
  notificationSent: boolean
  createdBy: string
}

export interface AdminReport {
  id: string
  type: 'system' | 'performance' | 'usage' | 'security' | 'data-quality'
  title: string
  description: string
  period: {
    start: Date
    end: Date
  }
  data: Record<string, any>
  generatedAt: Date
  generatedBy: string
  format: 'json' | 'csv' | 'pdf'
  size: number
  downloadUrl?: string
}

export interface AdminAuditLog {
  id: string
  action: string
  resource: string
  resourceId?: string
  userId: string
  userEmail: string
  timestamp: Date
  ipAddress: string
  userAgent: string
  changes?: {
    before: Record<string, any>
    after: Record<string, any>
  }
  metadata?: Record<string, any>
}

// Export shared types
export type {
  LaptopData,
  LLMModel,
  CompatibilityScore,
} from '@/shared/types'
