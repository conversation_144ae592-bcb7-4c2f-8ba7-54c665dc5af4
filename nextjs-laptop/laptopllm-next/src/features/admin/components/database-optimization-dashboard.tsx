'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { 
  Database, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  RefreshCw,
  BarChart3,
  Settings,
  Clock
} from 'lucide-react'

interface DatabaseOptimizationStats {
  indexUsage: IndexUsageStats[]
  queryPerformance: QueryPerformanceStats[]
  tableStats: TableStats[]
  optimizationRecommendations: OptimizationRecommendation[]
  summary: {
    totalIndexes: number
    activeIndexes: number
    unusedIndexes: number
    totalTables: number
    highPriorityRecommendations: number
    averageQueryPerformance: number
  }
}

interface IndexUsageStats {
  schemaname: string
  tablename: string
  indexname: string
  idx_scan: number
  idx_tup_read: number
  idx_tup_fetch: number
  usage_ratio: number
}

interface QueryPerformanceStats {
  query: string
  calls: number
  total_time: number
  mean_time: number
  rows: number
  performance_score: number
}

interface TableStats {
  tablename: string
  row_count: number
  table_size: string
  index_size: string
  total_size: string
  seq_scan: number
  seq_tup_read: number
  idx_scan: number
  idx_tup_fetch: number
}

interface OptimizationRecommendation {
  type: 'index' | 'query' | 'maintenance'
  priority: 'high' | 'medium' | 'low'
  table: string
  description: string
  action: string
  estimated_improvement: string
}

export function DatabaseOptimizationDashboard() {
  const [stats, setStats] = useState<DatabaseOptimizationStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [applying, setApplying] = useState(false)

  const fetchStats = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/database/optimization')
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch optimization stats')
      }
      
      setStats(result.data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const applyOptimizations = async (action: string) => {
    try {
      setApplying(true)
      
      const response = await fetch('/api/database/optimization', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action })
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to apply optimizations')
      }
      
      // Refresh stats after applying optimizations
      await fetchStats()
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setApplying(false)
    }
  }

  useEffect(() => {
    fetchStats()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading optimization statistics...</span>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (!stats) {
    return (
      <Alert>
        <Database className="h-4 w-4" />
        <AlertTitle>No Data</AlertTitle>
        <AlertDescription>No optimization statistics available</AlertDescription>
      </Alert>
    )
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'destructive'
      case 'medium': return 'default'
      case 'low': return 'secondary'
      default: return 'default'
    }
  }

  const getPerformanceColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Database Optimization</h1>
          <p className="text-muted-foreground">
            Monitor and optimize database performance
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={fetchStats}
            disabled={loading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button 
            onClick={() => applyOptimizations('updateStatistics')}
            disabled={applying}
          >
            <Settings className="h-4 w-4 mr-2" />
            Update Statistics
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Indexes</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.summary.totalIndexes}</div>
            <p className="text-xs text-muted-foreground">
              {stats.summary.activeIndexes} active, {stats.summary.unusedIndexes} unused
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Query Performance</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getPerformanceColor(stats.summary.averageQueryPerformance)}`}>
              {stats.summary.averageQueryPerformance}%
            </div>
            <p className="text-xs text-muted-foreground">
              Average performance score
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tables Monitored</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.summary.totalTables}</div>
            <p className="text-xs text-muted-foreground">
              Database tables tracked
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recommendations</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.optimizationRecommendations.length}</div>
            <p className="text-xs text-muted-foreground">
              {stats.summary.highPriorityRecommendations} high priority
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Statistics */}
      <Tabs defaultValue="indexes" className="space-y-4">
        <TabsList>
          <TabsTrigger value="indexes">Index Usage</TabsTrigger>
          <TabsTrigger value="tables">Table Statistics</TabsTrigger>
          <TabsTrigger value="queries">Query Performance</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="indexes" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Index Usage Statistics</CardTitle>
              <CardDescription>
                Monitor how frequently database indexes are being used
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.indexUsage.slice(0, 10).map((index, i) => (
                  <div key={i} className="flex items-center justify-between p-3 border rounded">
                    <div className="flex-1">
                      <div className="font-medium">{index.indexname}</div>
                      <div className="text-sm text-muted-foreground">
                        Table: {index.tablename}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{index.idx_scan.toLocaleString()} scans</div>
                      <div className="text-sm text-muted-foreground">
                        {index.usage_ratio.toFixed(1)}% efficiency
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tables" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Table Statistics</CardTitle>
              <CardDescription>
                Overview of table sizes and access patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.tableStats.slice(0, 10).map((table, i) => (
                  <div key={i} className="flex items-center justify-between p-3 border rounded">
                    <div className="flex-1">
                      <div className="font-medium">{table.tablename}</div>
                      <div className="text-sm text-muted-foreground">
                        {table.row_count.toLocaleString()} rows
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{table.total_size}</div>
                      <div className="text-sm text-muted-foreground">
                        {table.idx_scan.toLocaleString()} index scans
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="queries" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Query Performance</CardTitle>
              <CardDescription>
                Monitor slow queries and performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.queryPerformance.slice(0, 10).map((query, i) => (
                  <div key={i} className="p-3 border rounded">
                    <div className="flex items-center justify-between mb-2">
                      <div className="font-medium">Query #{i + 1}</div>
                      <Badge variant={query.performance_score >= 80 ? 'default' : query.performance_score >= 60 ? 'secondary' : 'destructive'}>
                        {query.performance_score}% performance
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground mb-2">
                      {query.query}...
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>{query.calls} calls</span>
                      <span>{query.mean_time.toFixed(2)}ms avg</span>
                      <span>{query.rows} rows</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Optimization Recommendations</CardTitle>
              <CardDescription>
                Suggested improvements for database performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.optimizationRecommendations.map((rec, i) => (
                  <div key={i} className="p-4 border rounded">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Badge variant={getPriorityColor(rec.priority)}>
                          {rec.priority}
                        </Badge>
                        <span className="font-medium">{rec.type}</span>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        Table: {rec.table}
                      </span>
                    </div>
                    <div className="mb-2">
                      <div className="font-medium">{rec.description}</div>
                    </div>
                    <div className="text-sm text-muted-foreground mb-2">
                      Action: {rec.action}
                    </div>
                    <div className="text-sm text-green-600">
                      Expected: {rec.estimated_improvement}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
