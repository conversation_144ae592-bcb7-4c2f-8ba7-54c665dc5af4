import type { ScrapedLaptopData } from '@/shared/types'

// Enhanced types for scraping operations
export interface EnhancedScrapingJob {
  id: string
  type: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused'
  priority: 'low' | 'normal' | 'high' | 'critical'
  progress: number
  batchId?: string
  sourceId?: string
  createdAt: string
  startedAt?: string
  completedAt?: string
  attempts: number
  maxAttempts: number
  error?: string
  results?: ScrapedLaptopData[]
  config?: any
}

export interface QueueStats {
  name: string
  waiting: number
  active: number
  completed: number
  failed: number
  delayed: number
  paused: boolean
}

export interface JobStats {
  total: number
  byStatus: Record<string, number>
  successRate: number
  averageProcessingTime: number
  throughput: {
    perHour: number
    perDay: number
  }
}

export interface DashboardData {
  queueStats: QueueStats[]
  jobStats: JobStats
  summary: {
    totalQueues: number
    totalActiveJobs: number
    totalWaitingJobs: number
    totalCompletedJobs: number
    totalFailedJobs: number
    overallSuccessRate: number
    averageProcessingTime: number
    throughputPerHour: number
    healthStatus: 'healthy' | 'warning' | 'critical'
  }
  timestamp: string
}

export interface ScrapingJobRequest {
  sourceId?: string
  urls?: string[]
  priority?: 'low' | 'normal' | 'high' | 'critical'
  config?: {
    batchSize?: number
    maxConcurrent?: number
    delayBetweenBatches?: number
    enableScreenshots?: boolean
    saveToDatabase?: boolean
    timeout?: number
    retryAttempts?: number
  }
  delay?: number
}

export interface BatchJobRequest {
  sourceIds?: string[]
  urlGroups?: string[][]
  priority?: 'low' | 'normal' | 'high' | 'critical'
  config?: {
    batchSize?: number
    maxConcurrent?: number
    delayBetweenBatches?: number
    enableScreenshots?: boolean
    saveToDatabase?: boolean
    timeout?: number
    retryAttempts?: number
  }
  staggerDelay?: number
}

export interface DiscoveredUrls {
  source: string
  urls: string[]
  discoveredAt: Date
}

// Query keys
export const scrapingKeys = {
  all: ['scraping'] as const,
  jobs: () => [...scrapingKeys.all, 'jobs'] as const,
  job: (id: string) => [...scrapingKeys.jobs(), id] as const,
  discovery: () => [...scrapingKeys.all, 'discovery'] as const,
  discoveredUrls: (source: string) => [...scrapingKeys.discovery(), source] as const,
  stats: () => [...scrapingKeys.all, 'stats'] as const,
  dashboard: () => [...scrapingKeys.all, 'dashboard'] as const,
  queueStats: () => [...scrapingKeys.all, 'queue-stats'] as const,
  jobStats: () => [...scrapingKeys.all, 'job-stats'] as const,
}

// Real API functions for enhanced scraping
const enhancedScrapingApi = {
  async getDashboardData(): Promise<DashboardData> {
    const response = await fetch('/api/enhanced-scraping/stats?includeQueueStats=true&includeJobStats=true')
    if (!response.ok) {
      throw new Error('Failed to fetch dashboard data')
    }
    const result = await response.json()
    if (!result.success) {
      throw new Error(result.error || 'Failed to fetch dashboard data')
    }
    return result.data
  },

  async getJobs(params?: {
    page?: number
    limit?: number
    status?: string
    type?: string
    batchId?: string
    priority?: string
  }): Promise<{
    jobs: EnhancedScrapingJob[]
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
      hasMore: boolean
    }
  }> {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.set('page', params.page.toString())
    if (params?.limit) searchParams.set('limit', params.limit.toString())
    if (params?.status) searchParams.set('status', params.status)
    if (params?.type) searchParams.set('type', params.type)
    if (params?.batchId) searchParams.set('batchId', params.batchId)
    if (params?.priority) searchParams.set('priority', params.priority)

    const response = await fetch(`/api/enhanced-scraping?${searchParams}`)
    if (!response.ok) {
      throw new Error('Failed to fetch jobs')
    }
    const result = await response.json()
    if (!result.success) {
      throw new Error(result.error || 'Failed to fetch jobs')
    }
    return {
      jobs: result.data.jobs,
      pagination: result.data.pagination
    }
  },

  async getJob(id: string): Promise<EnhancedScrapingJob | null> {
    const response = await fetch(`/api/enhanced-scraping/${id}`)
    if (!response.ok) {
      if (response.status === 404) return null
      throw new Error('Failed to fetch job')
    }
    const result = await response.json()
    if (!result.success) {
      throw new Error(result.error || 'Failed to fetch job')
    }
    return result.data
  },

  async startScrapingJob(request: ScrapingJobRequest): Promise<{ jobId: string }> {
    const response = await fetch('/api/enhanced-scraping', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    })
    if (!response.ok) {
      throw new Error('Failed to start scraping job')
    }
    const result = await response.json()
    if (!result.success) {
      throw new Error(result.error || 'Failed to start scraping job')
    }
    return { jobId: result.data.jobId }
  },

  async startBatchJobs(request: BatchJobRequest): Promise<{ jobIds: string[] }> {
    const response = await fetch('/api/enhanced-scraping', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    })
    if (!response.ok) {
      throw new Error('Failed to start batch jobs')
    }
    const result = await response.json()
    if (!result.success) {
      throw new Error(result.error || 'Failed to start batch jobs')
    }
    return { jobIds: result.data.jobIds }
  },

  async cancelJob(jobId: string): Promise<boolean> {
    const response = await fetch(`/api/enhanced-scraping/${jobId}`, {
      method: 'DELETE',
    })
    if (!response.ok) {
      throw new Error('Failed to cancel job')
    }
    const result = await response.json()
    return result.success
  },

  async retryJob(jobId: string): Promise<boolean> {
    const response = await fetch(`/api/enhanced-scraping/${jobId}/retry`, {
      method: 'POST',
    })
    if (!response.ok) {
      throw new Error('Failed to retry job')
    }
    const result = await response.json()
    return result.success
  },
}

// Mock API functions (fallback)
const mockScrapingApi = {
  async getJobs(): Promise<ScrapingJob[]> {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return [
      {
        id: 'job_1',
        status: 'completed',
        progress: 100,
        totalUrls: 50,
        processedUrls: 50,
        successfulUrls: 45,
        failedUrls: 5,
        startedAt: new Date(Date.now() - 3600000), // 1 hour ago
        completedAt: new Date(Date.now() - 3000000), // 50 minutes ago
      },
      {
        id: 'job_2',
        status: 'running',
        progress: 65,
        totalUrls: 30,
        processedUrls: 19,
        successfulUrls: 17,
        failedUrls: 2,
        startedAt: new Date(Date.now() - 600000), // 10 minutes ago
      },
    ]
  },

  async getJob(id: string): Promise<ScrapingJob | null> {
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const jobs = await this.getJobs()
    return jobs.find(job => job.id === id) || null
  },

  async startScrapingJob(request: ScrapingJobRequest): Promise<{ jobId: string }> {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    return { jobId }
  },

  async cancelJob(jobId: string): Promise<boolean> {
    await new Promise(resolve => setTimeout(resolve, 500))
    return true
  },

  async discoverUrls(sources: string[]): Promise<DiscoveredUrls[]> {
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    return sources.map(source => ({
      source,
      urls: [
        `${source}/laptop-1`,
        `${source}/laptop-2`,
        `${source}/laptop-3`,
      ],
      discoveredAt: new Date(),
    }))
  },

  async getScrapingStats(): Promise<{
    totalJobs: number
    activeJobs: number
    completedJobs: number
    failedJobs: number
    totalLaptopsScraped: number
    successRate: number
    averageJobDuration: number
  }> {
    await new Promise(resolve => setTimeout(resolve, 400))
    
    return {
      totalJobs: 25,
      activeJobs: 2,
      completedJobs: 20,
      failedJobs: 3,
      totalLaptopsScraped: 1250,
      successRate: 0.89,
      averageJobDuration: 1800, // seconds
    }
  },
}

// Enhanced Custom hooks
export function useEnhancedScrapingDashboard() {
  return useQuery({
    queryKey: scrapingKeys.dashboard(),
    queryFn: () => enhancedScrapingApi.getDashboardData(),
    refetchInterval: 5000, // Refetch every 5 seconds for real-time updates
    staleTime: 2000, // Consider data stale after 2 seconds
  })
}

export function useEnhancedScrapingJobs(params?: {
  page?: number
  limit?: number
  status?: string
  type?: string
  batchId?: string
  priority?: string
}) {
  return useQuery({
    queryKey: [...scrapingKeys.jobs(), params],
    queryFn: () => enhancedScrapingApi.getJobs(params),
    refetchInterval: 3000, // Refetch every 3 seconds for real-time updates
    staleTime: 1000, // Consider data stale after 1 second
  })
}

export function useEnhancedScrapingJob(jobId: string) {
  return useQuery({
    queryKey: scrapingKeys.job(jobId),
    queryFn: () => enhancedScrapingApi.getJob(jobId),
    enabled: !!jobId,
    refetchInterval: (data) => {
      // Only refetch if job is still running
      return data?.status === 'running' || data?.status === 'pending' ? 2000 : false
    },
    staleTime: 1000,
  })
}

// Legacy hooks (using mock API for backward compatibility)
export function useScrapingJobs() {
  return useQuery({
    queryKey: scrapingKeys.jobs(),
    queryFn: () => mockScrapingApi.getJobs(),
    refetchInterval: 5000, // Refetch every 5 seconds for real-time updates
    staleTime: 1000, // Consider data stale after 1 second
  })
}

export function useScrapingJob(jobId: string) {
  return useQuery({
    queryKey: scrapingKeys.job(jobId),
    queryFn: () => mockScrapingApi.getJob(jobId),
    enabled: !!jobId,
    refetchInterval: (data) => {
      // Only refetch if job is still running
      return data?.status === 'running' || data?.status === 'pending' ? 2000 : false
    },
    staleTime: 1000,
  })
}

export function useDiscoveredUrls(source: string) {
  return useQuery({
    queryKey: scrapingKeys.discoveredUrls(source),
    queryFn: () => mockScrapingApi.discoverUrls([source]),
    enabled: !!source,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  })
}

export function useScrapingStats() {
  return useQuery({
    queryKey: scrapingKeys.stats(),
    queryFn: () => mockScrapingApi.getScrapingStats(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 30000, // Refetch every 30 seconds
  })
}

// Mutation hooks
// Enhanced Mutation hooks
export function useStartEnhancedScrapingJob() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (request: ScrapingJobRequest) => enhancedScrapingApi.startScrapingJob(request),
    onSuccess: () => {
      // Invalidate jobs list to show the new job
      queryClient.invalidateQueries({ queryKey: scrapingKeys.jobs() })
      queryClient.invalidateQueries({ queryKey: scrapingKeys.dashboard() })
      queryClient.invalidateQueries({ queryKey: scrapingKeys.stats() })
    },
  })
}

export function useStartBatchScrapingJobs() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (request: BatchJobRequest) => enhancedScrapingApi.startBatchJobs(request),
    onSuccess: () => {
      // Invalidate jobs list to show the new jobs
      queryClient.invalidateQueries({ queryKey: scrapingKeys.jobs() })
      queryClient.invalidateQueries({ queryKey: scrapingKeys.dashboard() })
      queryClient.invalidateQueries({ queryKey: scrapingKeys.stats() })
    },
  })
}

export function useCancelEnhancedScrapingJob() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (jobId: string) => enhancedScrapingApi.cancelJob(jobId),
    onSuccess: () => {
      // Invalidate jobs list to reflect the cancellation
      queryClient.invalidateQueries({ queryKey: scrapingKeys.jobs() })
      queryClient.invalidateQueries({ queryKey: scrapingKeys.dashboard() })
      queryClient.invalidateQueries({ queryKey: scrapingKeys.stats() })
    },
  })
}

export function useRetryEnhancedScrapingJob() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (jobId: string) => enhancedScrapingApi.retryJob(jobId),
    onSuccess: () => {
      // Invalidate jobs list to reflect the retry
      queryClient.invalidateQueries({ queryKey: scrapingKeys.jobs() })
      queryClient.invalidateQueries({ queryKey: scrapingKeys.dashboard() })
      queryClient.invalidateQueries({ queryKey: scrapingKeys.stats() })
    },
  })
}

// Legacy hooks
export function useStartScrapingJob() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (request: ScrapingJobRequest) => mockScrapingApi.startScrapingJob(request),
    onSuccess: () => {
      // Invalidate jobs list to show the new job
      queryClient.invalidateQueries({ queryKey: scrapingKeys.jobs() })
      queryClient.invalidateQueries({ queryKey: scrapingKeys.stats() })
    },
  })
}

export function useCancelScrapingJob() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (jobId: string) => mockScrapingApi.cancelJob(jobId),
    onSuccess: (_, jobId) => {
      // Update the specific job cache
      queryClient.setQueryData(
        scrapingKeys.job(jobId),
        (oldData: ScrapingJob | undefined) => {
          if (oldData) {
            return {
              ...oldData,
              status: 'cancelled' as const,
              completedAt: new Date(),
            }
          }
          return oldData
        }
      )
      
      // Invalidate jobs list
      queryClient.invalidateQueries({ queryKey: scrapingKeys.jobs() })
      queryClient.invalidateQueries({ queryKey: scrapingKeys.stats() })
    },
  })
}

export function useDiscoverUrls() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (sources: string[]) => mockScrapingApi.discoverUrls(sources),
    onSuccess: (data, sources) => {
      // Cache the discovered URLs for each source
      data.forEach((result) => {
        queryClient.setQueryData(
          scrapingKeys.discoveredUrls(result.source),
          [result]
        )
      })
    },
  })
}

// Utility hooks
export function usePrefetchScrapingJob(jobId: string) {
  const queryClient = useQueryClient()
  
  return () => {
    queryClient.prefetchQuery({
      queryKey: scrapingKeys.job(jobId),
      queryFn: () => mockScrapingApi.getJob(jobId),
      staleTime: 1000,
    })
  }
}

export function useInvalidateScrapingData() {
  const queryClient = useQueryClient()
  
  return () => {
    queryClient.invalidateQueries({ queryKey: scrapingKeys.all })
  }
}

// Real-time job monitoring hook
export function useJobMonitoring(jobId: string) {
  const { data: job, isLoading, error } = useScrapingJob(jobId)
  
  const isActive = job?.status === 'running' || job?.status === 'pending'
  const isCompleted = job?.status === 'completed'
  const isFailed = job?.status === 'failed'
  const isCancelled = job?.status === 'cancelled'
  
  const progressPercentage = job?.progress || 0
  const estimatedTimeRemaining = job && isActive ? 
    calculateEstimatedTime(job) : null
  
  return {
    job,
    isLoading,
    error,
    isActive,
    isCompleted,
    isFailed,
    isCancelled,
    progressPercentage,
    estimatedTimeRemaining,
  }
}

// Helper function to calculate estimated time remaining
function calculateEstimatedTime(job: ScrapingJob): number | null {
  if (!job.startedAt || job.processedUrls === 0) return null
  
  const elapsedTime = Date.now() - job.startedAt.getTime()
  const avgTimePerUrl = elapsedTime / job.processedUrls
  const remainingUrls = job.totalUrls - job.processedUrls
  
  return Math.round((avgTimePerUrl * remainingUrls) / 1000) // Return in seconds
}

// Batch operations hook
export function useBatchScrapingOperations() {
  const startJob = useStartScrapingJob()
  const cancelJob = useCancelScrapingJob()
  const discoverUrls = useDiscoverUrls()
  
  const startMultipleJobs = async (requests: ScrapingJobRequest[]) => {
    const results = await Promise.allSettled(
      requests.map(request => startJob.mutateAsync(request))
    )
    
    return results.map((result, index) => ({
      request: requests[index],
      success: result.status === 'fulfilled',
      jobId: result.status === 'fulfilled' ? result.value.jobId : null,
      error: result.status === 'rejected' ? result.reason : null,
    }))
  }
  
  const cancelMultipleJobs = async (jobIds: string[]) => {
    const results = await Promise.allSettled(
      jobIds.map(jobId => cancelJob.mutateAsync(jobId))
    )
    
    return results.map((result, index) => ({
      jobId: jobIds[index],
      success: result.status === 'fulfilled',
      error: result.status === 'rejected' ? result.reason : null,
    }))
  }
  
  return {
    startMultipleJobs,
    cancelMultipleJobs,
    isLoading: startJob.isPending || cancelJob.isPending || discoverUrls.isPending,
  }
}
