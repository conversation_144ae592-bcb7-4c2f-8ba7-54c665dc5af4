/**
 * LLM Compatibility Page
 * Main page for exploring laptop-LLM compatibility
 */

import { Metadata } from 'next'
import { CompatibilityDashboard } from '@/features/llm-compatibility/components/compatibility-dashboard'

export const metadata: Metadata = {
  title: 'LLM Compatibility | LaptopLLM Finder',
  description: 'Discover the best laptop-LLM combinations for running Large Language Models locally',
  keywords: [
    'LLM compatibility',
    'laptop LLM',
    'local AI',
    'machine learning hardware',
    'AI laptop recommendations',
    'LLM performance',
    'laptop specifications',
    'AI hardware requirements'
  ],
}

export default function CompatibilityPage() {
  return (
    <div className="container mx-auto py-6">
      <CompatibilityDashboard />
    </div>
  )
}
