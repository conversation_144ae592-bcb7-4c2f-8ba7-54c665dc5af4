// Search Page - Advanced laptop search with filters and results

'use client'

import React, { useState, useEffect, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { LaptopCard } from '@/features/laptops/components/laptop-card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Search,
  Filter,
  SlidersHorizontal,
  X,
  AlertCircle,
  Laptop,
  RefreshCw,
  Loader2
} from 'lucide-react'
import {
  useLaptopSearch,
  useLaptopSearchSuggestions,
  usePopularSearches,
  type LaptopFilters,
  type LaptopSearchParams,
  type LaptopSearchResult,
  type Laptop
} from '@/features/laptops'
import {
  useCompatibilityScore,
  type CompatibilityAnalysis
} from '@/features/llm-compatibility'



function SearchPageContent() {
  const searchParams = useSearchParams()
  const [searchQuery, setSearchQuery] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState<LaptopFilters>({
    priceRange: { min: 0, max: 5000 },
    brands: [],
    minRam: 8,
    minStorage: 256,
    cpuTypes: [],
    gpuTypes: [],
    screenSizes: [],
    features: [],
    availability: 'all'
  })

  // Feature-based hooks
  const searchParams_obj: LaptopSearchParams = {
    query: searchQuery,
    filters,
    sortBy: 'relevance',
    sortOrder: 'desc',
    page: 1,
    limit: 20
  }

  const {
    data: searchResult,
    isLoading,
    error,
    refetch
  } = useLaptopSearch(searchParams_obj)

  const { data: suggestions = [] } = useLaptopSearchSuggestions(searchQuery)
  const { data: popularSearches = [] } = usePopularSearches()

  // Extract laptops from search result
  const laptops = searchResult?.laptops || []
  const totalCount = searchResult?.totalCount || 0
  const hasNextPage = searchResult?.hasNextPage || false

  // Initialize search query from URL params
  useEffect(() => {
    const query = searchParams.get('q')
    if (query) {
      setSearchQuery(query)
    }
  }, [searchParams])

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      // Search is handled automatically by the useLaptopSearch hook
    }
  }

  const handleFiltersChange = (newFilters: LaptopFilters) => {
    setFilters(newFilters)
  }

  const handleApplyFilters = () => {
    // Filters are applied automatically by the useLaptopSearch hook
    setShowFilters(false)
  }

  const handleRefresh = () => {
    refetch()
  }

  const clearFilters = () => {
    setFilters({
      priceRange: { min: 0, max: 5000 },
      brands: [],
      minRam: 8,
      minStorage: 256,
      cpuTypes: [],
      gpuTypes: [],
      screenSizes: [],
      features: [],
      availability: 'all'
    })
  }

  const hasActiveFilters =
    filters.brands?.length > 0 ||
    filters.cpuTypes?.length > 0 ||
    filters.gpuTypes?.length > 0 ||
    filters.priceRange?.min > 0 ||
    filters.priceRange?.max < 5000 ||
    filters.minRam > 8 ||
    filters.minStorage > 256

  return (
    <MainLayout>
      <div className="min-h-screen bg-background">
        {/* Search Header */}
        <section className="border-b bg-muted/30">
          <div className="container mx-auto px-4 py-8">
            <div className="space-y-6">
              <div>
                <h1 className="text-3xl font-bold mb-2">Search Laptops</h1>
                <p className="text-muted-foreground">
                  Find the perfect laptop for your LLM applications with advanced filtering
                </p>
              </div>

              {/* Search Bar */}
              <div className="flex gap-2">
                <div className="relative flex-1 max-w-2xl">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search laptops, brands, or specifications..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="pl-10 h-12"
                  />
                </div>
                <Button onClick={handleRefresh} size="lg" className="h-12">
                  Search
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => setShowFilters(!showFilters)}
                  className="h-12"
                >
                  <SlidersHorizontal className="mr-2 h-4 w-4" />
                  Filters
                  {hasActiveFilters && (
                    <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                      !
                    </Badge>
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={handleRefresh}
                  disabled={isLoading}
                  className="h-12"
                >
                  {isLoading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCw className="mr-2 h-4 w-4" />
                  )}
                  Refresh
                </Button>
              </div>

              {/* Active Filters */}
              {hasActiveFilters && (
                <div className="flex flex-wrap items-center gap-2">
                  <span className="text-sm text-muted-foreground">Active filters:</span>
                  {filters.brands?.map(brand => (
                    <Badge key={brand} variant="secondary" className="gap-1">
                      {brand}
                      <X className="h-3 w-3 cursor-pointer" />
                    </Badge>
                  ))}
                  {filters.cpuTypes?.map(cpu => (
                    <Badge key={cpu} variant="secondary" className="gap-1">
                      {cpu}
                      <X className="h-3 w-3 cursor-pointer" />
                    </Badge>
                  ))}
                  {filters.gpuTypes?.map(gpu => (
                    <Badge key={gpu} variant="secondary" className="gap-1">
                      {gpu}
                      <X className="h-3 w-3 cursor-pointer" />
                    </Badge>
                  ))}
                  <Button variant="ghost" size="sm" onClick={clearFilters}>
                    Clear all
                  </Button>
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-8">
          <div className="flex gap-8">
            {/* Filters Sidebar */}
            {showFilters && (
              <div className="w-80 flex-shrink-0">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>Filters</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowFilters(false)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <p className="text-sm text-muted-foreground">
                        Filter laptops by your preferences
                      </p>
                      <Button onClick={handleApplyFilters} className="w-full">
                        Apply Filters
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Results */}
            <div className="flex-1">
              {/* Results Header */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl font-semibold">
                    {isLoading ? 'Searching...' : `${totalCount} laptops found`}
                  </h2>
                  {searchQuery && (
                    <p className="text-muted-foreground">
                      Results for "{searchQuery}"
                    </p>
                  )}
                </div>
              </div>

              {/* Loading State */}
              {isLoading && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="bg-muted rounded-lg h-64"></div>
                    </div>
                  ))}
                </div>
              )}

              {/* Error State */}
              {error && (
                <Card className="text-center py-12 border-destructive">
                  <CardContent>
                    <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Error loading laptops</h3>
                    <p className="text-muted-foreground mb-4">
                      There was an error loading laptop data. Please try again.
                    </p>
                    <Button onClick={handleRefresh} disabled={isLoading}>
                      {isLoading ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <RefreshCw className="mr-2 h-4 w-4" />
                      )}
                      Try Again
                    </Button>
                  </CardContent>
                </Card>
              )}

              {/* Results */}
              {!isLoading && !error && laptops.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {laptops.map((laptop) => (
                    <LaptopCard
                      key={laptop.id}
                      laptop={laptop}
                      variant="default"
                      showCompatibility={true}
                    />
                  ))}
                </div>
              )}

              {/* No Results */}
              {!isLoading && !error && laptops.length === 0 && (
                <Card className="text-center py-12">
                  <CardContent>
                    <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No laptops found</h3>
                    <p className="text-muted-foreground mb-4">
                      Try adjusting your search terms or filters to find more results.
                    </p>
                    <div className="flex gap-2 justify-center">
                      <Button variant="outline" onClick={clearFilters}>
                        Clear Filters
                      </Button>
                      <Button onClick={() => setSearchQuery('')}>
                        Clear Search
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-muted rounded-lg h-64"></div>
              </div>
            ))}
          </div>
        </div>
      </MainLayout>
    }>
      <SearchPageContent />
    </Suspense>
  )
}
