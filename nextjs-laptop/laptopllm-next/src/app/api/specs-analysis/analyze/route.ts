/**
 * API Route: Specification Analysis
 * POST /api/specs-analysis/analyze
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { SpecsAnalysisService } from '@/features/specs-analysis/services/specs-analysis.service'
import { createAnalysisError, ANALYSIS_ERROR_CODES } from '@/features/specs-analysis'

// Request validation schema
const AnalyzeSpecsSchema = z.object({
  laptopId: z.string().min(1, 'Laptop ID is required'),
  rawText: z.string().min(10, 'Raw text must be at least 10 characters'),
  structuredData: z.record(z.unknown()).optional(),
  config: z.object({
    extractionMethods: z.object({
      regex: z.boolean().default(true),
      nlp: z.boolean().default(false),
      structured: z.boolean().default(true),
      hybrid: z.boolean().default(true)
    }).optional(),
    confidenceThresholds: z.object({
      minimum: z.number().min(0).max(100).default(30),
      warning: z.number().min(0).max(100).default(60),
      good: z.number().min(0).max(100).default(80)
    }).optional(),
    performance: z.object({
      timeout: z.number().min(5000).max(300000).default(30000),
      maxRetries: z.number().min(0).max(10).default(3),
      cacheResults: z.boolean().default(true)
    }).optional(),
    validation: z.object({
      strictMode: z.boolean().default(false),
      requireMinimumSpecs: z.boolean().default(true),
      validateBenchmarks: z.boolean().default(false)
    }).optional()
  }).optional()
})

export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json()
    const validatedData = AnalyzeSpecsSchema.parse(body)

    // Initialize analysis service
    const specsAnalysisService = new SpecsAnalysisService()

    // Perform specification analysis
    const result = await specsAnalysisService.analyzeSpecifications(
      validatedData.laptopId,
      validatedData.rawText,
      validatedData.structuredData,
      validatedData.config
    )

    // Return successful response
    return NextResponse.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    }, { status: 200 })

  } catch (error) {
    console.error('Specification analysis error:', error)

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: ANALYSIS_ERROR_CODES.INVALID_INPUT,
          message: 'Invalid request data',
          details: error.errors
        }
      }, { status: 400 })
    }

    // Handle analysis-specific errors
    if (error instanceof Error && 'code' in error) {
      const analysisError = error as { code: string; message: string; details: unknown }
      return NextResponse.json({
        success: false,
        error: {
          code: analysisError.code || ANALYSIS_ERROR_CODES.EXTRACTION_FAILED,
          message: analysisError.message,
          details: analysisError.details
        }
      }, { status: 500 })
    }

    // Handle generic errors
    return NextResponse.json({
      success: false,
      error: {
        code: ANALYSIS_ERROR_CODES.EXTRACTION_FAILED,
        message: 'Specification analysis failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    }, { status: 500 })
  }
}

// GET endpoint for retrieving analysis configuration
export async function GET() {
  try {
    const specsAnalysisService = new SpecsAnalysisService()
    
    return NextResponse.json({
      success: true,
      data: {
        defaultConfig: specsAnalysisService.getDefaultConfig(),
        supportedMethods: ['regex', 'nlp', 'structured', 'hybrid'],
        supportedComponents: [
          'cpu',
          'memory', 
          'storage',
          'gpu',
          'display',
          'connectivity',
          'physical',
          'battery',
          'thermal'
        ],
        version: '1.0.0'
      }
    }, { status: 200 })

  } catch (error) {
    console.error('Failed to get analysis configuration:', error)
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'CONFIG_ERROR',
        message: 'Failed to retrieve analysis configuration'
      }
    }, { status: 500 })
  }
}
