/**
 * API Route: Batch Specification Analysis
 * POST /api/specs-analysis/batch
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { SpecsAnalysisService } from '@/features/specs-analysis/services/specs-analysis.service'
import { createAnalysisError, ANALYSIS_ERROR_CODES } from '@/features/specs-analysis'

// Request validation schema
const BatchAnalyzeSchema = z.object({
  analyses: z.array(z.object({
    laptopId: z.string().min(1, 'Laptop ID is required'),
    rawText: z.string().min(10, 'Raw text must be at least 10 characters'),
    structuredData: z.record(z.unknown()).optional()
  })).min(1, 'At least one analysis is required').max(50, 'Maximum 50 analyses per batch'),
  config: z.object({
    extractionMethods: z.object({
      regex: z.boolean().default(true),
      nlp: z.boolean().default(false),
      structured: z.boolean().default(true),
      hybrid: z.boolean().default(true)
    }).optional(),
    confidenceThresholds: z.object({
      minimum: z.number().min(0).max(100).default(30),
      warning: z.number().min(0).max(100).default(60),
      good: z.number().min(0).max(100).default(80)
    }).optional(),
    performance: z.object({
      timeout: z.number().min(5000).max(300000).default(30000),
      maxRetries: z.number().min(0).max(10).default(3),
      cacheResults: z.boolean().default(true)
    }).optional(),
    validation: z.object({
      strictMode: z.boolean().default(false),
      requireMinimumSpecs: z.boolean().default(true),
      validateBenchmarks: z.boolean().default(false)
    }).optional()
  }).optional(),
  options: z.object({
    continueOnError: z.boolean().default(true),
    maxConcurrency: z.number().min(1).max(10).default(3),
    progressCallback: z.boolean().default(false)
  }).optional()
})

export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json()
    const validatedData = BatchAnalyzeSchema.parse(body)

    // Initialize analysis service
    const specsAnalysisService = new SpecsAnalysisService()

    // Track batch progress
    const results: Array<{
      laptopId: string
      success: boolean
      data?: Record<string, unknown>
      error?: Record<string, unknown>
    }> = []

    const batchConfig = validatedData.config
    const options = validatedData.options || {}

    // Process analyses with controlled concurrency
    const maxConcurrency = options.maxConcurrency || 3
    const chunks = []
    
    for (let i = 0; i < validatedData.analyses.length; i += maxConcurrency) {
      chunks.push(validatedData.analyses.slice(i, i + maxConcurrency))
    }

    let processedCount = 0
    const totalCount = validatedData.analyses.length

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (analysis) => {
        try {
          const result = await specsAnalysisService.analyzeSpecifications(
            analysis.laptopId,
            analysis.rawText,
            analysis.structuredData,
            batchConfig
          )

          processedCount++
          
          return {
            laptopId: analysis.laptopId,
            success: true,
            data: result
          }

        } catch (error) {
          processedCount++
          
          if (!options.continueOnError) {
            throw error
          }

          return {
            laptopId: analysis.laptopId,
            success: false,
            error: {
              code: error instanceof Error && 'code' in error 
                ? (error as { code: string }).code 
                : ANALYSIS_ERROR_CODES.EXTRACTION_FAILED,
              message: error instanceof Error ? error.message : 'Analysis failed',
              details: error instanceof Error && 'details' in error 
                ? (error as { details: unknown }).details 
                : undefined
            }
          }
        }
      })

      const chunkResults = await Promise.all(chunkPromises)
      results.push(...chunkResults)
    }

    // Calculate batch statistics
    const successCount = results.filter(r => r.success).length
    const errorCount = results.filter(r => !r.success).length
    const successRate = (successCount / totalCount) * 100

    // Return batch results
    return NextResponse.json({
      success: true,
      data: {
        results,
        statistics: {
          total: totalCount,
          successful: successCount,
          failed: errorCount,
          successRate: Math.round(successRate * 100) / 100
        },
        processingTime: Date.now(),
        batchId: `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      },
      timestamp: new Date().toISOString()
    }, { status: 200 })

  } catch (error) {
    console.error('Batch specification analysis error:', error)

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: ANALYSIS_ERROR_CODES.INVALID_INPUT,
          message: 'Invalid batch request data',
          details: error.errors
        }
      }, { status: 400 })
    }

    // Handle analysis-specific errors
    if (error instanceof Error && 'code' in error) {
      const analysisError = error as { code: string; message: string; details?: unknown }
      return NextResponse.json({
        success: false,
        error: {
          code: analysisError.code || ANALYSIS_ERROR_CODES.EXTRACTION_FAILED,
          message: analysisError.message,
          details: analysisError.details
        }
      }, { status: 500 })
    }

    // Handle generic errors
    return NextResponse.json({
      success: false,
      error: {
        code: ANALYSIS_ERROR_CODES.EXTRACTION_FAILED,
        message: 'Batch specification analysis failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    }, { status: 500 })
  }
}

// GET endpoint for batch analysis status/limits
export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      data: {
        limits: {
          maxBatchSize: 50,
          maxConcurrency: 10,
          timeoutPerAnalysis: 30000,
          maxRetries: 3
        },
        supportedOptions: {
          continueOnError: 'Continue processing even if individual analyses fail',
          maxConcurrency: 'Maximum number of concurrent analyses (1-10)',
          progressCallback: 'Enable progress tracking (future feature)'
        },
        estimatedProcessingTime: {
          perAnalysis: '2-5 seconds',
          batch10: '10-25 seconds',
          batch50: '50-125 seconds'
        }
      }
    }, { status: 200 })

  } catch (error) {
    console.error('Failed to get batch analysis info:', error)
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'CONFIG_ERROR',
        message: 'Failed to retrieve batch analysis information'
      }
    }, { status: 500 })
  }
}
