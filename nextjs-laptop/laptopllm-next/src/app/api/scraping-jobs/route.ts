import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { PrismaClient, Prisma } from '../../../../generated/prisma'

const prisma = new PrismaClient()

// Schema for creating a new scraping job
const CreateJobSchema = z.object({
  sourceId: z.string().min(1, 'Source ID is required'),
})

// Schema for listing jobs
const ListJobsSchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? Math.min(parseInt(val), 100) : 20),
  status: z.enum(['running', 'completed', 'failed']).optional(),
  sourceId: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = CreateJobSchema.parse(body)

    // Verify that the source exists and is active
    const source = await prisma.scrapingSource.findFirst({
      where: {
        id: validatedData.sourceId,
        isActive: true,
      },
    })

    if (!source) {
      return NextResponse.json(
        { error: 'Source not found or inactive' },
        { status: 404 }
      )
    }

    // For now, we'll simulate creating a job by returning the source info
    // In a real implementation, you would start the actual scraping process here
    const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    console.log(`🚀 Starting scraping job for source: ${source.name}`)
    console.log(`📋 Job ID: ${jobId}`)
    console.log(`🔗 URL: ${source.url}`)

    return NextResponse.json({
      success: true,
      data: {
        id: jobId,
        sourceId: validatedData.sourceId,
        status: 'running',
        itemsProcessed: 0,
        itemsTotal: null,
        progress: 0,
        createdAt: new Date().toISOString(),
        source: {
          id: source.id,
          name: source.name,
          url: source.url,
        },
      },
    })

  } catch (error) {
    console.error('Error creating scraping job:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = Object.fromEntries(searchParams.entries())
    const validatedQuery = ListJobsSchema.parse(query)

    const { page, limit, sourceId } = validatedQuery

    // For now, return active sources as potential jobs
    const where: Prisma.ScrapingSourceWhereInput = { isActive: true }
    if (sourceId) where.id = sourceId

    const sources = await prisma.scrapingSource.findMany({
      where,
      select: {
        id: true,
        name: true,
        url: true,
        lastScrapedAt: true,
        createdAt: true,
      },
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit,
    })

    // Transform sources to look like jobs
    const mockJobs = sources.map(source => ({
      id: `mock_${source.id}`,
      sourceId: source.id,
      status: 'pending',
      itemsProcessed: 0,
      itemsTotal: null,
      progress: 0,
      createdAt: source.createdAt.toISOString(),
      lastScrapedAt: source.lastScrapedAt?.toISOString() || null,
      source: {
        id: source.id,
        name: source.name,
        url: source.url,
      },
    }))

    const total = await prisma.scrapingSource.count({ where })
    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      success: true,
      data: mockJobs,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    })

  } catch (error) {
    console.error('Error fetching scraping jobs:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
