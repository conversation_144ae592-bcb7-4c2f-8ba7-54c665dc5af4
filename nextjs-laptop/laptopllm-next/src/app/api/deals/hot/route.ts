/**
 * API endpoint for hot deals
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { dealsService } from '@/features/laptops/services/deals-service'

const querySchema = z.object({
  limit: z.number().min(1).max(50).default(10),
})

/**
 * GET /api/deals/hot
 * Fetch hot deals (best deals based on scoring algorithm)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const limit = parseInt(searchParams.get('limit') || '10')
    
    const result = querySchema.safeParse({ limit })
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: result.error.errors },
        { status: 400 }
      )
    }

    const hotDeals = await dealsService.getHotDeals(result.data.limit)

    return NextResponse.json(hotDeals)
  } catch (error) {
    console.error('Error in GET /api/deals/hot:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
