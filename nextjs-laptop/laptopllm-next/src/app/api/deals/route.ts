/**
 * API endpoints for laptop deals
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { dealsService } from '@/features/laptops/services/deals-service'
import type { DealFilters } from '@/shared/types'

// Validation schemas
const dealFiltersSchema = z.object({
  dealTypes: z.array(z.enum(['price-drop', 'best-price', 'back-in-stock', 'new-listing', 'limited-time'])).optional(),
  minDiscount: z.number().min(0).max(100).optional(),
  maxPrice: z.number().min(0).optional(),
  onlyHotDeals: z.boolean().optional(),
  inStockOnly: z.boolean().optional(),
  freeShippingOnly: z.boolean().optional(),
  sources: z.array(z.string()).optional(),
  brands: z.array(z.string()).optional(),
  minDealScore: z.number().min(0).max(100).optional(),
})

const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
})

/**
 * GET /api/deals
 * Fetch deals with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse pagination parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    
    const paginationResult = paginationSchema.safeParse({ page, limit })
    if (!paginationResult.success) {
      return NextResponse.json(
        { error: 'Invalid pagination parameters', details: paginationResult.error.errors },
        { status: 400 }
      )
    }

    // Parse filter parameters
    const filters: DealFilters = {}
    
    // Deal types
    const dealTypesParam = searchParams.get('dealTypes')
    if (dealTypesParam) {
      filters.dealTypes = dealTypesParam.split(',') as DealFilters['dealTypes']
    }
    
    // Numeric filters
    const minDiscount = searchParams.get('minDiscount')
    if (minDiscount) filters.minDiscount = parseFloat(minDiscount)
    
    const maxPrice = searchParams.get('maxPrice')
    if (maxPrice) filters.maxPrice = parseFloat(maxPrice)
    
    const minDealScore = searchParams.get('minDealScore')
    if (minDealScore) filters.minDealScore = parseFloat(minDealScore)
    
    // Boolean filters
    const onlyHotDeals = searchParams.get('onlyHotDeals')
    if (onlyHotDeals) filters.onlyHotDeals = onlyHotDeals === 'true'
    
    const inStockOnly = searchParams.get('inStockOnly')
    if (inStockOnly) filters.inStockOnly = inStockOnly === 'true'
    
    const freeShippingOnly = searchParams.get('freeShippingOnly')
    if (freeShippingOnly) filters.freeShippingOnly = freeShippingOnly === 'true'
    
    // Array filters
    const sourcesParam = searchParams.get('sources')
    if (sourcesParam) filters.sources = sourcesParam.split(',')
    
    const brandsParam = searchParams.get('brands')
    if (brandsParam) filters.brands = brandsParam.split(',')

    // Validate filters
    const filtersResult = dealFiltersSchema.safeParse(filters)
    if (!filtersResult.success) {
      return NextResponse.json(
        { error: 'Invalid filter parameters', details: filtersResult.error.errors },
        { status: 400 }
      )
    }

    // Fetch deals
    const result = await dealsService.getDeals(
      filtersResult.data,
      paginationResult.data.page,
      paginationResult.data.limit
    )

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error in GET /api/deals:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/deals/compare
 * Compare multiple deals
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const schema = z.object({
      dealIds: z.array(z.string()).min(2).max(5)
    })
    
    const result = schema.safeParse(body)
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: result.error.errors },
        { status: 400 }
      )
    }

    // For now, return a placeholder response
    // This would be implemented with actual comparison logic
    const comparison = {
      deals: result.data.dealIds,
      bestDeal: result.data.dealIds[0],
      comparison: {
        price: {
          winner: result.data.dealIds[0],
          values: result.data.dealIds.map(id => ({ dealId: id, price: 999 }))
        },
        shipping: {
          winner: result.data.dealIds[0],
          values: result.data.dealIds.map(id => ({ dealId: id, isFree: true }))
        },
        dealScore: {
          winner: result.data.dealIds[0],
          values: result.data.dealIds.map(id => ({ dealId: id, score: 85 }))
        }
      }
    }

    return NextResponse.json(comparison)
  } catch (error) {
    console.error('Error in POST /api/deals/compare:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
