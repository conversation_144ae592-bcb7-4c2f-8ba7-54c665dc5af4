import { NextResponse } from 'next/server';
import { scrapingService } from '@/lib/scraping/scraping-service';
import { z } from 'zod';

const CrawlRequestSchema = z.object({
  url: z.string().url(),
  siteType: z.enum(['amazon', 'bestbuy', 'newegg', 'microcenter', 'bhphoto', 'adorama', 'generic']).optional().default('generic'),
  extractLaptopData: z.boolean().optional().default(false),
  waitFor: z.number().positive().optional(),
  timeout: z.number().positive().optional(),
});

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const validatedData = CrawlRequestSchema.parse(body);

    console.log(`🚀 Starting scraping job for URL: ${validatedData.url}`);
    console.log(`📋 Site type: ${validatedData.siteType}`);
    console.log(`🔍 Extract laptop data: ${validatedData.extractLaptopData}`);

    if (validatedData.extractLaptopData) {
      // Use specialized laptop data extraction
      try {
        const laptopData = await scrapingService.extractLaptopData(
          validatedData.url,
          validatedData.siteType
        );

        return NextResponse.json({
          success: true,
          data: {
            type: 'laptop_extraction',
            url: validatedData.url,
            laptopData,
            extractedAt: new Date().toISOString(),
          },
        });
      } catch (error) {
        console.error('Laptop extraction failed, falling back to general scraping:', error);
        // Fall through to general scraping
      }
    }

    // Use general scraping with fallback strategy
    const scrapingConfig = {
      url: validatedData.url,
      siteType: validatedData.siteType,
      ...(validatedData.waitFor && { waitFor: validatedData.waitFor }),
      timeout: validatedData.timeout || 30000,
    };

    const result = await scrapingService.scrape(scrapingConfig);

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: {
          type: 'general_scraping',
          url: validatedData.url,
          content: result.data?.content,
          title: result.data?.title,
          metadata: result.data?.metadata,
          scraper: result.scraper,
          duration: result.duration,
          scrapedAt: result.timestamp,
        },
      });
    } else {
      return NextResponse.json({
        success: false,
        error: result.error || 'Scraping failed',
        scraper: result.scraper,
        duration: result.duration,
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in crawl API:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Datos de entrada inválidos',
        details: error.issues,
      }, { status: 400 });
    }

    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: errorMessage,
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    // Return health status of all scrapers
    const health = await scrapingService.getHealth();

    return NextResponse.json({
      success: true,
      health,
      scrapers: {
        crawl4ai: {
          status: health.scrapers.crawl4ai ? 'healthy' : 'unhealthy',
          description: 'Self-hosted Crawl4AI service (primary)',
        },
        firecrawl: {
          status: health.scrapers.firecrawl ? 'healthy' : 'unhealthy',
          description: 'Firecrawl API service (secondary)',
        },
        puppeteer: {
          status: health.scrapers.puppeteer ? 'healthy' : 'unhealthy',
          description: 'Puppeteer browser automation (fallback)',
        },
      },
      hierarchy: ['crawl4ai', 'firecrawl', 'puppeteer'],
      timestamp: health.timestamp,
    });
  } catch (error) {
    console.error('Error checking scraper health:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to check scraper health',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
