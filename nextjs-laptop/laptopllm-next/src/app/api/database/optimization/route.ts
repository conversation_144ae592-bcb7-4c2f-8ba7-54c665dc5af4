import { NextRequest, NextResponse } from 'next/server'
import { databaseOptimizationService } from '../../../../lib/database/optimization.service'

/**
 * GET /api/database/optimization
 * 
 * Returns comprehensive database optimization statistics including:
 * - Index usage statistics
 * - Query performance metrics
 * - Table statistics
 * - Optimization recommendations
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeRecommendations = searchParams.get('recommendations') !== 'false'
    const includeQueryStats = searchParams.get('queryStats') !== 'false'

    console.log('Fetching database optimization statistics...')

    const stats = await databaseOptimizationService.getOptimizationStats()

    // Filter data based on query parameters
    const response = {
      success: true,
      data: {
        indexUsage: stats.indexUsage,
        tableStats: stats.tableStats,
        ...(includeQueryStats && { queryPerformance: stats.queryPerformance }),
        ...(includeRecommendations && { optimizationRecommendations: stats.optimizationRecommendations }),
        summary: {
          totalIndexes: stats.indexUsage.length,
          activeIndexes: stats.indexUsage.filter(idx => idx.idx_scan > 0).length,
          unusedIndexes: stats.indexUsage.filter(idx => idx.idx_scan === 0).length,
          totalTables: stats.tableStats.length,
          highPriorityRecommendations: stats.optimizationRecommendations.filter(r => r.priority === 'high').length,
          averageQueryPerformance: stats.queryPerformance.length > 0 
            ? Math.round(stats.queryPerformance.reduce((sum, q) => sum + q.performance_score, 0) / stats.queryPerformance.length)
            : 0
        }
      },
      timestamp: new Date().toISOString()
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error fetching database optimization statistics:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch database optimization statistics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/database/optimization
 * 
 * Applies database optimizations including:
 * - Updating table statistics
 * - Running maintenance operations
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body

    console.log(`Applying database optimization action: ${action}`)

    let result

    switch (action) {
      case 'updateStatistics':
        await databaseOptimizationService.updateTableStatistics()
        result = {
          success: true,
          message: 'Table statistics updated successfully',
          action: 'updateStatistics'
        }
        break

      case 'applyOptimizations':
        result = await databaseOptimizationService.applyOptimizations()
        result.action = 'applyOptimizations'
        break

      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid action',
            validActions: ['updateStatistics', 'applyOptimizations']
          },
          { status: 400 }
        )
    }

    return NextResponse.json({
      ...result,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error applying database optimizations:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to apply database optimizations',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/database/optimization/status
 * 
 * Returns the current optimization status
 */
export async function HEAD(request: NextRequest) {
  try {
    const status = await databaseOptimizationService.checkOptimizationStatus()
    
    return NextResponse.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error checking optimization status:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to check optimization status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
