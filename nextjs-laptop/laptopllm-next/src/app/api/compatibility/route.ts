/**
 * LLM Compatibility API Routes
 * Endpoints for calculating and retrieving compatibility scores
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { EnhancedCompatibilityService } from '@/lib/llm/enhanced-compatibility.service'
import { CompatibilityService } from '@/lib/services/compatibility.service'

// Request validation schemas
const CalculateCompatibilitySchema = z.object({
  laptopSpecs: z.object({
    cpu: z.object({
      cores: z.number(),
      threads: z.number(),
      baseFreq: z.number(),
      boostFreq: z.number(),
      architecture: z.string(),
      manufacturer: z.string(),
      model: z.string(),
      cache: z.object({
        l1: z.number(),
        l2: z.number(),
        l3: z.number(),
      }),
      tdp: z.number(),
      instructionSets: z.array(z.string()),
    }),
    memory: z.object({
      totalGb: z.number(),
      type: z.string(),
      speed: z.number(),
      channels: z.number(),
      bandwidth: z.number(),
      ecc: z.boolean(),
    }),
    gpu: z.object({
      model: z.string(),
      manufacturer: z.string(),
      vramGb: z.number(),
      vramType: z.string(),
      computeUnits: z.number(),
      baseClockMhz: z.number(),
      boostClockMhz: z.number(),
      memoryBandwidth: z.number(),
      tensorCores: z.number().optional(),
      rtCores: z.number().optional(),
      cudaCores: z.number().optional(),
      rocmSupport: z.boolean().optional(),
      openclSupport: z.boolean().optional(),
    }).optional(),
    storage: z.object({
      totalGb: z.number(),
      type: z.string(),
      interface: z.string(),
      readSpeed: z.number(),
      writeSpeed: z.number(),
      iops: z.object({
        read: z.number(),
        write: z.number(),
      }),
    }),
    thermal: z.object({
      maxTdp: z.number(),
      coolingType: z.string(),
      thermalThrottleTemp: z.number(),
      sustainedPerformance: z.boolean(),
    }),
    system: z.object({
      formFactor: z.string(),
      powerSupply: z.number(),
      batteryCapacity: z.number().optional(),
      portability: z.boolean(),
    }),
  }),
  model: z.object({
    id: z.string(),
    name: z.string(),
    parameters: z.number(),
    quantization: z.array(z.string()),
    architecture: z.string(),
    requirements: z.object({
      memory: z.object({
        fp16: z.number(),
        int8: z.number(),
        int4: z.number(),
        int2: z.number().optional(),
      }),
      compute: z.object({
        minCpuCores: z.number(),
        recommendedCpuCores: z.number(),
        minFreqGhz: z.number(),
        supportedArchitectures: z.array(z.string()),
        requiresAVX: z.boolean(),
        requiresAVX512: z.boolean().optional(),
      }),
      gpu: z.object({
        minVramGb: z.number(),
        recommendedVramGb: z.number(),
        supportedVendors: z.array(z.string()),
        requiresTensorCores: z.boolean().optional(),
        minComputeCapability: z.string().optional(),
      }).optional(),
      storage: z.object({
        modelSizeGb: z.number(),
        workingSpaceGb: z.number(),
        minReadSpeedMBps: z.number(),
        preferredInterface: z.array(z.string()),
      }),
    }),
    performance: z.object({
      tokensPerSecondBaseline: z.number(),
      memoryEfficiency: z.number(),
      computeEfficiency: z.number(),
      parallelizability: z.number(),
      contextLength: z.number(),
      batchingSupport: z.boolean(),
    }),
    useCases: z.object({
      development: z.number(),
      research: z.number(),
      production: z.number(),
      education: z.number(),
      inference: z.number(),
      training: z.number(),
    }),
  }),
})

const BasicCompatibilitySchema = z.object({
  laptopSpecs: z.object({
    ramGb: z.number(),
    cpuCores: z.number(),
    cpuArchitecture: z.string(),
    gpuVramGb: z.number().optional(),
    storageGb: z.number(),
    storageType: z.string(),
    cpuBaseClockGhz: z.number().optional(),
    cpuBoostClockGhz: z.number().optional(),
  }),
  llmModelId: z.number().optional(),
})

/**
 * POST /api/compatibility
 * Calculate detailed compatibility score
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Determine which service to use based on request structure
    if (body.model && body.laptopSpecs.cpu) {
      // Enhanced compatibility calculation
      const validatedData = CalculateCompatibilitySchema.parse(body)
      const enhancedService = new EnhancedCompatibilityService()
      
      const result = await enhancedService.calculateDetailedCompatibility(
        validatedData.laptopSpecs,
        validatedData.model
      )
      
      return NextResponse.json({
        success: true,
        data: result,
        type: 'detailed'
      })
    } else {
      // Basic compatibility calculation
      const validatedData = BasicCompatibilitySchema.parse(body)
      const basicService = new CompatibilityService()
      
      const result = await basicService.calculateCompatibility(
        validatedData.laptopSpecs,
        validatedData.llmModelId
      )
      
      return NextResponse.json({
        success: true,
        data: result,
        type: 'basic'
      })
    }
  } catch (error) {
    console.error('Compatibility calculation error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: error.errors
        },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to calculate compatibility',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/compatibility
 * Get compatibility scores for a laptop
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const laptopId = searchParams.get('laptopId')
    const modelId = searchParams.get('modelId')
    const type = searchParams.get('type') || 'basic'
    
    if (!laptopId) {
      return NextResponse.json(
        {
          success: false,
          error: 'laptopId parameter is required'
        },
        { status: 400 }
      )
    }
    
    const service = new CompatibilityService()
    
    if (modelId) {
      // Get compatibility for specific model
      const result = await service.getCompatibilityScore(
        parseInt(laptopId),
        parseInt(modelId)
      )
      
      return NextResponse.json({
        success: true,
        data: result
      })
    } else {
      // Get all compatibility scores for laptop
      const results = await service.getAllCompatibilityScores(parseInt(laptopId))
      
      return NextResponse.json({
        success: true,
        data: results
      })
    }
  } catch (error) {
    console.error('Get compatibility error:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get compatibility scores',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
