/**
 * LLM Models API Routes
 * Endpoints for managing and retrieving LLM model information
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { BaseService } from '@/lib/services/base.service'

// Request validation schemas
const ModelFiltersSchema = z.object({
  category: z.string().optional(),
  framework: z.string().optional(),
  minSize: z.string().optional(),
  maxSize: z.string().optional(),
  requiresGpu: z.boolean().optional(),
  minRamGb: z.number().optional(),
  maxRamGb: z.number().optional(),
  quantizationBits: z.array(z.number()).optional(),
})

class LLMModelsService extends BaseService {
  /**
   * Get all available LLM models with optional filtering
   */
  async getModels(filters?: {
    category?: string
    framework?: string
    minSize?: string
    maxSize?: string
    requiresGpu?: boolean
    minRamGb?: number
    maxRamGb?: number
    quantizationBits?: number[]
  }) {
    return this.executeWithErrorHandling(async () => {
      const whereClause: Record<string, unknown> = {}
      
      // Apply filters
      if (filters?.requiresGpu !== undefined) {
        whereClause.requires_gpu = filters.requiresGpu
      }
      
      if (filters?.minRamGb) {
        whereClause.min_ram_gb = {
          gte: filters.minRamGb
        }
      }
      
      if (filters?.maxRamGb) {
        whereClause.min_ram_gb = {
          ...whereClause.min_ram_gb,
          lte: filters.maxRamGb
        }
      }
      
      if (filters?.quantizationBits && filters.quantizationBits.length > 0) {
        whereClause.quantization_bits = {
          in: filters.quantizationBits
        }
      }
      
      // Parameter size filtering
      if (filters?.minSize || filters?.maxSize) {
        const sizeFilter: Record<string, unknown> = {}
        
        if (filters.minSize) {
          const minParams = this.parseParameterSize(filters.minSize)
          sizeFilter.gte = minParams
        }
        
        if (filters.maxSize) {
          const maxParams = this.parseParameterSize(filters.maxSize)
          sizeFilter.lte = maxParams
        }
        
        whereClause.parameters_billions = sizeFilter
      }
      
      const models = await this.db.llm_models.findMany({
        where: whereClause,
        orderBy: [
          { parameters_billions: 'asc' },
          { name: 'asc' }
        ],
        include: {
          laptop_llm_compatibility: {
            select: {
              overall_score: true,
              laptop_id: true
            }
          }
        }
      })
      
      // Add computed fields
      return models.map(model => ({
        ...model,
        averageCompatibilityScore: this.calculateAverageScore(model.laptop_llm_compatibility),
        compatibilityCount: model.laptop_llm_compatibility.length,
        category: this.categorizeModel(model),
        sizeCategory: this.getSizeCategory(model.parameters_billions?.toNumber() || 0)
      }))
    })
  }
  
  /**
   * Get model categories for filtering
   */
  async getModelCategories() {
    return this.executeWithErrorHandling(async () => {
      const models = await this.db.llm_models.findMany({
        select: {
          parameters_billions: true,
          requires_gpu: true,
          quantization_bits: true
        }
      })
      
      const categories = {
        sizes: this.extractSizeCategories(models),
        quantizations: [...new Set(models.map(m => m.quantization_bits).filter(Boolean))].sort(),
        gpuRequirements: [
          { value: true, label: 'GPU Required', count: models.filter(m => m.requires_gpu).length },
          { value: false, label: 'CPU Only', count: models.filter(m => !m.requires_gpu).length }
        ],
        frameworks: [
          'PyTorch',
          'TensorFlow',
          'ONNX',
          'Transformers',
          'llama.cpp',
          'GGML'
        ]
      }
      
      return categories
    })
  }
  
  /**
   * Get detailed model information
   */
  async getModelById(id: number) {
    return this.executeWithErrorHandling(async () => {
      const model = await this.db.llm_models.findUnique({
        where: { id },
        include: {
          laptop_llm_compatibility: {
            include: {
              laptops: {
                select: {
                  id: true,
                  model: true,
                  brand_id: true,
                  brands: {
                    select: {
                      name: true
                    }
                  }
                }
              }
            },
            orderBy: {
              overall_score: 'desc'
            }
          }
        }
      })
      
      if (!model) {
        throw new Error('Model not found')
      }
      
      // Add computed fields
      return {
        ...model,
        averageCompatibilityScore: this.calculateAverageScore(model.laptop_llm_compatibility),
        compatibilityCount: model.laptop_llm_compatibility.length,
        category: this.categorizeModel(model),
        sizeCategory: this.getSizeCategory(model.parameters_billions?.toNumber() || 0),
        topCompatibleLaptops: model.laptop_llm_compatibility
          .slice(0, 10)
          .map(comp => ({
            ...comp,
            laptop: {
              ...comp.laptops,
              displayName: `${comp.laptops.brands?.name} ${comp.laptops.model}`
            }
          }))
      }
    })
  }
  
  /**
   * Get compatibility statistics for models
   */
  async getCompatibilityStats() {
    return this.executeWithErrorHandling(async () => {
      const stats = await this.db.llm_models.findMany({
        select: {
          id: true,
          name: true,
          parameters_billions: true,
          requires_gpu: true,
          laptop_llm_compatibility: {
            select: {
              overall_score: true
            }
          }
        }
      })
      
      const totalModels = stats.length
      const totalCompatibilityScores = stats.reduce((sum, model) => 
        sum + model.laptop_llm_compatibility.length, 0
      )
      
      const averageScores = stats.map(model => ({
        modelId: model.id,
        modelName: model.name,
        parameters: model.parameters_billions?.toNumber() || 0,
        requiresGpu: model.requires_gpu,
        averageScore: this.calculateAverageScore(model.laptop_llm_compatibility),
        scoreCount: model.laptop_llm_compatibility.length
      }))
      
      const overallAverageScore = averageScores.reduce((sum, model) => 
        sum + (model.averageScore || 0), 0
      ) / averageScores.filter(m => m.averageScore).length
      
      return {
        totalModels,
        totalCompatibilityScores,
        overallAverageScore: Math.round(overallAverageScore || 0),
        modelStats: averageScores.sort((a, b) => (b.averageScore || 0) - (a.averageScore || 0)),
        distribution: {
          excellent: averageScores.filter(m => (m.averageScore || 0) >= 80).length,
          good: averageScores.filter(m => (m.averageScore || 0) >= 60 && (m.averageScore || 0) < 80).length,
          acceptable: averageScores.filter(m => (m.averageScore || 0) >= 40 && (m.averageScore || 0) < 60).length,
          poor: averageScores.filter(m => (m.averageScore || 0) < 40).length
        }
      }
    })
  }
  
  // Helper methods
  private parseParameterSize(sizeStr: string): number {
    const match = sizeStr.match(/^(\d+(?:\.\d+)?)\s*([KMGT]?)B?$/i)
    if (!match) return 0
    
    const value = parseFloat(match[1])
    const unit = match[2].toUpperCase()
    
    switch (unit) {
      case 'K': return value / 1000
      case 'M': return value / 1000
      case 'G': return value
      case 'T': return value * 1000
      default: return value
    }
  }
  
  private calculateAverageScore(compatibilityScores: Array<{ overall_score: number }>): number | null {
    if (compatibilityScores.length === 0) return null
    
    const sum = compatibilityScores.reduce((acc, score) => acc + score.overall_score, 0)
    return Math.round(sum / compatibilityScores.length)
  }
  
  private categorizeModel(model: { parameters_billions: { toNumber: () => number } | null }): string {
    const params = model.parameters_billions?.toNumber() || 0
    
    if (params < 1) return 'small'
    if (params < 7) return 'medium'
    if (params < 30) return 'large'
    if (params < 70) return 'very-large'
    return 'ultra-large'
  }
  
  private getSizeCategory(params: number): string {
    if (params < 1) return '<1B'
    if (params < 7) return '1-7B'
    if (params < 13) return '7-13B'
    if (params < 30) return '13-30B'
    if (params < 70) return '30-70B'
    return '70B+'
  }
  
  private extractSizeCategories(models: Array<{ parameters_billions: { toNumber: () => number } | null }>): Array<{ label: string, value: string, count: number }> {
    const categories = [
      { label: '<1B', value: '<1', min: 0, max: 1 },
      { label: '1-7B', value: '1-7', min: 1, max: 7 },
      { label: '7-13B', value: '7-13', min: 7, max: 13 },
      { label: '13-30B', value: '13-30', min: 13, max: 30 },
      { label: '30-70B', value: '30-70', min: 30, max: 70 },
      { label: '70B+', value: '70+', min: 70, max: Infinity }
    ]
    
    return categories.map(cat => ({
      ...cat,
      count: models.filter(m => {
        const params = m.parameters_billions?.toNumber() || 0
        return params >= cat.min && params < cat.max
      }).length
    }))
  }
}

/**
 * GET /api/compatibility/models
 * Get available LLM models with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    
    const service = new LLMModelsService()
    
    if (action === 'categories') {
      const categories = await service.getModelCategories()
      return NextResponse.json({
        success: true,
        data: categories
      })
    }
    
    if (action === 'stats') {
      const stats = await service.getCompatibilityStats()
      return NextResponse.json({
        success: true,
        data: stats
      })
    }
    
    // Parse filters
    const filters: Record<string, unknown> = {}
    
    if (searchParams.get('category')) filters.category = searchParams.get('category')
    if (searchParams.get('framework')) filters.framework = searchParams.get('framework')
    if (searchParams.get('minSize')) filters.minSize = searchParams.get('minSize')
    if (searchParams.get('maxSize')) filters.maxSize = searchParams.get('maxSize')
    if (searchParams.get('requiresGpu')) filters.requiresGpu = searchParams.get('requiresGpu') === 'true'
    if (searchParams.get('minRamGb')) filters.minRamGb = parseInt(searchParams.get('minRamGb')!)
    if (searchParams.get('maxRamGb')) filters.maxRamGb = parseInt(searchParams.get('maxRamGb')!)
    
    const quantizationBits = searchParams.get('quantizationBits')
    if (quantizationBits) {
      filters.quantizationBits = quantizationBits.split(',').map(Number)
    }
    
    const models = await service.getModels(Object.keys(filters).length > 0 ? filters : undefined)
    
    return NextResponse.json({
      success: true,
      data: models,
      count: models.length
    })
  } catch (error) {
    console.error('Get models error:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get models',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
