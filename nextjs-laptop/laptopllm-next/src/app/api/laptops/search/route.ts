/**
 * Advanced Laptop Search API
 * Comprehensive search endpoint with filtering, sorting, and pagination
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { BaseService } from '@/lib/services/base.service'
import { Prisma } from '@prisma/client'

// Request validation schema
const SearchParamsSchema = z.object({
  q: z.string().optional(),
  sortBy: z.enum(['price', 'rating', 'compatibility', 'name', 'newest']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  page: z.coerce.number().min(1).optional(),
  limit: z.coerce.number().min(1).max(100).optional(),
  // Filters
  'filter.brands': z.string().optional(),
  'filter.priceRange': z.string().optional(),
  'filter.memorySize': z.string().optional(),
  'filter.storageType': z.string().optional(),
  'filter.cpuBrands': z.string().optional(),
  'filter.gpuBrands': z.string().optional(),
  'filter.displaySize': z.string().optional(),
  'filter.weight': z.string().optional(),
  'filter.batteryLife': z.string().optional(),
  'filter.inStock': z.string().optional(),
  include: z.string().optional(), // Nuevo campo para incluir relaciones
})

class LaptopSearchService extends BaseService {
  /**
   * Advanced laptop search with comprehensive filtering
   */
  async searchLaptops(params: {
    query?: string
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
    page?: number
    limit?: number
    filters?: Record<string, unknown>
    include?: string // Add include to params
  }) {
    return this.executeWithErrorHandling(async () => {
      const {
        query = '',
        sortBy = 'compatibility',
        sortOrder = 'desc',
        page = 1,
        limit = 12,
        filters = {},
        include // Destructure include
      } = params

      // Build dynamic include object
      const includeClause = this.buildIncludeClause(include)

      // Build where clause
      const whereClause = this.buildWhereClause(query, { query, ...filters })
      
      // Build order by clause
      const orderByClause = this.buildOrderBy(sortBy, sortOrder)
      
      // Calculate offset
      const offset = (page - 1) * limit

      // Execute search
      const [laptops, total] = await Promise.all([
        this.db.laptops.findMany({
          where: whereClause,
          orderBy: orderByClause,
          skip: offset,
          take: limit,
          include: includeClause, // Use dynamic include
        }),
        this.db.laptops.count({
          where: whereClause
        })
      ])

      // Transform results
      const transformedLaptops = laptops.map(laptop => this.transformLaptop(laptop))

      return {
        laptops: transformedLaptops,
        total,
        page,
        totalPages: Math.ceil(total / limit),
        hasMore: page * limit < total
      }
    })
  }

  /**
   * Get search suggestions
   */
  async getSearchSuggestions(query: string, limit: number = 10) {
    return this.executeWithErrorHandling(async () => {
      if (query.length < 2) return []

      const suggestions = await this.db.laptops.findMany({
        where: {
          OR: [
            {
              model_name: {
                contains: query,
                mode: 'insensitive'
              }
            },
            {
              brands: {
                name: {
                  contains: query,
                  mode: 'insensitive'
                }
              }
            }
          ]
        },
        select: {
          model_name: true,
          brands: {
            select: {
              name: true
            }
          }
        },
        take: limit
      })

      return suggestions.map(laptop => 
        `${laptop.brands?.name || ''} ${laptop.model_name}`.trim()
      ).filter((value, index, self) => self.indexOf(value) === index)
    })
  }

  /**
   * Build where clause for filtering
   */
  private buildWhereClause(query: string, filters: Record<string, unknown>) {
    const whereClause: Record<string, unknown> = {}

    // Text search
    if (query) {
      whereClause.OR = [
        {
          model_name: {
            contains: query,
            mode: 'insensitive'
          }
        },
        {
          description: {
            contains: query,
            mode: 'insensitive'
          }
        },
        {
          brands: {
            name: {
              contains: query,
              mode: 'insensitive'
            }
          }
        }
      ]
    }

    // Brand filters
    if (filters.brands && filters.brands.length > 0) {
      whereClause.brands = {
        name: {
          in: filters.brands
        }
      }
    }

    // Price range
    if (filters.priceRange) {
      whereClause.current_price = {}
      if (filters.priceRange.min !== undefined) {
        whereClause.current_price.gte = filters.priceRange.min
      }
      if (filters.priceRange.max !== undefined) {
        whereClause.current_price.lte = filters.priceRange.max
      }
    }

    // Memory size
    if (filters.memorySize && filters.memorySize.length > 0) {
      whereClause.ram_gb = {
        in: filters.memorySize
      }
    }

    // Storage type
    if (filters.storageType && filters.storageType.length > 0) {
      whereClause.storage_type = {
        in: filters.storageType
      }
    }

    // CPU brands
    if (filters.cpuBrands && filters.cpuBrands.length > 0) {
      whereClause.laptop_cpus = {
        some: {
          cpus: {
            manufacturer: {
              in: filters.cpuBrands
            }
          }
        }
      }
    }

    // Display size
    if (filters.displaySize) {
      whereClause.screen_size_inches = {}
      if (filters.displaySize.min !== undefined) {
        whereClause.screen_size_inches.gte = filters.displaySize.min
      }
      if (filters.displaySize.max !== undefined) {
        whereClause.screen_size_inches.lte = filters.displaySize.max
      }
    }

    // In stock filter
    if (filters.inStock) {
      whereClause.is_available = true
    }

    return whereClause
  }

  /**
   * Build order by clause
   */
  private buildOrderBy(sortBy: string, sortOrder: 'asc' | 'desc') {
    const orderByClause: Record<string, unknown>[] = []

    switch (sortBy) {
      case 'price':
        orderByClause.push({ current_price: sortOrder })
        break
      case 'rating':
        orderByClause.push({ rating: sortOrder })
        break
      case 'compatibility':
        // Sort by average compatibility score (approximated by count)
        orderByClause.push({
          laptop_llm_compatibility: {
            _count: sortOrder
          }
        })
        break
      case 'name':
        orderByClause.push({ model_name: sortOrder })
        break
      case 'newest':
        orderByClause.push({ created_at: sortOrder })
        break
      default:
        orderByClause.push({ created_at: 'desc' })
    }

    orderByClause.push({ id: 'asc' })
    return orderByClause
  }

  /**
   * Transform laptop data for API response
   */
  private transformLaptop(laptop: Record<string, unknown> & { id: number; model_name: string; brands: { name: string }; image_urls: string; description: string; category: string; weight_lbs: number; battery_life_hours: number; rating: number; review_count: number; laptop_cpus: Array<{ cpus?: { model?: string; manufacturer?: string; cores?: number; threads?: number } }>; laptop_gpus: Array<{ gpus?: { model?: string; manufacturer?: string } }>; laptop_llm_compatibility: Array<{ llm_models?: { name: string }; overall_score: number }>; laptop_deals: Array<{ store_name: string; deal_price: number; deal_url: string }> }) {
    return {
      id: laptop.id,
      url: laptop.source_url || `laptop-${laptop.id}`,
      title: `${laptop.brands?.name || ''} ${laptop.model_name}`.trim(),
      brand: laptop.brands?.name || 'Unknown',
      price: laptop.current_price || 0,
      currency: laptop.currency || 'USD',
      images: laptop.image_urls ? JSON.parse(laptop.image_urls) : [],
      description: laptop.description || '',
      specifications: {
        cpu: {
          model: laptop.laptop_cpus?.[0]?.cpus?.model || 'Unknown',
          manufacturer: laptop.laptop_cpus?.[0]?.cpus?.manufacturer || 'Unknown',
          cores: laptop.laptop_cpus?.[0]?.cpus?.cores || 0,
          threads: laptop.laptop_cpus?.[0]?.cpus?.threads || 0
        },
        memory: {
          size: laptop.ram_gb || 0,
          type: laptop.ram_type || 'Unknown'
        },
        storage: {
          capacity: laptop.storage_gb || 0,
          type: laptop.storage_type || 'Unknown'
        },
        display: {
          size: laptop.screen_size_inches || 0,
          resolution: laptop.screen_resolution || 'Unknown'
        },
        gpu: laptop.laptop_gpus?.[0] ? {
          model: laptop.laptop_gpus[0].gpus?.model || 'Integrated',
          manufacturer: laptop.laptop_gpus[0].gpus?.manufacturer || 'Unknown'
        } : null
      },
      features: {
        category: laptop.category || 'General',
        weight: laptop.weight_lbs || 0,
        batteryLife: laptop.battery_life_hours || 0
      },
      availability: {
        inStock: laptop.is_available || false,
        stores: laptop.laptop_deals?.map((deal: { store_name: string; deal_price: number; deal_url: string }) => ({
          name: deal.store_name,
          price: deal.deal_price,
          url: deal.deal_url
        })) || []
      },
      compatibility: {
        averageScore: this.calculateAverageScore(laptop.laptop_llm_compatibility),
        topModels: laptop.laptop_llm_compatibility?.map((comp: { llm_models: { name: string }; overall_score: number }) => ({
          model: comp.llm_models?.name,
          score: comp.overall_score
        })) || []
      },
      rating: laptop.rating || 0,
      reviewCount: laptop.review_count || 0
    }
  }

  private calculateAverageScore(scores: { overall_score: number }[]): number {
    if (!scores || scores.length === 0) return 0
    const sum = scores.reduce((acc, score) => acc + score.overall_score, 0)
    return Math.round(sum / scores.length)
  }

  /**
   * Build include clause for relations
   */
  private buildIncludeClause(include?: string) {
    const includeClause: Record<string, boolean | Record<string, unknown>> = {}
    if (include) {
      include.split(',').forEach(relation => {
        const trimmedRelation = relation.trim()
        if (trimmedRelation === 'brands') {
          includeClause.brands = true
        } else if (trimmedRelation === 'laptop_cpus') {
          includeClause.laptop_cpus = { include: { cpus: true } }
        } else if (trimmedRelation === 'laptop_gpus') {
          includeClause.laptop_gpus = { include: { gpus: true } }
        } else if (trimmedRelation === 'laptop_llm_compatibility') {
          includeClause.laptop_llm_compatibility = { include: { llm_models: true } }
        } else if (trimmedRelation === 'laptop_deals') {
          includeClause.laptop_deals = true
        }
      })
    }
    return includeClause
  }
}

/**
 * GET /api/laptops/search
 * Search laptops with advanced filtering
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    
    const service = new LaptopSearchService()
    
    // Handle suggestions
    if (action === 'suggestions') {
      const query = searchParams.get('q') || ''
      const limit = parseInt(searchParams.get('limit') || '10')
      
      const suggestions = await service.getSearchSuggestions(query, limit)
      
      return NextResponse.json({
        success: true,
        data: suggestions
      })
    }
    
    // Validate search parameters
    const validatedParams = SearchParamsSchema.parse(Object.fromEntries(searchParams))
    
    // Parse filters
    const filters: Record<string, unknown> = {}
    Object.entries(validatedParams).forEach(([key, value]) => {
      if (key.startsWith('filter.') && value) {
        const filterKey = key.replace('filter.', '')
        try {
          // Attempt to parse as JSON, if it fails, use the value as is
          filters[filterKey] = JSON.parse(value as string)
        } catch {
          filters[filterKey] = value
        }
      }
    })
    
    // Execute search
    const result = await service.searchLaptops({
      query: validatedParams.q,
      sortBy: validatedParams.sortBy,
      sortOrder: validatedParams.sortOrder,
      page: validatedParams.page,
      limit: validatedParams.limit,
      filters,
      include: validatedParams.include // Pass the include parameter
    })
    
    return NextResponse.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Laptop search error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid search parameters',
          details: error.errors
        },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Search failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
