/**
 * Laptop Details API Endpoint
 * GET /api/laptops/[id] - Get detailed information for a specific laptop
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/prisma'

// Validation schema for laptop ID
const LaptopIdSchema = z.object({
  id: z.string().uuid('Invalid laptop ID format')
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate laptop ID
    const validationResult = LaptopIdSchema.safeParse({ id: params.id })
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid laptop ID',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { id } = validationResult.data

    // Fetch laptop with all related data
    const laptop = await prisma.laptop.findUnique({
      where: { id },
      include: {
        specifications: {
          include: {
            cpu: true,
            memory: true,
            storage: true,
            display: true,
            gpu: true,
            connectivity: true
          }
        },
        features: true,
        availability: {
          include: {
            stores: true
          }
        },
        compatibility: {
          include: {
            topModels: true,
            detailedScores: true
          }
        },
        images: true,
        reviews: {
          include: {
            source: true
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 10
        },
        deals: {
          where: {
            isActive: true,
            expiresAt: {
              gte: new Date()
            }
          },
          orderBy: {
            discountPercentage: 'desc'
          },
          take: 5
        },
        scrapingJobs: {
          orderBy: {
            createdAt: 'desc'
          },
          take: 1,
          select: {
            id: true,
            status: true,
            lastScrapedAt: true,
            nextScheduledAt: true
          }
        }
      }
    })

    if (!laptop) {
      return NextResponse.json(
        {
          success: false,
          error: 'Laptop not found'
        },
        { status: 404 }
      )
    }

    // Transform the data to match our frontend types
    const transformedLaptop = {
      id: laptop.id,
      title: laptop.title,
      brand: laptop.brand,
      model: laptop.model,
      price: laptop.price,
      originalPrice: laptop.originalPrice,
      currency: laptop.currency,
      rating: laptop.rating,
      reviewCount: laptop.reviewCount,
      url: laptop.url,
      description: laptop.description,
      category: laptop.category,
      isAvailable: laptop.isAvailable,
      createdAt: laptop.createdAt,
      updatedAt: laptop.updatedAt,
      
      // Specifications
      specifications: laptop.specifications ? {
        cpu: laptop.specifications.cpu ? {
          manufacturer: laptop.specifications.cpu.manufacturer,
          model: laptop.specifications.cpu.model,
          cores: laptop.specifications.cpu.cores,
          threads: laptop.specifications.cpu.threads,
          baseFrequency: laptop.specifications.cpu.baseFrequency,
          maxFrequency: laptop.specifications.cpu.maxFrequency,
          cache: laptop.specifications.cpu.cache,
          architecture: laptop.specifications.cpu.architecture,
          tdp: laptop.specifications.cpu.tdp
        } : null,
        
        memory: laptop.specifications.memory ? {
          size: laptop.specifications.memory.size,
          type: laptop.specifications.memory.type,
          speed: laptop.specifications.memory.speed,
          slots: laptop.specifications.memory.slots,
          maxCapacity: laptop.specifications.memory.maxCapacity,
          isUpgradeable: laptop.specifications.memory.isUpgradeable
        } : null,
        
        storage: laptop.specifications.storage ? {
          capacity: laptop.specifications.storage.capacity,
          type: laptop.specifications.storage.type,
          interface: laptop.specifications.storage.interface,
          speed: laptop.specifications.storage.speed,
          isUpgradeable: laptop.specifications.storage.isUpgradeable,
          additionalSlots: laptop.specifications.storage.additionalSlots
        } : null,
        
        display: laptop.specifications.display ? {
          size: laptop.specifications.display.size,
          resolution: laptop.specifications.display.resolution,
          refreshRate: laptop.specifications.display.refreshRate,
          panelType: laptop.specifications.display.panelType,
          brightness: laptop.specifications.display.brightness,
          colorGamut: laptop.specifications.display.colorGamut,
          isTouch: laptop.specifications.display.isTouch,
          aspectRatio: laptop.specifications.display.aspectRatio
        } : null,
        
        gpu: laptop.specifications.gpu ? {
          manufacturer: laptop.specifications.gpu.manufacturer,
          model: laptop.specifications.gpu.model,
          type: laptop.specifications.gpu.type,
          memory: laptop.specifications.gpu.memory,
          memoryType: laptop.specifications.gpu.memoryType,
          baseClock: laptop.specifications.gpu.baseClock,
          boostClock: laptop.specifications.gpu.boostClock,
          tdp: laptop.specifications.gpu.tdp
        } : null,
        
        connectivity: laptop.specifications.connectivity ? {
          wifi: laptop.specifications.connectivity.wifi,
          bluetooth: laptop.specifications.connectivity.bluetooth,
          ethernet: laptop.specifications.connectivity.ethernet,
          usbPorts: laptop.specifications.connectivity.usbPorts,
          hdmiPorts: laptop.specifications.connectivity.hdmiPorts,
          displayPorts: laptop.specifications.connectivity.displayPorts,
          audioJack: laptop.specifications.connectivity.audioJack,
          sdCardSlot: laptop.specifications.connectivity.sdCardSlot,
          thunderbolt: laptop.specifications.connectivity.thunderbolt
        } : null
      } : null,
      
      // Features
      features: laptop.features ? {
        weight: laptop.features.weight,
        dimensions: laptop.features.dimensions,
        batteryLife: laptop.features.batteryLife,
        batteryCapacity: laptop.features.batteryCapacity,
        chargingSpeed: laptop.features.chargingSpeed,
        keyboard: laptop.features.keyboard,
        trackpad: laptop.features.trackpad,
        webcam: laptop.features.webcam,
        speakers: laptop.features.speakers,
        cooling: laptop.features.cooling,
        build: laptop.features.build,
        color: laptop.features.color,
        operatingSystem: laptop.features.operatingSystem,
        warranty: laptop.features.warranty,
        certifications: laptop.features.certifications,
        category: laptop.features.category
      } : null,
      
      // Availability
      availability: laptop.availability ? {
        inStock: laptop.availability.inStock,
        stockLevel: laptop.availability.stockLevel,
        estimatedDelivery: laptop.availability.estimatedDelivery,
        stores: laptop.availability.stores?.map(store => ({
          name: store.name,
          url: store.url,
          price: store.price,
          inStock: store.inStock,
          shipping: store.shipping,
          rating: store.rating
        })) || []
      } : null,
      
      // LLM Compatibility
      compatibility: laptop.compatibility ? {
        averageScore: laptop.compatibility.averageScore,
        cpuScore: laptop.compatibility.cpuScore,
        memoryScore: laptop.compatibility.memoryScore,
        gpuScore: laptop.compatibility.gpuScore,
        storageScore: laptop.compatibility.storageScore,
        thermalScore: laptop.compatibility.thermalScore,
        powerScore: laptop.compatibility.powerScore,
        overallRating: laptop.compatibility.overallRating,
        recommendedModels: laptop.compatibility.recommendedModels,
        warnings: laptop.compatibility.warnings,
        recommendations: laptop.compatibility.recommendations,
        lastUpdated: laptop.compatibility.lastUpdated,
        
        topModels: laptop.compatibility.topModels?.map(model => ({
          model: model.model,
          score: model.score,
          estimatedPerformance: model.estimatedPerformance,
          memoryRequirement: model.memoryRequirement,
          notes: model.notes
        })) || [],
        
        detailedScores: laptop.compatibility.detailedScores?.map(score => ({
          category: score.category,
          score: score.score,
          weight: score.weight,
          description: score.description,
          factors: score.factors
        })) || []
      } : null,
      
      // Images
      images: laptop.images?.map(img => img.url) || [],
      
      // Recent reviews
      reviews: laptop.reviews?.map(review => ({
        id: review.id,
        rating: review.rating,
        title: review.title,
        content: review.content,
        author: review.author,
        date: review.date,
        verified: review.verified,
        helpful: review.helpful,
        source: review.source ? {
          name: review.source.name,
          url: review.source.url
        } : null
      })) || [],
      
      // Active deals
      deals: laptop.deals?.map(deal => ({
        id: deal.id,
        title: deal.title,
        description: deal.description,
        originalPrice: deal.originalPrice,
        discountedPrice: deal.discountedPrice,
        discountPercentage: deal.discountPercentage,
        startDate: deal.startDate,
        expiresAt: deal.expiresAt,
        store: deal.store,
        url: deal.url,
        isActive: deal.isActive
      })) || [],
      
      // Scraping status
      scrapingStatus: laptop.scrapingJobs?.[0] ? {
        status: laptop.scrapingJobs[0].status,
        lastScrapedAt: laptop.scrapingJobs[0].lastScrapedAt,
        nextScheduledAt: laptop.scrapingJobs[0].nextScheduledAt
      } : null
    }

    return NextResponse.json({
      success: true,
      data: transformedLaptop
    })

  } catch (error) {
    console.error('Error fetching laptop details:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}
