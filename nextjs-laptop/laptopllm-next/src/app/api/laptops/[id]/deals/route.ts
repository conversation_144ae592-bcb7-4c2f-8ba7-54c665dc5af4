/**
 * API endpoint for laptop-specific deals
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { dealsService } from '@/features/laptops/services/deals-service'

const paramsSchema = z.object({
  id: z.string().transform(val => parseInt(val, 10)).refine(val => !isNaN(val), {
    message: 'Invalid laptop ID'
  })
})

/**
 * GET /api/laptops/[id]/deals
 * Fetch deals for a specific laptop
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const result = paramsSchema.safeParse(params)
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid laptop ID', details: result.error.errors },
        { status: 400 }
      )
    }

    const laptopId = result.data.id
    const deals = await dealsService.getLaptopDeals(laptopId)

    return NextResponse.json(deals)
  } catch (error) {
    console.error(`Error in GET /api/laptops/${params.id}/deals:`, error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
