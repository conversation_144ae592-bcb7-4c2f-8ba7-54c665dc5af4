/**
 * Batch Laptop Retrieval API
 * Endpoint for fetching multiple laptops with comprehensive data
 */

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import type { LaptopData } from '@/shared/types'
import {
  BatchLaptopRequestSchema,
  BatchLaptopResponseSchema,
  validateBatchLaptopRequest,
  validateBatchLaptopResponse,
  type BatchLaptopRequest,
  type BatchLaptopResponse
} from '@/features/laptop-comparison/schemas'

/**
 * POST /api/laptops/batch
 * Fetch multiple laptops by IDs with optional data inclusions
 */
export async function POST(request: NextRequest): Promise<NextResponse<BatchLaptopResponse | { error: string }>> {
  try {
    const body = await request.json()

    // Validate request using Zod schema
    const validationResult = validateBatchLaptopRequest(body)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid request format',
          details: validationResult.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code
          }))
        },
        { status: 400 }
      )
    }

    const {
      laptopIds,
      includeSpecs,
      includeCompatibility,
      includePricing,
      includeReviews,
      includeAvailability
    } = validationResult.data

    // Build Prisma include object based on options
    const include = {
      brand: true,
      ...(includeSpecs && {
        laptop_cpus: {
          include: {
            cpu: true
          }
        },
        laptop_gpus: {
          include: {
            gpu: true
          }
        },
        laptop_ram: true,
        laptop_storage: true,
        displays: true
      }),
      ...(includeCompatibility && {
        laptop_llm_compatibility: {
          include: {
            llm_model: true
          }
        }
      }),
      ...(includePricing && {
        laptop_listings: {
          where: {
            isActive: true
          },
          orderBy: {
            price: 'asc'
          },
          take: 5
        }
      })
    }

    // Fetch laptops from database
    const laptops = await prisma.laptops.findMany({
      where: {
        id: {
          in: laptopIds
        }
      },
      include
    })

    // Transform data to LaptopData format
    const transformedLaptops: LaptopData[] = laptops.map(laptop => {
      const baseData: LaptopData = {
        id: laptop.id,
        brand: laptop.brand?.name || '',
        model: laptop.model,
        category: laptop.category || '',
        releaseDate: laptop.releaseDate,
        isActive: laptop.isActive,
        createdAt: laptop.createdAt,
        updatedAt: laptop.updatedAt
      }

      // Add specifications if requested
      if (includeSpecs) {
        const cpuData = laptop.laptop_cpus?.[0]?.cpu
        const gpuData = laptop.laptop_gpus?.[0]?.gpu
        const ramData = laptop.laptop_ram?.[0]
        const storageData = laptop.laptop_storage
        const displayData = laptop.displays?.[0]

        baseData.specifications = {
          cpu: cpuData ? {
            manufacturer: cpuData.manufacturer || '',
            model: cpuData.model,
            cores: cpuData.cores || 0,
            threads: cpuData.threads || 0,
            baseClockGhz: cpuData.baseClockGhz || 0,
            maxClockGhz: cpuData.maxClockGhz || 0,
            architecture: cpuData.architecture || '',
            tdpWatts: cpuData.tdpWatts || 0,
            integratedGraphics: cpuData.integratedGraphics || false,
            releaseDate: cpuData.releaseDate
          } : {
            manufacturer: '',
            model: '',
            cores: 0,
            threads: 0,
            baseClockGhz: 0,
            maxClockGhz: 0,
            architecture: '',
            tdpWatts: 0,
            integratedGraphics: false
          },
          gpu: gpuData ? {
            manufacturer: gpuData.manufacturer || '',
            model: gpuData.model,
            memoryGb: gpuData.memoryGb || 0,
            memoryType: gpuData.memoryType || '',
            baseClock: gpuData.baseClock || 0,
            boostClock: gpuData.boostClock || 0,
            tdpWatts: gpuData.tdpWatts || 0,
            architecture: gpuData.architecture || '',
            releaseDate: gpuData.releaseDate
          } : {
            manufacturer: '',
            model: '',
            memoryGb: 0,
            memoryType: '',
            baseClock: 0,
            boostClock: 0,
            tdpWatts: 0,
            architecture: ''
          },
          memory: ramData ? [{
            type: ramData.type || '',
            capacityGb: ramData.capacityGb || 0,
            speed: ramData.speed || 0,
            slots: ramData.slots || 1,
            maxCapacityGb: ramData.maxCapacityGb || 0,
            isUpgradeable: ramData.isUpgradeable || false
          }] : [],
          storage: storageData ? storageData.map(storage => ({
            type: storage.type || '',
            capacityGb: storage.capacityGb || 0,
            interface: storage.interface || '',
            readSpeedMbps: storage.readSpeedMbps,
            writeSpeedMbps: storage.writeSpeedMbps
          })) : [],
          display: displayData ? {
            sizeInches: displayData.sizeInches || 0,
            resolution: displayData.resolution || '',
            refreshRate: displayData.refreshRate || 60,
            panelType: displayData.panelType || '',
            brightness: displayData.brightness || 0,
            colorGamut: displayData.colorGamut || '',
            touchscreen: displayData.touchscreen || false,
            hdr: displayData.hdr || false
          } : {
            sizeInches: 0,
            resolution: '',
            refreshRate: 60,
            panelType: '',
            brightness: 0,
            colorGamut: '',
            touchscreen: false,
            hdr: false
          },
          connectivity: {
            wifi: '',
            bluetooth: '',
            usb: [],
            hdmi: 0,
            ethernet: false,
            audioJack: false,
            sdCard: false,
            thunderbolt: 0
          },
          physical: {
            weightKg: 0,
            dimensions: {
              widthMm: 0,
              heightMm: 0,
              depthMm: 0
            },
            material: '',
            color: ''
          },
          battery: {
            capacityWh: 0,
            estimatedLifeHours: 0,
            fastCharging: false,
            chargingWatts: 0
          },
          thermal: {
            coolingType: '',
            fanCount: 0,
            heatPipes: 0,
            maxTempCelsius: 0
          }
        }
      }

      // Add compatibility if requested
      if (includeCompatibility && laptop.laptop_llm_compatibility) {
        const compatibilityScores = laptop.laptop_llm_compatibility.map(comp => ({
          modelId: comp.llmModelId,
          modelName: comp.llm_model?.name || '',
          overallScore: comp.overallScore || 0,
          memoryScore: comp.memoryScore || 0,
          computeScore: comp.computeScore || 0,
          gpuScore: comp.gpuScore || 0,
          storageScore: comp.storageScore || 0
        }))

        const averageScore = compatibilityScores.length > 0
          ? compatibilityScores.reduce((sum, score) => sum + score.overallScore, 0) / compatibilityScores.length
          : 0

        baseData.compatibility = {
          scores: compatibilityScores,
          averageScore,
          bestModels: compatibilityScores
            .sort((a, b) => b.overallScore - a.overallScore)
            .slice(0, 3)
            .map(score => ({
              id: score.modelId,
              name: score.modelName,
              category: '',
              parameterCount: '',
              memoryRequirements: {
                minGb: 0,
                recommendedGb: 0
              },
              computeRequirements: {
                minFlops: 0,
                recommendedFlops: 0
              },
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            }))
        }
      }

      // Add pricing if requested
      if (includePricing && laptop.laptop_listings) {
        const listings = laptop.laptop_listings
        const currentPrice = listings.length > 0 ? listings[0].price : 0
        
        baseData.pricing = {
          currentPrice,
          currency: listings.length > 0 ? listings[0].currency : 'USD',
          priceHistory: [],
          bestDeal: listings.length > 0 ? {
            id: listings[0].id,
            laptopId: laptop.id,
            sourceId: listings[0].sourceId,
            title: listings[0].title,
            price: listings[0].price,
            currency: listings[0].currency,
            url: listings[0].url,
            imageUrl: listings[0].imageUrl,
            condition: listings[0].condition || 'new',
            availability: listings[0].availability || 'in_stock',
            shipping: listings[0].shipping,
            seller: listings[0].seller,
            rating: listings[0].rating,
            reviewCount: listings[0].reviewCount,
            isActive: listings[0].isActive,
            lastChecked: listings[0].lastChecked,
            createdAt: listings[0].createdAt,
            updatedAt: listings[0].updatedAt
          } : undefined
        }
      }

      // Add reviews if requested
      if (includeReviews) {
        baseData.reviews = {
          averageRating: 0,
          totalReviews: 0,
          categories: {
            performance: 0,
            design: 0,
            value: 0,
            battery: 0,
            display: 0
          }
        }
      }

      // Add metadata
      baseData.metadata = {
        popularity: 0,
        trendingScore: 0,
        lastUpdated: laptop.updatedAt
      }

      return baseData
    })

    // Identify missing laptops
    const foundIds = laptops.map(laptop => laptop.id)
    const missingIds = laptopIds.filter(id => !foundIds.includes(id))

    const response: BatchLaptopResponse = {
      laptops: transformedLaptops,
      found: transformedLaptops.length,
      requested: laptopIds.length,
      missing: missingIds,
      errors: []
    }

    // Validate response before returning
    const responseValidation = validateBatchLaptopResponse(response)
    if (!responseValidation.success) {
      console.error('Response validation failed:', responseValidation.error)
      return NextResponse.json(
        { error: 'Internal server error: invalid response format' },
        { status: 500 }
      )
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Batch laptop fetch error:', error)
    
    return NextResponse.json(
      { error: 'Internal server error while fetching laptops' },
      { status: 500 }
    )
  }
}
