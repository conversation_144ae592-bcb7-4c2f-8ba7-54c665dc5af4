/**
 * Enhanced Scraping Statistics API
 * API endpoints for queue and job statistics
 */

import { NextRequest, NextResponse } from 'next/server'
import { EnhancedScrapingService } from '@/lib/scraping/enhanced-scraping-service'
import { enhancedScrapingConfig } from '@/lib/scraping/config/enhanced-scraping.config'

// Initialize the enhanced scraping service
let scrapingService: EnhancedScrapingService | null = null

async function getScrapingService(): Promise<EnhancedScrapingService> {
  if (!scrapingService) {
    scrapingService = new EnhancedScrapingService(enhancedScrapingConfig)
    await scrapingService.initialize()
  }
  return scrapingService
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange')
    const includeQueueStats = searchParams.get('includeQueueStats') !== 'false'
    const includeJobStats = searchParams.get('includeJobStats') !== 'false'

    const service = await getScrapingService()

    // Parse time range
    let timeRangeFilter: { from: Date; to: Date } | undefined
    if (timeRange) {
      const now = new Date()
      switch (timeRange) {
        case '1h':
          timeRangeFilter = {
            from: new Date(now.getTime() - 60 * 60 * 1000),
            to: now
          }
          break
        case '24h':
          timeRangeFilter = {
            from: new Date(now.getTime() - 24 * 60 * 60 * 1000),
            to: now
          }
          break
        case '7d':
          timeRangeFilter = {
            from: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
            to: now
          }
          break
        case '30d':
          timeRangeFilter = {
            from: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
            to: now
          }
          break
      }
    }

    const results: {
      timestamp: string;
      timeRange: string;
      queueStats?: Record<string, unknown>;
      jobStats?: Record<string, unknown>;
      summary?: Record<string, unknown>;
      errors?: string[];
    } = {
      timestamp: new Date().toISOString(),
      timeRange: timeRange || 'all'
    }

    // Get queue statistics
    if (includeQueueStats) {
      try {
        results.queueStats = await service.getQueueStats()
      } catch (error) {
        console.error('Failed to get queue stats:', error)
        results.queueStats = null
        results.errors = results.errors || []
        results.errors.push('Failed to get queue statistics')
      }
    }

    // Get job statistics
    if (includeJobStats) {
      try {
        results.jobStats = await service.getJobStats(timeRangeFilter)
      } catch (error) {
        console.error('Failed to get job stats:', error)
        results.jobStats = null
        results.errors = results.errors || []
        results.errors.push('Failed to get job statistics')
      }
    }

    // Calculate summary metrics
    if (results.queueStats && results.jobStats) {
      results.summary = {
        totalQueues: results.queueStats.length,
        totalActiveJobs: results.queueStats.reduce((sum: number, queue: { active: number }) => sum + queue.active, 0),
        totalWaitingJobs: results.queueStats.reduce((sum: number, queue: { waiting: number }) => sum + queue.waiting, 0),
        totalCompletedJobs: results.jobStats.byStatus.completed || 0,
        totalFailedJobs: results.jobStats.byStatus.failed || 0,
        overallSuccessRate: results.jobStats.successRate,
        averageProcessingTime: results.jobStats.averageProcessingTime,
        throughputPerHour: results.jobStats.throughput.perHour,
        healthStatus: calculateHealthStatus(results.queueStats, results.jobStats)
      }
    }

    return NextResponse.json({
      success: true,
      data: results
    })
  } catch (error) {
    console.error('Failed to get enhanced scraping statistics:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get enhanced scraping statistics' },
      { status: 500 }
    )
  }
}

function calculateHealthStatus(queueStats: { failed: number; active: number; waiting: number }[], jobStats: { successRate: number; averageProcessingTime: number }): 'healthy' | 'warning' | 'critical' {
  if (!queueStats || !jobStats) return 'warning'

  // Check for critical issues
  const totalFailed = queueStats.reduce((sum, queue) => sum + queue.failed, 0)
  const totalActive = queueStats.reduce((sum, queue) => sum + queue.active, 0)
  const totalWaiting = queueStats.reduce((sum, queue) => sum + queue.waiting, 0)
  
  // Critical conditions
  if (jobStats.successRate < 50) return 'critical'
  if (totalWaiting > 1000) return 'critical'
  if (totalFailed > totalActive * 2) return 'critical'

  // Warning conditions
  if (jobStats.successRate < 80) return 'warning'
  if (totalWaiting > 100) return 'warning'
  if (jobStats.averageProcessingTime > 300000) return 'warning' // 5 minutes

  return 'healthy'
}
