/**
 * Enhanced Scraping Batch Management API
 * API endpoints for managing batch scraping jobs
 */

import { NextRequest, NextResponse } from 'next/server'
import { EnhancedScrapingService } from '@/lib/scraping/enhanced-scraping-service'
import { enhancedScrapingConfig } from '@/lib/scraping/config/enhanced-scraping.config'

// Initialize the enhanced scraping service
let scrapingService: EnhancedScrapingService | null = null

async function getScrapingService(): Promise<EnhancedScrapingService> {
  if (!scrapingService) {
    scrapingService = new EnhancedScrapingService(enhancedScrapingConfig)
    await scrapingService.initialize()
  }
  return scrapingService
}

export async function GET(
  request: NextRequest,
  { params }: { params: { batchId: string } }
) {
  try {
    const { batchId } = params

    if (!batchId) {
      return NextResponse.json(
        { success: false, error: 'Batch ID is required' },
        { status: 400 }
      )
    }

    const service = await getScrapingService()
    const batchStatus = await service.getBatchStatus(batchId)

    return NextResponse.json({
      success: true,
      data: batchStatus
    })
  } catch (error) {
    console.error(`Failed to get batch status for ${params.batchId}:`, error)
    return NextResponse.json(
      { success: false, error: 'Failed to get batch status' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { batchId: string } }
) {
  try {
    const { batchId } = params

    if (!batchId) {
      return NextResponse.json(
        { success: false, error: 'Batch ID is required' },
        { status: 400 }
      )
    }

    const service = await getScrapingService()
    await service.cancelBatch(batchId)

    return NextResponse.json({
      success: true,
      data: {
        batchId,
        status: 'cancelled',
        message: 'Batch has been cancelled successfully'
      }
    })
  } catch (error) {
    console.error(`Failed to cancel batch ${params.batchId}:`, error)
    return NextResponse.json(
      { success: false, error: 'Failed to cancel batch' },
      { status: 500 }
    )
  }
}
