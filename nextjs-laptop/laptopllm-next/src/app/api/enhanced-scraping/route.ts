/**
 * Enhanced Scraping API
 * API endpoints for the enhanced scraping system with queue management
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/prisma'
import { EnhancedScrapingService } from '@/lib/scraping/enhanced-scraping-service'
import { enhancedScrapingConfig, createScrapingTarget } from '@/lib/scraping/config/enhanced-scraping.config'

// Initialize the enhanced scraping service
let scrapingService: EnhancedScrapingService | null = null

async function getScrapingService(): Promise<EnhancedScrapingService> {
  if (!scrapingService) {
    scrapingService = new EnhancedScrapingService(enhancedScrapingConfig)
    await scrapingService.initialize()
  }
  return scrapingService
}

// Validation schemas
const CreateJobSchema = z.object({
  sourceId: z.string().optional(),
  urls: z.array(z.string().url()).optional(),
  priority: z.enum(['low', 'normal', 'high', 'critical']).default('normal'),
  config: z.object({
    batchSize: z.number().min(1).max(100).optional(),
    maxConcurrent: z.number().min(1).max(10).optional(),
    delayBetweenBatches: z.number().min(0).optional(),
    enableScreenshots: z.boolean().optional(),
    saveToDatabase: z.boolean().optional(),
    timeout: z.number().min(1000).optional(),
    retryAttempts: z.number().min(0).max(10).optional(),
  }).optional(),
  delay: z.number().min(0).optional(),
}).refine(data => data.sourceId || (data.urls && data.urls.length > 0), {
  message: "Either sourceId or urls must be provided"
})

const BatchJobSchema = z.object({
  sourceIds: z.array(z.string()).optional(),
  urlGroups: z.array(z.array(z.string().url())).optional(),
  priority: z.enum(['low', 'normal', 'high', 'critical']).default('normal'),
  config: z.object({
    batchSize: z.number().min(1).max(100).optional(),
    maxConcurrent: z.number().min(1).max(10).optional(),
    delayBetweenBatches: z.number().min(0).optional(),
    enableScreenshots: z.boolean().optional(),
    saveToDatabase: z.boolean().optional(),
    timeout: z.number().min(1000).optional(),
    retryAttempts: z.number().min(0).max(10).optional(),
  }).optional(),
  staggerDelay: z.number().min(0).optional(),
}).refine(data => data.sourceIds || (data.urlGroups && data.urlGroups.length > 0), {
  message: "Either sourceIds or urlGroups must be provided"
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    const type = searchParams.get('type')
    const batchId = searchParams.get('batchId')
    const priority = searchParams.get('priority')

    const service = await getScrapingService()

    // Get jobs from the enhanced job manager
    const jobs = await prisma.scrapingJob.findMany({
      where: {
        ...(status && { status }),
        ...(type && { type }),
        ...(batchId && { batchId }),
        ...(priority && { priority }),
      },
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit,
    })

    const total = await prisma.scrapingJob.count({
      where: {
        ...(status && { status }),
        ...(type && { type }),
        ...(batchId && { batchId }),
        ...(priority && { priority }),
      }
    })

    // Get queue and job statistics
    const [queueStats, jobStats] = await Promise.all([
      service.getQueueStats(),
      service.getJobStats()
    ])

    return NextResponse.json({
      success: true,
      data: {
        jobs,
        queueStats,
        jobStats,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasMore: (page - 1) * limit + jobs.length < total
        }
      }
    })
  } catch (error) {
    console.error('Failed to fetch enhanced scraping jobs:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch enhanced scraping jobs' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = CreateJobSchema.parse(body)

    const service = await getScrapingService()

    let targets: { url: string; siteType: string; }[] = []

    if (validatedData.sourceId) {
      // Get the source information
      const source = await prisma.scrapingSource.findUnique({
        where: { id: validatedData.sourceId }
      })

      if (!source) {
        return NextResponse.json(
          { success: false, error: 'Source not found' },
          { status: 404 }
        )
      }

      if (!source.isActive) {
        return NextResponse.json(
          { success: false, error: 'Source is not active' },
          { status: 400 }
        )
      }

      // Determine site type from URL
      const siteType = determineSiteType(source.url)
      targets = [createScrapingTarget(source.url, siteType)]

    } else if (validatedData.urls && validatedData.urls.length > 0) {
      // Create targets from provided URLs
      targets = validatedData.urls.map(url => {
        const siteType = determineSiteType(url)
        return createScrapingTarget(url, siteType)
      })
    }

    // Submit the scraping job
    const jobId = await service.submitScrapingJob(targets, {
      priority: validatedData.priority,
      config: validatedData.config,
      sourceId: validatedData.sourceId,
      delay: validatedData.delay,
    })

    console.log(`🚀 Enhanced scraping job submitted: ${jobId}`)
    console.log(`📋 Targets: ${targets.length}`)
    console.log(`🔗 URLs: ${targets.map(t => t.url).join(', ')}`)

    return NextResponse.json({
      success: true,
      data: {
        jobId,
        targets: targets.length,
        priority: validatedData.priority,
        status: 'queued',
        message: 'Enhanced scraping job has been queued successfully'
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Failed to create enhanced scraping job:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create enhanced scraping job' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = BatchJobSchema.parse(body)

    const service = await getScrapingService()

    let targetGroups: { url: string; siteType: string; }[][] = []

    if (validatedData.sourceIds && validatedData.sourceIds.length > 0) {
      // Get sources and create target groups
      const sources = await prisma.scrapingSource.findMany({
        where: {
          id: { in: validatedData.sourceIds },
          isActive: true
        }
      })

      if (sources.length === 0) {
        return NextResponse.json(
          { success: false, error: 'No active sources found' },
          { status: 404 }
        )
      }

      targetGroups = sources.map(source => {
        const siteType = determineSiteType(source.url)
        return [createScrapingTarget(source.url, siteType)]
      })

    } else if (validatedData.urlGroups && validatedData.urlGroups.length > 0) {
      // Create target groups from provided URL groups
      targetGroups = validatedData.urlGroups.map(urls => 
        urls.map(url => {
          const siteType = determineSiteType(url)
          return createScrapingTarget(url, siteType)
        })
      )
    }

    // Submit the batch scraping jobs
    const jobIds = await service.submitBatchScrapingJobs(targetGroups, {
      priority: validatedData.priority,
      config: validatedData.config,
      staggerDelay: validatedData.staggerDelay,
    })

    console.log(`🚀 Enhanced batch scraping jobs submitted: ${jobIds.length} jobs`)
    console.log(`📋 Total targets: ${targetGroups.reduce((sum, group) => sum + group.length, 0)}`)

    return NextResponse.json({
      success: true,
      data: {
        jobIds,
        batchSize: jobIds.length,
        totalTargets: targetGroups.reduce((sum, group) => sum + group.length, 0),
        priority: validatedData.priority,
        status: 'queued',
        message: 'Enhanced batch scraping jobs have been queued successfully'
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Failed to create enhanced batch scraping jobs:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create enhanced batch scraping jobs' },
      { status: 500 }
    )
  }
}

function determineSiteType(url: string): string {
  try {
    const hostname = new URL(url).hostname.toLowerCase()
    
    if (hostname.includes('amazon')) return 'amazon'
    if (hostname.includes('bestbuy')) return 'bestbuy'
    if (hostname.includes('newegg')) return 'newegg'
    if (hostname.includes('microcenter')) return 'microcenter'
    if (hostname.includes('bhphotovideo') || hostname.includes('bhphoto')) return 'bhphoto'
    if (hostname.includes('adorama')) return 'adorama'
    
    return 'generic'
  } catch {
    return 'generic'
  }
}
