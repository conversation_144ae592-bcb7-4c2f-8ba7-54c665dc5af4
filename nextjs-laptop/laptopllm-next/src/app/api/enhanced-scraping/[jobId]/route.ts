/**
 * Enhanced Scraping Job Management API
 * API endpoints for managing individual scraping jobs
 */

import { NextRequest, NextResponse } from 'next/server'
import { EnhancedScrapingService } from '@/lib/scraping/enhanced-scraping-service'
import { enhancedScrapingConfig } from '@/lib/scraping/config/enhanced-scraping.config'

// Initialize the enhanced scraping service
let scrapingService: EnhancedScrapingService | null = null

async function getScrapingService(): Promise<EnhancedScrapingService> {
  if (!scrapingService) {
    scrapingService = new EnhancedScrapingService(enhancedScrapingConfig)
    await scrapingService.initialize()
  }
  return scrapingService
}

export async function GET(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  try {
    const { jobId } = params

    if (!jobId) {
      return NextResponse.json(
        { success: false, error: 'Job ID is required' },
        { status: 400 }
      )
    }

    const service = await getScrapingService()
    const jobStatus = await service.getJobStatus(jobId)

    if (!jobStatus.database && !jobStatus.queue) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        jobId,
        database: jobStatus.database,
        queue: jobStatus.queue,
        status: jobStatus.database?.status || 'unknown',
        progress: jobStatus.queue?.progress || 0,
        createdAt: jobStatus.database?.createdAt,
        startedAt: jobStatus.database?.startedAt,
        completedAt: jobStatus.database?.completedAt,
        error: jobStatus.database?.error,
        result: jobStatus.database?.result,
        metrics: jobStatus.database?.metrics
      }
    })
  } catch (error) {
    console.error(`Failed to get job status for ${params.jobId}:`, error)
    return NextResponse.json(
      { success: false, error: 'Failed to get job status' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  try {
    const { jobId } = params

    if (!jobId) {
      return NextResponse.json(
        { success: false, error: 'Job ID is required' },
        { status: 400 }
      )
    }

    const service = await getScrapingService()
    await service.cancelJob(jobId)

    return NextResponse.json({
      success: true,
      data: {
        jobId,
        status: 'cancelled',
        message: 'Job has been cancelled successfully'
      }
    })
  } catch (error) {
    console.error(`Failed to cancel job ${params.jobId}:`, error)
    return NextResponse.json(
      { success: false, error: 'Failed to cancel job' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  try {
    const { jobId } = params
    const body = await request.json()
    const { action } = body

    if (!jobId) {
      return NextResponse.json(
        { success: false, error: 'Job ID is required' },
        { status: 400 }
      )
    }

    if (!action) {
      return NextResponse.json(
        { success: false, error: 'Action is required' },
        { status: 400 }
      )
    }

    const service = await getScrapingService()

    switch (action) {
      case 'cancel':
        await service.cancelJob(jobId)
        return NextResponse.json({
          success: true,
          data: {
            jobId,
            action: 'cancelled',
            message: 'Job has been cancelled successfully'
          }
        })

      case 'retry':
        // For retry, we would need to implement retry logic
        // This is a placeholder for now
        return NextResponse.json({
          success: false,
          error: 'Retry functionality not yet implemented'
        }, { status: 501 })

      default:
        return NextResponse.json(
          { success: false, error: `Unknown action: ${action}` },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error(`Failed to perform action on job ${params.jobId}:`, error)
    return NextResponse.json(
      { success: false, error: 'Failed to perform action on job' },
      { status: 500 }
    )
  }
}
