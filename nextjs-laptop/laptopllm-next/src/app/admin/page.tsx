'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { MainLayout } from '@/components/layout/main-layout'
import {
  Activity,
  Database,
  Globe,
  Settings,
  Users,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Clock,
  ExternalLink,
  RefreshCw,
  Server,
  Zap
} from 'lucide-react'

interface SystemHealth {
  healthy: boolean
  scrapers: {
    crawl4ai: boolean
    firecrawl: boolean
    puppeteer: boolean
  }
  timestamp: Date
}

interface AdminStats {
  totalLaptops: number
  totalSources: number
  activeJobs: number
  systemHealth: 'healthy' | 'warning' | 'critical'
}

export default function AdminDashboard() {
  const [health, setHealth] = useState<SystemHealth | null>(null)
  const [stats, setStats] = useState<AdminStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())

  const fetchHealthStatus = async () => {
    try {
      const response = await fetch('/api/crawl')
      if (response.ok) {
        const data = await response.json()
        setHealth(data.health || null)
      }
    } catch (error) {
      console.error('Failed to fetch health status:', error)
    }
  }

  const fetchStats = async () => {
    try {
      // Mock stats for now - replace with actual API calls
      setStats({
        totalLaptops: 1250,
        totalSources: 15,
        activeJobs: 3,
        systemHealth: health?.healthy ? 'healthy' : 'warning'
      })
    } catch (error) {
      console.error('Failed to fetch stats:', error)
    }
  }

  const refreshData = async () => {
    setLoading(true)
    await Promise.all([fetchHealthStatus(), fetchStats()])
    setLastRefresh(new Date())
    setLoading(false)
  }

  useEffect(() => {
    refreshData()
    // Auto-refresh every 30 seconds
    const interval = setInterval(refreshData, 30000)
    return () => clearInterval(interval)
  }, [])

  const getHealthStatus = () => {
    if (!health) return { status: 'unknown', color: 'gray', icon: Clock }
    
    const healthyCount = Object.values(health.scrapers).filter(Boolean).length
    
    if (healthyCount === 3) return { status: 'healthy', color: 'green', icon: CheckCircle }
    if (healthyCount >= 1) return { status: 'warning', color: 'yellow', icon: AlertTriangle }
    return { status: 'critical', color: 'red', icon: AlertTriangle }
  }

  const healthStatus = getHealthStatus()
  const HealthIcon = healthStatus.icon

  const adminSections = [
    {
      title: 'Scraping Management',
      description: 'Monitor and test web scraping services',
      icon: Globe,
      href: '/admin/crawl-test',
      status: health?.healthy ? 'operational' : 'warning',
      items: [
        'Test scraping services',
        'Monitor scraper health',
        'View scraping logs',
        'Configure scraping targets'
      ]
    },
    {
      title: 'Data Management',
      description: 'Manage laptop data and sources',
      icon: Database,
      href: '/admin/data',
      status: 'coming-soon',
      items: [
        'View laptop database',
        'Manage data sources',
        'Data quality reports',
        'Import/Export data'
      ]
    },
    {
      title: 'System Monitoring',
      description: 'Monitor system performance and health',
      icon: Activity,
      href: '/admin/monitoring',
      status: 'coming-soon',
      items: [
        'System metrics',
        'Performance analytics',
        'Error tracking',
        'Resource usage'
      ]
    },
    {
      title: 'User Management',
      description: 'Manage users and permissions',
      icon: Users,
      href: '/admin/users',
      status: 'coming-soon',
      items: [
        'User accounts',
        'Role management',
        'Access control',
        'Activity logs'
      ]
    },
    {
      title: 'Analytics',
      description: 'View usage analytics and reports',
      icon: BarChart3,
      href: '/admin/analytics',
      status: 'coming-soon',
      items: [
        'Usage statistics',
        'Search analytics',
        'Performance reports',
        'Custom dashboards'
      ]
    },
    {
      title: 'System Settings',
      description: 'Configure system settings and preferences',
      icon: Settings,
      href: '/admin/settings',
      status: 'coming-soon',
      items: [
        'General settings',
        'API configuration',
        'Feature toggles',
        'Maintenance mode'
      ]
    }
  ]

  return (
    <MainLayout>
      <div className="min-h-screen bg-background">
        {/* Admin Header */}
        <div className="border-b bg-muted/30">
          <div className="container flex h-16 items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Server className="h-6 w-6 text-primary" />
                <h1 className="text-2xl font-bold">Admin Dashboard</h1>
              </div>
              <Badge variant="outline" className="text-xs">
                System Management
              </Badge>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Clock className="h-4 w-4" />
                <span>Last updated: {lastRefresh.toLocaleTimeString()}</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={refreshData}
                disabled={loading}
                className="flex items-center space-x-2"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                <span>Refresh</span>
              </Button>
            </div>
          </div>
        </div>

        <div className="container py-8">
        {/* System Status Overview */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">System Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">System Health</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <HealthIcon className={`h-4 w-4 text-${healthStatus.color}-500`} />
                      <span className="text-2xl font-bold capitalize">{healthStatus.status}</span>
                    </div>
                  </div>
                  <Zap className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Laptops</p>
                    <p className="text-2xl font-bold">{stats?.totalLaptops?.toLocaleString() || '---'}</p>
                  </div>
                  <Database className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Data Sources</p>
                    <p className="text-2xl font-bold">{stats?.totalSources || '---'}</p>
                  </div>
                  <Globe className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Active Jobs</p>
                    <p className="text-2xl font-bold">{stats?.activeJobs || '---'}</p>
                  </div>
                  <Activity className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Scraper Status */}
          {health && (
            <Alert className="mb-6">
              <HealthIcon className="h-4 w-4" />
              <AlertDescription>
                <div className="flex items-center justify-between">
                  <span>
                    Scraper Status: 
                    <Badge className="ml-2" variant={health.scrapers.crawl4ai ? "default" : "secondary"}>
                      Crawl4AI {health.scrapers.crawl4ai ? '✓' : '✗'}
                    </Badge>
                    <Badge className="ml-2" variant={health.scrapers.firecrawl ? "default" : "secondary"}>
                      Firecrawl {health.scrapers.firecrawl ? '✓' : '✗'}
                    </Badge>
                    <Badge className="ml-2" variant={health.scrapers.puppeteer ? "default" : "secondary"}>
                      Puppeteer {health.scrapers.puppeteer ? '✓' : '✗'}
                    </Badge>
                  </span>
                  <Link href="/admin/crawl-test">
                    <Button variant="outline" size="sm">
                      Test Scrapers <ExternalLink className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Admin Sections */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Administration</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {adminSections.map((section) => {
              const Icon = section.icon
              const isAvailable = section.status === 'operational'
              const isComingSoon = section.status === 'coming-soon'
              
              return (
                <Card key={section.title} className={`transition-all hover:shadow-md ${!isAvailable ? 'opacity-75' : ''}`}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${isAvailable ? 'bg-primary/10' : 'bg-muted'}`}>
                          <Icon className={`h-5 w-5 ${isAvailable ? 'text-primary' : 'text-muted-foreground'}`} />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{section.title}</CardTitle>
                          <CardDescription className="text-sm">{section.description}</CardDescription>
                        </div>
                      </div>
                      {isComingSoon && (
                        <Badge variant="secondary" className="text-xs">
                          Coming Soon
                        </Badge>
                      )}
                      {section.status === 'warning' && (
                        <Badge variant="destructive" className="text-xs">
                          Warning
                        </Badge>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm text-muted-foreground mb-4">
                      {section.items.map((item, index) => (
                        <li key={index} className="flex items-center space-x-2">
                          <div className="w-1 h-1 bg-muted-foreground rounded-full" />
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>
                    
                    {isAvailable ? (
                      <Link href={section.href}>
                        <Button className="w-full">
                          Access {section.title}
                          <ExternalLink className="ml-2 h-4 w-4" />
                        </Button>
                      </Link>
                    ) : (
                      <Button disabled className="w-full">
                        {isComingSoon ? 'Coming Soon' : 'Unavailable'}
                      </Button>
                    )}
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        <Separator className="my-8" />

        {/* Quick Actions */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="flex flex-wrap gap-4">
            <Link href="/admin/crawl-test">
              <Button variant="outline">
                <Globe className="mr-2 h-4 w-4" />
                Test Scraping
              </Button>
            </Link>
            <Link href="/dashboard">
              <Button variant="outline">
                <BarChart3 className="mr-2 h-4 w-4" />
                View Dashboard
              </Button>
            </Link>
            <Link href="/">
              <Button variant="outline">
                <ExternalLink className="mr-2 h-4 w-4" />
                Go to Main Site
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
    </MainLayout>
  )
}
