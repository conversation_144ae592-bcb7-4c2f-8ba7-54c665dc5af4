/**
 * Admin Scraping Management Page
 * Main page for accessing the comprehensive scraping management dashboard
 */

import { Metadata } from 'next'
import { ScrapingManagementDashboard } from '@/features/scraper/components/scraping-management-dashboard'

export const metadata: Metadata = {
  title: 'Scraping Management | LaptopLLM Admin',
  description: 'Comprehensive dashboard for monitoring and managing scraping operations',
}

export default function ScrapingManagementPage() {
  return (
    <div className="container mx-auto py-6">
      <ScrapingManagementDashboard />
    </div>
  )
}
