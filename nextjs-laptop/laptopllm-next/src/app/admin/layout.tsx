import React from 'react'
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Admin Dashboard - LaptopLLM Finder',
  description: 'Administrative dashboard for LaptopLLM Finder system management',
  robots: 'noindex, nofollow', // Prevent search engine indexing of admin pages
}

interface AdminLayoutProps {
  children: React.ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  return (
    <div className="admin-layout">
      {children}
    </div>
  )
}
