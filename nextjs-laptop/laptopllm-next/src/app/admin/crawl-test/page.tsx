"use client";

import React, { useState, useEffect } from 'react';

// Define a more specific type for the crawl result
interface CrawlResult {
  success: boolean;
  data?: {
    type: 'laptop_extraction' | 'general_scraping';
    url: string;
    content?: string;
    title?: string;
    metadata?: Record<string, unknown>;
    laptopData?: Record<string, unknown>;
    scraper?: string;
    duration?: number;
    scrapedAt?: string;
    extractedAt?: string;
  };
  error?: string;
  details?: Record<string, unknown>;
  scraper?: string;
  duration?: number;
}

interface ScraperHealth {
  success: boolean;
  health?: {
    healthy: boolean;
    scrapers: {
      crawl4ai: boolean;
      firecrawl: boolean;
      puppeteer: boolean;
    };
    timestamp: string;
  };
  scrapers?: {
    crawl4ai: boolean;
    firecrawl: boolean;
    puppeteer: boolean;
  };
  hierarchy?: string[];
}

export default function CrawlTestPage() {
  const [url, setUrl] = useState<string>('https://www.amazon.com/dp/B0CX23V2ZK');
  const [siteType, setSiteType] = useState<string>('amazon');
  const [extractLaptopData, setExtractLaptopData] = useState<boolean>(true);
  const [result, setResult] = useState<CrawlResult | null>(null);
  const [health, setHealth] = useState<ScraperHealth | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/crawl', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          url,
          siteType,
          extractLaptopData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error en la solicitud');
      }

      const data = await response.json();
      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Ocurrió un error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  const checkHealth = async () => {
    try {
      const response = await fetch('/api/crawl', {
        method: 'GET',
      });
      const data: ScraperHealth = await response.json();
      setHealth(data);
    } catch (err) {
      console.error('Error checking health:', err);
    }
  };

  // Check health on component mount
  useEffect(() => {
    checkHealth();
  }, []);

  return (
    <div style={{ padding: '2rem' }}>
      <h1>Prueba de Integración con Crawl4AI</h1>

      {/* Health Status */}
      {health && (
        <div style={{
          marginBottom: '2rem',
          padding: '1rem',
          backgroundColor: health.health?.healthy ? '#d4edda' : '#f8d7da',
          border: `1px solid ${health.health?.healthy ? '#c3e6cb' : '#f5c6cb'}`,
          borderRadius: '4px'
        }}>
          <h3>Estado de los Scrapers</h3>
          <p>Estado general: {health.health?.healthy ? '✅ Saludable' : '❌ No saludable'}</p>
          <ul>
            <li>Crawl4AI (Primario): {health.health?.scrapers.crawl4ai ? '✅' : '❌'}</li>
            <li>Firecrawl (Secundario): {health.health?.scrapers.firecrawl ? '✅' : '❌'}</li>
            <li>Puppeteer (Respaldo): {health.health?.scrapers.puppeteer ? '✅' : '❌'}</li>
          </ul>
          <p>Jerarquía: {health.hierarchy?.join(' → ')}</p>
          <button onClick={checkHealth} style={{ padding: '0.25rem 0.5rem' }}>
            Actualizar Estado
          </button>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: '1rem' }}>
          <label htmlFor="url">URL:</label>
          <input
            id="url"
            type="url"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="Ingresa la URL a crawlear"
            style={{ width: '100%', padding: '0.5rem', marginTop: '0.25rem' }}
            required
          />
        </div>

        <div style={{ marginBottom: '1rem' }}>
          <label htmlFor="siteType">Tipo de Sitio:</label>
          <select
            id="siteType"
            value={siteType}
            onChange={(e) => setSiteType(e.target.value)}
            style={{ width: '100%', padding: '0.5rem', marginTop: '0.25rem' }}
          >
            <option value="generic">Genérico</option>
            <option value="amazon">Amazon</option>
            <option value="bestbuy">Best Buy</option>
            <option value="newegg">Newegg</option>
            <option value="microcenter">Micro Center</option>
            <option value="bhphoto">B&H Photo</option>
            <option value="adorama">Adorama</option>
          </select>
        </div>

        <div style={{ marginBottom: '1rem' }}>
          <label>
            <input
              type="checkbox"
              checked={extractLaptopData}
              onChange={(e) => setExtractLaptopData(e.target.checked)}
              style={{ marginRight: '0.5rem' }}
            />
            Extraer datos específicos de laptop
          </label>
        </div>

        <button
          type="submit"
          disabled={isLoading}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: isLoading ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          {isLoading ? 'Crawleando...' : 'Iniciar Crawl'}
        </button>
      </form>

      {error && <div style={{ color: 'red', marginTop: '1rem' }}>Error: {error}</div>}
      
      {result && (
        <div style={{ marginTop: '2rem' }}>
          <h2>Resultado del Rastreo</h2>

          {result.success ? (
            <div>
              <div style={{
                marginBottom: '1rem',
                padding: '1rem',
                backgroundColor: '#d4edda',
                border: '1px solid #c3e6cb',
                borderRadius: '4px'
              }}>
                <h3>✅ Scraping Exitoso</h3>
                <p><strong>Scraper usado:</strong> {result.data?.scraper || result.scraper}</p>
                <p><strong>Duración:</strong> {result.data?.duration || result.duration}ms</p>
                <p><strong>Tipo:</strong> {result.data?.type}</p>
                {result.data?.title && <p><strong>Título:</strong> {result.data.title}</p>}
              </div>

              {result.data?.type === 'laptop_extraction' && result.data.laptopData && (
                <div style={{ marginBottom: '1rem' }}>
                  <h3>📱 Datos de Laptop Extraídos</h3>
                  <pre style={{
                    background: '#e7f3ff',
                    padding: '1rem',
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-word',
                    borderRadius: '4px',
                    border: '1px solid #b3d9ff'
                  }}>
                    {JSON.stringify(result.data.laptopData, null, 2)}
                  </pre>
                </div>
              )}

              {result.data?.content && (
                <div style={{ marginBottom: '1rem' }}>
                  <h3>📄 Contenido Extraído</h3>
                  <div style={{
                    background: '#f8f9fa',
                    padding: '1rem',
                    maxHeight: '300px',
                    overflow: 'auto',
                    borderRadius: '4px',
                    border: '1px solid #dee2e6'
                  }}>
                    <p>{result.data.content.substring(0, 1000)}...</p>
                  </div>
                </div>
              )}

              <details style={{ marginTop: '1rem' }}>
                <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
                  🔍 Ver Datos Completos (JSON)
                </summary>
                <pre style={{
                  background: '#f4f4f4',
                  padding: '1rem',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-all',
                  marginTop: '0.5rem',
                  borderRadius: '4px'
                }}>
                  {JSON.stringify(result, null, 2)}
                </pre>
              </details>
            </div>
          ) : (
            <div style={{
              padding: '1rem',
              backgroundColor: '#f8d7da',
              border: '1px solid #f5c6cb',
              borderRadius: '4px'
            }}>
              <h3>❌ Error en el Scraping</h3>
              <p><strong>Error:</strong> {result.error}</p>
              {result.scraper && <p><strong>Último scraper intentado:</strong> {result.scraper}</p>}
              {result.duration && <p><strong>Duración:</strong> {result.duration}ms</p>}

              {result.details && (
                <details style={{ marginTop: '1rem' }}>
                  <summary style={{ cursor: 'pointer' }}>Ver detalles del error</summary>
                  <pre style={{
                    background: '#fff',
                    padding: '0.5rem',
                    marginTop: '0.5rem',
                    borderRadius: '4px'
                  }}>
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
