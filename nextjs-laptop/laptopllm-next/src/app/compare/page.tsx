/**
 * Laptop Comparison Page
 * Side-by-side comparison interface for laptops
 */

'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  Award, 
  Plus, 
  Search, 
  Download,
  Share,
  BarChart3,
  TrendingUp,
  Zap,
  DollarSign,
  Info,
  Lightbulb
} from 'lucide-react'
import { LaptopComparison } from '@/features/laptops/components/laptop-comparison'
import { useLaptopComparison } from '@/features/laptops/hooks/use-laptop-comparison'
import { useLaptopSearch } from '@/features/laptops/hooks/use-laptop-search'

export default function ComparePage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [isSearchDialogOpen, setIsSearchDialogOpen] = useState(false)

  const {
    comparedLaptops,
    comparisonCount,
    canAddMore,
    hasComparison,
    comparisonStats,
    addLaptop,
    removeLaptop,
    clearComparison,
    getBestLaptop,
    getRecommendations,
    exportComparison,
    maxLaptops
  } = useLaptopComparison()

  const {
    laptops: searchResults,
    isLoading: isSearching,
    updateQuery,
    resetSearch
  } = useLaptopSearch({
    limit: 20,
    sortBy: 'compatibility',
    sortOrder: 'desc'
  })

  const handleAddLaptop = () => {
    setIsSearchDialogOpen(true)
  }

  const handleSelectLaptop = (laptop: LaptopData) => {
    addLaptop(laptop)
    setIsSearchDialogOpen(false)
    setSearchQuery('')
    resetSearch()
  }

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    updateQuery(query)
  }

  const recommendations = getRecommendations()

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center">
            <Award className="h-8 w-8 mr-3" />
            Laptop Comparison
          </h1>
          <p className="text-muted-foreground">
            Compare laptops side by side to find the perfect match for your needs
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {hasComparison && (
            <>
              <Button variant="outline" size="sm" onClick={exportComparison}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                <Share className="h-4 w-4 mr-2" />
                Share
              </Button>
              <Button variant="destructive" size="sm" onClick={clearComparison}>
                Clear All
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Quick Stats */}
      {hasComparison && comparisonStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Price Range</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ${comparisonStats.priceRange.min.toLocaleString()} - ${comparisonStats.priceRange.max.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                Avg: ${Math.round(comparisonStats.priceRange.avg).toLocaleString()}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">LLM Compatibility</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(comparisonStats.compatibilityRange.min)} - {Math.round(comparisonStats.compatibilityRange.max)}
              </div>
              <p className="text-xs text-muted-foreground">
                Avg: {Math.round(comparisonStats.compatibilityRange.avg)}/100
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Memory Range</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {comparisonStats.memoryRange.min}GB - {comparisonStats.memoryRange.max}GB
              </div>
              <p className="text-xs text-muted-foreground">
                Avg: {Math.round(comparisonStats.memoryRange.avg)}GB
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Brands</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{comparisonStats.brands.length}</div>
              <p className="text-xs text-muted-foreground">
                {comparisonStats.brands.slice(0, 2).join(', ')}
                {comparisonStats.brands.length > 2 && '...'}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Lightbulb className="h-5 w-5 mr-2" />
              Recommendations
            </CardTitle>
            <CardDescription>
              Based on your comparison, here are our top picks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {recommendations.map((rec, index) => (
                <div key={index} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="outline" className="capitalize">
                      {rec.type.replace('-', ' ')}
                    </Badge>
                    <span className="text-sm font-medium">
                      ${rec.laptop.price?.toLocaleString() || 'N/A'}
                    </span>
                  </div>
                  <h4 className="font-medium mb-1">{rec.laptop.title}</h4>
                  <p className="text-sm text-muted-foreground">{rec.reason}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Comparison */}
      <LaptopComparison
        laptops={comparedLaptops}
        onRemoveLaptop={removeLaptop}
        onAddLaptop={handleAddLaptop}
        maxLaptops={maxLaptops}
      />

      {/* Getting Started Guide */}
      {!hasComparison && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Info className="h-5 w-5 mr-2" />
              How to Compare Laptops
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Search className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-medium mb-2">1. Search & Add</h3>
                <p className="text-sm text-muted-foreground">
                  Search for laptops and add them to your comparison list
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                  <BarChart3 className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-medium mb-2">2. Compare Specs</h3>
                <p className="text-sm text-muted-foreground">
                  View detailed specifications and LLM compatibility scores
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Award className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-medium mb-2">3. Make Decision</h3>
                <p className="text-sm text-muted-foreground">
                  Get recommendations and choose the best laptop for your needs
                </p>
              </div>
            </div>
            <div className="text-center mt-6">
              <Button onClick={handleAddLaptop}>
                <Plus className="h-4 w-4 mr-2" />
                Start Comparing Laptops
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search Dialog */}
      <Dialog open={isSearchDialogOpen} onOpenChange={setIsSearchDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add Laptop to Comparison</DialogTitle>
            <DialogDescription>
              Search for laptops to add to your comparison. You can compare up to {maxLaptops} laptops.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search laptops by name, brand, or specifications..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>

            {searchQuery && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    {searchResults.length} results found
                  </span>
                  {isSearching && (
                    <span className="text-sm text-muted-foreground">Searching...</span>
                  )}
                </div>

                <div className="grid gap-2 max-h-96 overflow-y-auto">
                  {searchResults.map((laptop) => (
                    <div
                      key={laptop.id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 cursor-pointer"
                      onClick={() => handleSelectLaptop(laptop)}
                    >
                      <div className="flex items-center space-x-3">
                        {laptop.images && laptop.images.length > 0 && (
                          <img
                            src={laptop.images[0]}
                            alt={laptop.title}
                            className="w-12 h-12 object-cover rounded"
                          />
                        )}
                        <div>
                          <h4 className="font-medium">{laptop.title}</h4>
                          <p className="text-sm text-muted-foreground">{laptop.brand}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">${laptop.price?.toLocaleString() || 'N/A'}</div>
                        {laptop.compatibility?.averageScore && (
                          <Badge variant="secondary" className="text-xs">
                            <Zap className="h-3 w-3 mr-1" />
                            {laptop.compatibility.averageScore}/100
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {!searchQuery && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Start typing to search for laptops. You can search by brand, model name, or specifications.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
