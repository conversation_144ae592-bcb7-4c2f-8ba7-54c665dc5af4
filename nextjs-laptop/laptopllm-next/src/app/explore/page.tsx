/**
 * Laptop Explorer Page
 * Advanced laptop search and exploration interface
 */

import { Metadata } from 'next'
import { LaptopExplorer } from '@/features/laptops/components/laptop-explorer'

export const metadata: Metadata = {
  title: 'Explore Laptops | LaptopLLM Finder',
  description: 'Advanced laptop search and exploration tools with comprehensive filters, sorting, and LLM compatibility analysis',
  keywords: [
    'laptop search',
    'laptop explorer',
    'laptop finder',
    'laptop comparison',
    'laptop filters',
    'laptop specifications',
    'LLM compatible laptops',
    'laptop recommendations',
    'laptop database',
    'laptop analytics'
  ],
}

export default function ExplorePage() {
  return (
    <div className="container mx-auto py-6">
      <LaptopExplorer />
    </div>
  )
}
