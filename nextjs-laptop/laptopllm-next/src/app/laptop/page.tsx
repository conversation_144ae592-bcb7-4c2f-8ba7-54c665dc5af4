// Laptop Details Page - Detailed view of a specific laptop

'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  ArrowLeft,
  ExternalLink,
  Heart,
  Share2,
  ShoppingCart,
  Cpu,
  HardDrive,
  Monitor,
  Zap,
  Thermometer,
  CheckCircle,
  AlertTriangle,
  Info,
  Star,
  TrendingUp,
  BarChart3
} from 'lucide-react'
import {
  useLaptopDetails,
  useLaptopAvailability,
  useLaptopPriceHistory,
  useSimilarLaptops,
  type Laptop,
  type LaptopAvailability,
  type PriceHistoryEntry
} from '@/features/laptops'
import {
  useCompatibilityScore,
  useModelRecommendations,
  usePerformanceEstimation,
  type CompatibilityAnalysis,
  type ModelRecommendation,
  type PerformanceBenchmark
} from '@/features/llm-compatibility'
import { useComparison, useFavorites } from '@/contexts/app-context'




export default function LaptopDetailsPage() {
  const params = useParams()
  const [activeTab, setActiveTab] = useState('overview')

  // Get laptop ID from params
  const laptopId = params.id as string

  // Feature-based hooks
  const { laptop, isLoading, error } = useLaptopDetails(laptopId)
  const { data: availability } = useLaptopAvailability(laptopId)
  const { data: priceHistory } = useLaptopPriceHistory(laptopId)
  const { data: similarLaptops } = useSimilarLaptops(laptopId, 4)

  // LLM Compatibility hooks
  const { data: compatibilityScore } = useCompatibilityScore(laptopId, 'llama2-7b')
  const { data: modelRecommendations } = useModelRecommendations(laptopId)
  const { data: performanceEstimation } = usePerformanceEstimation(laptopId, 'llama2-7b')

  // Context hooks
  const comparison = useComparison()
  const favorites = useFavorites()

  // Check if laptop is in favorites or comparison
  const isFavorite = laptop ? favorites.has(laptop.url) : false
  const isInComparison = laptop ? comparison.has(laptop.url) : false

  // Handlers
  const handleToggleFavorite = () => {
    if (laptop) {
      favorites.toggle(laptop.url)
    }
  }

  const handleToggleComparison = () => {
    if (laptop) {
      if (isInComparison) {
        comparison.remove(laptop.url)
      } else if (!comparison.isFull) {
        comparison.add(laptop)
      }
    }
  }

  const formatPrice = (price: number) => {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    })
    return formatter.format(price)
  }

  // Loading state
  if (isLoading) {
    return (
      <MainLayout>
        <div className="min-h-screen bg-background">
          <div className="container mx-auto px-4 py-8">
            <div className="text-center">Loading laptop details...</div>
          </div>
        </div>
      </MainLayout>
    )
  }

  // Error state
  if (error || !laptop) {
    return (
      <MainLayout>
        <div className="min-h-screen bg-background">
          <div className="container mx-auto px-4 py-8">
            <div className="text-center">
              <h1 className="text-2xl font-bold mb-4">Laptop Not Found</h1>
              <p className="text-muted-foreground mb-4">
                The laptop you're looking for doesn't exist or has been removed.
              </p>
              <Button asChild>
                <Link href="/search">Back to Search</Link>
              </Button>
            </div>
          </div>
        </div>
      </MainLayout>
    )
  }

  const getCompatibilityColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 75) return 'text-blue-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getCompatibilityLabel = (score: number) => {
    if (score >= 90) return 'Excellent'
    if (score >= 75) return 'Good'
    if (score >= 60) return 'Fair'
    return 'Limited'
  }



  return (
    <MainLayout>
      <div className="min-h-screen bg-background">
        {/* Header */}
        <section className="border-b bg-muted/30">
          <div className="container mx-auto px-4 py-6">
            <div className="flex items-center gap-4 mb-4">
              <Button variant="ghost" size="sm" asChild>
                <Link href="/search">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Search
                </Link>
              </Button>
              <div className="flex gap-2 ml-auto">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleToggleFavorite}
                >
                  <Heart className={`h-4 w-4 ${isFavorite ? 'fill-red-500 text-red-500' : ''}`} />
                </Button>
                <Button variant="ghost" size="sm">
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Product Image */}
              <div className="lg:col-span-1">
                <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
                  <Monitor className="h-24 w-24 text-muted-foreground" />
                </div>
              </div>

              {/* Product Info */}
              <div className="lg:col-span-2 space-y-4">
                <div>
                  <Badge variant="secondary" className="mb-2">{laptop.brand}</Badge>
                  <h1 className="text-3xl font-bold mb-2">{laptop.name}</h1>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <div className="flex">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star key={star} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                      <span className="text-sm text-muted-foreground">(4.5/5 - 127 reviews)</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="text-3xl font-bold text-primary">{formatPrice(laptop.currentPrice)}</div>
                  {laptop.originalPrice && laptop.originalPrice > laptop.currentPrice && (
                    <>
                      <div className="text-lg text-muted-foreground line-through">{formatPrice(laptop.originalPrice)}</div>
                      <Badge variant="destructive">
                        Save {Math.round(((laptop.originalPrice - laptop.currentPrice) / laptop.originalPrice) * 100)}%
                      </Badge>
                    </>
                  )}
                </div>

                {/* LLM Compatibility Score */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <BarChart3 className="h-5 w-5" />
                      LLM Compatibility Score
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {compatibilityScore ? (
                      <>
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-2xl font-bold">{compatibilityScore.overallScore}%</span>
                          <Badge variant="secondary" className={getCompatibilityColor(compatibilityScore.overallScore)}>
                            {getCompatibilityLabel(compatibilityScore.overallScore)}
                          </Badge>
                        </div>
                        <Progress value={compatibilityScore.overallScore} className="h-3 mb-4" />
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="flex justify-between">
                            <span>Est. Tokens/sec:</span>
                            <span className="font-medium">{compatibilityScore.performance?.tokensPerSecond || 0}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Memory Usage:</span>
                            <span className="font-medium">{compatibilityScore.performance?.memoryUsageGB || 0}GB</span>
                          </div>
                        </div>
                      </>
                    ) : (
                      <div className="text-center py-4 text-muted-foreground">
                        Loading compatibility analysis...
                      </div>
                    )}
                  </CardContent>
                </Card>

                <div className="flex gap-3">
                  <Button size="lg" className="flex-1">
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    Buy Now
                  </Button>
                  <Button variant="outline" size="lg" asChild>
                    <Link href={laptop.url} target="_blank">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      View on {laptop.source}
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Detailed Information */}
        <div className="container mx-auto px-4 py-8">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="specifications">Specifications</TabsTrigger>
              <TabsTrigger value="compatibility">LLM Compatibility</TabsTrigger>
              <TabsTrigger value="reviews">Reviews</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Key Features</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-3">
                      <Cpu className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Powerful Processor</div>
                        <div className="text-sm text-muted-foreground">AMD Ryzen 7 6800H 8-core</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <HardDrive className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Fast Storage</div>
                        <div className="text-sm text-muted-foreground">512GB NVMe PCIe 4.0 SSD</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Monitor className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">High Refresh Display</div>
                        <div className="text-sm text-muted-foreground">15.6" FHD 144Hz IPS</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Zap className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Dedicated Graphics</div>
                        <div className="text-sm text-muted-foreground">RTX 3060 6GB GDDR6</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Pros & Cons</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold mb-3 flex items-center gap-2 text-green-600">
                        <CheckCircle className="h-4 w-4" />
                        Pros
                      </h4>
                      <ul className="space-y-2 text-sm">
                        <li>• Excellent performance for 7B parameter models</li>
                        <li>• Good price-to-performance ratio</li>
                        <li>• Fast NVMe storage for quick model loading</li>
                        <li>• Adequate cooling for sustained workloads</li>
                        <li>• Gaming-grade build quality</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-3 flex items-center gap-2 text-red-600">
                        <AlertTriangle className="h-4 w-4" />
                        Cons
                      </h4>
                      <ul className="space-y-2 text-sm">
                        <li>• Limited RAM for larger models (13B+)</li>
                        <li>• High power consumption under load</li>
                        <li>• May throttle during extended inference</li>
                        <li>• Relatively heavy for portability</li>
                        <li>• Fan noise under heavy load</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="specifications" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Technical Specifications</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold mb-2">Performance</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">CPU:</span>
                            <span>{laptop.specifications?.cpu}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">GPU:</span>
                            <span>{laptop.specifications?.gpu}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">RAM:</span>
                            <span>{laptop.specifications?.ram}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Storage:</span>
                            <span>{laptop.specifications?.storage}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold mb-2">Display & Audio</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Display:</span>
                            <span>{laptop.specifications?.display}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Webcam:</span>
                            <span>{laptop.specifications?.webcam}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold mb-2">Physical</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Weight:</span>
                            <span>{laptop.specifications?.weight}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Dimensions:</span>
                            <span>{laptop.specifications?.dimensions}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Battery:</span>
                            <span>{laptop.specifications?.battery}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold mb-2">Connectivity</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Ports:</span>
                            <span>{laptop.specifications?.ports}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Wireless:</span>
                            <span>{laptop.specifications?.wireless}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">OS:</span>
                            <span>{laptop.specifications?.os}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="compatibility" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>LLM Model Compatibility</CardTitle>
                  <CardDescription>
                    How well this laptop can run different Large Language Models
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {modelRecommendations ? (
                      modelRecommendations.map((recommendation, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className="font-medium">{recommendation.model.name}</div>
                            <Badge variant="secondary" className={getCompatibilityColor(recommendation.score.overall)}>
                              {getCompatibilityLabel(recommendation.score.overall)}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-3">
                            <div className="text-sm text-muted-foreground w-16 text-right">
                              {recommendation.score.overall}%
                            </div>
                            <Progress value={recommendation.score.overall} className="w-24 h-2" />
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4 text-muted-foreground">
                        Loading model recommendations...
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Performance Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {compatibilityScore ? (
                      Object.entries(compatibilityScore.breakdown || {}).map(([key, value]) => (
                        <div key={key} className="flex items-center justify-between">
                          <span className="capitalize font-medium">{key}</span>
                          <div className="flex items-center gap-3">
                            <span className="text-sm text-muted-foreground w-12 text-right">{value as number}%</span>
                            <Progress value={value as number} className="w-32 h-2" />
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4 text-muted-foreground">
                        Loading performance breakdown...
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {compatibilityScore?.warnings && compatibilityScore.warnings.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-yellow-600">
                      <AlertTriangle className="h-5 w-5" />
                      Warnings
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {compatibilityScore.warnings.map((warning: string, index: number) => (
                        <li key={index} className="text-sm">• {warning}</li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}

              {compatibilityScore?.recommendations && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-green-600">
                      <CheckCircle className="h-5 w-5" />
                      Recommendations
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {compatibilityScore.recommendations.map((rec: string, index: number) => (
                        <li key={index} className="text-sm">• {rec}</li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="reviews" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Customer Reviews</CardTitle>
                  <CardDescription>
                    What users are saying about this laptop for LLM workloads
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12 text-muted-foreground">
                    <Info className="h-12 w-12 mx-auto mb-4" />
                    <p>Reviews feature coming soon...</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </MainLayout>
  )
}
