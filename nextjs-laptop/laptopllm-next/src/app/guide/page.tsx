// Guide Page - LLM Hardware Requirements Guide

'use client'

import React from 'react'
import Link from 'next/link'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Progress } from '@/components/ui/progress'
import { 
  Cpu, 
  HardDrive, 
  Monitor, 
  Zap, 
  Thermometer,
  Laptop,
  ArrowRight,
  CheckCircle,
  AlertTriangle,
  Info,
  BookOpen,
  Target,
  TrendingUp
} from 'lucide-react'

export default function GuidePage() {
  const llmModels = [
    {
      name: 'Llama 2 7B',
      minRam: 8,
      recommendedRam: 16,
      minVram: 6,
      recommendedVram: 8,
      difficulty: 'Beginner',
      color: 'bg-green-500',
    },
    {
      name: 'Mistral 7B',
      minRam: 8,
      recommendedRam: 16,
      minVram: 6,
      recommendedVram: 8,
      difficulty: 'Beginner',
      color: 'bg-blue-500',
    },
    {
      name: 'Code Llama 13B',
      minRam: 16,
      recommendedRam: 32,
      minVram: 8,
      recommendedVram: 12,
      difficulty: 'Intermediate',
      color: 'bg-purple-500',
    },
    {
      name: 'Llama 2 70B',
      minRam: 64,
      recommendedRam: 128,
      minVram: 24,
      recommendedVram: 48,
      difficulty: 'Advanced',
      color: 'bg-red-500',
    },
  ]

  const hardwareComponents = [
    {
      icon: Cpu,
      title: 'CPU (Processor)',
      description: 'The brain of your laptop that handles LLM computations',
      requirements: [
        'Minimum: 4-core processor (Intel i5 or AMD Ryzen 5)',
        'Recommended: 8+ core processor (Intel i7/i9 or AMD Ryzen 7/9)',
        'Best: Latest generation with high single-thread performance',
      ],
      tips: [
        'More cores = better parallel processing',
        'Higher clock speeds improve inference speed',
        'Modern architectures (12th gen Intel, Ryzen 6000+) are more efficient',
      ],
    },
    {
      icon: HardDrive,
      title: 'RAM (Memory)',
      description: 'System memory that holds the LLM model during execution',
      requirements: [
        'Minimum: 8GB for small models (7B parameters)',
        'Recommended: 16-32GB for medium models (13B parameters)',
        'Best: 64GB+ for large models (70B+ parameters)',
      ],
      tips: [
        'Model size roughly equals RAM requirement',
        'DDR5 is faster than DDR4 but not critical',
        'Unified memory (Apple Silicon) is very efficient',
      ],
    },
    {
      icon: Monitor,
      title: 'GPU (Graphics)',
      description: 'Accelerates LLM inference and training significantly',
      requirements: [
        'Minimum: Integrated graphics (CPU-only inference)',
        'Recommended: Dedicated GPU with 6-8GB VRAM',
        'Best: High-end GPU with 12GB+ VRAM (RTX 4070+)',
      ],
      tips: [
        'NVIDIA GPUs have better LLM software support',
        'More VRAM allows larger models or faster inference',
        'Apple Silicon GPUs are surprisingly capable',
      ],
    },
    {
      icon: HardDrive,
      title: 'Storage',
      description: 'Fast storage for model files and quick loading',
      requirements: [
        'Minimum: 256GB SSD',
        'Recommended: 512GB+ NVMe SSD',
        'Best: 1TB+ high-speed NVMe SSD',
      ],
      tips: [
        'Models can be 4-140GB+ in size',
        'NVMe SSDs load models much faster',
        'Consider external storage for model collections',
      ],
    },
    {
      icon: Thermometer,
      title: 'Cooling',
      description: 'Thermal management for sustained performance',
      requirements: [
        'Minimum: Basic laptop cooling',
        'Recommended: Good thermal design with multiple fans',
        'Best: Gaming laptop cooling or external solutions',
      ],
      tips: [
        'LLM inference generates significant heat',
        'Thermal throttling reduces performance',
        'Consider cooling pads for extended use',
      ],
    },
    {
      icon: Zap,
      title: 'Power',
      description: 'Adequate power delivery for high-performance components',
      requirements: [
        'Minimum: 65W power adapter',
        'Recommended: 100W+ power adapter',
        'Best: 150W+ for high-end gaming laptops',
      ],
      tips: [
        'Higher wattage = better sustained performance',
        'Battery life decreases significantly during LLM use',
        'Consider laptops with good power management',
      ],
    },
  ]

  const useCases = [
    {
      title: 'Development & Experimentation',
      description: 'Code assistance, learning, and small-scale testing',
      budget: '$800 - $1,500',
      specs: {
        cpu: 'Intel i5/i7 or AMD Ryzen 5/7',
        ram: '16GB',
        gpu: 'RTX 3060 or equivalent',
        storage: '512GB SSD',
      },
      models: ['Llama 2 7B', 'Mistral 7B', 'Code Llama 7B'],
    },
    {
      title: 'Professional Use',
      description: 'Business applications, content creation, research',
      budget: '$1,500 - $3,000',
      specs: {
        cpu: 'Intel i7/i9 or AMD Ryzen 7/9',
        ram: '32GB',
        gpu: 'RTX 4060/4070 or equivalent',
        storage: '1TB SSD',
      },
      models: ['Code Llama 13B', 'Llama 2 13B', 'Mistral 7B Instruct'],
    },
    {
      title: 'Research & Enterprise',
      description: 'Large model inference, fine-tuning, production use',
      budget: '$3,000+',
      specs: {
        cpu: 'Intel i9 or AMD Ryzen 9',
        ram: '64GB+',
        gpu: 'RTX 4080/4090 or equivalent',
        storage: '2TB+ SSD',
      },
      models: ['Llama 2 70B', 'Code Llama 34B', 'Custom fine-tuned models'],
    },
  ]

  return (
    <MainLayout>
      <div className="min-h-screen bg-background">
        {/* Hero Section */}
        <section className="border-b bg-gradient-to-r from-primary/5 to-primary/10">
          <div className="container mx-auto px-4 py-16">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge variant="secondary" className="mx-auto">
                <BookOpen className="h-3 w-3 mr-1" />
                Complete Guide
              </Badge>
              <h1 className="text-4xl md:text-5xl font-bold">
                LLM Hardware Requirements Guide
              </h1>
              <p className="text-xl text-muted-foreground">
                Everything you need to know about choosing the right laptop for running 
                Large Language Models locally. From beginner to expert level.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Button asChild>
                  <Link href="/search">
                    <Target className="mr-2 h-4 w-4" />
                    Find Recommended Laptops
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/compare">
                    <TrendingUp className="mr-2 h-4 w-4" />
                    Compare Options
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        <div className="container mx-auto px-4 py-12 space-y-16">
          {/* Popular LLM Models */}
          <section>
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Popular LLM Models & Requirements</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Understanding the hardware requirements for different model sizes helps you choose the right laptop.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {llmModels.map((model, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-4 h-4 ${model.color} rounded-full`} />
                        <CardTitle>{model.name}</CardTitle>
                      </div>
                      <Badge variant={
                        model.difficulty === 'Beginner' ? 'default' :
                        model.difficulty === 'Intermediate' ? 'secondary' : 'destructive'
                      }>
                        {model.difficulty}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="font-medium mb-1">System RAM</div>
                        <div className="text-muted-foreground">
                          Min: {model.minRam}GB | Rec: {model.recommendedRam}GB
                        </div>
                      </div>
                      <div>
                        <div className="font-medium mb-1">GPU VRAM</div>
                        <div className="text-muted-foreground">
                          Min: {model.minVram}GB | Rec: {model.recommendedVram}GB
                        </div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>RAM Usage</span>
                        <span>{model.minRam}GB - {model.recommendedRam}GB</span>
                      </div>
                      <Progress value={(model.minRam / 64) * 100} className="h-2" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          {/* Hardware Components */}
          <section>
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Hardware Components Explained</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Deep dive into each hardware component and how it affects LLM performance.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {hardwareComponents.map((component, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                        <component.icon className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <CardTitle>{component.title}</CardTitle>
                        <CardDescription>{component.description}</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2 flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        Requirements
                      </h4>
                      <ul className="space-y-1 text-sm text-muted-foreground">
                        {component.requirements.map((req, i) => (
                          <li key={i}>• {req}</li>
                        ))}
                      </ul>
                    </div>
                    <Separator />
                    <div>
                      <h4 className="font-semibold mb-2 flex items-center gap-2">
                        <Info className="h-4 w-4 text-blue-600" />
                        Pro Tips
                      </h4>
                      <ul className="space-y-1 text-sm text-muted-foreground">
                        {component.tips.map((tip, i) => (
                          <li key={i}>• {tip}</li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          {/* Use Cases */}
          <section>
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Choose by Use Case</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Find the right laptop configuration based on how you plan to use LLMs.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {useCases.map((useCase, index) => (
                <Card key={index} className="relative">
                  <CardHeader>
                    <CardTitle>{useCase.title}</CardTitle>
                    <CardDescription>{useCase.description}</CardDescription>
                    <div className="text-2xl font-bold text-primary">{useCase.budget}</div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-3">Recommended Specs</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">CPU:</span>
                          <span className="font-medium">{useCase.specs.cpu}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">RAM:</span>
                          <span className="font-medium">{useCase.specs.ram}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">GPU:</span>
                          <span className="font-medium">{useCase.specs.gpu}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Storage:</span>
                          <span className="font-medium">{useCase.specs.storage}</span>
                        </div>
                      </div>
                    </div>
                    <Separator />
                    <div>
                      <h4 className="font-semibold mb-2">Suitable Models</h4>
                      <div className="flex flex-wrap gap-1">
                        {useCase.models.map((model, i) => (
                          <Badge key={i} variant="secondary" className="text-xs">
                            {model}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <Button className="w-full" asChild>
                      <Link href={`/search?budget=${useCase.budget.split(' - ')[0].replace('$', '').replace(',', '')}`}>
                        Find Laptops
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          {/* Quick Tips */}
          <section className="bg-muted/50 rounded-lg p-8">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold mb-4">Quick Tips for Success</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="flex gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold mb-1">Start Small</h4>
                  <p className="text-sm text-muted-foreground">
                    Begin with 7B parameter models to test your setup before investing in larger models.
                  </p>
                </div>
              </div>
              
              <div className="flex gap-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold mb-1">Monitor Thermals</h4>
                  <p className="text-sm text-muted-foreground">
                    LLM inference generates heat. Ensure good cooling to maintain performance.
                  </p>
                </div>
              </div>
              
              <div className="flex gap-3">
                <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold mb-1">Consider Quantization</h4>
                  <p className="text-sm text-muted-foreground">
                    Quantized models (4-bit, 8-bit) can run on less powerful hardware with minimal quality loss.
                  </p>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </MainLayout>
  )
}
