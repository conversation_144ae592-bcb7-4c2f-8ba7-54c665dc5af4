// LaptopLLM Finder - Home Page

'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { MainLayout } from '@/components/layout/main-layout'
import { usePopularSearches } from '@/features/laptops'
import { useModelCategories } from '@/features/llm-compatibility'
import {
  Search,
  Laptop,
  Zap,
  Shield,
  TrendingUp,
  ArrowRight,
  Star,
  Users,
  CheckCircle,
  Sparkles
} from 'lucide-react'

export default function Page() {
  const [searchQuery, setSearchQuery] = useState('')

  // Fetch real data from feature modules
  const { data: popularSearches } = usePopularSearches()
  const { data: modelCategories } = useModelCategories()

  const handleSearch = () => {
    if (searchQuery.trim()) {
      // Navigate to search page with query
      window.location.href = `/search?q=${encodeURIComponent(searchQuery.trim())}`
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
        {/* Hero Section */}
        <section className="container mx-auto px-4 py-16 md:py-24">
          <div className="text-center space-y-8">
            {/* Badge */}
            <Badge variant="secondary" className="mx-auto">
              <Sparkles className="h-3 w-3 mr-1" />
              AI-Powered Laptop Recommendations
            </Badge>

            {/* Main Heading */}
            <div className="space-y-4">
              <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
                Find the Perfect Laptop for
                <span className="text-primary block">LLM Applications</span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Discover laptops optimized for running Large Language Models locally.
                Get compatibility scores, performance estimates, and personalized recommendations.
              </p>
            </div>

            {/* Search Bar */}
            <div className="max-w-2xl mx-auto">
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search for laptops, brands, or specifications..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={handleKeyDown}
                    className="pl-10 h-12 text-base"
                  />
                </div>
                <Button onClick={handleSearch} size="lg" className="h-12 px-8">
                  Search
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
              <p className="text-sm text-muted-foreground mt-2">
                Try searching for "gaming laptop", "MacBook Pro", or "RTX 4090"
              </p>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-wrap justify-center gap-4">
              <Button variant="outline" asChild>
                <Link href="/search">
                  <Search className="mr-2 h-4 w-4" />
                  Advanced Search
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="/compare">
                  <TrendingUp className="mr-2 h-4 w-4" />
                  Compare Laptops
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="/guide">
                  <Shield className="mr-2 h-4 w-4" />
                  LLM Guide
                </Link>
              </Button>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="container mx-auto px-4 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Choose LaptopLLM Finder?</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Our AI-powered platform analyzes hardware specifications and provides
              intelligent recommendations for running LLM models efficiently.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <Zap className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Smart Compatibility</CardTitle>
                <CardDescription>
                  Advanced algorithms analyze laptop specs against LLM requirements
                  to provide accurate compatibility scores.
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Feature 2 */}
            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <TrendingUp className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Performance Estimates</CardTitle>
                <CardDescription>
                  Get detailed performance predictions including tokens per second,
                  memory usage, and power consumption estimates.
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Feature 3 */}
            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <Shield className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Expert Recommendations</CardTitle>
                <CardDescription>
                  Personalized laptop recommendations based on your specific use case,
                  budget, and preferred LLM models.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </section>

        {/* Stats Section */}
        <section className="bg-muted/50 py-16">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-primary mb-2">500+</div>
                <div className="text-sm text-muted-foreground">Laptops Analyzed</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary mb-2">15+</div>
                <div className="text-sm text-muted-foreground">LLM Models Supported</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary mb-2">95%</div>
                <div className="text-sm text-muted-foreground">Accuracy Rate</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary mb-2">1000+</div>
                <div className="text-sm text-muted-foreground">Happy Users</div>
              </div>
            </div>
          </div>
        </section>

        {/* Popular Models Section */}
        <section className="container mx-auto px-4 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Popular LLM Models</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Find laptops optimized for these popular Large Language Models
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {modelCategories ? (
              modelCategories.slice(0, 4).map((category, index) => (
                <Card key={category.id} className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardContent className="p-4 text-center">
                    <div className={`w-8 h-8 ${['bg-blue-500', 'bg-purple-500', 'bg-green-500', 'bg-orange-500'][index % 4]} rounded-full mx-auto mb-3`} />
                    <h3 className="font-semibold mb-1">{category.name}</h3>
                    <p className="text-sm text-muted-foreground">{category.modelCount || 0} models</p>
                  </CardContent>
                </Card>
              ))
            ) : (
              // Loading skeleton
              Array.from({ length: 4 }).map((_, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4 text-center">
                    <div className="w-8 h-8 bg-muted rounded-full mx-auto mb-3 animate-pulse" />
                    <div className="h-4 bg-muted rounded mb-1 animate-pulse" />
                    <div className="h-3 bg-muted rounded animate-pulse" />
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-primary/5 py-16">
          <div className="container mx-auto px-4 text-center">
            <div className="max-w-3xl mx-auto space-y-6">
              <h2 className="text-3xl font-bold">Ready to Find Your Perfect Laptop?</h2>
              <p className="text-muted-foreground text-lg">
                Join thousands of developers, researchers, and AI enthusiasts who trust
                LaptopLLM Finder for their hardware decisions.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" asChild>
                  <Link href="/search">
                    <Search className="mr-2 h-5 w-5" />
                    Start Searching Now
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/guide">
                    <Shield className="mr-2 h-5 w-5" />
                    Learn About LLM Requirements
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </MainLayout>
  )
}