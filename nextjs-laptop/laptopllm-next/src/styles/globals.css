@import 'tailwindcss/preflight';
@import 'tailwindcss/utilities';
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* LaptopLLM specific component styles */
  .laptop-card {
    @apply bg-card border border-border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow;
  }

  .compatibility-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .compatibility-excellent {
    @apply bg-compatibility-excellent/10 text-compatibility-excellent border border-compatibility-excellent/20;
  }

  .compatibility-good {
    @apply bg-compatibility-good/10 text-compatibility-good border border-compatibility-good/20;
  }

  .compatibility-fair {
    @apply bg-compatibility-fair/10 text-compatibility-fair border border-compatibility-fair/20;
  }

  .compatibility-poor {
    @apply bg-compatibility-poor/10 text-compatibility-poor border border-compatibility-poor/20;
  }

  .performance-indicator {
    @apply flex items-center gap-1 text-sm;
  }

  .performance-high {
    @apply text-performance-high;
  }

  .performance-medium {
    @apply text-performance-medium;
  }

  .performance-low {
    @apply text-performance-low;
  }

  .spec-item {
    @apply flex justify-between items-center py-2 border-b border-border last:border-b-0;
  }

  .spec-label {
    @apply text-sm font-medium text-muted-foreground;
  }

  .spec-value {
    @apply text-sm font-semibold text-foreground;
  }

  .filter-section {
    @apply space-y-4 p-4 border border-border rounded-lg bg-card;
  }

  .filter-title {
    @apply text-sm font-semibold text-foreground border-b border-border pb-2;
  }

  .search-input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .loading-spinner {
    @apply animate-spin rounded-full h-4 w-4 border-b-2 border-primary;
  }

  .error-message {
    @apply text-destructive text-sm font-medium;
  }

  .success-message {
    @apply text-green-600 text-sm font-medium;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent;
  }

  .glass-effect {
    @apply backdrop-blur-sm bg-white/10 border border-white/20;
  }
}
