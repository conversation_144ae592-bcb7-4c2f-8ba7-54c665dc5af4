/**
 * Apollo Provider Component
 * Provides Apollo Client to the React component tree
 */

'use client'

import { ApolloProvider } from '@apollo/client'
import apolloClient from '@/lib/graphql/client'
import { ReactNode } from 'react'

interface ApolloProviderWrapperProps {
  children: ReactNode
}

export function ApolloProviderWrapper({ children }: ApolloProviderWrapperProps) {
  return (
    <ApolloProvider client={apolloClient}>
      {children}
    </ApolloProvider>
  )
}
