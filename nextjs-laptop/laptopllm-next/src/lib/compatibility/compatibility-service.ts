import type { LaptopData, LLMModel, CompatibilityScore, PerformanceMetrics } from '@/shared/types'

/**
 * Service for calculating LLM compatibility scores and performance estimates
 */
export class CompatibilityService {
  private readonly models: LLMModel[] = [
    {
      name: 'Llama 2 7B',
      size: '7B',
      memoryRequirement: 8,
      recommendedMemory: 16,
      computeRequirement: 'Medium',
      framework: 'PyTorch',
    },
    {
      name: 'Llama 2 13B',
      size: '13B',
      memoryRequirement: 16,
      recommendedMemory: 32,
      computeRequirement: 'High',
      framework: 'PyTorch',
    },
    {
      name: 'Mistral 7B',
      size: '7B',
      memoryRequirement: 8,
      recommendedMemory: 16,
      computeRequirement: 'Medium',
      framework: 'PyTorch',
    },
    {
      name: 'CodeLlama 7B',
      size: '7B',
      memoryRequirement: 8,
      recommendedMemory: 16,
      computeRequirement: 'Medium',
      framework: 'PyTorch',
    },
    {
      name: 'Llama 2 70B',
      size: '70B',
      memoryRequirement: 64,
      recommendedMemory: 128,
      computeRequirement: 'Very High',
      framework: 'PyTorch',
    },
  ]

  /**
   * Calculate compatibility score between laptop and LLM model
   */
  async calculateCompatibility(laptop: LaptopData, model: LLMModel): Promise<CompatibilityScore> {
    const performanceScore = this.calculatePerformanceScore(laptop, model)
    const memoryScore = this.calculateMemoryScore(laptop, model)
    const efficiencyScore = this.calculateEfficiencyScore(laptop, model)

    const overall = Math.round((performanceScore + memoryScore + efficiencyScore) / 3)

    return {
      overall,
      performance: performanceScore,
      memory: memoryScore,
      efficiency: efficiencyScore,
      recommendation: this.getRecommendationLabel(overall),
      details: {
        cpuCompatibility: this.assessCpuCompatibility(laptop, model),
        gpuCompatibility: this.assessGpuCompatibility(laptop, model),
        memoryAdequacy: this.assessMemoryAdequacy(laptop, model),
        thermalConsiderations: this.assessThermalConsiderations(laptop, model),
      },
    }
  }

  /**
   * Get model recommendations for a laptop
   */
  async getModelRecommendations(laptop: LaptopData): Promise<Array<{
    model: LLMModel
    score: CompatibilityScore
  }>> {
    const recommendations = await Promise.all(
      this.models.map(async (model) => ({
        model,
        score: await this.calculateCompatibility(laptop, model),
      }))
    )

    // Sort by overall score (descending)
    return recommendations
      .sort((a, b) => b.score.overall - a.score.overall)
      .slice(0, 5) // Return top 5 recommendations
  }

  /**
   * Estimate performance metrics for laptop and model combination
   */
  async estimatePerformance(laptop: LaptopData, model: LLMModel): Promise<PerformanceMetrics> {
    const cpuScore = this.getCpuPerformanceScore(laptop)
    const gpuScore = this.getGpuPerformanceScore(laptop)
    const memoryScore = this.getMemoryPerformanceScore(laptop)

    // Base performance calculations
    const baseTokensPerSecond = this.calculateBaseTokensPerSecond(cpuScore, gpuScore, model)
    const baseMemoryUsage = this.calculateMemoryUsage(model, laptop)
    const basePowerConsumption = this.calculatePowerConsumption(laptop, model)
    const baseThermalLoad = this.calculateThermalLoad(laptop, model)

    return {
      tokensPerSecond: Math.round(baseTokensPerSecond),
      memoryUsage: Math.round(baseMemoryUsage * 10) / 10,
      powerConsumption: Math.round(basePowerConsumption),
      thermalLoad: Math.round(baseThermalLoad),
      estimatedBatteryLife: this.estimateBatteryLife(laptop, basePowerConsumption),
    }
  }

  /**
   * Calculate performance score based on CPU and GPU capabilities
   */
  private calculatePerformanceScore(laptop: LaptopData, model: LLMModel): number {
    const cpuScore = this.getCpuPerformanceScore(laptop)
    const gpuScore = this.getGpuPerformanceScore(laptop)
    
    // Weight GPU more heavily for LLM inference
    const combinedScore = (cpuScore * 0.3) + (gpuScore * 0.7)
    
    // Adjust based on model requirements
    const requirementMultiplier = this.getRequirementMultiplier(model.computeRequirement)
    
    return Math.min(100, Math.round(combinedScore * requirementMultiplier))
  }

  /**
   * Calculate memory score based on available RAM vs model requirements
   */
  private calculateMemoryScore(laptop: LaptopData, model: LLMModel): number {
    const availableMemory = laptop.specifications.memory.size
    const requiredMemory = model.memoryRequirement
    const recommendedMemory = model.recommendedMemory

    if (availableMemory >= recommendedMemory) {
      return 100
    } else if (availableMemory >= requiredMemory) {
      // Linear interpolation between required and recommended
      const ratio = (availableMemory - requiredMemory) / (recommendedMemory - requiredMemory)
      return Math.round(70 + (ratio * 30))
    } else {
      // Below minimum requirements
      const ratio = availableMemory / requiredMemory
      return Math.round(ratio * 50)
    }
  }

  /**
   * Calculate efficiency score based on power consumption and thermal design
   */
  private calculateEfficiencyScore(laptop: LaptopData, model: LLMModel): number {
    const thermalScore = this.getThermalScore(laptop)
    const powerScore = this.getPowerEfficiencyScore(laptop)
    const portabilityScore = this.getPortabilityScore(laptop)

    return Math.round((thermalScore + powerScore + portabilityScore) / 3)
  }

  /**
   * Get CPU performance score
   */
  private getCpuPerformanceScore(laptop: LaptopData): number {
    const cpu = laptop.specifications.cpu
    const coreScore = Math.min(100, (cpu.cores / 16) * 100)
    const frequencyScore = Math.min(100, ((cpu.boostFrequency || cpu.baseFrequency) / 5.0) * 100)
    
    return Math.round((coreScore + frequencyScore) / 2)
  }

  /**
   * Get GPU performance score
   */
  private getGpuPerformanceScore(laptop: LaptopData): number {
    const gpu = laptop.specifications.gpu
    
    if (gpu.brand === 'NVIDIA' && gpu.model.includes('RTX')) {
      const memoryScore = Math.min(100, (gpu.memory / 16) * 100)
      const modelScore = this.getNvidiaModelScore(gpu.model)
      return Math.round((memoryScore + modelScore) / 2)
    } else if (gpu.brand === 'AMD' && gpu.model.includes('RX')) {
      const memoryScore = Math.min(100, (gpu.memory / 16) * 100)
      return Math.round(memoryScore * 0.8) // AMD slightly lower for LLM inference
    } else {
      // Integrated graphics
      return 30
    }
  }

  /**
   * Get memory performance score
   */
  private getMemoryPerformanceScore(laptop: LaptopData): number {
    const memory = laptop.specifications.memory
    const sizeScore = Math.min(100, (memory.size / 32) * 100)
    const speedScore = Math.min(100, (memory.speed / 5000) * 100)
    
    return Math.round((sizeScore + speedScore) / 2)
  }

  /**
   * Get NVIDIA GPU model score
   */
  private getNvidiaModelScore(model: string): number {
    if (model.includes('4090')) return 100
    if (model.includes('4080')) return 95
    if (model.includes('4070')) return 85
    if (model.includes('4060')) return 75
    if (model.includes('3080')) return 80
    if (model.includes('3070')) return 70
    if (model.includes('3060')) return 60
    if (model.includes('3050')) return 50
    return 40
  }

  /**
   * Get thermal score based on laptop design
   */
  private getThermalScore(laptop: LaptopData): number {
    const thermalDesign = laptop.features.thermalDesign
    
    switch (thermalDesign) {
      case 'Advanced': return 90
      case 'Standard': return 70
      case 'Basic': return 50
      case 'Passive': return 30
      default: return 60
    }
  }

  /**
   * Get power efficiency score
   */
  private getPowerEfficiencyScore(laptop: LaptopData): number {
    const cpu = laptop.specifications.cpu
    const gpu = laptop.specifications.gpu
    
    const totalTdp = cpu.tdp + (gpu.tdp || 0)
    
    if (totalTdp <= 50) return 90
    if (totalTdp <= 100) return 70
    if (totalTdp <= 150) return 50
    return 30
  }

  /**
   * Get portability score
   */
  private getPortabilityScore(laptop: LaptopData): number {
    const weight = laptop.features.weight
    const batteryLife = laptop.features.batteryLife
    
    const weightScore = weight <= 2 ? 90 : weight <= 3 ? 70 : 50
    const batteryScore = batteryLife >= 8 ? 90 : batteryLife >= 6 ? 70 : 50
    
    return Math.round((weightScore + batteryScore) / 2)
  }

  /**
   * Get requirement multiplier based on compute requirement
   */
  private getRequirementMultiplier(requirement: string): number {
    switch (requirement) {
      case 'Low': return 1.2
      case 'Medium': return 1.0
      case 'High': return 0.8
      case 'Very High': return 0.6
      default: return 1.0
    }
  }

  /**
   * Get recommendation label based on overall score
   */
  private getRecommendationLabel(score: number): string {
    if (score >= 85) return 'Excellent'
    if (score >= 70) return 'Good'
    if (score >= 55) return 'Fair'
    if (score >= 40) return 'Poor'
    return 'Not Recommended'
  }

  /**
   * Calculate base tokens per second
   */
  private calculateBaseTokensPerSecond(cpuScore: number, gpuScore: number, model: LLMModel): number {
    const baseRate = 20 // Base tokens per second
    const performanceMultiplier = ((cpuScore + gpuScore) / 2) / 100
    const modelSizeMultiplier = this.getModelSizeMultiplier(model.size)
    
    return baseRate * performanceMultiplier * modelSizeMultiplier
  }

  /**
   * Calculate memory usage
   */
  private calculateMemoryUsage(model: LLMModel, laptop: LaptopData): number {
    const baseUsage = model.memoryRequirement
    const overhead = 2 // OS and other processes
    
    return Math.min(baseUsage + overhead, laptop.specifications.memory.size * 0.8)
  }

  /**
   * Calculate power consumption
   */
  private calculatePowerConsumption(laptop: LaptopData, model: LLMModel): number {
    const cpu = laptop.specifications.cpu
    const gpu = laptop.specifications.gpu
    
    const basePower = cpu.tdp + (gpu.tdp || 0)
    const utilizationFactor = this.getUtilizationFactor(model.computeRequirement)
    
    return basePower * utilizationFactor
  }

  /**
   * Calculate thermal load
   */
  private calculateThermalLoad(laptop: LaptopData, model: LLMModel): number {
    const powerConsumption = this.calculatePowerConsumption(laptop, model)
    const thermalCapacity = this.getThermalCapacity(laptop.features.thermalDesign)
    
    return Math.min(100, (powerConsumption / thermalCapacity) * 100)
  }

  /**
   * Estimate battery life under load
   */
  private estimateBatteryLife(laptop: LaptopData, powerConsumption: number): number {
    const baseBatteryLife = laptop.features.batteryLife
    const idlePower = 20 // Estimated idle power consumption
    const loadFactor = powerConsumption / idlePower
    
    return Math.max(1, Math.round(baseBatteryLife / loadFactor))
  }

  /**
   * Get model size multiplier
   */
  private getModelSizeMultiplier(size: string): number {
    if (size.includes('70B')) return 0.3
    if (size.includes('13B')) return 0.6
    if (size.includes('7B')) return 1.0
    if (size.includes('3B')) return 1.3
    return 1.0
  }

  /**
   * Get utilization factor based on compute requirement
   */
  private getUtilizationFactor(requirement: string): number {
    switch (requirement) {
      case 'Low': return 0.5
      case 'Medium': return 0.7
      case 'High': return 0.9
      case 'Very High': return 1.0
      default: return 0.7
    }
  }

  /**
   * Get thermal capacity based on design
   */
  private getThermalCapacity(design: string): number {
    switch (design) {
      case 'Advanced': return 200
      case 'Standard': return 150
      case 'Basic': return 100
      case 'Passive': return 50
      default: return 120
    }
  }

  /**
   * Assess CPU compatibility
   */
  private assessCpuCompatibility(laptop: LaptopData, model: LLMModel): string {
    const score = this.getCpuPerformanceScore(laptop)
    if (score >= 80) return 'Excellent CPU performance for this model'
    if (score >= 60) return 'Good CPU performance, suitable for this model'
    if (score >= 40) return 'Adequate CPU performance, may be slower'
    return 'CPU may struggle with this model'
  }

  /**
   * Assess GPU compatibility
   */
  private assessGpuCompatibility(laptop: LaptopData, model: LLMModel): string {
    const score = this.getGpuPerformanceScore(laptop)
    if (score >= 80) return 'Excellent GPU acceleration available'
    if (score >= 60) return 'Good GPU acceleration support'
    if (score >= 40) return 'Limited GPU acceleration'
    return 'No meaningful GPU acceleration'
  }

  /**
   * Assess memory adequacy
   */
  private assessMemoryAdequacy(laptop: LaptopData, model: LLMModel): string {
    const available = laptop.specifications.memory.size
    const required = model.memoryRequirement
    const recommended = model.recommendedMemory

    if (available >= recommended) return 'Plenty of memory for optimal performance'
    if (available >= required) return 'Sufficient memory, good performance expected'
    return 'Insufficient memory, performance will be limited'
  }

  /**
   * Assess thermal considerations
   */
  private assessThermalConsiderations(laptop: LaptopData, model: LLMModel): string {
    const thermalLoad = this.calculateThermalLoad(laptop, model)
    
    if (thermalLoad <= 60) return 'Good thermal management expected'
    if (thermalLoad <= 80) return 'Moderate thermal load, may throttle under sustained use'
    return 'High thermal load, expect thermal throttling'
  }
}

// Export singleton instance
export const compatibilityService = new CompatibilityService()
