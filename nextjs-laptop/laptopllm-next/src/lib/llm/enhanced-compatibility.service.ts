/**
 * Enhanced LLM Compatibility Service
 * Advanced algorithms for calculating detailed compatibility scores and performance metrics
 */

import { BaseService } from '../services/base.service'
import type {
  LLMModel,
  LLMCompatibilityScore,
  LaptopSpecifications,
} from '@/shared/types'

export interface EnhancedLaptopSpecs {
  // CPU Specifications
  cpu: {
    cores: number
    threads: number
    baseFreq: number
    boostFreq: number
    architecture: string
    manufacturer: string
    model: string
    cache: {
      l1: number
      l2: number
      l3: number
    }
    tdp: number
    instructionSets: string[]
  }
  
  // Memory Specifications
  memory: {
    totalGb: number
    type: string // DDR4, DDR5, LPDDR5, etc.
    speed: number // MHz
    channels: number
    bandwidth: number // GB/s
    ecc: boolean
  }
  
  // GPU Specifications
  gpu?: {
    model: string
    manufacturer: string
    vramGb: number
    vramType: string
    computeUnits: number
    baseClockMhz: number
    boostClockMhz: number
    memoryBandwidth: number // GB/s
    tensorCores?: number
    rtCores?: number
    cudaCores?: number
    rocmSupport?: boolean
    openclSupport?: boolean
  }
  
  // Storage Specifications
  storage: {
    totalGb: number
    type: string // NVMe, SATA SSD, HDD
    interface: string // PCIe 3.0, PCIe 4.0, SATA III
    readSpeed: number // MB/s
    writeSpeed: number // MB/s
    iops: {
      read: number
      write: number
    }
  }
  
  // Thermal and Power
  thermal: {
    maxTdp: number
    coolingType: string
    thermalThrottleTemp: number
    sustainedPerformance: boolean
  }
  
  // System
  system: {
    formFactor: string
    powerSupply: number // Watts
    batteryCapacity?: number // Wh
    portability: boolean
  }
}

export interface EnhancedLLMModel {
  id: string
  name: string
  parameters: number // billions
  quantization: string[] // Available quantizations
  architecture: string
  
  // Resource Requirements
  requirements: {
    // Memory requirements by quantization
    memory: {
      fp16: number
      int8: number
      int4: number
      int2?: number
    }
    
    // Compute requirements
    compute: {
      minCpuCores: number
      recommendedCpuCores: number
      minFreqGhz: number
      supportedArchitectures: string[]
      requiresAVX: boolean
      requiresAVX512?: boolean
    }
    
    // GPU requirements (optional)
    gpu?: {
      minVramGb: number
      recommendedVramGb: number
      supportedVendors: string[]
      requiresTensorCores?: boolean
      minComputeCapability?: string
    }
    
    // Storage requirements
    storage: {
      modelSizeGb: number
      workingSpaceGb: number
      minReadSpeedMBps: number
      preferredInterface: string[]
    }
  }
  
  // Performance characteristics
  performance: {
    tokensPerSecondBaseline: number // On reference hardware
    memoryEfficiency: number // 0-1 scale
    computeEfficiency: number // 0-1 scale
    parallelizability: number // 0-1 scale
    contextLength: number
    batchingSupport: boolean
  }
  
  // Use case optimization
  useCases: {
    development: number // 0-1 suitability score
    research: number
    production: number
    education: number
    inference: number
    training: number
  }
}

export interface DetailedCompatibilityScore {
  overall: number // 0-100
  
  // Component scores
  components: {
    cpu: {
      score: number
      factors: {
        coreCount: number
        frequency: number
        architecture: number
        cache: number
        instructionSets: number
      }
      bottleneck: boolean
      recommendations: string[]
    }
    
    memory: {
      score: number
      factors: {
        capacity: number
        bandwidth: number
        type: number
        channels: number
      }
      bottleneck: boolean
      recommendations: string[]
    }
    
    gpu?: {
      score: number
      factors: {
        vram: number
        compute: number
        acceleration: number
        compatibility: number
      }
      bottleneck: boolean
      recommendations: string[]
    }
    
    storage: {
      score: number
      factors: {
        capacity: number
        speed: number
        interface: number
        iops: number
      }
      bottleneck: boolean
      recommendations: string[]
    }
    
    thermal: {
      score: number
      factors: {
        cooling: number
        tdp: number
        sustainability: number
      }
      throttlingRisk: 'low' | 'medium' | 'high'
      recommendations: string[]
    }
  }
  
  // Performance predictions
  performance: {
    tokensPerSecond: {
      fp16?: number
      int8?: number
      int4?: number
      optimal: number
    }
    memoryUsage: {
      model: number
      inference: number
      peak: number
    }
    powerConsumption: {
      idle: number
      load: number
      peak: number
    }
    thermalLoad: {
      sustained: number
      peak: number
    }
    batteryLife?: {
      inference: number // hours
      idle: number
    }
  }
  
  // Optimization recommendations
  optimizations: {
    quantization: string
    batchSize: number
    contextLength: number
    parallelization: {
      cpuThreads: number
      gpuStreams?: number
    }
    memoryOptimizations: string[]
    thermalOptimizations: string[]
  }
  
  // Use case suitability
  useCaseSuitability: {
    development: {
      score: number
      reasoning: string[]
    }
    research: {
      score: number
      reasoning: string[]
    }
    production: {
      score: number
      reasoning: string[]
    }
    education: {
      score: number
      reasoning: string[]
    }
  }
  
  // Overall assessment
  assessment: {
    category: 'excellent' | 'good' | 'acceptable' | 'limited' | 'unsuitable'
    confidence: number // 0-1
    primaryLimitations: string[]
    keyStrengths: string[]
    upgradeRecommendations: string[]
  }
}

export class EnhancedCompatibilityService extends BaseService {
  private readonly REFERENCE_HARDWARE = {
    cpu: { cores: 8, freq: 3.0, architecture: 'x86_64' },
    memory: { gb: 32, bandwidth: 50 },
    gpu: { vram: 8, compute: 1000 },
    storage: { speed: 3500 }
  }

  /**
   * Calculate detailed compatibility score
   */
  async calculateDetailedCompatibility(
    specs: EnhancedLaptopSpecs,
    model: EnhancedLLMModel
  ): Promise<DetailedCompatibilityScore> {
    return this.executeWithErrorHandling(async () => {
      // Calculate component scores
      const cpuScore = this.calculateCpuCompatibility(specs.cpu, model)
      const memoryScore = this.calculateMemoryCompatibility(specs.memory, model)
      const gpuScore = specs.gpu ? this.calculateGpuCompatibility(specs.gpu, model) : null
      const storageScore = this.calculateStorageCompatibility(specs.storage, model)
      const thermalScore = this.calculateThermalCompatibility(specs.thermal, model)

      // Calculate performance predictions
      const performance = this.predictPerformance(specs, model)

      // Generate optimizations
      const optimizations = this.generateOptimizations(specs, model, performance)

      // Calculate use case suitability
      const useCaseSuitability = this.calculateUseCaseSuitability(specs, model, performance)

      // Calculate overall score
      const overall = this.calculateOverallScore({
        cpu: cpuScore.score,
        memory: memoryScore.score,
        gpu: gpuScore?.score || 0,
        storage: storageScore.score,
        thermal: thermalScore.score
      }, model)

      // Generate assessment
      const assessment = this.generateAssessment(overall, {
        cpu: cpuScore,
        memory: memoryScore,
        gpu: gpuScore,
        storage: storageScore,
        thermal: thermalScore
      })

      return {
        overall,
        components: {
          cpu: cpuScore,
          memory: memoryScore,
          gpu: gpuScore || undefined,
          storage: storageScore,
          thermal: thermalScore
        },
        performance,
        optimizations,
        useCaseSuitability,
        assessment
      }
    })
  }

  /**
   * Calculate CPU compatibility with detailed factors
   */
  private calculateCpuCompatibility(cpu: EnhancedLaptopSpecs['cpu'], model: EnhancedLLMModel) {
    const factors = {
      coreCount: Math.min(cpu.cores / model.requirements.compute.recommendedCpuCores, 1),
      frequency: Math.min(cpu.boostFreq / model.requirements.compute.minFreqGhz, 1),
      architecture: model.requirements.compute.supportedArchitectures.includes(cpu.architecture) ? 1 : 0.5,
      cache: Math.min(cpu.cache.l3 / 16, 1), // Assume 16MB L3 as good baseline
      instructionSets: this.calculateInstructionSetScore(cpu.instructionSets, model.requirements.compute)
    }

    const score = Math.round(
      (factors.coreCount * 0.3 +
       factors.frequency * 0.25 +
       factors.architecture * 0.2 +
       factors.cache * 0.15 +
       factors.instructionSets * 0.1) * 100
    )

    const bottleneck = score < 70
    const recommendations = this.generateCpuRecommendations(factors, model)

    return { score, factors, bottleneck, recommendations }
  }

  /**
   * Calculate memory compatibility
   */
  private calculateMemoryCompatibility(memory: EnhancedLaptopSpecs['memory'], model: EnhancedLLMModel) {
    // Get optimal quantization based on available memory
    const optimalQuant = this.getOptimalQuantization(memory.totalGb, model.requirements.memory)
    
    const factors = {
      capacity: Math.min(memory.totalGb / model.requirements.memory[optimalQuant as keyof typeof model.requirements.memory], 1),
      bandwidth: Math.min(memory.bandwidth / 50, 1), // 50 GB/s as baseline
      type: this.getMemoryTypeScore(memory.type),
      channels: Math.min(memory.channels / 2, 1) // Dual channel as baseline
    }

    const score = Math.round(
      (factors.capacity * 0.5 +
       factors.bandwidth * 0.25 +
       factors.type * 0.15 +
       factors.channels * 0.1) * 100
    )

    const bottleneck = score < 60
    const recommendations = this.generateMemoryRecommendations(factors, model, optimalQuant)

    return { score, factors, bottleneck, recommendations }
  }

  /**
   * Predict performance metrics
   */
  private predictPerformance(specs: EnhancedLaptopSpecs, model: EnhancedLLMModel) {
    const cpuPerformanceRatio = (specs.cpu.cores / this.REFERENCE_HARDWARE.cpu.cores) * 
                               (specs.cpu.boostFreq / this.REFERENCE_HARDWARE.cpu.freq)
    
    const memoryPerformanceRatio = Math.min(specs.memory.bandwidth / this.REFERENCE_HARDWARE.memory.bandwidth, 2)
    
    const baseTokensPerSecond = model.performance.tokensPerSecondBaseline
    
    // Calculate tokens per second for different quantizations
    const tokensPerSecond = {
      fp16: Math.round(baseTokensPerSecond * cpuPerformanceRatio * 0.8),
      int8: Math.round(baseTokensPerSecond * cpuPerformanceRatio * 1.2),
      int4: Math.round(baseTokensPerSecond * cpuPerformanceRatio * 1.8),
      optimal: 0
    }
    
    // Determine optimal quantization
    const optimalQuant = this.getOptimalQuantization(specs.memory.totalGb, model.requirements.memory)
    tokensPerSecond.optimal = tokensPerSecond[optimalQuant as keyof typeof tokensPerSecond] || tokensPerSecond.int8

    // Memory usage predictions
    const memoryUsage = {
      model: model.requirements.memory[optimalQuant as keyof typeof model.requirements.memory],
      inference: model.requirements.memory[optimalQuant as keyof typeof model.requirements.memory] * 1.2,
      peak: model.requirements.memory[optimalQuant as keyof typeof model.requirements.memory] * 1.5
    }

    // Power consumption estimates
    const powerConsumption = {
      idle: specs.cpu.tdp * 0.1 + (specs.gpu?.vramGb || 0) * 2,
      load: specs.cpu.tdp * 0.7 + (specs.gpu?.vramGb || 0) * 8,
      peak: specs.cpu.tdp + (specs.gpu?.vramGb || 0) * 12
    }

    // Thermal load estimates
    const thermalLoad = {
      sustained: Math.min(powerConsumption.load / specs.thermal.maxTdp, 1) * 100,
      peak: Math.min(powerConsumption.peak / specs.thermal.maxTdp, 1) * 100
    }

    // Battery life estimates (if applicable)
    const batteryLife = specs.system.batteryCapacity ? {
      inference: specs.system.batteryCapacity / powerConsumption.load,
      idle: specs.system.batteryCapacity / powerConsumption.idle
    } : undefined

    return {
      tokensPerSecond,
      memoryUsage,
      powerConsumption,
      thermalLoad,
      batteryLife
    }
  }

  /**
   * Helper methods
   */
  private calculateInstructionSetScore(instructionSets: string[], requirements: EnhancedLLMModel['requirements']['compute']): number {
    let score = 0.5 // Base score
    
    if (instructionSets.includes('AVX2')) score += 0.3
    if (instructionSets.includes('AVX512') && requirements.requiresAVX512) score += 0.2
    if (instructionSets.includes('FMA')) score += 0.1
    
    return Math.min(score, 1)
  }

  private getMemoryTypeScore(type: string): number {
    const scores: Record<string, number> = {
      'DDR5': 1.0,
      'LPDDR5': 0.95,
      'DDR4': 0.8,
      'LPDDR4': 0.75,
      'DDR3': 0.5
    }
    return scores[type] || 0.5
  }

  private getOptimalQuantization(availableMemoryGb: number, requirements: EnhancedLLMModel['requirements']['memory']): string {
    if (availableMemoryGb >= requirements.fp16) return 'fp16'
    if (availableMemoryGb >= requirements.int8) return 'int8'
    if (availableMemoryGb >= requirements.int4) return 'int4'
    return 'int4' // Fallback
  }

  private calculateOverallScore(componentScores: Record<string, number>, model: EnhancedLLMModel): number {
    // Weighted scoring based on model characteristics
    const weights = {
      cpu: 0.25,
      memory: 0.35,
      gpu: model.requirements.gpu ? 0.25 : 0,
      storage: 0.1,
      thermal: 0.05
    }

    // Redistribute GPU weight if no GPU requirements
    if (!model.requirements.gpu) {
      weights.cpu += 0.125
      weights.memory += 0.125
    }

    return Math.round(
      componentScores.cpu * weights.cpu +
      componentScores.memory * weights.memory +
      componentScores.gpu * weights.gpu +
      componentScores.storage * weights.storage +
      componentScores.thermal * weights.thermal
    )
  }

  private generateCpuRecommendations(factors: any, model: EnhancedLLMModel): string[] {
    const recommendations: string[] = []
    
    if (factors.coreCount < 0.8) {
      recommendations.push(`Consider a CPU with at least ${model.requirements.compute.recommendedCpuCores} cores`)
    }
    if (factors.frequency < 0.8) {
      recommendations.push(`Higher CPU frequency (${model.requirements.compute.minFreqGhz}+ GHz) recommended`)
    }
    if (factors.architecture < 1) {
      recommendations.push(`Ensure CPU architecture compatibility: ${model.requirements.compute.supportedArchitectures.join(', ')}`)
    }
    
    return recommendations
  }

  private generateMemoryRecommendations(factors: any, model: EnhancedLLMModel, quantization: string): string[] {
    const recommendations: string[] = []
    
    if (factors.capacity < 0.8) {
      recommendations.push(`Increase RAM to at least ${model.requirements.memory[quantization as keyof typeof model.requirements.memory]}GB for ${quantization} quantization`)
    }
    if (factors.bandwidth < 0.8) {
      recommendations.push('Consider faster memory (higher frequency/more channels)')
    }
    if (factors.type < 0.9) {
      recommendations.push('Upgrade to DDR5 or LPDDR5 for better performance')
    }
    
    return recommendations
  }

  private calculateStorageCompatibility(storage: EnhancedLaptopSpecs['storage'], model: EnhancedLLMModel) {
    const factors = {
      capacity: Math.min(storage.totalGb / (model.requirements.storage.modelSizeGb + model.requirements.storage.workingSpaceGb), 1),
      speed: Math.min(storage.readSpeed / model.requirements.storage.minReadSpeedMBps, 1),
      interface: model.requirements.storage.preferredInterface.includes(storage.interface) ? 1 : 0.7,
      iops: Math.min(storage.iops.read / 100000, 1) // 100K IOPS as baseline
    }

    const score = Math.round(
      (factors.capacity * 0.4 +
       factors.speed * 0.3 +
       factors.interface * 0.2 +
       factors.iops * 0.1) * 100
    )

    const bottleneck = score < 70
    const recommendations: string[] = []
    
    if (factors.capacity < 0.8) {
      recommendations.push(`Ensure at least ${model.requirements.storage.modelSizeGb + model.requirements.storage.workingSpaceGb}GB free storage`)
    }
    if (factors.speed < 0.8) {
      recommendations.push('Consider NVMe SSD for faster model loading')
    }

    return { score, factors, bottleneck, recommendations }
  }

  private calculateThermalCompatibility(thermal: EnhancedLaptopSpecs['thermal'], model: EnhancedLLMModel) {
    const factors = {
      cooling: thermal.coolingType === 'liquid' ? 1 : thermal.coolingType === 'dual-fan' ? 0.8 : 0.6,
      tdp: thermal.maxTdp > 65 ? 1 : thermal.maxTdp > 45 ? 0.8 : 0.6,
      sustainability: thermal.sustainedPerformance ? 1 : 0.7
    }

    const score = Math.round(
      (factors.cooling * 0.4 +
       factors.tdp * 0.4 +
       factors.sustainability * 0.2) * 100
    )

    const throttlingRisk = score > 80 ? 'low' : score > 60 ? 'medium' : 'high'
    const recommendations: string[] = []
    
    if (throttlingRisk !== 'low') {
      recommendations.push('Monitor temperatures during extended inference sessions')
      recommendations.push('Consider external cooling or reduced workloads')
    }

    return { score, factors, throttlingRisk, recommendations }
  }

  private calculateGpuCompatibility(gpu: EnhancedLaptopSpecs['gpu'], model: EnhancedLLMModel) {
    if (!model.requirements.gpu) {
      return { score: 100, factors: {}, bottleneck: false, recommendations: [] }
    }

    const factors = {
      vram: Math.min(gpu.vramGb / model.requirements.gpu.minVramGb, 1),
      compute: Math.min(gpu.computeUnits / 20, 1), // Baseline assumption
      acceleration: (gpu.tensorCores || gpu.cudaCores || gpu.rocmSupport) ? 1 : 0.5,
      compatibility: model.requirements.gpu.supportedVendors.includes(gpu.manufacturer.toLowerCase()) ? 1 : 0.3
    }

    const score = Math.round(
      (factors.vram * 0.4 +
       factors.compute * 0.3 +
       factors.acceleration * 0.2 +
       factors.compatibility * 0.1) * 100
    )

    const bottleneck = score < 60
    const recommendations: string[] = []
    
    if (factors.vram < 0.8) {
      recommendations.push(`GPU needs at least ${model.requirements.gpu.minVramGb}GB VRAM`)
    }
    if (factors.compatibility < 1) {
      recommendations.push('GPU may have limited compatibility with this model')
    }

    return { score, factors, bottleneck, recommendations }
  }

  private generateOptimizations(specs: EnhancedLaptopSpecs, model: EnhancedLLMModel, performance: any) {
    const optimalQuantization = this.getOptimalQuantization(specs.memory.totalGb, model.requirements.memory)
    
    return {
      quantization: optimalQuantization,
      batchSize: Math.min(Math.floor(specs.memory.totalGb / 4), 8),
      contextLength: Math.min(model.performance.contextLength, 4096),
      parallelization: {
        cpuThreads: Math.min(specs.cpu.threads, specs.cpu.cores * 2),
        gpuStreams: specs.gpu ? 2 : undefined
      },
      memoryOptimizations: [
        'Enable memory mapping for large models',
        'Use gradient checkpointing if training',
        'Consider model sharding for very large models'
      ],
      thermalOptimizations: [
        'Monitor CPU/GPU temperatures',
        'Reduce batch size if thermal throttling occurs',
        'Ensure adequate ventilation'
      ]
    }
  }

  private calculateUseCaseSuitability(specs: EnhancedLaptopSpecs, model: EnhancedLLMModel, performance: any) {
    const baseScore = Math.min(performance.tokensPerSecond.optimal / 10, 1) // 10 tokens/sec as baseline

    return {
      development: {
        score: Math.round(baseScore * model.useCases.development * 100),
        reasoning: ['Good for prototyping and testing', 'Adequate performance for development workflows']
      },
      research: {
        score: Math.round(baseScore * model.useCases.research * 100),
        reasoning: ['Suitable for research experiments', 'Can handle moderate workloads']
      },
      production: {
        score: Math.round(baseScore * model.useCases.production * 0.8 * 100), // Production needs higher standards
        reasoning: ['Consider performance requirements', 'May need optimization for production use']
      },
      education: {
        score: Math.round(baseScore * model.useCases.education * 100),
        reasoning: ['Good for learning and experimentation', 'Adequate for educational purposes']
      }
    }
  }

  private generateAssessment(overall: number, components: any) {
    let category: 'excellent' | 'good' | 'acceptable' | 'limited' | 'unsuitable'
    let confidence: number

    if (overall >= 85) {
      category = 'excellent'
      confidence = 0.9
    } else if (overall >= 70) {
      category = 'good'
      confidence = 0.8
    } else if (overall >= 55) {
      category = 'acceptable'
      confidence = 0.7
    } else if (overall >= 40) {
      category = 'limited'
      confidence = 0.6
    } else {
      category = 'unsuitable'
      confidence = 0.8
    }

    const primaryLimitations: string[] = []
    const keyStrengths: string[] = []
    const upgradeRecommendations: string[] = []

    // Analyze components for limitations and strengths
    Object.entries(components).forEach(([component, data]: [string, any]) => {
      if (data && data.score < 60) {
        primaryLimitations.push(`${component.toUpperCase()}: ${data.score}/100`)
        upgradeRecommendations.push(...data.recommendations)
      } else if (data && data.score > 80) {
        keyStrengths.push(`${component.toUpperCase()}: ${data.score}/100`)
      }
    })

    return {
      category,
      confidence,
      primaryLimitations,
      keyStrengths,
      upgradeRecommendations
    }
  }
}
