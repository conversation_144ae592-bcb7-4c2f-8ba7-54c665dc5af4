// LLM Compatibility Service - Calculates compatibility scores for laptops running LLMs

import { BaseService } from '../services/base.service'
import type { LaptopData } from '../scraping/firecrawl.service'

export interface LLMModel {
  id: string
  name: string
  provider: 'ollama' | 'huggingface' | 'openai' | 'anthropic' | 'custom'
  modelSize: 'small' | 'medium' | 'large' | 'xl'
  parameterCount: number // in billions
  quantization?: '4bit' | '8bit' | '16bit' | 'fp32'
  requirements: {
    minRam: number // in GB
    minVram: number // in GB
    minCpuCores: number
    recommendedRam: number // in GB
    recommendedVram: number // in GB
    supportedArchitectures: ('x86_64' | 'arm64' | 'apple_silicon')[]
  }
  performance: {
    tokensPerSecond: number // estimated tokens/sec on recommended hardware
    memoryEfficiency: number // 0-1 scale
    cpuOptimized: boolean
    gpuAccelerated: boolean
  }
}

export interface CompatibilityScore {
  overall: number // 0-100
  breakdown: {
    memory: number // 0-100
    processing: number // 0-100
    graphics: number // 0-100
    storage: number // 0-100
    thermals: number // 0-100
  }
  recommendations: string[]
  warnings: string[]
  estimatedPerformance: {
    tokensPerSecond: number
    maxContextLength: number
    concurrentSessions: number
  }
}

export interface LaptopSpecs {
  ram: number // in GB
  vram?: number // in GB (for dedicated GPU)
  cpu: {
    cores: number
    threads: number
    baseFreq: number // in GHz
    architecture: 'x86_64' | 'arm64' | 'apple_silicon'
    brand: 'intel' | 'amd' | 'apple' | 'qualcomm'
  }
  gpu?: {
    brand: 'nvidia' | 'amd' | 'intel' | 'apple'
    model: string
    vram: number // in GB
    computeUnits: number
  }
  storage: {
    type: 'ssd' | 'hdd' | 'nvme'
    capacity: number // in GB
    speed?: number // read speed in MB/s
  }
  thermals: {
    tdp: number // in watts
    coolingType: 'passive' | 'active' | 'liquid'
    maxTemp: number // in celsius
  }
}

export class LLMCompatibilityService extends BaseService {
  private readonly popularModels: LLMModel[] = [
    {
      id: 'llama2-7b',
      name: 'Llama 2 7B',
      provider: 'ollama',
      modelSize: 'small',
      parameterCount: 7,
      quantization: '4bit',
      requirements: {
        minRam: 8,
        minVram: 0,
        minCpuCores: 4,
        recommendedRam: 16,
        recommendedVram: 6,
        supportedArchitectures: ['x86_64', 'arm64', 'apple_silicon'],
      },
      performance: {
        tokensPerSecond: 25,
        memoryEfficiency: 0.8,
        cpuOptimized: true,
        gpuAccelerated: true,
      },
    },
    {
      id: 'llama2-13b',
      name: 'Llama 2 13B',
      provider: 'ollama',
      modelSize: 'medium',
      parameterCount: 13,
      quantization: '4bit',
      requirements: {
        minRam: 16,
        minVram: 0,
        minCpuCores: 6,
        recommendedRam: 32,
        recommendedVram: 12,
        supportedArchitectures: ['x86_64', 'arm64', 'apple_silicon'],
      },
      performance: {
        tokensPerSecond: 15,
        memoryEfficiency: 0.7,
        cpuOptimized: true,
        gpuAccelerated: true,
      },
    },
    {
      id: 'codellama-7b',
      name: 'Code Llama 7B',
      provider: 'ollama',
      modelSize: 'small',
      parameterCount: 7,
      quantization: '4bit',
      requirements: {
        minRam: 8,
        minVram: 0,
        minCpuCores: 4,
        recommendedRam: 16,
        recommendedVram: 6,
        supportedArchitectures: ['x86_64', 'arm64', 'apple_silicon'],
      },
      performance: {
        tokensPerSecond: 20,
        memoryEfficiency: 0.8,
        cpuOptimized: true,
        gpuAccelerated: true,
      },
    },
    {
      id: 'mistral-7b',
      name: 'Mistral 7B',
      provider: 'ollama',
      modelSize: 'small',
      parameterCount: 7,
      quantization: '4bit',
      requirements: {
        minRam: 6,
        minVram: 0,
        minCpuCores: 4,
        recommendedRam: 12,
        recommendedVram: 6,
        supportedArchitectures: ['x86_64', 'arm64', 'apple_silicon'],
      },
      performance: {
        tokensPerSecond: 30,
        memoryEfficiency: 0.9,
        cpuOptimized: true,
        gpuAccelerated: true,
      },
    },
    {
      id: 'phi3-mini',
      name: 'Phi-3 Mini',
      provider: 'ollama',
      modelSize: 'small',
      parameterCount: 3.8,
      quantization: '4bit',
      requirements: {
        minRam: 4,
        minVram: 0,
        minCpuCores: 2,
        recommendedRam: 8,
        recommendedVram: 4,
        supportedArchitectures: ['x86_64', 'arm64', 'apple_silicon'],
      },
      performance: {
        tokensPerSecond: 40,
        memoryEfficiency: 0.95,
        cpuOptimized: true,
        gpuAccelerated: true,
      },
    },
  ]

  /**
   * Calculate compatibility score for a laptop with specific LLM models
   */
  async calculateCompatibility(
    laptopSpecs: LaptopSpecs,
    models: LLMModel[] = this.popularModels
  ): Promise<Map<string, CompatibilityScore>> {
    return this.executeWithErrorHandling(async () => {
      const results = new Map<string, CompatibilityScore>()

      for (const model of models) {
        const score = this.calculateModelCompatibility(laptopSpecs, model)
        results.set(model.id, score)
      }

      return results
    })
  }

  /**
   * Calculate compatibility score for a single model
   */
  private calculateModelCompatibility(
    specs: LaptopSpecs,
    model: LLMModel
  ): CompatibilityScore {
    const memoryScore = this.calculateMemoryScore(specs, model)
    const processingScore = this.calculateProcessingScore(specs, model)
    const graphicsScore = this.calculateGraphicsScore(specs, model)
    const storageScore = this.calculateStorageScore(specs, model)
    const thermalsScore = this.calculateThermalsScore(specs, model)

    const overall = Math.round(
      (memoryScore * 0.3 +
        processingScore * 0.25 +
        graphicsScore * 0.2 +
        storageScore * 0.15 +
        thermalsScore * 0.1) *
        100
    )

    const recommendations: string[] = []
    const warnings: string[] = []

    // Generate recommendations and warnings
    if (memoryScore < 0.7) {
      warnings.push(`Insufficient RAM: ${specs.ram}GB available, ${model.requirements.recommendedRam}GB recommended`)
      recommendations.push(`Upgrade to at least ${model.requirements.recommendedRam}GB RAM for optimal performance`)
    }

    if (processingScore < 0.6) {
      warnings.push(`CPU may be underpowered for this model`)
      recommendations.push(`Consider a laptop with more CPU cores or higher clock speed`)
    }

    if (graphicsScore < 0.5 && model.performance.gpuAccelerated) {
      warnings.push(`No dedicated GPU detected - model will run on CPU only`)
      recommendations.push(`Consider a laptop with dedicated GPU for better performance`)
    }

    if (storageScore < 0.7) {
      recommendations.push(`Upgrade to NVMe SSD for faster model loading`)
    }

    if (thermalsScore < 0.6) {
      warnings.push(`Thermal constraints may limit sustained performance`)
      recommendations.push(`Ensure adequate cooling for extended LLM usage`)
    }

    // Estimate performance
    const baseTokensPerSecond = model.performance.tokensPerSecond
    const performanceMultiplier = (processingScore + graphicsScore + memoryScore) / 3
    const estimatedTokensPerSecond = Math.round(baseTokensPerSecond * performanceMultiplier)

    const maxContextLength = this.estimateMaxContextLength(specs, model)
    const concurrentSessions = this.estimateConcurrentSessions(specs, model)

    return {
      overall,
      breakdown: {
        memory: Math.round(memoryScore * 100),
        processing: Math.round(processingScore * 100),
        graphics: Math.round(graphicsScore * 100),
        storage: Math.round(storageScore * 100),
        thermals: Math.round(thermalsScore * 100),
      },
      recommendations,
      warnings,
      estimatedPerformance: {
        tokensPerSecond: estimatedTokensPerSecond,
        maxContextLength,
        concurrentSessions,
      },
    }
  }

  /**
   * Calculate memory compatibility score (0-1)
   */
  private calculateMemoryScore(specs: LaptopSpecs, model: LLMModel): number {
    const availableRam = specs.ram
    const minRequired = model.requirements.minRam
    const recommended = model.requirements.recommendedRam

    if (availableRam < minRequired) {
      return 0
    }

    if (availableRam >= recommended) {
      return 1
    }

    // Linear interpolation between min and recommended
    return (availableRam - minRequired) / (recommended - minRequired)
  }

  /**
   * Calculate processing compatibility score (0-1)
   */
  private calculateProcessingScore(specs: LaptopSpecs, model: LLMModel): number {
    const coreScore = Math.min(specs.cpu.cores / model.requirements.minCpuCores, 1)
    const freqScore = Math.min(specs.cpu.baseFreq / 2.5, 1) // Assume 2.5GHz as baseline
    const archScore = model.requirements.supportedArchitectures.includes(specs.cpu.architecture) ? 1 : 0

    return (coreScore * 0.4 + freqScore * 0.4 + archScore * 0.2)
  }

  /**
   * Calculate graphics compatibility score (0-1)
   */
  private calculateGraphicsScore(specs: LaptopSpecs, model: LLMModel): number {
    if (!model.performance.gpuAccelerated) {
      return 1 // GPU not needed
    }

    if (!specs.gpu) {
      return 0.3 // Can run on CPU but not optimal
    }

    const vramScore = specs.gpu.vram >= model.requirements.minVram ? 1 : 0.5
    const brandScore = specs.gpu.brand === 'nvidia' ? 1 : 0.8 // NVIDIA generally better for AI

    return (vramScore * 0.7 + brandScore * 0.3)
  }

  /**
   * Calculate storage compatibility score (0-1)
   */
  private calculateStorageScore(specs: LaptopSpecs, model: LLMModel): number {
    const requiredSpace = model.parameterCount * 2 // Rough estimate: 2GB per billion parameters
    const capacityScore = specs.storage.capacity >= requiredSpace ? 1 : 0.5

    let typeScore = 0.5
    if (specs.storage.type === 'nvme') typeScore = 1
    else if (specs.storage.type === 'ssd') typeScore = 0.8

    return (capacityScore * 0.6 + typeScore * 0.4)
  }

  /**
   * Calculate thermal compatibility score (0-1)
   */
  private calculateThermalsScore(specs: LaptopSpecs, model: LLMModel): number {
    const tdpScore = specs.thermals.tdp >= 45 ? 1 : specs.thermals.tdp / 45
    const coolingScore = specs.thermals.coolingType === 'liquid' ? 1 : 
                        specs.thermals.coolingType === 'active' ? 0.8 : 0.5

    return (tdpScore * 0.6 + coolingScore * 0.4)
  }

  /**
   * Estimate maximum context length based on available memory
   */
  private estimateMaxContextLength(specs: LaptopSpecs, model: LLMModel): number {
    const availableMemoryGB = specs.ram - model.requirements.minRam
    const memoryPerToken = 0.002 // Rough estimate: 2MB per 1000 tokens
    const maxTokens = Math.floor((availableMemoryGB * 1024) / memoryPerToken)
    
    return Math.min(maxTokens, 32000) // Cap at reasonable context length
  }

  /**
   * Estimate number of concurrent sessions
   */
  private estimateConcurrentSessions(specs: LaptopSpecs, model: LLMModel): number {
    const memoryPerSession = model.requirements.recommendedRam
    const availableMemory = specs.ram - model.requirements.minRam
    
    return Math.max(1, Math.floor(availableMemory / memoryPerSession))
  }

  /**
   * Parse laptop specifications from scraped data
   */
  async parseLaptopSpecs(laptopData: LaptopData): Promise<LaptopSpecs | null> {
    return this.executeWithErrorHandling(async () => {
      if (!laptopData.specifications) {
        return null
      }

      const specs = laptopData.specifications
      
      // Parse RAM
      const ramMatch = specs.ram?.match(/(\d+)\s*GB/i)
      const ram = ramMatch ? parseInt(ramMatch[1]) : 8 // Default to 8GB

      // Parse CPU
      const cpu = this.parseCpuSpecs(specs.cpu || '')

      // Parse GPU
      const gpu = this.parseGpuSpecs(specs.gpu || '')

      // Parse storage
      const storage = this.parseStorageSpecs(specs.storage || '')

      // Estimate thermals based on laptop type
      const thermals = this.estimateThermals(laptopData)

      return {
        ram,
        vram: gpu?.vram,
        cpu,
        gpu,
        storage,
        thermals,
      }
    })
  }

  /**
   * Parse CPU specifications from text
   */
  private parseCpuSpecs(cpuText: string): LaptopSpecs['cpu'] {
    const coreMatch = cpuText.match(/(\d+)[-\s]*core/i)
    const cores = coreMatch ? parseInt(coreMatch[1]) : 4

    const freqMatch = cpuText.match(/(\d+\.?\d*)\s*GHz/i)
    const baseFreq = freqMatch ? parseFloat(freqMatch[1]) : 2.5

    let brand: LaptopSpecs['cpu']['brand'] = 'intel'
    if (/amd/i.test(cpuText)) brand = 'amd'
    else if (/apple/i.test(cpuText)) brand = 'apple'
    else if (/qualcomm/i.test(cpuText)) brand = 'qualcomm'

    let architecture: LaptopSpecs['cpu']['architecture'] = 'x86_64'
    if (brand === 'apple') architecture = 'apple_silicon'
    else if (/arm/i.test(cpuText)) architecture = 'arm64'

    return {
      cores,
      threads: cores * 2, // Assume hyperthreading
      baseFreq,
      architecture,
      brand,
    }
  }

  /**
   * Parse GPU specifications from text
   */
  private parseGpuSpecs(gpuText: string): LaptopSpecs['gpu'] | undefined {
    if (!gpuText || /integrated/i.test(gpuText)) {
      return undefined
    }

    const vramMatch = gpuText.match(/(\d+)\s*GB/i)
    const vram = vramMatch ? parseInt(vramMatch[1]) : 4

    let brand: LaptopSpecs['gpu']['brand'] = 'nvidia'
    if (/amd|radeon/i.test(gpuText)) brand = 'amd'
    else if (/intel/i.test(gpuText)) brand = 'intel'
    else if (/apple/i.test(gpuText)) brand = 'apple'

    return {
      brand,
      model: gpuText.trim(),
      vram,
      computeUnits: vram * 256, // Rough estimate
    }
  }

  /**
   * Parse storage specifications from text
   */
  private parseStorageSpecs(storageText: string): LaptopSpecs['storage'] {
    const capacityMatch = storageText.match(/(\d+)\s*(GB|TB)/i)
    let capacity = 512 // Default 512GB
    
    if (capacityMatch) {
      const value = parseInt(capacityMatch[1])
      const unit = capacityMatch[2].toUpperCase()
      capacity = unit === 'TB' ? value * 1024 : value
    }

    let type: LaptopSpecs['storage']['type'] = 'ssd'
    if (/nvme|m\.2/i.test(storageText)) type = 'nvme'
    else if (/hdd|mechanical/i.test(storageText)) type = 'hdd'

    const speed = type === 'nvme' ? 3500 : type === 'ssd' ? 550 : 120

    return {
      type,
      capacity,
      speed,
    }
  }

  /**
   * Estimate thermal characteristics based on laptop data
   */
  private estimateThermals(laptopData: LaptopData): LaptopSpecs['thermals'] {
    const isGaming = /gaming|rog|predator|legion/i.test(laptopData.title)
    const isUltrabook = /ultrabook|thin|light|air/i.test(laptopData.title)

    let tdp = 45 // Default TDP
    let coolingType: LaptopSpecs['thermals']['coolingType'] = 'active'

    if (isGaming) {
      tdp = 65
      coolingType = 'active'
    } else if (isUltrabook) {
      tdp = 25
      coolingType = 'passive'
    }

    return {
      tdp,
      coolingType,
      maxTemp: 85,
    }
  }

  /**
   * Get popular LLM models
   */
  getPopularModels(): LLMModel[] {
    return [...this.popularModels]
  }

  /**
   * Find best models for given laptop specs
   */
  async findBestModels(
    specs: LaptopSpecs,
    maxResults: number = 5
  ): Promise<Array<{ model: LLMModel; score: CompatibilityScore }>> {
    const compatibility = await this.calculateCompatibility(specs)
    
    const results = Array.from(compatibility.entries())
      .map(([modelId, score]) => ({
        model: this.popularModels.find(m => m.id === modelId)!,
        score,
      }))
      .sort((a, b) => b.score.overall - a.score.overall)
      .slice(0, maxResults)

    return results
  }
}
