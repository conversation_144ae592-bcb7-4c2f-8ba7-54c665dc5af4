// LLM Recommendation Service - Provides intelligent laptop recommendations for LLM usage

import { BaseService } from '../services/base.service'
import { LLMCompatibilityService, type LaptopSpecs, type LLMModel, type CompatibilityScore } from './compatibility.service'
import type { LaptopData } from '../scraping/firecrawl.service'

export interface RecommendationCriteria {
  budget?: {
    min?: number
    max?: number
    currency?: string
  }
  preferredModels?: string[] // LLM model IDs
  usageType?: 'development' | 'research' | 'production' | 'education' | 'hobby'
  portability?: 'desktop_replacement' | 'portable' | 'ultraportable'
  performance?: 'basic' | 'good' | 'high' | 'extreme'
  brands?: string[] // Preferred laptop brands
  excludeBrands?: string[] // Brands to exclude
  minScreenSize?: number // in inches
  maxScreenSize?: number // in inches
  requiredFeatures?: string[] // e.g., 'thunderbolt', 'ethernet', 'webcam'
  batteryLife?: 'short' | 'medium' | 'long' // 4h, 8h, 12h+
}

export interface LaptopRecommendation {
  laptop: LaptopData
  specs: LaptopSpecs
  compatibilityScores: Map<string, CompatibilityScore>
  overallScore: number // 0-100
  ranking: number
  pros: string[]
  cons: string[]
  bestFor: string[]
  pricePerformanceRatio: number
  recommendation: {
    confidence: number // 0-100
    reasoning: string
    alternatives?: string[]
  }
}

export interface RecommendationResult {
  recommendations: LaptopRecommendation[]
  summary: {
    totalEvaluated: number
    criteriaMatched: number
    averageScore: number
    priceRange: { min: number; max: number; currency: string }
    topBrands: string[]
  }
  insights: {
    budgetAnalysis: string
    performanceAnalysis: string
    compatibilityAnalysis: string
    marketTrends: string[]
  }
}

export class LLMRecommendationService extends BaseService {
  private compatibilityService: LLMCompatibilityService

  constructor() {
    super()
    this.compatibilityService = new LLMCompatibilityService()
  }

  /**
   * Generate laptop recommendations based on criteria
   */
  async generateRecommendations(
    laptops: LaptopData[],
    criteria: RecommendationCriteria = {}
  ): Promise<RecommendationResult> {
    return this.executeWithErrorHandling(async () => {
      // Filter laptops based on basic criteria
      const filteredLaptops = this.filterLaptopsByCriteria(laptops, criteria)

      // Parse specs and calculate compatibility for each laptop
      const evaluatedLaptops: LaptopRecommendation[] = []

      for (const laptop of filteredLaptops) {
        const specs = await this.compatibilityService.parseLaptopSpecs(laptop)
        if (!specs) continue

        const models = this.getRelevantModels(criteria.preferredModels)
        const compatibilityScores = await this.compatibilityService.calculateCompatibility(specs, models)

        const recommendation = await this.createLaptopRecommendation(
          laptop,
          specs,
          compatibilityScores,
          criteria
        )

        evaluatedLaptops.push(recommendation)
      }

      // Sort by overall score
      evaluatedLaptops.sort((a, b) => b.overallScore - a.overallScore)

      // Assign rankings
      evaluatedLaptops.forEach((rec, index) => {
        rec.ranking = index + 1
      })

      // Generate summary and insights
      const summary = this.generateSummary(evaluatedLaptops, laptops.length)
      const insights = this.generateInsights(evaluatedLaptops, criteria)

      return {
        recommendations: evaluatedLaptops,
        summary,
        insights,
      }
    })
  }

  /**
   * Filter laptops based on basic criteria
   */
  private filterLaptopsByCriteria(
    laptops: LaptopData[],
    criteria: RecommendationCriteria
  ): LaptopData[] {
    return laptops.filter(laptop => {
      // Budget filter
      if (criteria.budget && laptop.price?.current) {
        const price = laptop.price.current
        if (criteria.budget.min && price < criteria.budget.min) return false
        if (criteria.budget.max && price > criteria.budget.max) return false
      }

      // Brand filter
      if (criteria.brands && criteria.brands.length > 0) {
        const laptopBrand = laptop.brand?.toLowerCase()
        if (!laptopBrand || !criteria.brands.some(brand => 
          laptopBrand.includes(brand.toLowerCase())
        )) return false
      }

      // Exclude brands
      if (criteria.excludeBrands && criteria.excludeBrands.length > 0) {
        const laptopBrand = laptop.brand?.toLowerCase()
        if (laptopBrand && criteria.excludeBrands.some(brand => 
          laptopBrand.includes(brand.toLowerCase())
        )) return false
      }

      // Screen size filter
      if (laptop.specifications?.display) {
        const screenMatch = laptop.specifications.display.match(/(\d+\.?\d*)\s*inch/i)
        if (screenMatch) {
          const screenSize = parseFloat(screenMatch[1])
          if (criteria.minScreenSize && screenSize < criteria.minScreenSize) return false
          if (criteria.maxScreenSize && screenSize > criteria.maxScreenSize) return false
        }
      }

      return true
    })
  }

  /**
   * Get relevant LLM models based on preferences
   */
  private getRelevantModels(preferredModels?: string[]): LLMModel[] {
    const allModels = this.compatibilityService.getPopularModels()
    
    if (!preferredModels || preferredModels.length === 0) {
      return allModels
    }

    const preferred = allModels.filter(model => 
      preferredModels.includes(model.id)
    )

    // If no preferred models found, return all
    return preferred.length > 0 ? preferred : allModels
  }

  /**
   * Create a laptop recommendation with scoring
   */
  private async createLaptopRecommendation(
    laptop: LaptopData,
    specs: LaptopSpecs,
    compatibilityScores: Map<string, CompatibilityScore>,
    criteria: RecommendationCriteria
  ): Promise<LaptopRecommendation> {
    // Calculate overall score based on compatibility and criteria
    const compatibilityScore = this.calculateAverageCompatibility(compatibilityScores)
    const criteriaScore = this.calculateCriteriaScore(laptop, specs, criteria)
    const priceScore = this.calculatePriceScore(laptop, criteria)

    const overallScore = Math.round(
      compatibilityScore * 0.5 + 
      criteriaScore * 0.3 + 
      priceScore * 0.2
    )

    // Generate pros and cons
    const { pros, cons } = this.generateProsAndCons(laptop, specs, compatibilityScores)

    // Determine what this laptop is best for
    const bestFor = this.determineBestUseCase(specs, compatibilityScores)

    // Calculate price-performance ratio
    const pricePerformanceRatio = this.calculatePricePerformanceRatio(laptop, compatibilityScore)

    // Generate recommendation reasoning
    const recommendation = this.generateRecommendationReasoning(
      laptop,
      specs,
      compatibilityScores,
      overallScore
    )

    return {
      laptop,
      specs,
      compatibilityScores,
      overallScore,
      ranking: 0, // Will be set later
      pros,
      cons,
      bestFor,
      pricePerformanceRatio,
      recommendation,
    }
  }

  /**
   * Calculate average compatibility score across all models
   */
  private calculateAverageCompatibility(scores: Map<string, CompatibilityScore>): number {
    if (scores.size === 0) return 0

    const totalScore = Array.from(scores.values())
      .reduce((sum, score) => sum + score.overall, 0)

    return totalScore / scores.size
  }

  /**
   * Calculate score based on how well laptop meets criteria
   */
  private calculateCriteriaScore(
    laptop: LaptopData,
    specs: LaptopSpecs,
    criteria: RecommendationCriteria
  ): number {
    let score = 80 // Base score

    // Performance preference
    if (criteria.performance) {
      const performanceScore = this.getPerformanceScore(specs, criteria.performance)
      score = (score + performanceScore) / 2
    }

    // Portability preference
    if (criteria.portability) {
      const portabilityScore = this.getPortabilityScore(laptop, criteria.portability)
      score = (score + portabilityScore) / 2
    }

    // Usage type optimization
    if (criteria.usageType) {
      const usageScore = this.getUsageTypeScore(specs, criteria.usageType)
      score = (score + usageScore) / 2
    }

    return Math.max(0, Math.min(100, score))
  }

  /**
   * Calculate price score based on budget and value
   */
  private calculatePriceScore(laptop: LaptopData, criteria: RecommendationCriteria): number {
    if (!laptop.price?.current || !criteria.budget) return 75

    const price = laptop.price.current
    const { min = 0, max = Infinity } = criteria.budget

    // If within budget, score based on value position
    if (price >= min && price <= max) {
      const budgetRange = max - min
      const pricePosition = (price - min) / budgetRange
      
      // Sweet spot is around 60-80% of budget
      if (pricePosition >= 0.6 && pricePosition <= 0.8) return 100
      if (pricePosition >= 0.4 && pricePosition <= 0.9) return 85
      return 70
    }

    // Penalty for being outside budget
    if (price > max) return Math.max(0, 50 - ((price - max) / max) * 100)
    if (price < min) return 60 // Might be too cheap/low quality

    return 75
  }

  /**
   * Get performance score based on specs and preference
   */
  private getPerformanceScore(specs: LaptopSpecs, preference: string): number {
    const ramScore = Math.min(specs.ram / 32, 1) * 100
    const cpuScore = Math.min(specs.cpu.cores / 8, 1) * 100
    const gpuScore = specs.gpu ? 100 : 50

    const baseScore = (ramScore + cpuScore + gpuScore) / 3

    switch (preference) {
      case 'extreme': return baseScore * 1.2
      case 'high': return baseScore * 1.1
      case 'good': return baseScore
      case 'basic': return Math.min(baseScore * 0.8, 100)
      default: return baseScore
    }
  }

  /**
   * Get portability score based on laptop characteristics
   */
  private getPortabilityScore(laptop: LaptopData, preference: string): number {
    const isGaming = /gaming|rog|predator|legion/i.test(laptop.title)
    const isUltrabook = /ultrabook|thin|light|air/i.test(laptop.title)
    const isWorkstation = /workstation|precision|thinkpad/i.test(laptop.title)

    switch (preference) {
      case 'ultraportable':
        if (isUltrabook) return 100
        if (isGaming || isWorkstation) return 30
        return 70
      case 'portable':
        if (isUltrabook) return 90
        if (isGaming) return 50
        return 80
      case 'desktop_replacement':
        if (isGaming || isWorkstation) return 100
        if (isUltrabook) return 40
        return 70
      default:
        return 75
    }
  }

  /**
   * Get usage type score based on specs and intended use
   */
  private getUsageTypeScore(specs: LaptopSpecs, usageType: string): number {
    switch (usageType) {
      case 'development':
        return Math.min((specs.ram / 16) * 50 + (specs.cpu.cores / 6) * 50, 100)
      case 'research':
        return Math.min((specs.ram / 32) * 60 + (specs.gpu ? 40 : 20), 100)
      case 'production':
        return Math.min((specs.ram / 64) * 40 + (specs.gpu ? 60 : 30), 100)
      case 'education':
        return Math.min((specs.ram / 8) * 70 + 30, 100)
      case 'hobby':
        return Math.min((specs.ram / 12) * 80 + 20, 100)
      default:
        return 75
    }
  }

  /**
   * Generate pros and cons for a laptop
   */
  private generateProsAndCons(
    laptop: LaptopData,
    specs: LaptopSpecs,
    compatibilityScores: Map<string, CompatibilityScore>
  ): { pros: string[]; cons: string[] } {
    const pros: string[] = []
    const cons: string[] = []

    // RAM analysis
    if (specs.ram >= 32) pros.push('Excellent RAM capacity for large models')
    else if (specs.ram >= 16) pros.push('Good RAM for most LLM models')
    else if (specs.ram < 8) cons.push('Limited RAM may restrict model choices')

    // CPU analysis
    if (specs.cpu.cores >= 8) pros.push('High-performance multi-core CPU')
    else if (specs.cpu.cores < 4) cons.push('Limited CPU cores for parallel processing')

    // GPU analysis
    if (specs.gpu) {
      if (specs.gpu.vram >= 8) pros.push('Dedicated GPU with ample VRAM')
      else pros.push('Dedicated GPU for acceleration')
    } else {
      cons.push('No dedicated GPU - CPU-only inference')
    }

    // Storage analysis
    if (specs.storage.type === 'nvme') pros.push('Fast NVMe storage for quick model loading')
    else if (specs.storage.type === 'hdd') cons.push('Slow HDD storage may impact performance')

    // Price analysis
    if (laptop.price?.current) {
      if (laptop.price.current < 800) pros.push('Budget-friendly option')
      else if (laptop.price.current > 2500) cons.push('Premium pricing')
    }

    // Compatibility analysis
    const avgCompatibility = this.calculateAverageCompatibility(compatibilityScores)
    if (avgCompatibility >= 80) pros.push('Excellent compatibility with popular LLM models')
    else if (avgCompatibility < 50) cons.push('Limited compatibility with demanding models')

    return { pros, cons }
  }

  /**
   * Determine what this laptop is best suited for
   */
  private determineBestUseCase(
    specs: LaptopSpecs,
    compatibilityScores: Map<string, CompatibilityScore>
  ): string[] {
    const bestFor: string[] = []

    const avgCompatibility = this.calculateAverageCompatibility(compatibilityScores)

    if (specs.ram >= 32 && specs.gpu && avgCompatibility >= 80) {
      bestFor.push('Large language model research')
      bestFor.push('Production AI applications')
    }

    if (specs.ram >= 16 && avgCompatibility >= 70) {
      bestFor.push('AI development and experimentation')
      bestFor.push('Code generation with LLMs')
    }

    if (specs.ram >= 8 && avgCompatibility >= 50) {
      bestFor.push('Learning and education with LLMs')
      bestFor.push('Personal AI assistant usage')
    }

    if (specs.cpu.cores >= 6) {
      bestFor.push('Parallel model inference')
    }

    if (specs.gpu) {
      bestFor.push('GPU-accelerated AI workloads')
    }

    return bestFor.length > 0 ? bestFor : ['Basic LLM experimentation']
  }

  /**
   * Calculate price-performance ratio
   */
  private calculatePricePerformanceRatio(laptop: LaptopData, compatibilityScore: number): number {
    if (!laptop.price?.current) return 0

    // Higher score means better value (more performance per dollar)
    return Math.round((compatibilityScore / laptop.price.current) * 1000) / 10
  }

  /**
   * Generate recommendation reasoning
   */
  private generateRecommendationReasoning(
    laptop: LaptopData,
    specs: LaptopSpecs,
    compatibilityScores: Map<string, CompatibilityScore>,
    overallScore: number
  ): LaptopRecommendation['recommendation'] {
    const avgCompatibility = this.calculateAverageCompatibility(compatibilityScores)
    
    let confidence = overallScore
    let reasoning = ''

    if (overallScore >= 85) {
      reasoning = `Excellent choice for LLM applications with ${specs.ram}GB RAM and strong compatibility scores. `
      confidence = Math.min(95, confidence)
    } else if (overallScore >= 70) {
      reasoning = `Good option for most LLM use cases with decent specifications. `
      confidence = Math.min(85, confidence)
    } else if (overallScore >= 50) {
      reasoning = `Suitable for basic LLM experimentation but may have limitations. `
      confidence = Math.min(70, confidence)
    } else {
      reasoning = `Limited suitability for LLM applications due to hardware constraints. `
      confidence = Math.min(50, confidence)
    }

    // Add specific details
    if (specs.gpu) {
      reasoning += `The ${specs.gpu.brand} GPU with ${specs.gpu.vram}GB VRAM provides acceleration capabilities. `
    } else {
      reasoning += `CPU-only inference may be slower but still functional for smaller models. `
    }

    if (avgCompatibility >= 80) {
      reasoning += `High compatibility with popular models like Llama 2 and Mistral.`
    } else if (avgCompatibility < 50) {
      reasoning += `Consider upgrading RAM or choosing smaller models for optimal performance.`
    }

    return {
      confidence: Math.round(confidence),
      reasoning: reasoning.trim(),
    }
  }

  /**
   * Generate summary statistics
   */
  private generateSummary(
    recommendations: LaptopRecommendation[],
    totalLaptops: number
  ): RecommendationResult['summary'] {
    if (recommendations.length === 0) {
      return {
        totalEvaluated: 0,
        criteriaMatched: 0,
        averageScore: 0,
        priceRange: { min: 0, max: 0, currency: 'USD' },
        topBrands: [],
      }
    }

    const prices = recommendations
      .map(r => r.laptop.price?.current)
      .filter((p): p is number => p !== undefined)

    const brands = recommendations
      .map(r => r.laptop.brand)
      .filter((b): b is string => b !== undefined)

    const brandCounts = brands.reduce((acc, brand) => {
      acc[brand] = (acc[brand] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const topBrands = Object.entries(brandCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([brand]) => brand)

    return {
      totalEvaluated: recommendations.length,
      criteriaMatched: recommendations.filter(r => r.overallScore >= 70).length,
      averageScore: Math.round(
        recommendations.reduce((sum, r) => sum + r.overallScore, 0) / recommendations.length
      ),
      priceRange: {
        min: prices.length > 0 ? Math.min(...prices) : 0,
        max: prices.length > 0 ? Math.max(...prices) : 0,
        currency: 'USD',
      },
      topBrands,
    }
  }

  /**
   * Generate insights about the recommendations
   */
  private generateInsights(
    recommendations: LaptopRecommendation[],
    criteria: RecommendationCriteria
  ): RecommendationResult['insights'] {
    const budgetAnalysis = this.analyzeBudget(recommendations, criteria)
    const performanceAnalysis = this.analyzePerformance(recommendations)
    const compatibilityAnalysis = this.analyzeCompatibility(recommendations)
    const marketTrends = this.identifyMarketTrends(recommendations)

    return {
      budgetAnalysis,
      performanceAnalysis,
      compatibilityAnalysis,
      marketTrends,
    }
  }

  private analyzeBudget(
    recommendations: LaptopRecommendation[],
    criteria: RecommendationCriteria
  ): string {
    const prices = recommendations
      .map(r => r.laptop.price?.current)
      .filter((p): p is number => p !== undefined)

    if (prices.length === 0) return 'No pricing information available'

    const avgPrice = prices.reduce((sum, p) => sum + p, 0) / prices.length
    const budget = criteria.budget

    if (budget?.max && avgPrice > budget.max * 0.8) {
      return `Recommended laptops average $${Math.round(avgPrice)}, near your budget limit. Consider increasing budget for better options.`
    } else if (budget?.max && avgPrice < budget.max * 0.6) {
      return `Great value found! Recommended laptops average $${Math.round(avgPrice)}, well within your budget.`
    }

    return `Recommended laptops average $${Math.round(avgPrice)}, offering good value for LLM capabilities.`
  }

  private analyzePerformance(recommendations: LaptopRecommendation[]): string {
    const highPerf = recommendations.filter(r => r.overallScore >= 80).length
    const total = recommendations.length

    if (highPerf / total >= 0.7) {
      return 'Excellent performance options available with strong LLM compatibility across the board.'
    } else if (highPerf / total >= 0.3) {
      return 'Mixed performance levels - several good options with some trade-offs to consider.'
    } else {
      return 'Limited high-performance options - consider adjusting criteria or budget for better LLM capabilities.'
    }
  }

  private analyzeCompatibility(recommendations: LaptopRecommendation[]): string {
    const avgCompatibility = recommendations.reduce((sum, r) => 
      sum + this.calculateAverageCompatibility(r.compatibilityScores), 0
    ) / recommendations.length

    if (avgCompatibility >= 80) {
      return 'Excellent LLM compatibility across recommendations - most popular models will run well.'
    } else if (avgCompatibility >= 60) {
      return 'Good compatibility with mainstream LLM models - some limitations with larger models.'
    } else {
      return 'Limited compatibility - focus on smaller, more efficient models for best results.'
    }
  }

  private identifyMarketTrends(recommendations: LaptopRecommendation[]): string[] {
    const trends: string[] = []

    const hasGpu = recommendations.filter(r => r.specs.gpu).length / recommendations.length
    if (hasGpu >= 0.7) trends.push('Strong trend toward GPU-equipped laptops for AI workloads')

    const highRam = recommendations.filter(r => r.specs.ram >= 32).length / recommendations.length
    if (highRam >= 0.5) trends.push('Increasing RAM capacity becoming standard for LLM applications')

    const nvmeStorage = recommendations.filter(r => r.specs.storage.type === 'nvme').length / recommendations.length
    if (nvmeStorage >= 0.8) trends.push('NVMe storage now standard for fast model loading')

    return trends.length > 0 ? trends : ['Diverse hardware configurations available for different use cases']
  }
}
