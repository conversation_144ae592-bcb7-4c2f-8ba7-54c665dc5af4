// Tests for LLMRecommendationService

import { describe, it, expect, beforeEach } from 'vitest'
import { LLMRecommendationService, type RecommendationCriteria } from '../recommendation.service'
import type { LaptopData } from '../../scraping/firecrawl.service'

describe('LLMRecommendationService', () => {
  let service: LLMRecommendationService

  beforeEach(() => {
    service = new LLMRecommendationService()
  })

  const mockLaptops: LaptopData[] = [
    {
      title: 'ASUS ROG Gaming Laptop',
      brand: 'ASUS',
      url: 'https://example.com/laptop1',
      source: 'example.com',
      scrapedAt: new Date(),
      price: { current: 1299, currency: 'USD' },
      specifications: {
        ram: '16GB DDR4',
        cpu: 'Intel Core i7-12700H 8-core 2.7GHz',
        gpu: 'NVIDIA GeForce RTX 3060 6GB',
        storage: '512GB NVMe SSD',
        display: '15.6 inch FHD',
      },
    },
    {
      title: 'Dell XPS 13 Ultrabook',
      brand: 'Dell',
      url: 'https://example.com/laptop2',
      source: 'example.com',
      scrapedAt: new Date(),
      price: { current: 999, currency: 'USD' },
      specifications: {
        ram: '8GB LPDDR4',
        cpu: 'Intel Core i5-1135G7 4-core 2.4GHz',
        gpu: 'Intel Iris Xe Graphics (Integrated)',
        storage: '256GB SSD',
        display: '13.3 inch FHD',
      },
    },
    {
      title: 'MacBook Pro M2',
      brand: 'Apple',
      url: 'https://example.com/laptop3',
      source: 'example.com',
      scrapedAt: new Date(),
      price: { current: 1999, currency: 'USD' },
      specifications: {
        ram: '16GB',
        cpu: 'Apple M2 Pro 10-core',
        gpu: 'Apple M2 Pro GPU',
        storage: '512GB SSD',
        display: '14 inch Retina',
      },
    },
    {
      title: 'Budget Laptop Basic',
      brand: 'Generic',
      url: 'https://example.com/laptop4',
      source: 'example.com',
      scrapedAt: new Date(),
      price: { current: 499, currency: 'USD' },
      specifications: {
        ram: '4GB DDR4',
        cpu: 'Intel Celeron 2-core 1.8GHz',
        storage: '128GB eMMC',
        display: '14 inch HD',
      },
    },
  ]

  describe('Initialization', () => {
    it('should initialize successfully', () => {
      expect(service).toBeDefined()
    })
  })

  describe('Basic recommendations', () => {
    it('should generate recommendations without criteria', async () => {
      const result = await service.generateRecommendations(mockLaptops)

      expect(result).toBeDefined()
      expect(result.recommendations).toBeDefined()
      expect(result.summary).toBeDefined()
      expect(result.insights).toBeDefined()
      expect(result.recommendations.length).toBeGreaterThan(0)
    })

    it('should rank recommendations by overall score', async () => {
      const result = await service.generateRecommendations(mockLaptops)

      expect(result.recommendations.length).toBeGreaterThan(1)
      
      // Check that rankings are in order
      for (let i = 0; i < result.recommendations.length - 1; i++) {
        expect(result.recommendations[i].overallScore).toBeGreaterThanOrEqual(
          result.recommendations[i + 1].overallScore
        )
        expect(result.recommendations[i].ranking).toBe(i + 1)
      }
    })

    it('should include all required recommendation fields', async () => {
      const result = await service.generateRecommendations(mockLaptops)
      const rec = result.recommendations[0]

      expect(rec.laptop).toBeDefined()
      expect(rec.specs).toBeDefined()
      expect(rec.compatibilityScores).toBeDefined()
      expect(rec.overallScore).toBeGreaterThanOrEqual(0)
      expect(rec.overallScore).toBeLessThanOrEqual(100)
      expect(rec.ranking).toBeGreaterThan(0)
      expect(rec.pros).toBeDefined()
      expect(rec.cons).toBeDefined()
      expect(rec.bestFor).toBeDefined()
      expect(rec.pricePerformanceRatio).toBeGreaterThanOrEqual(0)
      expect(rec.recommendation).toBeDefined()
      expect(rec.recommendation.confidence).toBeGreaterThanOrEqual(0)
      expect(rec.recommendation.confidence).toBeLessThanOrEqual(100)
      expect(rec.recommendation.reasoning).toBeDefined()
    })
  })

  describe('Budget filtering', () => {
    it('should filter by budget range', async () => {
      const criteria: RecommendationCriteria = {
        budget: { min: 800, max: 1500 }
      }

      const result = await service.generateRecommendations(mockLaptops, criteria)

      result.recommendations.forEach(rec => {
        const price = rec.laptop.price?.current
        if (price) {
          expect(price).toBeGreaterThanOrEqual(800)
          expect(price).toBeLessThanOrEqual(1500)
        }
      })
    })

    it('should handle minimum budget only', async () => {
      const criteria: RecommendationCriteria = {
        budget: { min: 1000 }
      }

      const result = await service.generateRecommendations(mockLaptops, criteria)

      result.recommendations.forEach(rec => {
        const price = rec.laptop.price?.current
        if (price) {
          expect(price).toBeGreaterThanOrEqual(1000)
        }
      })
    })

    it('should handle maximum budget only', async () => {
      const criteria: RecommendationCriteria = {
        budget: { max: 1200 }
      }

      const result = await service.generateRecommendations(mockLaptops, criteria)

      result.recommendations.forEach(rec => {
        const price = rec.laptop.price?.current
        if (price) {
          expect(price).toBeLessThanOrEqual(1200)
        }
      })
    })
  })

  describe('Brand filtering', () => {
    it('should filter by preferred brands', async () => {
      const criteria: RecommendationCriteria = {
        brands: ['ASUS', 'Dell']
      }

      const result = await service.generateRecommendations(mockLaptops, criteria)

      result.recommendations.forEach(rec => {
        expect(['ASUS', 'Dell']).toContain(rec.laptop.brand)
      })
    })

    it('should exclude specified brands', async () => {
      const criteria: RecommendationCriteria = {
        excludeBrands: ['Apple']
      }

      const result = await service.generateRecommendations(mockLaptops, criteria)

      result.recommendations.forEach(rec => {
        expect(rec.laptop.brand).not.toBe('Apple')
      })
    })

    it('should handle both preferred and excluded brands', async () => {
      const criteria: RecommendationCriteria = {
        brands: ['ASUS', 'Dell', 'Apple'],
        excludeBrands: ['Apple']
      }

      const result = await service.generateRecommendations(mockLaptops, criteria)

      result.recommendations.forEach(rec => {
        expect(['ASUS', 'Dell']).toContain(rec.laptop.brand)
        expect(rec.laptop.brand).not.toBe('Apple')
      })
    })
  })

  describe('Screen size filtering', () => {
    it('should filter by minimum screen size', async () => {
      const criteria: RecommendationCriteria = {
        minScreenSize: 14
      }

      const result = await service.generateRecommendations(mockLaptops, criteria)

      // Should exclude the 13.3 inch laptop
      expect(result.recommendations.some(rec => 
        rec.laptop.title.includes('XPS 13')
      )).toBe(false)
    })

    it('should filter by maximum screen size', async () => {
      const criteria: RecommendationCriteria = {
        maxScreenSize: 14
      }

      const result = await service.generateRecommendations(mockLaptops, criteria)

      // Should exclude the 15.6 inch laptop
      expect(result.recommendations.some(rec => 
        rec.laptop.title.includes('ROG Gaming')
      )).toBe(false)
    })
  })

  describe('Performance and usage type', () => {
    it('should handle development usage type', async () => {
      const criteria: RecommendationCriteria = {
        usageType: 'development'
      }

      const result = await service.generateRecommendations(mockLaptops, criteria)

      expect(result.recommendations.length).toBeGreaterThan(0)
      // Development should favor laptops with good RAM and CPU
      const topRec = result.recommendations[0]
      expect(topRec.specs.ram).toBeGreaterThanOrEqual(8)
    })

    it('should handle research usage type', async () => {
      const criteria: RecommendationCriteria = {
        usageType: 'research'
      }

      const result = await service.generateRecommendations(mockLaptops, criteria)

      expect(result.recommendations.length).toBeGreaterThan(0)
      // Research should favor high-end specs
    })

    it('should handle portability preferences', async () => {
      const criteria: RecommendationCriteria = {
        portability: 'ultraportable'
      }

      const result = await service.generateRecommendations(mockLaptops, criteria)

      expect(result.recommendations.length).toBeGreaterThan(0)
      // Should favor ultrabooks
      const hasUltrabook = result.recommendations.some(rec => 
        rec.laptop.title.toLowerCase().includes('ultrabook')
      )
      expect(hasUltrabook).toBe(true)
    })
  })

  describe('Summary and insights', () => {
    it('should generate accurate summary', async () => {
      const result = await service.generateRecommendations(mockLaptops)

      expect(result.summary.totalEvaluated).toBeGreaterThan(0)
      expect(result.summary.totalEvaluated).toBeLessThanOrEqual(mockLaptops.length)
      expect(result.summary.averageScore).toBeGreaterThanOrEqual(0)
      expect(result.summary.averageScore).toBeLessThanOrEqual(100)
      expect(result.summary.priceRange.min).toBeGreaterThanOrEqual(0)
      expect(result.summary.priceRange.max).toBeGreaterThanOrEqual(result.summary.priceRange.min)
      expect(result.summary.topBrands).toBeDefined()
    })

    it('should generate meaningful insights', async () => {
      const result = await service.generateRecommendations(mockLaptops)

      expect(result.insights.budgetAnalysis).toBeDefined()
      expect(result.insights.budgetAnalysis.length).toBeGreaterThan(0)
      expect(result.insights.performanceAnalysis).toBeDefined()
      expect(result.insights.performanceAnalysis.length).toBeGreaterThan(0)
      expect(result.insights.compatibilityAnalysis).toBeDefined()
      expect(result.insights.compatibilityAnalysis.length).toBeGreaterThan(0)
      expect(result.insights.marketTrends).toBeDefined()
      expect(result.insights.marketTrends.length).toBeGreaterThan(0)
    })
  })

  describe('Edge cases', () => {
    it('should handle empty laptop list', async () => {
      const result = await service.generateRecommendations([])

      expect(result.recommendations).toHaveLength(0)
      expect(result.summary.totalEvaluated).toBe(0)
      expect(result.summary.averageScore).toBe(0)
    })

    it('should handle laptops without specifications', async () => {
      const laptopsWithoutSpecs: LaptopData[] = [
        {
          title: 'Unknown Laptop',
          url: 'https://example.com/laptop',
          source: 'example.com',
          scrapedAt: new Date(),
        }
      ]

      const result = await service.generateRecommendations(laptopsWithoutSpecs)

      // Should handle gracefully, might have 0 recommendations
      expect(result).toBeDefined()
      expect(result.recommendations).toBeDefined()
    })

    it('should handle laptops without pricing', async () => {
      const laptopsWithoutPrice: LaptopData[] = [
        {
          title: 'No Price Laptop',
          brand: 'Test',
          url: 'https://example.com/laptop',
          source: 'example.com',
          scrapedAt: new Date(),
          specifications: {
            ram: '8GB',
            cpu: 'Intel Core i5 4-core 2.5GHz',
            storage: '256GB SSD',
          },
        }
      ]

      const result = await service.generateRecommendations(laptopsWithoutPrice)

      expect(result).toBeDefined()
      expect(result.recommendations.length).toBeGreaterThan(0)
      expect(result.recommendations[0].pricePerformanceRatio).toBe(0)
    })

    it('should handle very restrictive criteria', async () => {
      const criteria: RecommendationCriteria = {
        budget: { min: 5000, max: 6000 }, // Very high budget
        brands: ['NonExistentBrand'],
        minScreenSize: 20, // Very large screen
      }

      const result = await service.generateRecommendations(mockLaptops, criteria)

      // Should return empty results gracefully
      expect(result.recommendations).toHaveLength(0)
      expect(result.summary.totalEvaluated).toBe(0)
    })
  })

  describe('Preferred models', () => {
    it('should handle preferred LLM models', async () => {
      const criteria: RecommendationCriteria = {
        preferredModels: ['llama2-7b', 'mistral-7b']
      }

      const result = await service.generateRecommendations(mockLaptops, criteria)

      expect(result.recommendations.length).toBeGreaterThan(0)
      
      // Check that compatibility scores include the preferred models
      const firstRec = result.recommendations[0]
      expect(firstRec.compatibilityScores.has('llama2-7b')).toBe(true)
      expect(firstRec.compatibilityScores.has('mistral-7b')).toBe(true)
    })

    it('should fallback to all models when preferred models not found', async () => {
      const criteria: RecommendationCriteria = {
        preferredModels: ['non-existent-model']
      }

      const result = await service.generateRecommendations(mockLaptops, criteria)

      expect(result.recommendations.length).toBeGreaterThan(0)
      
      // Should still have compatibility scores for popular models
      const firstRec = result.recommendations[0]
      expect(firstRec.compatibilityScores.size).toBeGreaterThan(0)
    })
  })
})
