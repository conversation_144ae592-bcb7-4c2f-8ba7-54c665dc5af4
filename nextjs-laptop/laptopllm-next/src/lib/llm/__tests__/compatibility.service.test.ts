// Tests for LLMCompatibilityService

import { describe, it, expect, beforeEach } from 'vitest'
import { LLMCompatibilityService, type LaptopSpecs, type LLMModel } from '../compatibility.service'
import type { LaptopData } from '../../scraping/firecrawl.service'

describe('LLMCompatibilityService', () => {
  let service: LLMCompatibilityService

  beforeEach(() => {
    service = new LLMCompatibilityService()
  })

  describe('Initialization', () => {
    it('should initialize with popular models', () => {
      const models = service.getPopularModels()
      expect(models).toBeDefined()
      expect(models.length).toBeGreaterThan(0)
      expect(models[0]).toHaveProperty('id')
      expect(models[0]).toHaveProperty('name')
      expect(models[0]).toHaveProperty('requirements')
    })
  })

  describe('Laptop specs parsing', () => {
    it('should parse complete laptop specifications', async () => {
      const laptopData: LaptopData = {
        title: 'Gaming Laptop ASUS ROG',
        url: 'https://example.com/laptop',
        source: 'example.com',
        scrapedAt: new Date(),
        specifications: {
          ram: '16GB DDR4',
          cpu: 'Intel Core i7-12700H 8-core 2.7GHz',
          gpu: 'NVIDIA GeForce RTX 3060 6GB',
          storage: '512GB NVMe SSD',
        },
      }

      const specs = await service.parseLaptopSpecs(laptopData)

      expect(specs).toBeDefined()
      expect(specs?.ram).toBe(16)
      expect(specs?.cpu.cores).toBe(8)
      expect(specs?.cpu.baseFreq).toBe(2.7)
      expect(specs?.cpu.brand).toBe('intel')
      expect(specs?.cpu.architecture).toBe('x86_64')
      expect(specs?.gpu?.brand).toBe('nvidia')
      expect(specs?.gpu?.vram).toBe(6)
      expect(specs?.storage.type).toBe('nvme')
      expect(specs?.storage.capacity).toBe(512)
    })

    it('should handle missing specifications gracefully', async () => {
      const laptopData: LaptopData = {
        title: 'Basic Laptop',
        url: 'https://example.com/laptop',
        source: 'example.com',
        scrapedAt: new Date(),
      }

      const specs = await service.parseLaptopSpecs(laptopData)
      expect(specs).toBeNull()
    })

    it('should parse AMD laptop specifications', async () => {
      const laptopData: LaptopData = {
        title: 'AMD Laptop',
        url: 'https://example.com/laptop',
        source: 'example.com',
        scrapedAt: new Date(),
        specifications: {
          ram: '32GB',
          cpu: 'AMD Ryzen 7 5800H 8-core 3.2GHz',
          gpu: 'AMD Radeon RX 6600M 8GB',
          storage: '1TB SSD',
        },
      }

      const specs = await service.parseLaptopSpecs(laptopData)

      expect(specs?.cpu.brand).toBe('amd')
      expect(specs?.cpu.baseFreq).toBe(3.2)
      expect(specs?.gpu?.brand).toBe('amd')
      expect(specs?.gpu?.vram).toBe(8)
      expect(specs?.storage.type).toBe('ssd')
      expect(specs?.storage.capacity).toBe(1024)
    })

    it('should parse Apple Silicon specifications', async () => {
      const laptopData: LaptopData = {
        title: 'MacBook Pro',
        url: 'https://example.com/laptop',
        source: 'example.com',
        scrapedAt: new Date(),
        specifications: {
          ram: '16GB',
          cpu: 'Apple M2 Pro 10-core',
          gpu: 'Apple M2 Pro GPU',
          storage: '512GB SSD',
        },
      }

      const specs = await service.parseLaptopSpecs(laptopData)

      expect(specs?.cpu.brand).toBe('apple')
      expect(specs?.cpu.architecture).toBe('apple_silicon')
      expect(specs?.gpu?.brand).toBe('apple')
    })

    it('should handle integrated graphics', async () => {
      const laptopData: LaptopData = {
        title: 'Business Laptop',
        url: 'https://example.com/laptop',
        source: 'example.com',
        scrapedAt: new Date(),
        specifications: {
          ram: '8GB',
          cpu: 'Intel Core i5-1135G7 4-core 2.4GHz',
          gpu: 'Intel Iris Xe Graphics (Integrated)',
          storage: '256GB SSD',
        },
      }

      const specs = await service.parseLaptopSpecs(laptopData)

      expect(specs?.gpu).toBeUndefined()
      expect(specs?.ram).toBe(8)
      expect(specs?.cpu.cores).toBe(4)
    })
  })

  describe('Compatibility calculation', () => {
    const highEndSpecs: LaptopSpecs = {
      ram: 32,
      vram: 8,
      cpu: {
        cores: 8,
        threads: 16,
        baseFreq: 3.2,
        architecture: 'x86_64',
        brand: 'amd',
      },
      gpu: {
        brand: 'nvidia',
        model: 'RTX 3070',
        vram: 8,
        computeUnits: 2048,
      },
      storage: {
        type: 'nvme',
        capacity: 1024,
        speed: 3500,
      },
      thermals: {
        tdp: 65,
        coolingType: 'active',
        maxTemp: 85,
      },
    }

    const lowEndSpecs: LaptopSpecs = {
      ram: 8,
      cpu: {
        cores: 4,
        threads: 8,
        baseFreq: 2.0,
        architecture: 'x86_64',
        brand: 'intel',
      },
      storage: {
        type: 'ssd',
        capacity: 256,
        speed: 550,
      },
      thermals: {
        tdp: 25,
        coolingType: 'passive',
        maxTemp: 85,
      },
    }

    it('should calculate high compatibility for powerful laptop', async () => {
      const compatibility = await service.calculateCompatibility(highEndSpecs)

      expect(compatibility.size).toBeGreaterThan(0)

      const llama7bScore = compatibility.get('llama2-7b')
      expect(llama7bScore).toBeDefined()
      expect(llama7bScore?.overall).toBeGreaterThan(80)
      expect(llama7bScore?.breakdown.memory).toBeGreaterThan(80)
      expect(llama7bScore?.breakdown.processing).toBeGreaterThan(70)
      expect(llama7bScore?.breakdown.graphics).toBeGreaterThan(80)
      expect(llama7bScore?.warnings).toHaveLength(0)
    })

    it('should calculate lower compatibility for basic laptop', async () => {
      const compatibility = await service.calculateCompatibility(lowEndSpecs)

      const llama13bScore = compatibility.get('llama2-13b')
      expect(llama13bScore).toBeDefined()
      expect(llama13bScore?.overall).toBeLessThan(60)
      expect(llama13bScore?.warnings.length).toBeGreaterThan(0)
      expect(llama13bScore?.recommendations.length).toBeGreaterThan(0)
    })

    it('should provide performance estimates', async () => {
      const compatibility = await service.calculateCompatibility(highEndSpecs)

      const phi3Score = compatibility.get('phi3-mini')
      expect(phi3Score?.estimatedPerformance.tokensPerSecond).toBeGreaterThan(0)
      expect(phi3Score?.estimatedPerformance.maxContextLength).toBeGreaterThan(0)
      expect(phi3Score?.estimatedPerformance.concurrentSessions).toBeGreaterThan(0)
    })

    it('should handle laptops without dedicated GPU', async () => {
      const compatibility = await service.calculateCompatibility(lowEndSpecs)

      const mistralScore = compatibility.get('mistral-7b')
      expect(mistralScore?.breakdown.graphics).toBeLessThan(50)
      expect(mistralScore?.warnings.some(w => w.includes('GPU'))).toBe(true)
    })

    it('should calculate compatibility for single model', async () => {
      const testModel: LLMModel = {
        id: 'test-model',
        name: 'Test Model',
        provider: 'ollama',
        modelSize: 'small',
        parameterCount: 3,
        requirements: {
          minRam: 4,
          minVram: 0,
          minCpuCores: 2,
          recommendedRam: 8,
          recommendedVram: 4,
          supportedArchitectures: ['x86_64'],
        },
        performance: {
          tokensPerSecond: 50,
          memoryEfficiency: 0.9,
          cpuOptimized: true,
          gpuAccelerated: false,
        },
      }

      const compatibility = await service.calculateCompatibility(highEndSpecs, [testModel])

      expect(compatibility.size).toBe(1)
      const score = compatibility.get('test-model')
      expect(score).toBeDefined()
      expect(score?.overall).toBeGreaterThan(80)
    })
  })

  describe('Best models recommendation', () => {
    const midRangeSpecs: LaptopSpecs = {
      ram: 16,
      vram: 6,
      cpu: {
        cores: 6,
        threads: 12,
        baseFreq: 2.8,
        architecture: 'x86_64',
        brand: 'intel',
      },
      gpu: {
        brand: 'nvidia',
        model: 'RTX 3060',
        vram: 6,
        computeUnits: 1536,
      },
      storage: {
        type: 'nvme',
        capacity: 512,
        speed: 3000,
      },
      thermals: {
        tdp: 45,
        coolingType: 'active',
        maxTemp: 85,
      },
    }

    it('should find best models for given specs', async () => {
      const bestModels = await service.findBestModels(midRangeSpecs, 3)

      expect(bestModels).toHaveLength(3)
      expect(bestModels[0].score.overall).toBeGreaterThanOrEqual(bestModels[1].score.overall)
      expect(bestModels[1].score.overall).toBeGreaterThanOrEqual(bestModels[2].score.overall)

      // Should include model information
      expect(bestModels[0].model.name).toBeDefined()
      expect(bestModels[0].model.requirements).toBeDefined()
    })

    it('should limit results to requested number', async () => {
      const bestModels = await service.findBestModels(midRangeSpecs, 2)
      expect(bestModels).toHaveLength(2)
    })

    it('should return all models if fewer than requested', async () => {
      const bestModels = await service.findBestModels(midRangeSpecs, 10)
      expect(bestModels.length).toBeLessThanOrEqual(service.getPopularModels().length)
    })
  })

  describe('Specification parsing edge cases', () => {
    it('should handle various RAM formats', async () => {
      const testCases = [
        { ram: '8GB DDR4', expected: 8 },
        { ram: '16 GB', expected: 16 },
        { ram: '32GB LPDDR5', expected: 32 },
        { ram: '4GB', expected: 4 },
      ]

      for (const testCase of testCases) {
        const laptopData: LaptopData = {
          title: 'Test Laptop',
          url: 'https://example.com/laptop',
          source: 'example.com',
          scrapedAt: new Date(),
          specifications: {
            ram: testCase.ram,
            cpu: 'Intel Core i5 4-core 2.5GHz',
            storage: '256GB SSD',
          },
        }

        const specs = await service.parseLaptopSpecs(laptopData)
        expect(specs?.ram).toBe(testCase.expected)
      }
    })

    it('should handle various storage formats', async () => {
      const testCases = [
        { storage: '512GB NVMe SSD', expectedType: 'nvme', expectedCapacity: 512 },
        { storage: '1TB SSD', expectedType: 'ssd', expectedCapacity: 1024 },
        { storage: '256GB M.2 SSD', expectedType: 'nvme', expectedCapacity: 256 },
        { storage: '2TB HDD', expectedType: 'hdd', expectedCapacity: 2048 },
      ]

      for (const testCase of testCases) {
        const laptopData: LaptopData = {
          title: 'Test Laptop',
          url: 'https://example.com/laptop',
          source: 'example.com',
          scrapedAt: new Date(),
          specifications: {
            ram: '8GB',
            cpu: 'Intel Core i5 4-core 2.5GHz',
            storage: testCase.storage,
          },
        }

        const specs = await service.parseLaptopSpecs(laptopData)
        expect(specs?.storage.type).toBe(testCase.expectedType)
        expect(specs?.storage.capacity).toBe(testCase.expectedCapacity)
      }
    })

    it('should estimate thermals based on laptop type', async () => {
      const gamingLaptop: LaptopData = {
        title: 'ASUS ROG Gaming Laptop',
        url: 'https://example.com/laptop',
        source: 'example.com',
        scrapedAt: new Date(),
        specifications: {
          ram: '16GB',
          cpu: 'Intel Core i7 8-core 3.0GHz',
          storage: '512GB SSD',
        },
      }

      const ultrabook: LaptopData = {
        title: 'Dell XPS 13 Ultrabook',
        url: 'https://example.com/laptop',
        source: 'example.com',
        scrapedAt: new Date(),
        specifications: {
          ram: '8GB',
          cpu: 'Intel Core i5 4-core 2.5GHz',
          storage: '256GB SSD',
        },
      }

      const gamingSpecs = await service.parseLaptopSpecs(gamingLaptop)
      const ultrabookSpecs = await service.parseLaptopSpecs(ultrabook)

      expect(gamingSpecs?.thermals.tdp).toBeGreaterThan(ultrabookSpecs?.thermals.tdp || 0)
      expect(gamingSpecs?.thermals.coolingType).toBe('active')
    })
  })
})
