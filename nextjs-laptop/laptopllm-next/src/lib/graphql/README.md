# GraphQL Integration

## Overview

This directory contains the complete GraphQL implementation for the LaptopLLM Finder application. It provides a modern, type-safe API layer that enables efficient data fetching and manipulation for laptop search, compatibility analysis, and analytics.

## Architecture

```
src/lib/graphql/
├── schema.ts          # GraphQL schema definition
├── resolvers.ts       # Query and mutation resolvers
├── types.ts           # TypeScript type definitions
├── server.ts          # Apollo Server configuration
├── client.ts          # Apollo Client setup
├── queries.ts         # Predefined queries and mutations
├── hooks.ts           # React hooks for GraphQL operations
└── __tests__/         # Test suite
```

## Key Features

### 🔍 **Advanced Search & Filtering**
- Full-text search across laptop titles, brands, and descriptions
- Complex filtering by specifications, price range, compatibility scores
- Intelligent sorting by price, compatibility, rating, and popularity
- Cursor-based pagination for efficient data loading

### 📊 **Real-time Analytics**
- Comprehensive laptop statistics and market insights
- Brand distribution and compatibility score analysis
- Price trends and performance metrics
- Top-performing laptops for LLM workloads

### 🏷️ **Deals Management**
- Hot deals tracking with discount calculations
- Active deal filtering and expiration monitoring
- Store-specific pricing and availability

### 🔧 **LLM Compatibility Analysis**
- Detailed compatibility scoring for different LLM models
- Hardware requirement analysis and recommendations
- Performance predictions and optimization suggestions

### 🚀 **Performance Optimizations**
- Intelligent caching strategies with Apollo Client
- Optimized database queries with Prisma
- Efficient pagination and data loading
- Real-time updates with minimal re-fetching

## Quick Start

### 1. Server Setup

The GraphQL server is automatically configured and runs at `/api/graphql`:

```typescript
// Already configured in src/app/api/graphql/route.ts
import { handler } from '@/lib/graphql/server'
export { handler as GET, handler as POST }
```

### 2. Client Usage

Use the provided hooks for easy GraphQL integration:

```typescript
import { useSearchLaptops, useGetLaptop } from '@/lib/graphql/hooks'

function LaptopSearch() {
  const { data, loading, error } = useSearchLaptops({
    query: "gaming laptop",
    filters: {
      brands: ["ASUS", "MSI"],
      priceRange: { min: 1000, max: 3000 }
    }
  })

  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error.message}</div>

  return (
    <div>
      {data?.laptops.edges.map(({ node: laptop }) => (
        <div key={laptop.id}>
          <h3>{laptop.title}</h3>
          <p>{laptop.brand} - ${laptop.price}</p>
        </div>
      ))}
    </div>
  )
}
```

### 3. Direct Apollo Client Usage

For more control, use Apollo Client directly:

```typescript
import { apolloClient } from '@/lib/graphql/client'
import { SEARCH_LAPTOPS } from '@/lib/graphql/queries'

const { data } = await apolloClient.query({
  query: SEARCH_LAPTOPS,
  variables: {
    input: {
      query: "laptop",
      pagination: { page: 1, limit: 20 }
    }
  }
})
```

## Schema Highlights

### Core Types

- **Laptop**: Main entity with specifications, compatibility, deals, and reviews
- **Specifications**: Detailed hardware specs (CPU, memory, storage, GPU, display)
- **Compatibility**: LLM compatibility analysis with scoring and recommendations
- **Deal**: Active deals and discounts with expiration tracking
- **Analytics**: Market insights and statistical data

### Key Queries

- `laptops(input: LaptopSearchInput)`: Advanced laptop search with filtering
- `laptop(id: ID!)`: Get detailed laptop information
- `laptopAnalytics`: Comprehensive market analytics
- `hotDeals(limit: Int)`: Current active deals
- `getRecommendations(input: RecommendationInput!)`: Personalized recommendations

### Mutations

- `createLaptop(input: CreateLaptopInput!)`: Add new laptop (admin)
- `updateLaptop(id: ID!, input: UpdateLaptopInput!)`: Update laptop data (admin)
- `triggerScraping(laptopId: ID)`: Trigger data scraping
- `updateCompatibility(laptopId: ID!, input: CompatibilityInput!)`: Update compatibility scores

## Advanced Features

### Intelligent Caching

The Apollo Client is configured with smart caching policies:

```typescript
// Laptop data cached for 5 minutes
// Search results cached with query-specific keys
// Analytics cached for 15 minutes
// Real-time updates for critical data
```

### Error Handling

Comprehensive error handling with user-friendly messages:

```typescript
// Network errors, GraphQL errors, and validation errors
// Automatic retry logic for transient failures
// Detailed error logging in development
```

### Type Safety

Full TypeScript integration with generated types:

```bash
npm run graphql:codegen  # Generate types from schema
npm run graphql:watch   # Watch mode for development
```

### Performance Monitoring

Built-in performance tracking:

```typescript
// Request timing and performance metrics
// Query complexity analysis
// Cache hit/miss ratios
// Real-time monitoring dashboard
```

## Testing

Comprehensive test suite covering:

- Query and mutation functionality
- Error handling and edge cases
- Pagination and filtering
- Performance and caching
- Type safety and validation

```bash
npm test src/lib/graphql/__tests__/
```

## Development Tools

### GraphQL Playground

Access the interactive GraphQL Playground in development:
- URL: `http://localhost:3000/api/graphql`
- Features: Query builder, schema docs, real-time testing

### Apollo DevTools

Install the Apollo Client DevTools browser extension for:
- Query inspection and debugging
- Cache visualization
- Performance monitoring
- Mutation tracking

## Best Practices

### Query Optimization

1. **Use fragments** for reusable field selections
2. **Request only needed fields** to minimize payload
3. **Implement pagination** for large datasets
4. **Cache results** with appropriate policies
5. **Use variables** instead of string interpolation

### Error Handling

1. **Check loading states** before rendering data
2. **Handle network errors** gracefully
3. **Provide fallback UI** for error states
4. **Log errors** for debugging
5. **Retry failed requests** when appropriate

### Performance

1. **Batch queries** when possible
2. **Use lazy queries** for conditional loading
3. **Implement infinite scrolling** for large lists
4. **Optimize cache policies** for your use case
5. **Monitor query performance** in production

## Integration Examples

### Search Component

```typescript
function LaptopSearchComponent() {
  const [searchLaptops, { data, loading }] = useLazySearchLaptops()
  
  const handleSearch = (query: string) => {
    searchLaptops({
      variables: {
        input: { query, pagination: { limit: 20 } }
      }
    })
  }

  return (
    <SearchInterface 
      onSearch={handleSearch}
      results={data?.laptops.edges}
      loading={loading}
    />
  )
}
```

### Analytics Dashboard

```typescript
function AnalyticsDashboard() {
  const { data: analytics } = useGetLaptopAnalytics()
  const { data: deals } = useGetHotDeals(10)
  
  return (
    <Dashboard>
      <StatsCards data={analytics} />
      <DealsSection deals={deals?.hotDeals} />
      <BrandDistribution brands={analytics?.brandDistribution} />
    </Dashboard>
  )
}
```

### Laptop Comparison

```typescript
function LaptopComparison({ laptopIds }: { laptopIds: string[] }) {
  const { laptops, loading } = useLaptopComparison(laptopIds)
  
  return (
    <ComparisonTable 
      laptops={laptops}
      loading={loading}
    />
  )
}
```

## Contributing

When adding new GraphQL features:

1. **Update the schema** in `schema.ts`
2. **Implement resolvers** in `resolvers.ts`
3. **Add TypeScript types** in `types.ts`
4. **Create queries/mutations** in `queries.ts`
5. **Add React hooks** in `hooks.ts`
6. **Write tests** in `__tests__/`
7. **Update documentation**

## Support

For GraphQL-related issues:
- Check the GraphQL Playground for schema documentation
- Review error messages for debugging hints
- Use TypeScript for better development experience
- Consult the Apollo Client documentation for advanced features
