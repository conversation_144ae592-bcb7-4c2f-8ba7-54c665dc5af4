/**
 * GraphQL TypeScript Types
 * Type definitions for GraphQL schema
 */

// Enums
export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC'
}

export enum LaptopSortBy {
  PRICE = 'PRICE',
  COMPATIBILITY = 'COMPATIBILITY',
  RATING = 'RATING',
  NAME = 'NAME',
  NEWEST = 'NEWEST',
  POPULARITY = 'POPULARITY'
}

export enum ScrapingStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  PAUSED = 'PAUSED'
}

export enum GPUType {
  INTEGRATED = 'INTEGRATED',
  DEDICATED = 'DEDICATED'
}

export enum StorageType {
  HDD = 'HDD',
  SSD = 'SSD',
  NVME = 'NVME',
  HYBRID = 'HYBRID'
}

export enum MemoryType {
  DDR3 = 'DDR3',
  DDR4 = 'DDR4',
  DDR5 = 'DDR5',
  LPDDR4 = 'LPDDR4',
  LPDDR5 = 'LPDDR5'
}

// Input types
export interface PaginationInput {
  page?: number
  limit?: number
}

export interface SortInput {
  sortBy?: LaptopSortBy
  sortOrder?: SortOrder
}

export interface PriceRangeInput {
  min?: number
  max?: number
}

export interface SpecificationFiltersInput {
  minMemory?: number
  maxMemory?: number
  minStorage?: number
  maxStorage?: number
  gpuType?: GPUType
  storageType?: StorageType
  memoryType?: MemoryType
}

export interface SearchFiltersInput {
  brands?: string[]
  priceRange?: PriceRangeInput
  specifications?: SpecificationFiltersInput
  minCompatibilityScore?: number
  maxCompatibilityScore?: number
  inStock?: boolean
  hasDeals?: boolean
}

export interface LaptopSearchInput {
  query?: string
  filters?: SearchFiltersInput
  pagination?: PaginationInput
  sort?: SortInput
}

export interface RecommendationInput {
  budget?: PriceRangeInput
  primaryUse?: string
  portabilityImportance?: number
  performanceImportance?: number
  batteryImportance?: number
  preferredBrands?: string[]
}

export interface CreateLaptopInput {
  title: string
  brand: string
  model: string
  price?: number
  url: string
  description?: string
  category?: string
}

export interface UpdateLaptopInput {
  title?: string
  brand?: string
  model?: string
  price?: number
  description?: string
  category?: string
  isAvailable?: boolean
}

export interface CompatibilityInput {
  averageScore: number
  cpuScore: number
  memoryScore: number
  gpuScore: number
  storageScore: number
  thermalScore: number
  powerScore: number
  overallRating: string
  recommendedModels?: string[]
  warnings?: string[]
  recommendations?: string[]
}

// Core types
export interface CPU {
  id: string
  manufacturer: string
  model: string
  cores: number
  threads: number
  baseFrequency: number
  maxFrequency?: number
  cache?: string
  architecture: string
  tdp?: number
}

export interface Memory {
  id: string
  size: number
  type: MemoryType
  speed: number
  slots?: number
  maxCapacity?: number
  isUpgradeable: boolean
}

export interface Storage {
  id: string
  capacity: number
  type: StorageType
  interface?: string
  speed?: number
  isUpgradeable: boolean
  additionalSlots?: number
}

export interface Display {
  id: string
  size: number
  resolution: string
  refreshRate: number
  panelType?: string
  brightness?: number
  colorGamut?: string
  isTouch: boolean
  aspectRatio?: string
}

export interface GPU {
  id: string
  manufacturer: string
  model: string
  type: GPUType
  memory?: number
  memoryType?: string
  baseClock?: number
  boostClock?: number
  tdp?: number
}

export interface Connectivity {
  id: string
  wifi?: string
  bluetooth?: string
  ethernet?: boolean
  usbPorts?: any
  hdmiPorts?: number
  displayPorts?: number
  audioJack?: boolean
  sdCardSlot?: boolean
  thunderbolt?: number
}

export interface Specifications {
  id: string
  cpu?: CPU
  memory?: Memory
  storage?: Storage
  display?: Display
  gpu?: GPU
  connectivity?: Connectivity
}

export interface Features {
  id: string
  weight?: number
  dimensions?: string
  batteryLife?: number
  batteryCapacity?: number
  chargingSpeed?: number
  keyboard?: string
  trackpad?: string
  webcam?: string
  speakers?: string
  cooling?: string
  build?: string
  color?: string
  operatingSystem?: string
  warranty?: string
  certifications?: string[]
  category?: string
}

export interface Store {
  id: string
  name: string
  url: string
  price?: number
  inStock: boolean
  shipping?: string
  rating?: number
}

export interface Availability {
  id: string
  inStock: boolean
  stockLevel?: string
  estimatedDelivery?: string
  stores: Store[]
}

export interface LLMModel {
  model: string
  score: number
  estimatedPerformance?: string
  memoryRequirement?: number
  notes?: string
}

export interface DetailedScore {
  category: string
  score: number
  weight: number
  description?: string
  factors?: string[]
}

export interface Compatibility {
  id: string
  averageScore: number
  cpuScore: number
  memoryScore: number
  gpuScore: number
  storageScore: number
  thermalScore: number
  powerScore: number
  overallRating: string
  recommendedModels?: string[]
  warnings?: string[]
  recommendations?: string[]
  lastUpdated: Date
  topModels: LLMModel[]
  detailedScores: DetailedScore[]
}

export interface Deal {
  id: string
  title: string
  description?: string
  originalPrice: number
  discountedPrice: number
  discountPercentage: number
  startDate: Date
  expiresAt: Date
  store: string
  url: string
  isActive: boolean
}

export interface ReviewSource {
  id: string
  name: string
  url?: string
}

export interface Review {
  id: string
  rating: number
  title?: string
  content: string
  author?: string
  date: Date
  verified: boolean
  helpful?: number
  source?: ReviewSource
}

export interface ScrapingJob {
  id: string
  status: ScrapingStatus
  lastScrapedAt?: Date
  nextScheduledAt?: Date
}

export interface Laptop {
  id: string
  title: string
  brand: string
  model: string
  price?: number
  originalPrice?: number
  currency: string
  rating?: number
  reviewCount?: number
  url: string
  description?: string
  category?: string
  isAvailable: boolean
  createdAt: Date
  updatedAt: Date
  
  // Relations
  specifications?: Specifications
  features?: Features
  availability?: Availability
  compatibility?: Compatibility
  images: string[]
  reviews: Review[]
  deals: Deal[]
  scrapingStatus?: ScrapingJob
}

// Pagination and search results
export interface PageInfo {
  hasNextPage: boolean
  hasPreviousPage: boolean
  startCursor?: string
  endCursor?: string
  totalCount: number
  totalPages: number
  currentPage: number
}

export interface LaptopEdge {
  node: Laptop
  cursor: string
}

export interface SearchSummary {
  appliedFilters?: any
  resultCount: number
  priceRange?: PriceRange
  brandDistribution: BrandCount[]
  compatibilityDistribution: CompatibilityDistribution
}

export interface LaptopConnection {
  edges: LaptopEdge[]
  pageInfo: PageInfo
  totalCount: number
  filters?: SearchSummary
}

export interface PriceRange {
  min: number
  max: number
  average: number
}

export interface BrandCount {
  brand: string
  count: number
}

export interface CompatibilityDistribution {
  excellent: number  // 90-100
  good: number       // 75-89
  fair: number       // 60-74
  poor: number       // <60
}

// Analytics types
export interface PriceDistribution {
  range: string
  count: number
  percentage: number
}

export interface CompatibilityStats {
  averageScore: number
  distribution: CompatibilityDistribution
  topPerformers: Laptop[]
}

export interface FeatureCount {
  feature: string
  count: number
  percentage: number
}

export interface LaptopAnalytics {
  totalLaptops: number
  averagePrice: number
  priceDistribution: PriceDistribution[]
  brandDistribution: BrandCount[]
  compatibilityStats: CompatibilityStats
  popularFeatures: FeatureCount[]
  trendingLaptops: Laptop[]
}

// Recommendation types
export interface Recommendation {
  laptop: Laptop
  score: number
  reasons: string[]
  pros: string[]
  cons: string[]
  matchPercentage: number
}

export interface RecommendationResult {
  recommendations: Recommendation[]
  alternatives: Recommendation[]
  summary: string
}

// GraphQL Context
export interface GraphQLContext {
  prisma: any
  user?: any
  req: any
  res: any
}
