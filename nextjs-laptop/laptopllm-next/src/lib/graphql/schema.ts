/**
 * GraphQL Schema Definition
 * Comprehensive schema for LaptopLLM Finder API
 */

import { gql } from 'graphql-tag'

export const typeDefs = gql`
  # Scala<PERSON> types
  scalar DateTime
  scalar JSON

  # Enums
  enum SortOrder {
    ASC
    DESC
  }

  enum LaptopSortBy {
    PRICE
    COMPATIBILITY
    RATING
    NAME
    NEWEST
    POPULARITY
  }

  enum ScrapingStatus {
    PENDING
    RUNNING
    COMPLETED
    FAILED
    CANCELLED
    PAUSED
  }

  enum GPUType {
    INTEGRATED
    DEDICATED
  }

  enum StorageType {
    HDD
    SSD
    NVME
    HYBRID
  }

  enum MemoryType {
    DDR3
    DDR4
    DDR5
    LPDDR4
    LPDDR5
  }

  # Input types
  input PaginationInput {
    page: Int = 1
    limit: Int = 20
  }

  input SortInput {
    sortBy: LaptopSortBy = COMPATIBILITY
    sortOrder: SortOrder = DESC
  }

  input PriceRangeInput {
    min: Float
    max: Float
  }

  input SpecificationFiltersInput {
    minMemory: Int
    maxMemory: Int
    minStorage: Int
    maxStorage: Int
    gpuType: GPUType
    storageType: StorageType
    memoryType: MemoryType
  }

  input SearchFiltersInput {
    brands: [String!]
    priceRange: PriceRangeInput
    specifications: SpecificationFiltersInput
    minCompatibilityScore: Int
    maxCompatibilityScore: Int
    inStock: Boolean
    hasDeals: Boolean
  }

  input LaptopSearchInput {
    query: String
    filters: SearchFiltersInput
    pagination: PaginationInput
    sort: SortInput
  }

  # Core types
  type CPU {
    id: ID!
    manufacturer: String!
    model: String!
    cores: Int!
    threads: Int!
    baseFrequency: Float!
    maxFrequency: Float
    cache: String
    architecture: String!
    tdp: Int
  }

  type Memory {
    id: ID!
    size: Int!
    type: MemoryType!
    speed: Int!
    slots: Int
    maxCapacity: Int
    isUpgradeable: Boolean!
  }

  type Storage {
    id: ID!
    capacity: Int!
    type: StorageType!
    interface: String
    speed: Int
    isUpgradeable: Boolean!
    additionalSlots: Int
  }

  type Display {
    id: ID!
    size: Float!
    resolution: String!
    refreshRate: Int!
    panelType: String
    brightness: Int
    colorGamut: String
    isTouch: Boolean!
    aspectRatio: String
  }

  type GPU {
    id: ID!
    manufacturer: String!
    model: String!
    type: GPUType!
    memory: Int
    memoryType: String
    baseClock: Int
    boostClock: Int
    tdp: Int
  }

  type Connectivity {
    id: ID!
    wifi: String
    bluetooth: String
    ethernet: Boolean
    usbPorts: JSON
    hdmiPorts: Int
    displayPorts: Int
    audioJack: Boolean
    sdCardSlot: Boolean
    thunderbolt: Int
  }

  type Specifications {
    id: ID!
    cpu: CPU
    memory: Memory
    storage: Storage
    display: Display
    gpu: GPU
    connectivity: Connectivity
  }

  type Features {
    id: ID!
    weight: Float
    dimensions: String
    batteryLife: Int
    batteryCapacity: Int
    chargingSpeed: Int
    keyboard: String
    trackpad: String
    webcam: String
    speakers: String
    cooling: String
    build: String
    color: String
    operatingSystem: String
    warranty: String
    certifications: [String!]
    category: String
  }

  type Store {
    id: ID!
    name: String!
    url: String!
    price: Float
    inStock: Boolean!
    shipping: String
    rating: Float
  }

  type Availability {
    id: ID!
    inStock: Boolean!
    stockLevel: String
    estimatedDelivery: String
    stores: [Store!]!
  }

  type LLMModel {
    model: String!
    score: Int!
    estimatedPerformance: String
    memoryRequirement: Int
    notes: String
  }

  type DetailedScore {
    category: String!
    score: Int!
    weight: Float!
    description: String
    factors: [String!]
  }

  type Compatibility {
    id: ID!
    averageScore: Int!
    cpuScore: Int!
    memoryScore: Int!
    gpuScore: Int!
    storageScore: Int!
    thermalScore: Int!
    powerScore: Int!
    overallRating: String!
    recommendedModels: [String!]
    warnings: [String!]
    recommendations: [String!]
    lastUpdated: DateTime!
    topModels: [LLMModel!]!
    detailedScores: [DetailedScore!]!
  }

  type Deal {
    id: ID!
    title: String!
    description: String
    originalPrice: Float!
    discountedPrice: Float!
    discountPercentage: Float!
    startDate: DateTime!
    expiresAt: DateTime!
    store: String!
    url: String!
    isActive: Boolean!
  }

  type Review {
    id: ID!
    rating: Float!
    title: String
    content: String!
    author: String
    date: DateTime!
    verified: Boolean!
    helpful: Int
    source: ReviewSource
  }

  type ReviewSource {
    id: ID!
    name: String!
    url: String
  }

  type ScrapingJob {
    id: ID!
    status: ScrapingStatus!
    lastScrapedAt: DateTime
    nextScheduledAt: DateTime
  }

  type Laptop {
    id: ID!
    title: String!
    brand: String!
    model: String!
    price: Float
    originalPrice: Float
    currency: String!
    rating: Float
    reviewCount: Int
    url: String!
    description: String
    category: String
    isAvailable: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relations
    specifications: Specifications
    features: Features
    availability: Availability
    compatibility: Compatibility
    images: [String!]!
    reviews: [Review!]!
    deals: [Deal!]!
    scrapingStatus: ScrapingJob
  }

  # Pagination and search results
  type PageInfo {
    hasNextPage: Boolean!
    hasPreviousPage: Boolean!
    startCursor: String
    endCursor: String
    totalCount: Int!
    totalPages: Int!
    currentPage: Int!
  }

  type LaptopConnection {
    edges: [LaptopEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
    filters: SearchSummary
  }

  type LaptopEdge {
    node: Laptop!
    cursor: String!
  }

  type SearchSummary {
    appliedFilters: JSON
    resultCount: Int!
    priceRange: PriceRange
    brandDistribution: [BrandCount!]!
    compatibilityDistribution: CompatibilityDistribution!
  }

  type PriceRange {
    min: Float!
    max: Float!
    average: Float!
  }

  type BrandCount {
    brand: String!
    count: Int!
  }

  type CompatibilityDistribution {
    excellent: Int!  # 90-100
    good: Int!       # 75-89
    fair: Int!       # 60-74
    poor: Int!       # <60
  }

  # Analytics types
  type LaptopAnalytics {
    totalLaptops: Int!
    averagePrice: Float!
    priceDistribution: [PriceDistribution!]!
    brandDistribution: [BrandCount!]!
    compatibilityStats: CompatibilityStats!
    popularFeatures: [FeatureCount!]!
    trendingLaptops: [Laptop!]!
  }

  type PriceDistribution {
    range: String!
    count: Int!
    percentage: Float!
  }

  type CompatibilityStats {
    averageScore: Float!
    distribution: CompatibilityDistribution!
    topPerformers: [Laptop!]!
  }

  type FeatureCount {
    feature: String!
    count: Int!
    percentage: Float!
  }

  # Recommendation types
  type RecommendationInput {
    budget: PriceRangeInput
    primaryUse: String
    portabilityImportance: Int
    performanceImportance: Int
    batteryImportance: Int
    preferredBrands: [String!]
  }

  type Recommendation {
    laptop: Laptop!
    score: Float!
    reasons: [String!]!
    pros: [String!]!
    cons: [String!]!
    matchPercentage: Float!
  }

  type RecommendationResult {
    recommendations: [Recommendation!]!
    alternatives: [Recommendation!]!
    summary: String!
  }

  # Root types
  type Query {
    # Laptop queries
    laptop(id: ID!): Laptop
    laptops(input: LaptopSearchInput): LaptopConnection!
    
    # Search and filtering
    searchLaptops(query: String!, filters: SearchFiltersInput, pagination: PaginationInput, sort: SortInput): LaptopConnection!
    
    # Analytics
    laptopAnalytics: LaptopAnalytics!
    
    # Recommendations
    getRecommendations(input: RecommendationInput!): RecommendationResult!
    
    # Deals
    hotDeals(limit: Int = 10): [Deal!]!
    
    # Brands and filters
    availableBrands: [String!]!
    priceRange: PriceRange!
    
    # Health check
    health: String!
  }

  type Mutation {
    # Laptop management (admin only)
    createLaptop(input: CreateLaptopInput!): Laptop!
    updateLaptop(id: ID!, input: UpdateLaptopInput!): Laptop!
    deleteLaptop(id: ID!): Boolean!
    
    # Scraping operations
    triggerScraping(laptopId: ID): ScrapingJob!
    
    # Compatibility updates
    updateCompatibility(laptopId: ID!, input: CompatibilityInput!): Compatibility!
  }

  # Mutation input types
  input CreateLaptopInput {
    title: String!
    brand: String!
    model: String!
    price: Float
    url: String!
    description: String
    category: String
  }

  input UpdateLaptopInput {
    title: String
    brand: String
    model: String
    price: Float
    description: String
    category: String
    isAvailable: Boolean
  }

  input CompatibilityInput {
    averageScore: Int!
    cpuScore: Int!
    memoryScore: Int!
    gpuScore: Int!
    storageScore: Int!
    thermalScore: Int!
    powerScore: Int!
    overallRating: String!
    recommendedModels: [String!]
    warnings: [String!]
    recommendations: [String!]
  }
`
