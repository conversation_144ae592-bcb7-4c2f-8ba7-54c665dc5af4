/**
 * Apollo GraphQL Client Configuration
 * Client setup for GraphQL queries and mutations
 */

import { ApolloClient, InMemoryCache, createHttpLink, from } from '@apollo/client'
import { setContext } from '@apollo/client/link/context'
import { onError } from '@apollo/client/link/error'

// HTTP Link for GraphQL endpoint
const httpLink = createHttpLink({
  uri: process.env.NODE_ENV === 'development' 
    ? 'http://localhost:3000/api/graphql'
    : '/api/graphql',
  credentials: 'same-origin'
})

// Auth link for adding authentication headers
const authLink = setContext((_, { headers }) => {
  // Get authentication token from localStorage or cookies
  // TODO: Implement authentication token retrieval
  const token = null

  return {
    headers: {
      ...headers,
      ...(token && { authorization: `Bearer ${token}` })
    }
  }
})

// Error link for handling GraphQL errors
const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path }) => {
      console.error(
        `GraphQL error: Message: ${message}, Location: ${locations}, Path: ${path}`
      )
    })
  }

  if (networkError) {
    console.error(`Network error: ${networkError}`)
    
    // Handle specific network errors
    if ('statusCode' in networkError) {
      switch (networkError.statusCode) {
        case 401:
          // Handle unauthorized access
          console.error('Unauthorized access - redirecting to login')
          // TODO: Redirect to login page
          break
        case 403:
          // Handle forbidden access
          console.error('Forbidden access')
          break
        case 500:
          // Handle server errors
          console.error('Server error')
          break
      }
    }
  }
})

// Combine links
const link = from([
  errorLink,
  authLink,
  httpLink
])

// Apollo Client configuration
export const apolloClient = new ApolloClient({
  link,
  cache: new InMemoryCache({
    typePolicies: {
      // Laptop type policies
      Laptop: {
        fields: {
          // Cache laptop reviews with pagination
          reviews: {
            merge(existing = [], incoming) {
              return [...existing, ...incoming]
            }
          },
          // Cache laptop deals
          deals: {
            merge(existing = [], incoming) {
              return incoming
            }
          }
        }
      },
      // LaptopConnection type policies for pagination
      LaptopConnection: {
        fields: {
          edges: {
            merge(existing = [], incoming) {
              return incoming
            }
          }
        }
      },
      // Query type policies
      Query: {
        fields: {
          // Cache laptop search results
          laptops: {
            keyArgs: ['input', ['query', 'filters', 'sort']],
            merge(existing, incoming) {
              if (!existing) return incoming
              
              return {
                ...incoming,
                edges: [...(existing.edges || []), ...incoming.edges]
              }
            }
          },
          // Cache search results
          searchLaptops: {
            keyArgs: ['query', 'filters', 'sort'],
            merge(existing, incoming) {
              if (!existing) return incoming
              
              return {
                ...incoming,
                edges: [...(existing.edges || []), ...incoming.edges]
              }
            }
          }
        }
      }
    }
  }),
  // Default options for queries and mutations
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all',
      fetchPolicy: 'cache-and-network'
    },
    query: {
      errorPolicy: 'all',
      fetchPolicy: 'cache-first'
    },
    mutate: {
      errorPolicy: 'all'
    }
  },
  // Enable dev tools in development
  connectToDevTools: process.env.NODE_ENV === 'development'
})

// Export client instance
export default apolloClient

// Helper function to clear cache
export const clearCache = () => {
  apolloClient.cache.reset()
}

// Helper function to refetch all active queries
export const refetchQueries = () => {
  apolloClient.refetchQueries({
    include: 'active'
  })
}
