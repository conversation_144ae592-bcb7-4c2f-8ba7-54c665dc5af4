/**
 * GraphQL Queries and Mutations
 * Predefined queries and mutations for the LaptopLLM Finder
 */

import { gql } from '@apollo/client'

// Fragment for laptop basic info
export const LAPTOP_BASIC_FRAGMENT = gql`
  fragment LaptopBasic on Laptop {
    id
    title
    brand
    model
    price
    originalPrice
    currency
    rating
    reviewCount
    url
    description
    category
    isAvailable
    createdAt
    updatedAt
    images
  }
`

// Fragment for laptop specifications
export const LAPTOP_SPECS_FRAGMENT = gql`
  fragment LaptopSpecs on Laptop {
    specifications {
      id
      cpu {
        id
        manufacturer
        model
        cores
        threads
        baseFrequency
        maxFrequency
        cache
        architecture
        tdp
      }
      memory {
        id
        size
        type
        speed
        slots
        maxCapacity
        isUpgradeable
      }
      storage {
        id
        capacity
        type
        interface
        speed
        isUpgradeable
        additionalSlots
      }
      display {
        id
        size
        resolution
        refreshRate
        panelType
        brightness
        colorGamut
        isTouch
        aspectRatio
      }
      gpu {
        id
        manufacturer
        model
        type
        memory
        memoryType
        baseClock
        boostClock
        tdp
      }
      connectivity {
        id
        wifi
        bluetooth
        ethernet
        usbPorts
        hdmiPorts
        displayPorts
        audioJack
        sdCardSlot
        thunderbolt
      }
    }
  }
`

// Fragment for laptop compatibility
export const LAPTOP_COMPATIBILITY_FRAGMENT = gql`
  fragment LaptopCompatibility on Laptop {
    compatibility {
      id
      averageScore
      cpuScore
      memoryScore
      gpuScore
      storageScore
      thermalScore
      powerScore
      overallRating
      recommendedModels
      warnings
      recommendations
      lastUpdated
      topModels {
        model
        score
        estimatedPerformance
        memoryRequirement
        notes
      }
      detailedScores {
        category
        score
        weight
        description
        factors
      }
    }
  }
`

// Fragment for laptop deals
export const LAPTOP_DEALS_FRAGMENT = gql`
  fragment LaptopDeals on Laptop {
    deals {
      id
      title
      description
      originalPrice
      discountedPrice
      discountPercentage
      startDate
      expiresAt
      store
      url
      isActive
    }
  }
`

// Fragment for full laptop data
export const LAPTOP_FULL_FRAGMENT = gql`
  fragment LaptopFull on Laptop {
    ...LaptopBasic
    ...LaptopSpecs
    ...LaptopCompatibility
    ...LaptopDeals
    features {
      id
      weight
      dimensions
      batteryLife
      batteryCapacity
      chargingSpeed
      keyboard
      trackpad
      webcam
      speakers
      cooling
      build
      color
      operatingSystem
      warranty
      certifications
      category
    }
    availability {
      id
      inStock
      stockLevel
      estimatedDelivery
      stores {
        id
        name
        url
        price
        inStock
        shipping
        rating
      }
    }
    reviews {
      id
      rating
      title
      content
      author
      date
      verified
      helpful
      source {
        id
        name
        url
      }
    }
    scrapingStatus {
      id
      status
      lastScrapedAt
      nextScheduledAt
    }
  }
  ${LAPTOP_BASIC_FRAGMENT}
  ${LAPTOP_SPECS_FRAGMENT}
  ${LAPTOP_COMPATIBILITY_FRAGMENT}
  ${LAPTOP_DEALS_FRAGMENT}
`

// QUERIES

// Get single laptop by ID
export const GET_LAPTOP = gql`
  query GetLaptop($id: ID!) {
    laptop(id: $id) {
      ...LaptopFull
    }
  }
  ${LAPTOP_FULL_FRAGMENT}
`

// Search laptops with filters and pagination
export const SEARCH_LAPTOPS = gql`
  query SearchLaptops($input: LaptopSearchInput) {
    laptops(input: $input) {
      edges {
        node {
          ...LaptopBasic
          ...LaptopCompatibility
          specifications {
            cpu {
              manufacturer
              model
              cores
              threads
            }
            memory {
              size
              type
            }
            storage {
              capacity
              type
            }
            gpu {
              manufacturer
              model
              type
            }
          }
        }
        cursor
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
        totalCount
        totalPages
        currentPage
      }
      totalCount
    }
  }
  ${LAPTOP_BASIC_FRAGMENT}
  ${LAPTOP_COMPATIBILITY_FRAGMENT}
`

// Get laptop analytics
export const GET_LAPTOP_ANALYTICS = gql`
  query GetLaptopAnalytics {
    laptopAnalytics {
      totalLaptops
      averagePrice
      brandDistribution {
        brand
        count
      }
      compatibilityStats {
        averageScore
        distribution {
          excellent
          good
          fair
          poor
        }
        topPerformers {
          ...LaptopBasic
          ...LaptopCompatibility
        }
      }
      priceDistribution {
        range
        count
        percentage
      }
      popularFeatures {
        feature
        count
        percentage
      }
      trendingLaptops {
        ...LaptopBasic
        ...LaptopCompatibility
      }
    }
  }
  ${LAPTOP_BASIC_FRAGMENT}
  ${LAPTOP_COMPATIBILITY_FRAGMENT}
`

// Get hot deals
export const GET_HOT_DEALS = gql`
  query GetHotDeals($limit: Int = 10) {
    hotDeals(limit: $limit) {
      id
      title
      description
      originalPrice
      discountedPrice
      discountPercentage
      startDate
      expiresAt
      store
      url
      isActive
    }
  }
`

// Get available brands
export const GET_AVAILABLE_BRANDS = gql`
  query GetAvailableBrands {
    availableBrands
  }
`

// Get price range
export const GET_PRICE_RANGE = gql`
  query GetPriceRange {
    priceRange {
      min
      max
      average
    }
  }
`

// Get recommendations
export const GET_RECOMMENDATIONS = gql`
  query GetRecommendations($input: RecommendationInput!) {
    getRecommendations(input: $input) {
      recommendations {
        laptop {
          ...LaptopFull
        }
        score
        reasons
        pros
        cons
        matchPercentage
      }
      alternatives {
        laptop {
          ...LaptopBasic
          ...LaptopCompatibility
        }
        score
        reasons
        pros
        cons
        matchPercentage
      }
      summary
    }
  }
  ${LAPTOP_FULL_FRAGMENT}
  ${LAPTOP_BASIC_FRAGMENT}
  ${LAPTOP_COMPATIBILITY_FRAGMENT}
`

// Health check
export const HEALTH_CHECK = gql`
  query HealthCheck {
    health
  }
`

// MUTATIONS

// Create laptop (admin only)
export const CREATE_LAPTOP = gql`
  mutation CreateLaptop($input: CreateLaptopInput!) {
    createLaptop(input: $input) {
      ...LaptopFull
    }
  }
  ${LAPTOP_FULL_FRAGMENT}
`

// Update laptop (admin only)
export const UPDATE_LAPTOP = gql`
  mutation UpdateLaptop($id: ID!, $input: UpdateLaptopInput!) {
    updateLaptop(id: $id, input: $input) {
      ...LaptopFull
    }
  }
  ${LAPTOP_FULL_FRAGMENT}
`

// Delete laptop (admin only)
export const DELETE_LAPTOP = gql`
  mutation DeleteLaptop($id: ID!) {
    deleteLaptop(id: $id)
  }
`

// Trigger scraping
export const TRIGGER_SCRAPING = gql`
  mutation TriggerScraping($laptopId: ID) {
    triggerScraping(laptopId: $laptopId) {
      id
      status
      lastScrapedAt
      nextScheduledAt
    }
  }
`

// Update compatibility
export const UPDATE_COMPATIBILITY = gql`
  mutation UpdateCompatibility($laptopId: ID!, $input: CompatibilityInput!) {
    updateCompatibility(laptopId: $laptopId, input: $input) {
      ...LaptopCompatibility
    }
  }
  ${LAPTOP_COMPATIBILITY_FRAGMENT}
`
