/**
 * GraphQL API Tests
 * Comprehensive tests for GraphQL queries and mutations
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { ApolloServer } from '@apollo/server'
import { typeDefs } from '../schema'
import { resolvers } from '../resolvers'
import { prisma } from '@/lib/prisma'
import type { GraphQLContext } from '../types'

// Test server setup
let testServer: ApolloServer<GraphQLContext>

beforeAll(async () => {
  testServer = new ApolloServer<GraphQLContext>({
    typeDefs,
    resolvers,
  })
})

afterAll(async () => {
  await testServer?.stop()
})

// Mock context
const mockContext: GraphQLContext = {
  prisma,
  user: null,
  req: {} as any,
  res: {} as any,
}

describe('GraphQL API', () => {
  describe('Health Check', () => {
    it('should return health status', async () => {
      const query = `
        query {
          health
        }
      `

      const response = await testServer.executeOperation(
        { query },
        { contextValue: mockContext }
      )

      expect(response.body.kind).toBe('single')
      if (response.body.kind === 'single') {
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data?.health).toBe('GraphQL API is running!')
      }
    })
  })

  describe('Laptop Queries', () => {
    it('should search laptops with basic query', async () => {
      const query = `
        query SearchLaptops($input: LaptopSearchInput) {
          laptops(input: $input) {
            edges {
              node {
                id
                title
                brand
                model
                price
                currency
              }
            }
            pageInfo {
              hasNextPage
              totalCount
              currentPage
            }
          }
        }
      `

      const variables = {
        input: {
          pagination: { page: 1, limit: 5 }
        }
      }

      const response = await testServer.executeOperation(
        { query, variables },
        { contextValue: mockContext }
      )

      expect(response.body.kind).toBe('single')
      if (response.body.kind === 'single') {
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data?.laptops).toBeDefined()
        expect(response.body.singleResult.data?.laptops.edges).toBeInstanceOf(Array)
        expect(response.body.singleResult.data?.laptops.pageInfo).toBeDefined()
      }
    })

    it('should search laptops with filters', async () => {
      const query = `
        query SearchLaptops($input: LaptopSearchInput) {
          laptops(input: $input) {
            edges {
              node {
                id
                title
                brand
                price
              }
            }
            totalCount
          }
        }
      `

      const variables = {
        input: {
          query: "gaming",
          filters: {
            brands: ["ASUS", "MSI"],
            priceRange: {
              min: 1000,
              max: 3000
            }
          },
          pagination: { page: 1, limit: 10 }
        }
      }

      const response = await testServer.executeOperation(
        { query, variables },
        { contextValue: mockContext }
      )

      expect(response.body.kind).toBe('single')
      if (response.body.kind === 'single') {
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data?.laptops).toBeDefined()
      }
    })

    it('should get single laptop by ID', async () => {
      // First, get a laptop ID from search
      const searchQuery = `
        query {
          laptops(input: { pagination: { limit: 1 } }) {
            edges {
              node {
                id
              }
            }
          }
        }
      `

      const searchResponse = await testServer.executeOperation(
        { query: searchQuery },
        { contextValue: mockContext }
      )

      if (searchResponse.body.kind === 'single' && 
          searchResponse.body.singleResult.data?.laptops.edges.length > 0) {
        
        const laptopId = searchResponse.body.singleResult.data.laptops.edges[0].node.id

        const query = `
          query GetLaptop($id: ID!) {
            laptop(id: $id) {
              id
              title
              brand
              model
              price
              specifications {
                cpu {
                  manufacturer
                  model
                  cores
                }
                memory {
                  size
                  type
                }
              }
              compatibility {
                averageScore
                overallRating
              }
            }
          }
        `

        const response = await testServer.executeOperation(
          { query, variables: { id: laptopId } },
          { contextValue: mockContext }
        )

        expect(response.body.kind).toBe('single')
        if (response.body.kind === 'single') {
          expect(response.body.singleResult.errors).toBeUndefined()
          expect(response.body.singleResult.data?.laptop).toBeDefined()
          expect(response.body.singleResult.data?.laptop.id).toBe(laptopId)
        }
      }
    })
  })

  describe('Analytics Queries', () => {
    it('should get laptop analytics', async () => {
      const query = `
        query {
          laptopAnalytics {
            totalLaptops
            averagePrice
            brandDistribution {
              brand
              count
            }
            compatibilityStats {
              averageScore
              distribution {
                excellent
                good
                fair
                poor
              }
            }
          }
        }
      `

      const response = await testServer.executeOperation(
        { query },
        { contextValue: mockContext }
      )

      expect(response.body.kind).toBe('single')
      if (response.body.kind === 'single') {
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data?.laptopAnalytics).toBeDefined()
        expect(typeof response.body.singleResult.data?.laptopAnalytics.totalLaptops).toBe('number')
        expect(typeof response.body.singleResult.data?.laptopAnalytics.averagePrice).toBe('number')
      }
    })

    it('should get available brands', async () => {
      const query = `
        query {
          availableBrands
        }
      `

      const response = await testServer.executeOperation(
        { query },
        { contextValue: mockContext }
      )

      expect(response.body.kind).toBe('single')
      if (response.body.kind === 'single') {
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data?.availableBrands).toBeInstanceOf(Array)
      }
    })

    it('should get price range', async () => {
      const query = `
        query {
          priceRange {
            min
            max
            average
          }
        }
      `

      const response = await testServer.executeOperation(
        { query },
        { contextValue: mockContext }
      )

      expect(response.body.kind).toBe('single')
      if (response.body.kind === 'single') {
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data?.priceRange).toBeDefined()
        expect(typeof response.body.singleResult.data?.priceRange.min).toBe('number')
        expect(typeof response.body.singleResult.data?.priceRange.max).toBe('number')
        expect(typeof response.body.singleResult.data?.priceRange.average).toBe('number')
      }
    })
  })

  describe('Deals Queries', () => {
    it('should get hot deals', async () => {
      const query = `
        query GetHotDeals($limit: Int) {
          hotDeals(limit: $limit) {
            id
            title
            originalPrice
            discountedPrice
            discountPercentage
            store
            isActive
          }
        }
      `

      const response = await testServer.executeOperation(
        { query, variables: { limit: 5 } },
        { contextValue: mockContext }
      )

      expect(response.body.kind).toBe('single')
      if (response.body.kind === 'single') {
        expect(response.body.singleResult.errors).toBeUndefined()
        expect(response.body.singleResult.data?.hotDeals).toBeInstanceOf(Array)
      }
    })
  })

  describe('Error Handling', () => {
    it('should handle invalid laptop ID', async () => {
      const query = `
        query GetLaptop($id: ID!) {
          laptop(id: $id) {
            id
            title
          }
        }
      `

      const response = await testServer.executeOperation(
        { query, variables: { id: 'invalid-id' } },
        { contextValue: mockContext }
      )

      expect(response.body.kind).toBe('single')
      if (response.body.kind === 'single') {
        // Should return null for non-existent laptop, not an error
        expect(response.body.singleResult.data?.laptop).toBeNull()
      }
    })

    it('should handle malformed queries', async () => {
      const query = `
        query {
          invalidField
        }
      `

      const response = await testServer.executeOperation(
        { query },
        { contextValue: mockContext }
      )

      expect(response.body.kind).toBe('single')
      if (response.body.kind === 'single') {
        expect(response.body.singleResult.errors).toBeDefined()
        expect(response.body.singleResult.errors?.length).toBeGreaterThan(0)
      }
    })
  })

  describe('Pagination', () => {
    it('should handle pagination correctly', async () => {
      const query = `
        query SearchLaptops($input: LaptopSearchInput) {
          laptops(input: $input) {
            edges {
              node {
                id
              }
            }
            pageInfo {
              hasNextPage
              hasPreviousPage
              totalCount
              totalPages
              currentPage
            }
          }
        }
      `

      const variables = {
        input: {
          pagination: { page: 1, limit: 5 }
        }
      }

      const response = await testServer.executeOperation(
        { query, variables },
        { contextValue: mockContext }
      )

      expect(response.body.kind).toBe('single')
      if (response.body.kind === 'single') {
        expect(response.body.singleResult.errors).toBeUndefined()
        const pageInfo = response.body.singleResult.data?.laptops.pageInfo
        expect(pageInfo).toBeDefined()
        expect(typeof pageInfo?.totalCount).toBe('number')
        expect(typeof pageInfo?.currentPage).toBe('number')
        expect(typeof pageInfo?.hasNextPage).toBe('boolean')
        expect(typeof pageInfo?.hasPreviousPage).toBe('boolean')
      }
    })
  })
})
