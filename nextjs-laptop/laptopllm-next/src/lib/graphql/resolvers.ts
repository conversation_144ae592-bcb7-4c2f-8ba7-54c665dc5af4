/**
 * GraphQL Resolvers
 * Comprehensive resolvers for LaptopLLM Finder API
 */

import { prisma } from '@/lib/prisma'
import { GraphQLScalarType, Kind } from 'graphql'
import type { 
  LaptopSearchInput, 
  SearchFiltersInput, 
  PaginationInput, 
  SortInput,
  RecommendationInput,
  CreateLaptopInput,
  UpdateLaptopInput,
  CompatibilityInput
} from './types'

// Custom scalar for DateTime
const DateTimeScalar = new GraphQLScalarType({
  name: 'DateTime',
  description: 'Date custom scalar type',
  serialize(value: any) {
    return value instanceof Date ? value.toISOString() : value
  },
  parseValue(value: any) {
    return new Date(value)
  },
  parseLiteral(ast) {
    if (ast.kind === Kind.STRING) {
      return new Date(ast.value)
    }
    return null
  },
})

// Custom scalar for JSON
const JSONScalar = new GraphQLScalarType({
  name: '<PERSON><PERSON><PERSON>',
  description: 'JSON custom scalar type',
  serialize(value: any) {
    return value
  },
  parseValue(value: any) {
    return value
  },
  parseLiteral(ast) {
    if (ast.kind === Kind.STRING) {
      try {
        return JSON.parse(ast.value)
      } catch {
        return null
      }
    }
    return null
  },
})

// Helper functions
function buildWhereClause(filters?: SearchFiltersInput) {
  const where: any = {}

  if (filters) {
    // Brand filters
    if (filters.brands && filters.brands.length > 0) {
      where.brand = { in: filters.brands }
    }

    // Price range
    if (filters.priceRange) {
      where.price = {}
      if (filters.priceRange.min !== undefined) {
        where.price.gte = filters.priceRange.min
      }
      if (filters.priceRange.max !== undefined) {
        where.price.lte = filters.priceRange.max
      }
    }

    // Stock filter
    if (filters.inStock !== undefined) {
      where.isAvailable = filters.inStock
    }

    // Deals filter
    if (filters.hasDeals) {
      where.deals = {
        some: {
          isActive: true,
          expiresAt: { gte: new Date() }
        }
      }
    }

    // Compatibility score
    if (filters.minCompatibilityScore !== undefined || filters.maxCompatibilityScore !== undefined) {
      where.compatibility = {}
      if (filters.minCompatibilityScore !== undefined) {
        where.compatibility.averageScore = { gte: filters.minCompatibilityScore }
      }
      if (filters.maxCompatibilityScore !== undefined) {
        where.compatibility.averageScore = { 
          ...where.compatibility.averageScore,
          lte: filters.maxCompatibilityScore 
        }
      }
    }

    // Specification filters
    if (filters.specifications) {
      const specs = filters.specifications

      if (specs.minMemory !== undefined || specs.maxMemory !== undefined) {
        where.specifications = { memory: {} }
        if (specs.minMemory !== undefined) {
          where.specifications.memory.size = { gte: specs.minMemory }
        }
        if (specs.maxMemory !== undefined) {
          where.specifications.memory.size = { 
            ...where.specifications.memory.size,
            lte: specs.maxMemory 
          }
        }
      }

      if (specs.minStorage !== undefined || specs.maxStorage !== undefined) {
        where.specifications = { ...where.specifications, storage: {} }
        if (specs.minStorage !== undefined) {
          where.specifications.storage.capacity = { gte: specs.minStorage }
        }
        if (specs.maxStorage !== undefined) {
          where.specifications.storage.capacity = { 
            ...where.specifications.storage.capacity,
            lte: specs.maxStorage 
          }
        }
      }

      if (specs.gpuType) {
        where.specifications = { ...where.specifications, gpu: { type: specs.gpuType } }
      }

      if (specs.storageType) {
        where.specifications = { ...where.specifications, storage: { 
          ...where.specifications?.storage,
          type: specs.storageType 
        }}
      }

      if (specs.memoryType) {
        where.specifications = { ...where.specifications, memory: { 
          ...where.specifications?.memory,
          type: specs.memoryType 
        }}
      }
    }
  }

  return where
}

function buildOrderBy(sort?: SortInput) {
  if (!sort) {
    return { compatibility: { averageScore: 'desc' } }
  }

  const { sortBy, sortOrder } = sort
  const order = sortOrder?.toLowerCase() || 'desc'

  switch (sortBy) {
    case 'PRICE':
      return { price: order }
    case 'COMPATIBILITY':
      return { compatibility: { averageScore: order } }
    case 'RATING':
      return { rating: order }
    case 'NAME':
      return { title: order }
    case 'NEWEST':
      return { createdAt: order }
    case 'POPULARITY':
      return { reviewCount: order }
    default:
      return { compatibility: { averageScore: 'desc' } }
  }
}

// Include clause for full laptop data
const fullLaptopInclude = {
  specifications: {
    include: {
      cpu: true,
      memory: true,
      storage: true,
      display: true,
      gpu: true,
      connectivity: true
    }
  },
  features: true,
  availability: {
    include: {
      stores: true
    }
  },
  compatibility: {
    include: {
      topModels: true,
      detailedScores: true
    }
  },
  images: true,
  reviews: {
    include: {
      source: true
    },
    orderBy: {
      createdAt: 'desc'
    },
    take: 10
  },
  deals: {
    where: {
      isActive: true,
      expiresAt: { gte: new Date() }
    },
    orderBy: {
      discountPercentage: 'desc'
    }
  },
  scrapingJobs: {
    orderBy: {
      createdAt: 'desc'
    },
    take: 1,
    select: {
      id: true,
      status: true,
      lastScrapedAt: true,
      nextScheduledAt: true
    }
  }
}

export const resolvers = {
  // Custom scalars
  DateTime: DateTimeScalar,
  JSON: JSONScalar,

  // Query resolvers
  Query: {
    // Get single laptop
    laptop: async (_: any, { id }: { id: string }) => {
      return await prisma.laptop.findUnique({
        where: { id },
        include: fullLaptopInclude
      })
    },

    // Search laptops with pagination
    laptops: async (_: any, { input }: { input?: LaptopSearchInput }) => {
      const { query, filters, pagination, sort } = input || {}
      const { page = 1, limit = 20 } = pagination || {}
      
      const where = buildWhereClause(filters)
      
      // Add text search if query provided
      if (query) {
        where.OR = [
          { title: { contains: query, mode: 'insensitive' } },
          { brand: { contains: query, mode: 'insensitive' } },
          { model: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } }
        ]
      }

      const orderBy = buildOrderBy(sort)
      const skip = (page - 1) * limit

      const [laptops, totalCount] = await Promise.all([
        prisma.laptop.findMany({
          where,
          include: fullLaptopInclude,
          orderBy,
          skip,
          take: limit
        }),
        prisma.laptop.count({ where })
      ])

      const totalPages = Math.ceil(totalCount / limit)
      const hasNextPage = page < totalPages
      const hasPreviousPage = page > 1

      return {
        edges: laptops.map((laptop, index) => ({
          node: laptop,
          cursor: Buffer.from(`${skip + index}`).toString('base64')
        })),
        pageInfo: {
          hasNextPage,
          hasPreviousPage,
          startCursor: laptops.length > 0 ? Buffer.from(`${skip}`).toString('base64') : null,
          endCursor: laptops.length > 0 ? Buffer.from(`${skip + laptops.length - 1}`).toString('base64') : null,
          totalCount,
          totalPages,
          currentPage: page
        },
        totalCount
      }
    },

    // Search laptops (alias for laptops with query)
    searchLaptops: async (_: any, args: any) => {
      return resolvers.Query.laptops(_, { 
        input: {
          query: args.query,
          filters: args.filters,
          pagination: args.pagination,
          sort: args.sort
        }
      })
    },

    // Get analytics
    laptopAnalytics: async () => {
      const [
        totalLaptops,
        avgPrice,
        brandStats,
        compatibilityStats
      ] = await Promise.all([
        prisma.laptop.count(),
        prisma.laptop.aggregate({
          _avg: { price: true }
        }),
        prisma.laptop.groupBy({
          by: ['brand'],
          _count: { brand: true },
          orderBy: { _count: { brand: 'desc' } }
        }),
        prisma.laptop.findMany({
          include: { compatibility: true },
          where: { compatibility: { isNot: null } }
        })
      ])

      // Calculate compatibility distribution
      const compatibilityDistribution = compatibilityStats.reduce(
        (acc, laptop) => {
          const score = laptop.compatibility?.averageScore || 0
          if (score >= 90) acc.excellent++
          else if (score >= 75) acc.good++
          else if (score >= 60) acc.fair++
          else acc.poor++
          return acc
        },
        { excellent: 0, good: 0, fair: 0, poor: 0 }
      )

      return {
        totalLaptops,
        averagePrice: avgPrice._avg.price || 0,
        brandDistribution: brandStats.map(stat => ({
          brand: stat.brand,
          count: stat._count.brand
        })),
        compatibilityStats: {
          averageScore: compatibilityStats.reduce((sum, laptop) => 
            sum + (laptop.compatibility?.averageScore || 0), 0
          ) / compatibilityStats.length,
          distribution: compatibilityDistribution,
          topPerformers: compatibilityStats
            .filter(laptop => laptop.compatibility?.averageScore >= 90)
            .slice(0, 5)
        },
        priceDistribution: [], // TODO: Implement price distribution
        popularFeatures: [], // TODO: Implement popular features
        trendingLaptops: [] // TODO: Implement trending laptops
      }
    },

    // Get hot deals
    hotDeals: async (_: any, { limit = 10 }: { limit?: number }) => {
      return await prisma.deal.findMany({
        where: {
          isActive: true,
          expiresAt: { gte: new Date() }
        },
        orderBy: { discountPercentage: 'desc' },
        take: limit
      })
    },

    // Get available brands
    availableBrands: async () => {
      const brands = await prisma.laptop.findMany({
        select: { brand: true },
        distinct: ['brand'],
        orderBy: { brand: 'asc' }
      })
      return brands.map(b => b.brand)
    },

    // Get price range
    priceRange: async () => {
      const result = await prisma.laptop.aggregate({
        _min: { price: true },
        _max: { price: true },
        _avg: { price: true }
      })

      return {
        min: result._min.price || 0,
        max: result._max.price || 0,
        average: result._avg.price || 0
      }
    },

    // Health check
    health: () => 'GraphQL API is running!'
  },

  // Mutation resolvers
  Mutation: {
    // Create laptop (admin only)
    createLaptop: async (_: any, { input }: { input: CreateLaptopInput }) => {
      return await prisma.laptop.create({
        data: {
          ...input,
          currency: 'USD',
          isAvailable: true
        },
        include: fullLaptopInclude
      })
    },

    // Update laptop (admin only)
    updateLaptop: async (_: any, { id, input }: { id: string, input: UpdateLaptopInput }) => {
      return await prisma.laptop.update({
        where: { id },
        data: input,
        include: fullLaptopInclude
      })
    },

    // Delete laptop (admin only)
    deleteLaptop: async (_: any, { id }: { id: string }) => {
      await prisma.laptop.delete({ where: { id } })
      return true
    }
  },

  // Field resolvers
  Laptop: {
    scrapingStatus: (parent: any) => {
      return parent.scrapingJobs?.[0] || null
    }
  }
}
