/**
 * GraphQL Custom Hooks
 * React hooks for GraphQL queries and mutations
 */

import { useQuery, useMutation, useSubscription, useLazyQuery } from '@apollo/client'
import {
  GET_LAPTOP,
  SEARCH_LAPTOPS,
  GET_LAPTOP_ANALYTICS,
  GET_HOT_DEALS,
  GET_AVAILABLE_BRANDS,
  GET_PRICE_RANGE,
  GET_RECOMMENDATIONS,
  HEALTH_CHECK,
  CREATE_LAPTOP,
  UPDATE_LAPTOP,
  DELETE_LAPTOP,
  TRIGGER_SCRAPING,
  UPDATE_COMPATIBILITY
} from './queries'
import type {
  LaptopSearchInput,
  RecommendationInput,
  CreateLaptopInput,
  UpdateLaptopInput,
  CompatibilityInput
} from './types'

// QUERY HOOKS

/**
 * Hook to get a single laptop by ID
 */
export const useGetLaptop = (id: string) => {
  return useQuery(GET_LAPTOP, {
    variables: { id },
    skip: !id,
    errorPolicy: 'all'
  })
}

/**
 * Hook to search laptops with filters and pagination
 */
export const useSearchLaptops = (input?: LaptopSearchInput) => {
  return useQuery(SEARCH_LAPTOPS, {
    variables: { input },
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true
  })
}

/**
 * Lazy hook for searching laptops (triggered manually)
 */
export const useLazySearchLaptops = () => {
  return useLazyQuery(SEARCH_LAPTOPS, {
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true
  })
}

/**
 * Hook to get laptop analytics
 */
export const useGetLaptopAnalytics = () => {
  return useQuery(GET_LAPTOP_ANALYTICS, {
    errorPolicy: 'all'
  })
}

/**
 * Hook to get hot deals
 */
export const useGetHotDeals = (limit = 10) => {
  return useQuery(GET_HOT_DEALS, {
    variables: { limit },
    errorPolicy: 'all'
  })
}

/**
 * Hook to get available brands
 */
export const useGetAvailableBrands = () => {
  return useQuery(GET_AVAILABLE_BRANDS, {
    errorPolicy: 'all'
  })
}

/**
 * Hook to get price range
 */
export const useGetPriceRange = () => {
  return useQuery(GET_PRICE_RANGE, {
    errorPolicy: 'all'
  })
}

/**
 * Hook to get recommendations
 */
export const useGetRecommendations = (input: RecommendationInput) => {
  return useQuery(GET_RECOMMENDATIONS, {
    variables: { input },
    skip: !input,
    errorPolicy: 'all'
  })
}

/**
 * Lazy hook for getting recommendations (triggered manually)
 */
export const useLazyGetRecommendations = () => {
  return useLazyQuery(GET_RECOMMENDATIONS, {
    errorPolicy: 'all'
  })
}

/**
 * Hook for health check
 */
export const useHealthCheck = () => {
  return useQuery(HEALTH_CHECK, {
    errorPolicy: 'all'
  })
}

// MUTATION HOOKS

/**
 * Hook to create a new laptop (admin only)
 */
export const useCreateLaptop = () => {
  return useMutation(CREATE_LAPTOP, {
    errorPolicy: 'all',
    // Refetch queries after successful creation
    refetchQueries: [
      { query: SEARCH_LAPTOPS },
      { query: GET_LAPTOP_ANALYTICS },
      { query: GET_AVAILABLE_BRANDS }
    ]
  })
}

/**
 * Hook to update a laptop (admin only)
 */
export const useUpdateLaptop = () => {
  return useMutation(UPDATE_LAPTOP, {
    errorPolicy: 'all',
    // Update cache after successful update
    update: (cache, { data }) => {
      if (data?.updateLaptop) {
        cache.writeQuery({
          query: GET_LAPTOP,
          variables: { id: data.updateLaptop.id },
          data: { laptop: data.updateLaptop }
        })
      }
    }
  })
}

/**
 * Hook to delete a laptop (admin only)
 */
export const useDeleteLaptop = () => {
  return useMutation(DELETE_LAPTOP, {
    errorPolicy: 'all',
    // Remove from cache after successful deletion
    update: (cache, { data }, { variables }) => {
      if (data?.deleteLaptop && variables?.id) {
        cache.evict({ id: cache.identify({ __typename: 'Laptop', id: variables.id }) })
        cache.gc()
      }
    },
    // Refetch queries after successful deletion
    refetchQueries: [
      { query: SEARCH_LAPTOPS },
      { query: GET_LAPTOP_ANALYTICS },
      { query: GET_AVAILABLE_BRANDS }
    ]
  })
}

/**
 * Hook to trigger scraping
 */
export const useTriggerScraping = () => {
  return useMutation(TRIGGER_SCRAPING, {
    errorPolicy: 'all'
  })
}

/**
 * Hook to update compatibility
 */
export const useUpdateCompatibility = () => {
  return useMutation(UPDATE_COMPATIBILITY, {
    errorPolicy: 'all',
    // Update cache after successful update
    update: (cache, { data }, { variables }) => {
      if (data?.updateCompatibility && variables?.laptopId) {
        // Update the laptop's compatibility in cache
        const laptopId = variables.laptopId
        const existingLaptop = cache.readQuery({
          query: GET_LAPTOP,
          variables: { id: laptopId }
        })

        if (existingLaptop) {
          cache.writeQuery({
            query: GET_LAPTOP,
            variables: { id: laptopId },
            data: {
              laptop: {
                ...existingLaptop.laptop,
                compatibility: data.updateCompatibility
              }
            }
          })
        }
      }
    }
  })
}

// CUSTOM HOOKS WITH BUSINESS LOGIC

/**
 * Hook for laptop search with debouncing and caching
 */
export const useDebouncedLaptopSearch = (
  query: string,
  filters?: LaptopSearchInput['filters'],
  delay = 300
) => {
  const [searchLaptops, { loading, data, error }] = useLazySearchLaptops()

  // Debounce search
  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (query.trim() || filters) {
        searchLaptops({
          variables: {
            input: {
              query: query.trim() || undefined,
              filters,
              pagination: { page: 1, limit: 20 }
            }
          }
        })
      }
    }, delay)

    return () => clearTimeout(timer)
  }, [query, filters, delay, searchLaptops])

  return { loading, data, error, searchLaptops }
}

/**
 * Hook for infinite scrolling laptop search
 */
export const useInfiniteLaptopSearch = (input: LaptopSearchInput) => {
  const { data, loading, error, fetchMore } = useSearchLaptops(input)

  const loadMore = React.useCallback(() => {
    if (!data?.laptops.pageInfo.hasNextPage || loading) return

    return fetchMore({
      variables: {
        input: {
          ...input,
          pagination: {
            ...input.pagination,
            page: (data.laptops.pageInfo.currentPage || 1) + 1
          }
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev

        return {
          laptops: {
            ...fetchMoreResult.laptops,
            edges: [
              ...prev.laptops.edges,
              ...fetchMoreResult.laptops.edges
            ]
          }
        }
      }
    })
  }, [data, loading, fetchMore, input])

  return {
    data,
    loading,
    error,
    loadMore,
    hasMore: data?.laptops.pageInfo.hasNextPage || false
  }
}

/**
 * Hook for laptop comparison functionality
 */
export const useLaptopComparison = (laptopIds: string[]) => {
  const laptopQueries = laptopIds.map(id => useGetLaptop(id))

  const laptops = laptopQueries
    .map(query => query.data?.laptop)
    .filter(Boolean)

  const loading = laptopQueries.some(query => query.loading)
  const error = laptopQueries.find(query => query.error)?.error

  return {
    laptops,
    loading,
    error,
    isComplete: laptops.length === laptopIds.length
  }
}

// Re-export React for hooks that use it
import React from 'react'
