/**
 * Apollo GraphQL Server Configuration
 * Server setup for GraphQL API with Next.js integration
 */

import { ApolloServer } from '@apollo/server'
import { startServerAndCreateNextHandler } from '@as-integrations/next'
import { typeDefs } from './schema'
import { resolvers } from './resolvers'
import { prisma } from '@/lib/prisma'
import type { GraphQLContext } from './types'
import type { NextRequest } from 'next/server'

// Create Apollo Server instance
const server = new ApolloServer<GraphQLContext>({
  typeDefs,
  resolvers,
  // Enable GraphQL Playground in development
  introspection: process.env.NODE_ENV === 'development',
  // Custom formatting for errors
  formatError: (err) => {
    // Log error details in development
    if (process.env.NODE_ENV === 'development') {
      console.error('GraphQL Error:', err)
    }

    // Return sanitized error for production
    return {
      message: err.message,
      // Include error details only in development
      ...(process.env.NODE_ENV === 'development' && {
        locations: err.locations,
        path: err.path,
        extensions: err.extensions
      })
    }
  },
  // Custom plugins for enhanced functionality
  plugins: [
    // Request logging plugin
    {
      requestDidStart() {
        return {
          didResolveOperation(requestContext) {
            if (process.env.NODE_ENV === 'development') {
              console.log('GraphQL Operation:', requestContext.request.operationName)
            }
          },
          didEncounterErrors(requestContext) {
            console.error('GraphQL Errors:', requestContext.errors)
          }
        }
      }
    },
    // Performance monitoring plugin
    {
      requestDidStart() {
        const startTime = Date.now()
        return {
          willSendResponse(requestContext) {
            const duration = Date.now() - startTime
            if (process.env.NODE_ENV === 'development') {
              console.log(`GraphQL Request completed in ${duration}ms`)
            }
            
            // Add performance headers
            if (requestContext.response.http) {
              requestContext.response.http.headers.set('X-Response-Time', `${duration}ms`)
            }
          }
        }
      }
    }
  ]
})

// Create the Next.js handler
const handler = startServerAndCreateNextHandler<NextRequest>(server, {
  // Context function - provides data to resolvers
  context: async (req, res) => {
    // Extract user information from request (if authentication is implemented)
    const user = null // TODO: Implement authentication

    return {
      prisma,
      user,
      req,
      res
    }
  }
})

export { handler }

// Export server for testing
export { server }

// Export types for use in other files
export type { GraphQLContext }
