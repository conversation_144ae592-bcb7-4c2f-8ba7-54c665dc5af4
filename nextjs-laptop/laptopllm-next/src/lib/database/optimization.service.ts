/**
 * Database Optimization Service
 * 
 * Provides utilities for monitoring and managing database performance optimizations
 * including index usage, query performance, and maintenance operations.
 */

import { PrismaClient } from '../../../generated/prisma'

export interface IndexUsageStats {
  schemaname: string
  tablename: string
  indexname: string
  idx_scan: number
  idx_tup_read: number
  idx_tup_fetch: number
  usage_ratio: number
}

export interface QueryPerformanceStats {
  query: string
  calls: number
  total_time: number
  mean_time: number
  rows: number
  performance_score: number
}

export interface DatabaseOptimizationStats {
  indexUsage: IndexUsageStats[]
  queryPerformance: QueryPerformanceStats[]
  tableStats: TableStats[]
  optimizationRecommendations: OptimizationRecommendation[]
}

export interface TableStats {
  tablename: string
  row_count: number
  table_size: string
  index_size: string
  total_size: string
  seq_scan: number
  seq_tup_read: number
  idx_scan: number
  idx_tup_fetch: number
}

export interface OptimizationRecommendation {
  type: 'index' | 'query' | 'maintenance'
  priority: 'high' | 'medium' | 'low'
  table: string
  description: string
  action: string
  estimated_improvement: string
}

export class DatabaseOptimizationService {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  /**
   * Get comprehensive database optimization statistics
   */
  async getOptimizationStats(): Promise<DatabaseOptimizationStats> {
    try {
      const [indexUsage, queryPerformance, tableStats] = await Promise.all([
        this.getIndexUsageStats(),
        this.getQueryPerformanceStats(),
        this.getTableStats()
      ])

      const recommendations = this.generateOptimizationRecommendations(
        indexUsage,
        queryPerformance,
        tableStats
      )

      return {
        indexUsage,
        queryPerformance,
        tableStats,
        optimizationRecommendations: recommendations
      }
    } catch (error) {
      console.error('Error getting optimization stats:', error)
      throw new Error('Failed to get database optimization statistics')
    }
  }

  /**
   * Get index usage statistics
   */
  async getIndexUsageStats(): Promise<IndexUsageStats[]> {
    try {
      const result = await this.prisma.$queryRaw<IndexUsageStats[]>`
        SELECT 
          schemaname,
          tablename,
          indexname,
          idx_scan,
          idx_tup_read,
          idx_tup_fetch,
          CASE 
            WHEN idx_scan = 0 THEN 0
            ELSE ROUND((idx_tup_fetch::numeric / idx_tup_read::numeric) * 100, 2)
          END as usage_ratio
        FROM pg_stat_user_indexes 
        WHERE schemaname = 'public'
          AND indexname LIKE 'idx_%'
        ORDER BY idx_scan DESC, usage_ratio DESC
      `

      return result.map(row => ({
        schemaname: row.schemaname,
        tablename: row.tablename,
        indexname: row.indexname,
        idx_scan: Number(row.idx_scan),
        idx_tup_read: Number(row.idx_tup_read),
        idx_tup_fetch: Number(row.idx_tup_fetch),
        usage_ratio: Number(row.usage_ratio) || 0
      }))
    } catch (error) {
      console.error('Error getting index usage stats:', error)
      return []
    }
  }

  /**
   * Get query performance statistics
   */
  async getQueryPerformanceStats(): Promise<QueryPerformanceStats[]> {
    try {
      // Note: Requires pg_stat_statements extension
      const result = await this.prisma.$queryRaw<QueryPerformanceStats[]>`
        SELECT 
          LEFT(query, 100) as query,
          calls,
          total_time,
          mean_time,
          rows,
          CASE 
            WHEN mean_time < 10 THEN 100
            WHEN mean_time < 50 THEN 80
            WHEN mean_time < 100 THEN 60
            WHEN mean_time < 500 THEN 40
            ELSE 20
          END as performance_score
        FROM pg_stat_statements 
        WHERE query LIKE '%laptop%' 
           OR query LIKE '%scraping%'
           OR query LIKE '%llm%'
        ORDER BY total_time DESC
        LIMIT 20
      `

      return result.map(row => ({
        query: row.query,
        calls: Number(row.calls),
        total_time: Number(row.total_time),
        mean_time: Number(row.mean_time),
        rows: Number(row.rows),
        performance_score: Number(row.performance_score)
      }))
    } catch (error) {
      console.error('Error getting query performance stats (pg_stat_statements may not be enabled):', error)
      return []
    }
  }

  /**
   * Get table statistics
   */
  async getTableStats(): Promise<TableStats[]> {
    try {
      const result = await this.prisma.$queryRaw<TableStats[]>`
        SELECT 
          t.tablename,
          t.n_tup_ins + t.n_tup_upd + t.n_tup_del as row_count,
          pg_size_pretty(pg_total_relation_size(c.oid)) as total_size,
          pg_size_pretty(pg_relation_size(c.oid)) as table_size,
          pg_size_pretty(pg_total_relation_size(c.oid) - pg_relation_size(c.oid)) as index_size,
          t.seq_scan,
          t.seq_tup_read,
          t.idx_scan,
          t.idx_tup_fetch
        FROM pg_stat_user_tables t
        JOIN pg_class c ON c.relname = t.tablename
        WHERE t.schemaname = 'public'
        ORDER BY pg_total_relation_size(c.oid) DESC
      `

      return result.map(row => ({
        tablename: row.tablename,
        row_count: Number(row.row_count),
        table_size: row.table_size,
        index_size: row.index_size,
        total_size: row.total_size,
        seq_scan: Number(row.seq_scan),
        seq_tup_read: Number(row.seq_tup_read),
        idx_scan: Number(row.idx_scan) || 0,
        idx_tup_fetch: Number(row.idx_tup_fetch) || 0
      }))
    } catch (error) {
      console.error('Error getting table stats:', error)
      return []
    }
  }

  /**
   * Generate optimization recommendations based on statistics
   */
  private generateOptimizationRecommendations(
    indexUsage: IndexUsageStats[],
    queryPerformance: QueryPerformanceStats[],
    tableStats: TableStats[]
  ): OptimizationRecommendation[] {
    const recommendations: OptimizationRecommendation[] = []

    // Check for unused indexes
    const unusedIndexes = indexUsage.filter(idx => idx.idx_scan === 0)
    unusedIndexes.forEach(idx => {
      recommendations.push({
        type: 'index',
        priority: 'medium',
        table: idx.tablename,
        description: `Index ${idx.indexname} is not being used`,
        action: `Consider dropping unused index: DROP INDEX ${idx.indexname}`,
        estimated_improvement: 'Reduced storage and faster writes'
      })
    })

    // Check for slow queries
    const slowQueries = queryPerformance.filter(q => q.performance_score < 60)
    slowQueries.forEach(query => {
      recommendations.push({
        type: 'query',
        priority: query.performance_score < 40 ? 'high' : 'medium',
        table: 'multiple',
        description: `Slow query detected (${query.mean_time.toFixed(2)}ms avg)`,
        action: 'Optimize query or add missing indexes',
        estimated_improvement: `Potential ${100 - query.performance_score}% improvement`
      })
    })

    // Check for tables with high sequential scans
    const highSeqScanTables = tableStats.filter(t => 
      t.seq_scan > 1000 && (t.idx_scan === 0 || t.seq_scan > t.idx_scan * 2)
    )
    highSeqScanTables.forEach(table => {
      recommendations.push({
        type: 'index',
        priority: 'high',
        table: table.tablename,
        description: `Table ${table.tablename} has high sequential scan ratio`,
        action: 'Add indexes for frequently filtered columns',
        estimated_improvement: 'Significant query performance improvement'
      })
    })

    // Check for maintenance needs
    const largeTables = tableStats.filter(t => t.row_count > 100000)
    largeTables.forEach(table => {
      recommendations.push({
        type: 'maintenance',
        priority: 'low',
        table: table.tablename,
        description: `Large table ${table.tablename} may need maintenance`,
        action: `Run ANALYZE and consider VACUUM on ${table.tablename}`,
        estimated_improvement: 'Better query planning and performance'
      })
    })

    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })
  }

  /**
   * Apply database optimizations
   */
  async applyOptimizations(): Promise<{ success: boolean; message: string }> {
    try {
      // Update table statistics
      await this.updateTableStatistics()
      
      return {
        success: true,
        message: 'Database optimizations applied successfully'
      }
    } catch (error) {
      console.error('Error applying optimizations:', error)
      return {
        success: false,
        message: `Failed to apply optimizations: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Update table statistics for better query planning
   */
  async updateTableStatistics(): Promise<void> {
    const criticalTables = [
      'laptop_listings',
      'laptop_llm_compatibility', 
      'laptops',
      'scraping_jobs',
      'price_history',
      'laptop_scores'
    ]

    for (const table of criticalTables) {
      try {
        await this.prisma.$executeRaw`ANALYZE ${table}`
        console.log(`Updated statistics for table: ${table}`)
      } catch (error) {
        console.error(`Error updating statistics for ${table}:`, error)
      }
    }
  }

  /**
   * Check if optimization migration has been applied
   */
  async checkOptimizationStatus(): Promise<{
    applied: boolean
    indexCount: number
    viewCount: number
    functionCount: number
  }> {
    try {
      const [indexResult, viewResult, functionResult] = await Promise.all([
        this.prisma.$queryRaw<[{ count: bigint }]>`
          SELECT COUNT(*) as count 
          FROM pg_indexes 
          WHERE schemaname = 'public' AND indexname LIKE 'idx_%'
        `,
        this.prisma.$queryRaw<[{ count: bigint }]>`
          SELECT COUNT(*) as count 
          FROM pg_views 
          WHERE schemaname = 'public' 
            AND viewname IN ('laptop_deals_summary', 'laptop_compatibility_summary', 'laptop_search_optimized')
        `,
        this.prisma.$queryRaw<[{ count: bigint }]>`
          SELECT COUNT(*) as count 
          FROM pg_proc p
          JOIN pg_namespace n ON p.pronamespace = n.oid
          WHERE n.nspname = 'public' 
            AND p.proname IN ('get_laptop_deals', 'update_laptop_scores_on_compatibility_change')
        `
      ])

      const indexCount = Number(indexResult[0].count)
      const viewCount = Number(viewResult[0].count)
      const functionCount = Number(functionResult[0].count)

      return {
        applied: indexCount >= 30 && viewCount >= 3 && functionCount >= 2,
        indexCount,
        viewCount,
        functionCount
      }
    } catch (error) {
      console.error('Error checking optimization status:', error)
      return {
        applied: false,
        indexCount: 0,
        viewCount: 0,
        functionCount: 0
      }
    }
  }

  /**
   * Cleanup resources
   */
  async disconnect(): Promise<void> {
    await this.prisma.$disconnect()
  }
}

export const databaseOptimizationService = new DatabaseOptimizationService()
