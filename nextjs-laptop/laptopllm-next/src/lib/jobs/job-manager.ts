/**
 * Job Manager
 * Manages job persistence and lifecycle in the database
 */

import { prisma } from '@/lib/prisma'
import pino from 'pino'
import type {
  JobType,
  JobStatus,
  JobPriority,
  BaseJobData,
  JobResult,
  JobMetrics,
  ScrapingJobData,
  DataProcessingJobData,
  NotificationJobData
} from '../queue/types'

export interface DatabaseJob {
  id: string
  type: JobType
  status: JobStatus
  priority: JobPriority
  data: any
  result?: any
  error?: string
  metrics?: JobMetrics
  attempts: number
  maxAttempts: number
  createdAt: Date
  startedAt?: Date
  completedAt?: Date
  updatedAt: Date
  parentJobId?: string
  batchId?: string
  tags: string[]
}

export interface JobFilter {
  type?: JobType
  status?: JobStatus
  priority?: JobPriority
  batchId?: string
  parentJobId?: string
  tags?: string[]
  createdAfter?: Date
  createdBefore?: Date
  limit?: number
  offset?: number
  orderBy?: 'createdAt' | 'updatedAt' | 'priority'
  orderDirection?: 'asc' | 'desc'
}

export class JobManager {
  private logger: pino.Logger

  constructor() {
    this.logger = pino({
      name: 'job-manager',
      level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
      transport: process.env.NODE_ENV === 'development' ? {
        target: 'pino-pretty',
        options: { colorize: true }
      } : undefined
    })
  }

  /**
   * Create a new job in the database
   */
  async createJob(jobData: BaseJobData, maxAttempts: number = 3): Promise<DatabaseJob> {
    try {
      const job = await prisma.scrapingJob.create({
        data: {
          id: jobData.id,
          type: jobData.type,
          status: 'waiting' as JobStatus,
          priority: jobData.priority,
          data: jobData,
          attempts: 0,
          maxAttempts,
          tags: this.extractTags(jobData),
          createdAt: jobData.createdAt,
          updatedAt: new Date(),
        }
      })

      this.logger.info(`Job '${job.id}' created in database`)
      return this.mapToJobInterface(job)
    } catch (error) {
      this.logger.error(`Failed to create job '${jobData.id}':`, error)
      throw error
    }
  }

  /**
   * Update job status
   */
  async updateJobStatus(
    jobId: string, 
    status: JobStatus, 
    data?: {
      error?: string
      result?: any
      metrics?: Partial<JobMetrics>
      incrementAttempts?: boolean
    }
  ): Promise<DatabaseJob> {
    try {
      const updateData: any = {
        status,
        updatedAt: new Date(),
      }

      if (status === 'active' && !data?.metrics?.startTime) {
        updateData.startedAt = new Date()
      }

      if (status === 'completed' || status === 'failed') {
        updateData.completedAt = new Date()
      }

      if (data?.error) {
        updateData.error = data.error
      }

      if (data?.result) {
        updateData.result = data.result
      }

      if (data?.metrics) {
        const existingJob = await this.getJob(jobId)
        const existingMetrics = existingJob?.metrics || {}
        updateData.metrics = { ...existingMetrics, ...data.metrics }
      }

      if (data?.incrementAttempts) {
        updateData.attempts = { increment: 1 }
      }

      const job = await prisma.scrapingJob.update({
        where: { id: jobId },
        data: updateData
      })

      this.logger.debug(`Job '${jobId}' status updated to '${status}'`)
      return this.mapToJobInterface(job)
    } catch (error) {
      this.logger.error(`Failed to update job '${jobId}' status:`, error)
      throw error
    }
  }

  /**
   * Get job by ID
   */
  async getJob(jobId: string): Promise<DatabaseJob | null> {
    try {
      const job = await prisma.scrapingJob.findUnique({
        where: { id: jobId }
      })

      return job ? this.mapToJobInterface(job) : null
    } catch (error) {
      this.logger.error(`Failed to get job '${jobId}':`, error)
      throw error
    }
  }

  /**
   * Get jobs with filters
   */
  async getJobs(filter: JobFilter = {}): Promise<{
    jobs: DatabaseJob[]
    total: number
    hasMore: boolean
  }> {
    try {
      const where: any = {}

      if (filter.type) where.type = filter.type
      if (filter.status) where.status = filter.status
      if (filter.priority) where.priority = filter.priority
      if (filter.batchId) where.batchId = filter.batchId
      if (filter.parentJobId) where.parentJobId = filter.parentJobId
      if (filter.tags?.length) {
        where.tags = { hasSome: filter.tags }
      }
      if (filter.createdAfter || filter.createdBefore) {
        where.createdAt = {}
        if (filter.createdAfter) where.createdAt.gte = filter.createdAfter
        if (filter.createdBefore) where.createdAt.lte = filter.createdBefore
      }

      const orderBy: any = {}
      if (filter.orderBy) {
        orderBy[filter.orderBy] = filter.orderDirection || 'desc'
      } else {
        orderBy.createdAt = 'desc'
      }

      const limit = filter.limit || 50
      const offset = filter.offset || 0

      const [jobs, total] = await Promise.all([
        prisma.scrapingJob.findMany({
          where,
          orderBy,
          take: limit,
          skip: offset,
        }),
        prisma.scrapingJob.count({ where })
      ])

      return {
        jobs: jobs.map(job => this.mapToJobInterface(job)),
        total,
        hasMore: offset + jobs.length < total
      }
    } catch (error) {
      this.logger.error('Failed to get jobs:', error)
      throw error
    }
  }

  /**
   * Get job statistics
   */
  async getJobStats(timeRange?: { from: Date; to: Date }): Promise<{
    total: number
    byStatus: Record<JobStatus, number>
    byType: Record<JobType, number>
    byPriority: Record<JobPriority, number>
    averageProcessingTime: number
    successRate: number
    throughput: {
      perHour: number
      perDay: number
    }
  }> {
    try {
      const where: any = {}
      if (timeRange) {
        where.createdAt = {
          gte: timeRange.from,
          lte: timeRange.to
        }
      }

      const [
        total,
        statusCounts,
        typeCounts,
        priorityCounts,
        completedJobs
      ] = await Promise.all([
        prisma.scrapingJob.count({ where }),
        prisma.scrapingJob.groupBy({
          by: ['status'],
          where,
          _count: { status: true }
        }),
        prisma.scrapingJob.groupBy({
          by: ['type'],
          where,
          _count: { type: true }
        }),
        prisma.scrapingJob.groupBy({
          by: ['priority'],
          where,
          _count: { priority: true }
        }),
        prisma.scrapingJob.findMany({
          where: {
            ...where,
            status: { in: ['completed', 'failed'] },
            startedAt: { not: null },
            completedAt: { not: null }
          },
          select: {
            status: true,
            startedAt: true,
            completedAt: true,
            createdAt: true
          }
        })
      ])

      // Calculate statistics
      const byStatus = statusCounts.reduce((acc, item) => {
        acc[item.status as JobStatus] = item._count.status
        return acc
      }, {} as Record<JobStatus, number>)

      const byType = typeCounts.reduce((acc, item) => {
        acc[item.type as JobType] = item._count.type
        return acc
      }, {} as Record<JobType, number>)

      const byPriority = priorityCounts.reduce((acc, item) => {
        acc[item.priority as JobPriority] = item._count.priority
        return acc
      }, {} as Record<JobPriority, number>)

      // Calculate average processing time
      const processingTimes = completedJobs
        .filter(job => job.startedAt && job.completedAt)
        .map(job => job.completedAt!.getTime() - job.startedAt!.getTime())

      const averageProcessingTime = processingTimes.length > 0
        ? processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length
        : 0

      // Calculate success rate
      const completedCount = byStatus.completed || 0
      const failedCount = byStatus.failed || 0
      const successRate = (completedCount + failedCount) > 0
        ? (completedCount / (completedCount + failedCount)) * 100
        : 100

      // Calculate throughput
      const timeRangeHours = timeRange
        ? (timeRange.to.getTime() - timeRange.from.getTime()) / (1000 * 60 * 60)
        : 24 // Default to last 24 hours

      const throughputPerHour = completedCount / Math.max(timeRangeHours, 1)

      return {
        total,
        byStatus,
        byType,
        byPriority,
        averageProcessingTime: Math.round(averageProcessingTime),
        successRate: Math.round(successRate * 100) / 100,
        throughput: {
          perHour: Math.round(throughputPerHour * 100) / 100,
          perDay: Math.round(throughputPerHour * 24 * 100) / 100
        }
      }
    } catch (error) {
      this.logger.error('Failed to get job statistics:', error)
      throw error
    }
  }

  /**
   * Delete old jobs
   */
  async cleanupOldJobs(olderThan: Date, statuses?: JobStatus[]): Promise<number> {
    try {
      const where: any = {
        createdAt: { lt: olderThan }
      }

      if (statuses?.length) {
        where.status = { in: statuses }
      }

      const result = await prisma.scrapingJob.deleteMany({ where })

      this.logger.info(`Cleaned up ${result.count} old jobs`)
      return result.count
    } catch (error) {
      this.logger.error('Failed to cleanup old jobs:', error)
      throw error
    }
  }

  /**
   * Retry failed jobs
   */
  async retryFailedJobs(filter: { 
    batchId?: string
    olderThan?: Date
    maxAttempts?: number
  } = {}): Promise<string[]> {
    try {
      const where: any = {
        status: 'failed'
      }

      if (filter.batchId) where.batchId = filter.batchId
      if (filter.olderThan) where.updatedAt = { lt: filter.olderThan }
      if (filter.maxAttempts) where.attempts = { lt: filter.maxAttempts }

      const failedJobs = await prisma.scrapingJob.findMany({
        where,
        select: { id: true }
      })

      const jobIds = failedJobs.map(job => job.id)

      if (jobIds.length > 0) {
        await prisma.scrapingJob.updateMany({
          where: { id: { in: jobIds } },
          data: {
            status: 'waiting',
            error: null,
            updatedAt: new Date()
          }
        })

        this.logger.info(`Retrying ${jobIds.length} failed jobs`)
      }

      return jobIds
    } catch (error) {
      this.logger.error('Failed to retry failed jobs:', error)
      throw error
    }
  }

  /**
   * Extract tags from job data
   */
  private extractTags(jobData: BaseJobData): string[] {
    const tags: string[] = [jobData.type, jobData.priority]

    if (jobData.type === 'scraping') {
      const scrapingData = jobData as ScrapingJobData
      if (scrapingData.sourceId) tags.push(`source:${scrapingData.sourceId}`)
      if (scrapingData.batchId) tags.push(`batch:${scrapingData.batchId}`)
      scrapingData.targets.forEach(target => {
        tags.push(`site:${target.siteType}`)
      })
    }

    return tags
  }

  /**
   * Map database record to job interface
   */
  private mapToJobInterface(job: any): DatabaseJob {
    return {
      id: job.id,
      type: job.type,
      status: job.status,
      priority: job.priority,
      data: job.data,
      result: job.result,
      error: job.error,
      metrics: job.metrics,
      attempts: job.attempts,
      maxAttempts: job.maxAttempts,
      createdAt: job.createdAt,
      startedAt: job.startedAt,
      completedAt: job.completedAt,
      updatedAt: job.updatedAt,
      parentJobId: job.parentJobId,
      batchId: job.batchId,
      tags: job.tags || []
    }
  }
}
