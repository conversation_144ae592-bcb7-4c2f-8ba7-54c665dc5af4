/**
 * Scraping Worker
 * Processes scraping jobs from the queue
 */

import { Job } from 'bullmq'
import pino from 'pino'
import { ScrapingService } from '../../scraping/scraping-service'
import { ScrapingOrchestrator } from '../../scraping/scraping-orchestrator'
import { JobManager } from '../../jobs/job-manager'
import { DataProcessor } from '../processors/data-processor'
import type {
  ScrapingJobData,
  JobResult,
  JobMetrics,
  ScrapingTarget
} from '../types'

export class ScrapingWorker {
  private scrapingService: ScrapingService
  private orchestrator: ScrapingOrchestrator
  private jobManager: JobManager
  private dataProcessor: DataProcessor
  private logger: pino.Logger

  constructor() {
    this.logger = pino({
      name: 'scraping-worker',
      level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
      transport: process.env.NODE_ENV === 'development' ? {
        target: 'pino-pretty',
        options: { colorize: true }
      } : undefined
    })

    this.scrapingService = new ScrapingService()
    this.orchestrator = new ScrapingOrchestrator()
    this.jobManager = new JobManager()
    this.dataProcessor = new DataProcessor()
  }

  /**
   * Process a scraping job
   */
  async processJob(job: Job<ScrapingJobData>): Promise<JobResult> {
    const startTime = new Date()
    const jobData = job.data
    const metrics: Partial<JobMetrics> = {
      startTime,
      itemsProcessed: 0,
      itemsSuccessful: 0,
      itemsFailed: 0
    }

    this.logger.info(`Processing scraping job '${job.id}' with ${jobData.targets.length} targets`)

    try {
      // Update job status to active
      await this.jobManager.updateJobStatus(jobData.id, 'active', {
        metrics: { startTime }
      })

      const results = []
      const errors = []

      // Process targets in batches
      const batches = this.createBatches(jobData.targets, jobData.config.batchSize)
      
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex]
        
        this.logger.debug(`Processing batch ${batchIndex + 1}/${batches.length} with ${batch.length} targets`)

        // Update progress
        const progress = Math.round((batchIndex / batches.length) * 100)
        await job.updateProgress(progress)

        // Process batch with concurrency control
        const batchResults = await this.processBatch(batch, jobData.config.maxConcurrent)
        
        for (const result of batchResults) {
          if (result.success) {
            results.push(result.data)
            metrics.itemsSuccessful = (metrics.itemsSuccessful || 0) + 1
          } else {
            errors.push(result.error)
            metrics.itemsFailed = (metrics.itemsFailed || 0) + 1
          }
          metrics.itemsProcessed = (metrics.itemsProcessed || 0) + 1
        }

        // Delay between batches if configured
        if (batchIndex < batches.length - 1 && jobData.config.delayBetweenBatches > 0) {
          await this.delay(jobData.config.delayBetweenBatches)
        }
      }

      // Final progress update
      await job.updateProgress(100)

      // Calculate final metrics
      const endTime = new Date()
      metrics.endTime = endTime
      metrics.duration = endTime.getTime() - startTime.getTime()
      metrics.successRate = metrics.itemsProcessed! > 0 
        ? (metrics.itemsSuccessful! / metrics.itemsProcessed!) * 100 
        : 100
      metrics.throughput = metrics.duration! > 0 
        ? (metrics.itemsProcessed! / (metrics.duration! / 1000)) 
        : 0

      // Process and save data if configured
      let processedData = results
      if (jobData.config.saveToDatabase && results.length > 0) {
        try {
          processedData = await this.dataProcessor.processScrapedData(results, {
            validateSchema: true,
            transformData: true,
            enrichWithLLMCompatibility: true,
            detectDuplicates: true,
            mergeStrategy: 'merge'
          })
          this.logger.info(`Processed and saved ${processedData.length} items to database`)
        } catch (error) {
          this.logger.error('Failed to process scraped data:', error)
          // Don't fail the job if data processing fails
        }
      }

      const jobResult: JobResult = {
        success: true,
        data: {
          results: processedData,
          summary: {
            totalTargets: jobData.targets.length,
            itemsProcessed: metrics.itemsProcessed,
            itemsSuccessful: metrics.itemsSuccessful,
            itemsFailed: metrics.itemsFailed,
            successRate: metrics.successRate,
            duration: metrics.duration,
            errors: errors.slice(0, 10) // Limit error details
          }
        },
        metrics: metrics as JobMetrics,
        timestamp: endTime
      }

      this.logger.info(`Scraping job '${job.id}' completed successfully: ${metrics.itemsSuccessful}/${metrics.itemsProcessed} items`)
      return jobResult

    } catch (error) {
      const endTime = new Date()
      metrics.endTime = endTime
      metrics.duration = endTime.getTime() - startTime.getTime()

      const jobResult: JobResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metrics: metrics as JobMetrics,
        timestamp: endTime
      }

      this.logger.error(`Scraping job '${job.id}' failed:`, error)
      return jobResult
    }
  }

  /**
   * Process a batch of targets with concurrency control
   */
  private async processBatch(
    targets: ScrapingTarget[], 
    maxConcurrent: number
  ): Promise<Array<{ success: boolean; data?: any; error?: string }>> {
    const results = []
    
    // Process targets in chunks to respect concurrency limits
    for (let i = 0; i < targets.length; i += maxConcurrent) {
      const chunk = targets.slice(i, i + maxConcurrent)
      
      const chunkPromises = chunk.map(async (target) => {
        try {
          const result = await this.scrapeSingleTarget(target)
          return { success: true, data: result }
        } catch (error) {
          return { 
            success: false, 
            error: error instanceof Error ? error.message : 'Unknown error' 
          }
        }
      })

      const chunkResults = await Promise.allSettled(chunkPromises)
      
      for (const result of chunkResults) {
        if (result.status === 'fulfilled') {
          results.push(result.value)
        } else {
          results.push({ 
            success: false, 
            error: result.reason?.message || 'Promise rejected' 
          })
        }
      }
    }

    return results
  }

  /**
   * Scrape a single target
   */
  private async scrapeSingleTarget(target: ScrapingTarget): Promise<any> {
    this.logger.debug(`Scraping target: ${target.url}`)

    try {
      // Use the orchestrator to handle the scraping with fallback strategy
      const result = await this.orchestrator.scrapeUrl(target.url, {
        siteType: target.siteType,
        selectors: target.selectors,
        waitFor: target.waitFor,
        actions: target.actions
      })

      return {
        url: target.url,
        siteType: target.siteType,
        scrapedAt: new Date(),
        data: result,
        success: true
      }
    } catch (error) {
      this.logger.error(`Failed to scrape ${target.url}:`, error)
      throw error
    }
  }

  /**
   * Create batches from targets array
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = []
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize))
    }
    return batches
  }

  /**
   * Delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

/**
 * Worker processor function for BullMQ
 */
export async function scrapingJobProcessor(job: Job<ScrapingJobData>): Promise<JobResult> {
  const worker = new ScrapingWorker()
  return await worker.processJob(job)
}
