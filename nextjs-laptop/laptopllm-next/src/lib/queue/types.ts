/**
 * Queue System Types
 * Enhanced types for the scraping queue system with BullMQ
 */

import type { Job, JobsOptions, QueueOptions } from 'bullmq'

// Job Types
export type JobType = 
  | 'scraping'
  | 'data-processing'
  | 'notification'
  | 'cleanup'
  | 'health-check'

// Job Status
export type JobStatus = 
  | 'waiting'
  | 'active'
  | 'completed'
  | 'failed'
  | 'delayed'
  | 'paused'
  | 'cancelled'

// Job Priority
export type JobPriority = 'low' | 'normal' | 'high' | 'critical'

// Base Job Data Interface
export interface BaseJobData {
  id: string
  type: JobType
  priority: JobPriority
  createdAt: Date
  metadata?: Record<string, any>
}

// Scraping Job Data
export interface ScrapingJobData extends BaseJobData {
  type: 'scraping'
  targets: ScrapingTarget[]
  config: ScrapingJobConfig
  sourceId?: string
  batchId?: string
}

export interface ScrapingTarget {
  url: string
  siteType: 'amazon' | 'bestbuy' | 'newegg' | 'microcenter' | 'bhphoto' | 'adorama' | 'generic'
  selectors?: Record<string, string>
  waitFor?: number
  actions?: Array<{
    type: 'click' | 'scroll' | 'wait' | 'type'
    selector?: string
    value?: string | number
  }>
}

export interface ScrapingJobConfig {
  strategy: {
    primary: 'crawl4ai' | 'firecrawl' | 'puppeteer'
    fallback: 'crawl4ai' | 'firecrawl' | 'puppeteer'
    retryWithFallback: boolean
  }
  batchSize: number
  maxConcurrent: number
  delayBetweenBatches: number
  enableScreenshots: boolean
  saveToDatabase: boolean
  timeout: number
  retryAttempts: number
  retryDelay: number
}

// Data Processing Job Data
export interface DataProcessingJobData extends BaseJobData {
  type: 'data-processing'
  scrapingJobId: string
  rawData: any[]
  processingType: 'validation' | 'transformation' | 'enrichment' | 'deduplication'
  config: DataProcessingConfig
}

export interface DataProcessingConfig {
  validateSchema: boolean
  transformData: boolean
  enrichWithLLMCompatibility: boolean
  detectDuplicates: boolean
  mergeStrategy: 'replace' | 'merge' | 'skip'
}

// Notification Job Data
export interface NotificationJobData extends BaseJobData {
  type: 'notification'
  notificationType: 'email' | 'webhook' | 'slack' | 'discord'
  recipients: string[]
  subject: string
  message: string
  data?: Record<string, any>
}

// Job Result Types
export interface JobResult {
  success: boolean
  data?: any
  error?: string
  metrics: JobMetrics
  timestamp: Date
}

export interface JobMetrics {
  startTime: Date
  endTime?: Date
  duration?: number
  itemsProcessed: number
  itemsSuccessful: number
  itemsFailed: number
  successRate: number
  throughput?: number
  memoryUsage?: number
  cpuUsage?: number
}

// Queue Configuration
export interface QueueConfig extends QueueOptions {
  name: string
  defaultJobOptions: JobsOptions
  concurrency: number
  rateLimiter?: {
    max: number
    duration: number
  }
}

// Queue Statistics
export interface QueueStats {
  name: string
  waiting: number
  active: number
  completed: number
  failed: number
  delayed: number
  paused: number
  total: number
  throughput: {
    perMinute: number
    perHour: number
    perDay: number
  }
  averageProcessingTime: number
  successRate: number
  lastProcessedAt?: Date
}

// Worker Configuration
export interface WorkerConfig {
  queueName: string
  concurrency: number
  processor: string | ((job: Job) => Promise<any>)
  options?: {
    stalledInterval?: number
    maxStalledCount?: number
    retryProcessDelay?: number
  }
}

// Circuit Breaker Configuration
export interface CircuitBreakerConfig {
  failureThreshold: number
  resetTimeout: number
  monitoringPeriod: number
  expectedErrors: string[]
}

// Retry Configuration
export interface RetryConfig {
  attempts: number
  delay: number
  backoff: 'fixed' | 'exponential' | 'linear'
  maxDelay?: number
  jitter?: boolean
}

// Health Check Configuration
export interface HealthCheckConfig {
  interval: number
  timeout: number
  retries: number
  endpoints: Array<{
    name: string
    url: string
    method: 'GET' | 'POST'
    expectedStatus: number
  }>
}

// Monitoring Configuration
export interface MonitoringConfig {
  enabled: boolean
  metricsInterval: number
  alertThresholds: {
    failureRate: number
    queueLength: number
    processingTime: number
    memoryUsage: number
  }
  notifications: {
    email?: string[]
    webhook?: string
    slack?: string
  }
}

// Job Event Types
export type JobEvent = 
  | 'waiting'
  | 'active'
  | 'completed'
  | 'failed'
  | 'progress'
  | 'stalled'
  | 'removed'

// Job Event Data
export interface JobEventData {
  jobId: string
  event: JobEvent
  data?: any
  timestamp: Date
  queueName: string
}

// Queue Manager Configuration
export interface QueueManagerConfig {
  redis: {
    host: string
    port: number
    password?: string
    db?: number
    maxRetriesPerRequest?: number
    retryDelayOnFailover?: number
  }
  queues: QueueConfig[]
  workers: WorkerConfig[]
  monitoring: MonitoringConfig
  circuitBreaker: CircuitBreakerConfig
  retry: RetryConfig
  healthCheck: HealthCheckConfig
}

// Export utility types
export type QueueJob<T = any> = Job<T>
export type QueueJobOptions = JobsOptions
