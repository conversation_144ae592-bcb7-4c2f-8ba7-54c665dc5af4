/**
 * Data Processor
 * Handles validation, transformation, and enrichment of scraped data
 */

import { z } from 'zod'
import { prisma } from '@/lib/prisma'
import pino from 'pino'
import type { DataProcessingConfig } from '../types'

// Validation schemas
const LaptopSpecsSchema = z.object({
  brand: z.string().min(1),
  model: z.string().min(1),
  price: z.number().positive().optional(),
  cpu: z.object({
    brand: z.string().optional(),
    model: z.string().optional(),
    cores: z.number().optional(),
    threads: z.number().optional(),
    baseClockGhz: z.number().optional(),
    boostClockGhz: z.number().optional()
  }).optional(),
  memory: z.object({
    sizeGb: z.number().optional(),
    type: z.string().optional(),
    speed: z.string().optional()
  }).optional(),
  storage: z.array(z.object({
    type: z.string().optional(),
    sizeGb: z.number().optional(),
    interface: z.string().optional()
  })).optional(),
  gpu: z.object({
    brand: z.string().optional(),
    model: z.string().optional(),
    vramGb: z.number().optional(),
    type: z.enum(['integrated', 'discrete']).optional()
  }).optional(),
  display: z.object({
    sizeInches: z.number().optional(),
    resolution: z.string().optional(),
    refreshRate: z.number().optional(),
    panelType: z.string().optional()
  }).optional(),
  connectivity: z.object({
    wifi: z.string().optional(),
    bluetooth: z.string().optional(),
    ports: z.array(z.string()).optional()
  }).optional(),
  dimensions: z.object({
    widthMm: z.number().optional(),
    heightMm: z.number().optional(),
    depthMm: z.number().optional(),
    weightKg: z.number().optional()
  }).optional(),
  battery: z.object({
    capacityWh: z.number().optional(),
    estimatedLifeHours: z.number().optional()
  }).optional()
})

const ScrapedDataSchema = z.object({
  url: z.string().url(),
  siteType: z.string(),
  scrapedAt: z.date(),
  data: z.object({
    title: z.string().optional(),
    price: z.number().optional(),
    currency: z.string().optional(),
    availability: z.string().optional(),
    specifications: LaptopSpecsSchema.optional(),
    images: z.array(z.string()).optional(),
    description: z.string().optional(),
    reviews: z.object({
      rating: z.number().optional(),
      count: z.number().optional()
    }).optional()
  }),
  success: z.boolean()
})

export class DataProcessor {
  private logger: pino.Logger

  constructor() {
    this.logger = pino({
      name: 'data-processor',
      level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
      transport: process.env.NODE_ENV === 'development' ? {
        target: 'pino-pretty',
        options: { colorize: true }
      } : undefined
    })
  }

  /**
   * Process scraped data with validation, transformation, and enrichment
   */
  async processScrapedData(
    rawData: z.infer<typeof ScrapedDataSchema>[],
    config: DataProcessingConfig
  ): Promise<any[]> {
    this.logger.info(`Processing ${rawData.length} scraped items`)

    let processedData = rawData

    // Step 1: Validate data
    if (config.validateSchema) {
      processedData = await this.validateData(processedData)
    }

    // Step 2: Transform data
    if (config.transformData) {
      processedData = await this.transformData(processedData)
    }

    // Step 3: Detect and handle duplicates
    if (config.detectDuplicates) {
      processedData = await this.deduplicateData(processedData, config.mergeStrategy)
    }

    // Step 4: Enrich with LLM compatibility data
    if (config.enrichWithLLMCompatibility) {
      processedData = await this.enrichWithLLMCompatibility(processedData)
    }

    // Step 5: Save to database
    const savedData = await this.saveToDatabase(processedData)

    this.logger.info(`Successfully processed and saved ${savedData.length} items`)
    return savedData
  }

  /**
   * Validate scraped data against schema
   */
  private async validateData(data: z.infer<typeof ScrapedDataSchema>[]): Promise<z.infer<typeof ScrapedDataSchema>[]> {
    const validData = []
    const errors = []

    for (const item of data) {
      try {
        const validated = ScrapedDataSchema.parse(item)
        validData.push(validated)
      } catch (error) {
        errors.push({
          item,
          error: error instanceof z.ZodError ? error.errors : error
        })
      }
    }

    if (errors.length > 0) {
      this.logger.warn(`Validation failed for ${errors.length}/${data.length} items`)
      this.logger.debug('Validation errors:', errors.slice(0, 5)) // Log first 5 errors
    }

    return validData
  }

  /**
   * Transform raw data into standardized format
   */
  private async transformData(data: z.infer<typeof ScrapedDataSchema>[]): Promise<z.infer<typeof ScrapedDataSchema>[]> {
    return data.map(item => {
      const transformed = { ...item }

      // Normalize price data
      if (transformed.data.price) {
        transformed.data.price = this.normalizePrice(transformed.data.price, transformed.data.currency)
      }

      // Normalize specifications
      if (transformed.data.specifications) {
        transformed.data.specifications = this.normalizeSpecifications(transformed.data.specifications)
      }

      // Extract and normalize images
      if (transformed.data.images) {
        transformed.data.images = this.normalizeImages(transformed.data.images, item.url)
      }

      // Clean and normalize text fields
      if (transformed.data.title) {
        transformed.data.title = this.cleanText(transformed.data.title)
      }

      if (transformed.data.description) {
        transformed.data.description = this.cleanText(transformed.data.description)
      }

      return transformed
    })
  }

  /**
   * Detect and handle duplicate data
   */
  private async deduplicateData(data: z.infer<typeof ScrapedDataSchema>[], mergeStrategy: 'replace' | 'merge' | 'skip'): Promise<z.infer<typeof ScrapedDataSchema>[]> {
    const uniqueData = new Map<string, any>()

    for (const item of data) {
      // Create a unique key based on brand, model, and source
      const key = this.createUniqueKey(item)

      if (uniqueData.has(key)) {
        const existing = uniqueData.get(key)!
        
        switch (mergeStrategy) {
          case 'replace':
            uniqueData.set(key, item)
            break
          case 'merge':
            uniqueData.set(key, this.mergeItems(existing, item))
            break
          case 'skip':
            // Keep existing, skip new
            break
        }
      } else {
        uniqueData.set(key, item)
      }
    }

    const deduplicatedData = Array.from(uniqueData.values())
    
    if (deduplicatedData.length < data.length) {
      this.logger.info(`Deduplicated ${data.length - deduplicatedData.length} items`)
    }

    return deduplicatedData
  }

  /**
   * Enrich data with LLM compatibility information
   */
  private async enrichWithLLMCompatibility(data: z.infer<typeof ScrapedDataSchema>[]): Promise<z.infer<typeof ScrapedDataSchema>[]> {
    return data.map(item => {
      if (item.data.specifications) {
        const compatibility = this.calculateLLMCompatibility(item.data.specifications)
        item.data.llmCompatibility = compatibility
      }
      return item
    })
  }

  /**
   * Save processed data to database
   */
  private async saveToDatabase(data: any[]): Promise<any[]> {
    const savedItems = []

    for (const item of data) {
      try {
        // Create or update laptop listing
        const laptopListing = await prisma.laptopListing.upsert({
          where: {
            url: item.url
          },
          update: {
            title: item.data.title,
            price: item.data.price,
            currency: item.data.currency || 'USD',
            availability: item.data.availability,
            specifications: item.data.specifications,
            images: item.data.images,
            description: item.data.description,
            lastScrapedAt: item.scrapedAt,
            updatedAt: new Date()
          },
          create: {
            url: item.url,
            title: item.data.title || 'Unknown',
            price: item.data.price,
            currency: item.data.currency || 'USD',
            availability: item.data.availability,
            specifications: item.data.specifications,
            images: item.data.images,
            description: item.data.description,
            siteType: item.siteType,
            lastScrapedAt: item.scrapedAt,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })

        savedItems.push(laptopListing)
      } catch (error) {
        this.logger.error(`Failed to save item ${item.url}:`, error)
      }
    }

    return savedItems
  }

  /**
   * Helper methods
   */
  private normalizePrice(price: string | number, currency?: string): number {
    if (typeof price === 'number') return price
    if (typeof price === 'string') {
      const numericPrice = parseFloat(price.replace(/[^0-9.]/g, ''))
      return isNaN(numericPrice) ? 0 : numericPrice
    }
    return 0
  }

  private normalizeSpecifications(specs: z.infer<typeof LaptopSpecsSchema>): z.infer<typeof LaptopSpecsSchema> {
    // Normalize CPU information
    if (specs.cpu) {
      if (typeof specs.cpu === 'string') {
        specs.cpu = this.parseCpuString(specs.cpu)
      }
    }

    // Normalize memory information
    if (specs.memory) {
      if (typeof specs.memory === 'string') {
        specs.memory = this.parseMemoryString(specs.memory)
      }
    }

    // Normalize storage information
    if (specs.storage && !Array.isArray(specs.storage)) {
      specs.storage = [specs.storage]
    }

    return specs
  }

  private normalizeImages(images: string[], baseUrl: string): string[] {
    return images
      .filter(img => img && typeof img === 'string')
      .map(img => {
        // Convert relative URLs to absolute
        if (img.startsWith('//')) return `https:${img}`
        if (img.startsWith('/')) return new URL(img, baseUrl).href
        return img
      })
      .filter(img => this.isValidImageUrl(img))
  }

  private cleanText(text: string): string {
    return text
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s\-.,()]/g, '')
  }

  private createUniqueKey(item: z.infer<typeof ScrapedDataSchema>): string {
    const specs = item.data.specifications || {}
    const brand = specs.brand || 'unknown'
    const model = specs.model || item.data.title || 'unknown'
    return `${brand}-${model}`.toLowerCase().replace(/\s+/g, '-')
  }

  private mergeItems(existing: z.infer<typeof ScrapedDataSchema>, newItem: z.infer<typeof ScrapedDataSchema>): z.infer<typeof ScrapedDataSchema> {
    // Simple merge strategy - prefer newer data but keep existing if new is empty
    const merged = { ...existing }
    
    Object.keys(newItem.data).forEach(key => {
      if (newItem.data[key] && (!existing.data[key] || newItem.scrapedAt > existing.scrapedAt)) {
        merged.data[key] = newItem.data[key]
      }
    })

    merged.scrapedAt = newItem.scrapedAt
    return merged
  }

  private calculateLLMCompatibility(specs: z.infer<typeof LaptopSpecsSchema>): { score: number; factors: Record<string, string>; recommendations: string[] } {
    // Simplified LLM compatibility calculation
    const compatibility = {
      score: 0,
      factors: {},
      recommendations: []
    }

    // CPU scoring
    if (specs.cpu?.cores) {
      compatibility.factors.cpu = specs.cpu.cores >= 8 ? 'excellent' : specs.cpu.cores >= 4 ? 'good' : 'limited'
      compatibility.score += specs.cpu.cores >= 8 ? 25 : specs.cpu.cores >= 4 ? 15 : 5
    }

    // Memory scoring
    if (specs.memory?.sizeGb) {
      compatibility.factors.memory = specs.memory.sizeGb >= 32 ? 'excellent' : specs.memory.sizeGb >= 16 ? 'good' : 'limited'
      compatibility.score += specs.memory.sizeGb >= 32 ? 25 : specs.memory.sizeGb >= 16 ? 15 : 5
    }

    // GPU scoring
    if (specs.gpu?.vramGb) {
      compatibility.factors.gpu = specs.gpu.vramGb >= 8 ? 'excellent' : specs.gpu.vramGb >= 4 ? 'good' : 'limited'
      compatibility.score += specs.gpu.vramGb >= 8 ? 25 : specs.gpu.vramGb >= 4 ? 15 : 5
    }

    // Storage scoring
    if (specs.storage?.length) {
      const hasSSD = specs.storage.some((s: any) => s.type?.toLowerCase().includes('ssd'))
      compatibility.factors.storage = hasSSD ? 'good' : 'limited'
      compatibility.score += hasSSD ? 15 : 5
    }

    return compatibility
  }

  private parseCpuString(cpuStr: string): { model: string; cores?: number; baseClockGhz?: number } {
    // Simple CPU string parsing - can be enhanced
    const cores = cpuStr.match(/(\d+)[\s-]?core/i)?.[1]
    const ghz = cpuStr.match(/(\d+\.?\d*)\s?ghz/i)?.[1]
    
    return {
      model: cpuStr,
      cores: cores ? parseInt(cores) : undefined,
      baseClockGhz: ghz ? parseFloat(ghz) : undefined
    }
  }

  private parseMemoryString(memStr: string): { sizeGb?: number; type?: string } {
    const sizeMatch = memStr.match(/(\d+)\s?gb/i)
    const typeMatch = memStr.match(/(ddr\d+)/i)
    
    return {
      sizeGb: sizeMatch ? parseInt(sizeMatch[1]) : undefined,
      type: typeMatch ? typeMatch[1].toUpperCase() : undefined
    }
  }

  private isValidImageUrl(url: string): boolean {
    try {
      new URL(url)
      return /\.(jpg|jpeg|png|webp|gif)$/i.test(url)
    } catch {
      return false
    }
  }
}
