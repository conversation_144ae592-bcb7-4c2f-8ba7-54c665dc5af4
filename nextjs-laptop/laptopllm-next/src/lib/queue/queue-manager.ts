/**
 * Queue Manager
 * Manages BullMQ queues for scraping and data processing
 */

import { Queue, Worker, QueueEvents, RepeatOptions } from 'bullmq'
import Redis from 'ioredis'
import pino from 'pino'
import type {
  QueueManagerConfig,
  QueueConfig,
  WorkerConfig,
  JobType,
  JobPriority,
  BaseJobData,
  QueueStats,
  JobEventData,
  JobEvent
} from './types'

export class QueueManager {
  private redis: Redis
  private queues: Map<string, Queue> = new Map()
  private workers: Map<string, Worker> = new Map()
  private queueEvents: Map<string, QueueEvents> = new Map()
  private logger: pino.Logger
  private config: QueueManagerConfig
  private eventHandlers: Map<string, ((data: JobEventData) => void)[]> = new Map()

  constructor(config: QueueManagerConfig) {
    this.config = config
    this.logger = pino({
      name: 'queue-manager',
      level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
      transport: process.env.NODE_ENV === 'development' ? {
        target: 'pino-pretty',
        options: { colorize: true }
      } : undefined
    })

    // Initialize Redis connection
    this.redis = new Redis({
      host: config.redis.host,
      port: config.redis.port,
      password: config.redis.password,
      db: config.redis.db || 0,
      maxRetriesPerRequest: config.redis.maxRetriesPerRequest || 3,
      retryDelayOnFailover: config.redis.retryDelayOnFailover || 100,
      lazyConnect: true,
    })

    this.setupRedisEventHandlers()
  }

  /**
   * Initialize all queues and workers
   */
  async initialize(): Promise<void> {
    try {
      await this.redis.connect()
      this.logger.info('Connected to Redis')

      // Initialize queues
      for (const queueConfig of this.config.queues) {
        await this.createQueue(queueConfig)
      }

      // Initialize workers
      for (const workerConfig of this.config.workers) {
        await this.createWorker(workerConfig)
      }

      this.logger.info('Queue Manager initialized successfully')
    } catch (error) {
      this.logger.error('Failed to initialize Queue Manager:', error)
      throw error
    }
  }

  /**
   * Create a queue
   */
  private async createQueue(config: QueueConfig): Promise<Queue> {
    const queue = new Queue(config.name, {
      connection: this.redis,
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        ...config.defaultJobOptions,
      },
      ...config,
    })

    // Setup queue events
    const queueEvents = new QueueEvents(config.name, {
      connection: this.redis,
    })

    this.setupQueueEventHandlers(queueEvents, config.name)

    this.queues.set(config.name, queue)
    this.queueEvents.set(config.name, queueEvents)

    this.logger.info(`Queue '${config.name}' created`)
    return queue
  }

  /**
   * Create a worker
   */
  private async createWorker(config: WorkerConfig): Promise<Worker> {
    const worker = new Worker(
      config.queueName,
      config.processor,
      {
        connection: this.redis,
        concurrency: config.concurrency,
        ...config.options,
      }
    )

    this.setupWorkerEventHandlers(worker, config.queueName)

    this.workers.set(config.queueName, worker)

    this.logger.info(`Worker for queue '${config.queueName}' created`)
    return worker
  }

  /**
   * Add a job to a queue
   */
  async addJob<T extends BaseJobData>(
    queueName: string,
    jobData: T,
    options?: {
      priority?: number
      delay?: number
      repeat?: RepeatOptions
    }
  ): Promise<string> {
    const queue = this.queues.get(queueName)
    if (!queue) {
      throw new Error(`Queue '${queueName}' not found`)
    }

    const priorityMap: Record<JobPriority, number> = {
      critical: 1,
      high: 2,
      normal: 3,
      low: 4,
    }

    const job = await queue.add(
      jobData.type,
      jobData,
      {
        priority: options?.priority || priorityMap[jobData.priority],
        delay: options?.delay,
        repeat: options?.repeat,
        jobId: jobData.id,
      }
    )

    this.logger.info(`Job '${job.id}' added to queue '${queueName}'`)
    return job.id!
  }

  /**
   * Get job by ID
   */
  async getJob(queueName: string, jobId: string) {
    const queue = this.queues.get(queueName)
    if (!queue) {
      throw new Error(`Queue '${queueName}' not found`)
    }

    return await queue.getJob(jobId)
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(queueName: string): Promise<QueueStats> {
    const queue = this.queues.get(queueName)
    if (!queue) {
      throw new Error(`Queue '${queueName}' not found`)
    }

    const [waiting, active, completed, failed, delayed, paused] = await Promise.all([
      queue.getWaiting(),
      queue.getActive(),
      queue.getCompleted(),
      queue.getFailed(),
      queue.getDelayed(),
      queue.getPaused(),
    ])

    const total = waiting.length + active.length + completed.length + failed.length + delayed.length + paused.length

    // Calculate throughput (simplified)
    const now = new Date()
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
    const completedLastHour = completed.filter(job => 
      job.finishedOn && job.finishedOn > oneHourAgo.getTime()
    ).length

    // Calculate average processing time
    const recentCompleted = completed.slice(0, 10)
    const avgProcessingTime = recentCompleted.length > 0
      ? recentCompleted.reduce((sum, job) => {
          const duration = job.finishedOn! - job.processedOn!
          return sum + duration
        }, 0) / recentCompleted.length
      : 0

    const successRate = total > 0 ? (completed.length / (completed.length + failed.length)) * 100 : 100

    return {
      name: queueName,
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
      paused: paused.length,
      total,
      throughput: {
        perMinute: Math.round(completedLastHour / 60),
        perHour: completedLastHour,
        perDay: completedLastHour * 24,
      },
      averageProcessingTime: Math.round(avgProcessingTime),
      successRate: Math.round(successRate * 100) / 100,
      lastProcessedAt: recentCompleted[0]?.finishedOn ? new Date(recentCompleted[0].finishedOn) : undefined,
    }
  }

  /**
   * Get all queue statistics
   */
  async getAllQueueStats(): Promise<QueueStats[]> {
    const stats: QueueStats[] = []
    
    for (const queueName of this.queues.keys()) {
      try {
        const queueStats = await this.getQueueStats(queueName)
        stats.push(queueStats)
      } catch (error) {
        this.logger.error(`Failed to get stats for queue '${queueName}':`, error)
      }
    }

    return stats
  }

  /**
   * Pause a queue
   */
  async pauseQueue(queueName: string): Promise<void> {
    const queue = this.queues.get(queueName)
    if (!queue) {
      throw new Error(`Queue '${queueName}' not found`)
    }

    await queue.pause()
    this.logger.info(`Queue '${queueName}' paused`)
  }

  /**
   * Resume a queue
   */
  async resumeQueue(queueName: string): Promise<void> {
    const queue = this.queues.get(queueName)
    if (!queue) {
      throw new Error(`Queue '${queueName}' not found`)
    }

    await queue.resume()
    this.logger.info(`Queue '${queueName}' resumed`)
  }

  /**
   * Clean a queue
   */
  async cleanQueue(queueName: string, grace: number = 0, status?: 'completed' | 'failed'): Promise<void> {
    const queue = this.queues.get(queueName)
    if (!queue) {
      throw new Error(`Queue '${queueName}' not found`)
    }

    if (status) {
      await queue.clean(grace, status)
    } else {
      await queue.clean(grace, 'completed')
      await queue.clean(grace, 'failed')
    }

    this.logger.info(`Queue '${queueName}' cleaned`)
  }

  /**
   * Setup Redis event handlers
   */
  private setupRedisEventHandlers(): void {
    this.redis.on('connect', () => {
      this.logger.info('Redis connected')
    })

    this.redis.on('error', (error) => {
      this.logger.error('Redis error:', error)
    })

    this.redis.on('close', () => {
      this.logger.warn('Redis connection closed')
    })
  }

  /**
   * Setup queue event handlers
   */
  private setupQueueEventHandlers(queueEvents: QueueEvents, queueName: string): void {
    const events: JobEvent[] = ['waiting', 'active', 'completed', 'failed', 'progress', 'stalled', 'removed']

    events.forEach(event => {
      queueEvents.on(event, (data) => {
        const eventData: JobEventData = {
          jobId: data.jobId,
          event,
          data,
          timestamp: new Date(),
          queueName,
        }

        this.emitEvent(event, eventData)
      })
    })
  }

  /**
   * Setup worker event handlers
   */
  private setupWorkerEventHandlers(worker: Worker, queueName: string): void {
    worker.on('completed', (job) => {
      this.logger.info(`Job '${job.id}' completed in queue '${queueName}'`)
    })

    worker.on('failed', (job, err) => {
      this.logger.error(`Job '${job?.id}' failed in queue '${queueName}':`, err)
    })

    worker.on('error', (err) => {
      this.logger.error(`Worker error in queue '${queueName}':`, err)
    })
  }

  /**
   * Event handling
   */
  on(event: JobEvent, handler: (data: JobEventData) => void): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, [])
    }
    this.eventHandlers.get(event)!.push(handler)
  }

  private emitEvent(event: JobEvent, data: JobEventData): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          this.logger.error(`Error in event handler for '${event}':`, error)
        }
      })
    }
  }

  /**
   * Shutdown gracefully
   */
  async shutdown(): Promise<void> {
    this.logger.info('Shutting down Queue Manager...')

    // Close workers
    for (const [name, worker] of this.workers) {
      await worker.close()
      this.logger.info(`Worker '${name}' closed`)
    }

    // Close queue events
    for (const [name, queueEvents] of this.queueEvents) {
      await queueEvents.close()
      this.logger.info(`Queue events '${name}' closed`)
    }

    // Close queues
    for (const [name, queue] of this.queues) {
      await queue.close()
      this.logger.info(`Queue '${name}' closed`)
    }

    // Close Redis connection
    await this.redis.quit()
    this.logger.info('Redis connection closed')

    this.logger.info('Queue Manager shutdown complete')
  }
}
