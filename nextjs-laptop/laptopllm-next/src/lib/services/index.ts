// Service exports for the LaptopLLM Finder application

export { BaseService } from './base.service'
export { LaptopService } from './laptop.service'
export { CompatibilityService } from './compatibility.service'
export { AnalyticsService } from './analytics.service'

// Service instances (singletons)
export const laptopService = new LaptopService()
export const compatibilityService = new CompatibilityService()
export const analyticsService = new AnalyticsService()

// Type exports
export type {
  CreateLaptopData,
  UpdateLaptopData,
  LaptopFilters,
} from './laptop.service'

export type {
  CompatibilityCalculationInput,
  CompatibilityResult,
  LLMModelRequirements,
} from './compatibility.service'

export type {
  SearchAnalyticsData,
  LaptopViewData,
  PriceAlertData,
  AnalyticsInsights,
  PopularFilters,
} from './analytics.service'
