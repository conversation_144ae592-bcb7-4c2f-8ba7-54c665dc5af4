// Laptop service for database operations

import { Prisma } from '@prisma/client'
import { BaseService } from './base.service'
import type {
  Laptop,
  SearchFilters,
  PaginationParams,
  SearchResults,
  LaptopWithCompatibility,
  LaptopSearchResult,
} from '@/shared/types'

export interface LaptopSpecifications {
  cpu?: {
    model: string
    cores: number
    threads: number
    base_clock_ghz: number
    boost_clock_ghz?: number
  }
  gpu?: {
    model: string
    vram_gb: number
    is_discrete: boolean
  }
  ram?: {
    size_gb: number
    speed_mhz: number
    is_dual_channel: boolean
  }
  storage?: {
    capacity_gb: number
    type: 'SSD' | 'HDD' | 'NVMe'
    interface: string
  }
  display?: {
    size_inches: number
    resolution: string
    refresh_rate?: number
    panel_type?: string
  }
}

export interface CompatibilityScore {
  score: number | null
}

interface LaptopForRelevance {
  model_name: string
  description: string | null
  brands: { name: string }
  laptop_cpus: { cpus: { model: string } }[]
  laptop_gpus: { gpus: { model: string } }[]
}

export interface CreateLaptopData {
  model_name: string
  brand_id: number
  release_date?: Date
  description?: string
  image_url?: string
  msrp?: number
  specifications?: LaptopSpecifications
}

export interface UpdateLaptopData extends Partial<CreateLaptopData> {
  id: number
}

export interface LaptopFilters extends SearchFilters {
  brandIds?: number[]
  minPrice?: number
  maxPrice?: number
  minRam?: number
  maxRam?: number
  minStorage?: number
  maxStorage?: number
  cpuBrands?: string[]
  gpuBrands?: string[]
  displaySizes?: number[]
  isAvailable?: boolean
  hasCompatibilityScore?: boolean
  minCompatibilityScore?: number
}

export class LaptopService extends BaseService {
  /**
   * Get all laptops with optional filtering and pagination
   */
  async getLaptops(
    filters: LaptopFilters = {},
    pagination: PaginationParams = { page: 1, limit: 20 },
    includeCompatibility: boolean = false
  ): Promise<SearchResults<LaptopWithCompatibility>> {
    this.validatePagination(pagination)

    return this.executeWithErrorHandling(async () => {
      const where = this.buildLaptopWhereClause(filters)
      const orderBy = this.buildOrderByClause(filters.sortBy, filters.sortOrder)

      const [laptops, total] = await Promise.all([
        this.db.laptops.findMany({
          where,
          orderBy,
          skip: this.getOffset(pagination.page, pagination.limit),
          take: pagination.limit,
          include: {
            brands: true,
            laptop_cpus: {
              include: {
                cpus: {
                  include: {
                    manufacturers: true,
                  },
                },
              },
            },
            laptop_gpus: {
              include: {
                gpus: {
                  include: {
                    manufacturers: true,
                  },
                },
              },
            },
            laptop_ram: {
              include: {
                ram_configurations: {
                  include: {
                    manufacturers: true,
                    memory_types: true,
                  },
                },
              },
            },
            laptop_storage: {
              include: {
                storage_devices: {
                  include: {
                    manufacturers: true,
                    storage_interfaces: true,
                  },
                },
              },
            },
            displays: {
              include: {
                resolution_types: true,
                panel_types: true,
              },
            },
            physical_specs: true,
            laptop_llm_compatibility: includeCompatibility
              ? {
                  include: {
                    llm_models: true,
                  },
                }
              : false,
          },
        }),
        this.db.laptops.count({ where }),
      ])

      const laptopsWithCompatibility: LaptopWithCompatibility[] = laptops.map(laptop => ({
        laptop: laptop as Laptop,
        compatibility: laptop.laptop_llm_compatibility || [],
        averageCompatibilityScore: this.calculateAverageCompatibilityScore(
          laptop.laptop_llm_compatibility
        ),
      }))

      return this.createPaginatedResults(laptopsWithCompatibility, total, pagination)
    }, 'getLaptops')
  }

  /**
   * Get laptop by ID with full details
   */
  async getLaptopById(
    id: number,
    includeCompatibility: boolean = true
  ): Promise<LaptopWithCompatibility | null> {
    return this.executeWithErrorHandling(async () => {
      const laptop = await this.db.laptops.findUnique({
        where: { id },
        include: {
          brands: true,
          laptop_cpus: {
            include: {
              cpus: {
                include: {
                  manufacturers: true,
                  cpu_architectures: true,
                },
              },
            },
          },
          laptop_gpus: {
            include: {
              gpus: {
                include: {
                  manufacturers: true,
                  memory_types: true,
                },
              },
            },
          },
          laptop_ram: {
            include: {
              ram_configurations: {
                include: {
                  manufacturers: true,
                  memory_types: true,
                },
              },
            },
          },
          laptop_storage: {
            include: {
              storage_devices: {
                include: {
                  manufacturers: true,
                  storage_interfaces: true,
                },
              },
            },
          },
          displays: {
            include: {
              resolution_types: true,
              panel_types: true,
            },
          },
          physical_specs: true,
          laptop_listings: {
            include: {
              sources: true,
              price_history: {
                orderBy: { recorded_at: 'desc' },
                take: 10,
              },
            },
          },
          laptop_llm_compatibility: includeCompatibility
            ? {
                include: {
                  llm_models: true,
                },
              }
            : false,
          laptop_scores: true,
        },
      })

      if (!laptop) return null

      return {
        laptop: laptop as Laptop,
        compatibility: laptop.laptop_llm_compatibility || [],
        averageCompatibilityScore: this.calculateAverageCompatibilityScore(
          laptop.laptop_llm_compatibility
        ),
      }
    }, 'getLaptopById')
  }

  /**
   * Search laptops by text query
   */
  async searchLaptops(
    query: string,
    filters: LaptopFilters = {},
    pagination: PaginationParams = { page: 1, limit: 20 },
    fuzzySearch: boolean = true
  ): Promise<SearchResults<LaptopSearchResult>> {
    this.validatePagination(pagination)

    return this.executeWithErrorHandling(async () => {
      const searchMode = fuzzySearch ? 'insensitive' : 'default'
      const searchTerms = query.trim().split(/\s+/)

      const where = {
        ...this.buildLaptopWhereClause(filters),
        OR: [
          {
            model_name: {
              contains: query,
              mode: searchMode,
            },
          },
          {
            description: {
              contains: query,
              mode: searchMode,
            },
          },
          {
            brands: {
              name: {
                contains: query,
                mode: searchMode,
              },
            },
          },
          // Search in CPU models
          {
            laptop_cpus: {
              some: {
                cpus: {
                  model: {
                    contains: query,
                    mode: searchMode,
                  },
                },
              },
            },
          },
          // Search in GPU models
          {
            laptop_gpus: {
              some: {
                gpus: {
                  model: {
                    contains: query,
                    mode: searchMode,
                  },
                },
              },
            },
          },
        ],
      }

      const [laptops, total] = await Promise.all([
        this.db.laptops.findMany({
          where,
          orderBy: this.buildOrderByClause(filters.sortBy, filters.sortOrder),
          skip: this.getOffset(pagination.page, pagination.limit),
          take: pagination.limit,
          include: {
            brands: true,
            laptop_cpus: {
              include: {
                cpus: {
                  include: {
                    manufacturers: true,
                  },
                },
              },
            },
            laptop_gpus: {
              include: {
                gpus: {
                  include: {
                    manufacturers: true,
                  },
                },
              },
            },
            laptop_llm_compatibility: {
              include: {
                llm_models: true,
              },
            },
          },
        }),
        this.db.laptops.count({ where }),
      ])

      const laptopsWithRelevance: LaptopSearchResult[] = laptops.map(laptop => ({
        laptop: laptop as Laptop,
        compatibility: laptop.laptop_llm_compatibility || [],
        averageCompatibilityScore: this.calculateAverageCompatibilityScore(
          laptop.laptop_llm_compatibility
        ),
        relevanceScore: this.calculateRelevanceScore(laptop, searchTerms),
      }))

      // Sort by relevance if no specific sort order is provided
      if (!filters.sortBy) {
        laptopsWithRelevance.sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
      }

      return this.createPaginatedResults(laptopsWithRelevance, total, pagination)
    }, 'searchLaptops')
  }

  /**
   * Create a new laptop
   */
  async createLaptop(data: CreateLaptopData): Promise<Laptop> {
    return this.executeWithErrorHandling(async () => {
      return (await this.db.laptops.create({
        data: {
          ...data,
          created_at: new Date(),
          updated_at: new Date(),
        },
        include: {
          brands: true,
        },
      })) as Laptop
    }, 'createLaptop')
  }

  /**
   * Update an existing laptop
   */
  async updateLaptop(data: UpdateLaptopData): Promise<Laptop> {
    const { id, ...updateData } = data

    return this.executeWithErrorHandling(async () => {
      return (await this.db.laptops.update({
        where: { id },
        data: {
          ...updateData,
          updated_at: new Date(),
        },
        include: {
          brands: true,
        },
      })) as Laptop
    }, 'updateLaptop')
  }

  /**
   * Delete a laptop
   */
  async deleteLaptop(id: number): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      await this.db.laptops.delete({
        where: { id },
      })
    }, 'deleteLaptop')
  }

  /**
   * Get laptop statistics
   */
  async getLaptopStats(): Promise<{
    total: number
    byBrand: Array<{ brand: string; count: number }>
    priceRange: { min: number; max: number }
    averagePrice: number
  }> {
    return this.executeWithErrorHandling(async () => {
      const [total, byBrand, priceStats] = await Promise.all([
        this.db.laptops.count(),
        this.db.laptops.groupBy({
          by: ['brand_id'],
          _count: {
            id: true,
          },
          orderBy: {
            _count: {
              id: 'desc',
            },
          },
        }),
        this.db.laptops.aggregate({
          _min: { msrp: true },
          _max: { msrp: true },
          _avg: { msrp: true },
        }),
      ])

      // Get brand names for the grouped results
      const brandIds = byBrand.map(item => item.brand_id)
      const brands = await this.db.brands.findMany({
        where: { id: { in: brandIds } },
        select: { id: true, name: true },
      })

      const brandMap = new Map(brands.map(brand => [brand.id, brand.name]))

      const byBrandWithNames = byBrand.map(item => ({
        brand: brandMap.get(item.brand_id) || 'Unknown',
        count: item._count.id,
      }))

      return {
        total,
        byBrand: byBrandWithNames,
        priceRange: {
          min: Number(priceStats._min.msrp) || 0,
          max: Number(priceStats._max.msrp) || 0,
        },
        averagePrice: Number(priceStats._avg.msrp) || 0,
      }
    }, 'getLaptopStats')
  }

  /**
   * Build where clause for laptop filtering
   */
  private buildLaptopWhereClause(filters: LaptopFilters): Prisma.LaptopsWhereInput {
    const where: Prisma.LaptopsWhereInput = {}

    if (filters.brandIds && filters.brandIds.length > 0) {
      where.brand_id = { in: filters.brandIds }
    }

    if (filters.brands && filters.brands.length > 0) {
      where.brands = {
        name: { in: filters.brands },
      }
    }

    if (filters.isAvailable !== undefined) {
      where.is_available = filters.isAvailable
    }

    if (filters.priceRange) {
      where.msrp = {
        gte: filters.priceRange.min,
        lte: filters.priceRange.max,
      }
    }

    if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
      where.msrp = {}
      if (filters.minPrice !== undefined) where.msrp.gte = filters.minPrice
      if (filters.maxPrice !== undefined) where.msrp.lte = filters.maxPrice
    }

    // Add RAM filtering
    if (filters.ramRange || filters.minRam !== undefined || filters.maxRam !== undefined) {
      where.laptop_ram = {
        some: {
          ram_configurations: {
            capacity_gb: {
              gte: filters.ramRange?.min || filters.minRam,
              lte: filters.ramRange?.max || filters.maxRam,
            },
          },
        },
      }
    }

    // Add storage filtering
    if (filters.storageRange || filters.minStorage !== undefined || filters.maxStorage !== undefined) {
      where.laptop_storage = {
        some: {
          storage_devices: {
            capacity_gb: {
              gte: filters.storageRange?.min || filters.minStorage,
              lte: filters.storageRange?.max || filters.maxStorage,
            },
          },
        },
      }
    }

    // Add CPU brand filtering
    if (filters.cpuBrands && filters.cpuBrands.length > 0) {
      where.laptop_cpus = {
        some: {
          cpus: {
            manufacturers: {
              name: { in: filters.cpuBrands },
            },
          },
        },
      }
    }

    // Add GPU brand filtering
    if (filters.gpuBrands && filters.gpuBrands.length > 0) {
      where.laptop_gpus = {
        some: {
          gpus: {
            manufacturers: {
              name: { in: filters.gpuBrands },
            },
          },
        },
      }
    }

    // Add display size filtering
    if (filters.displaySizes && filters.displaySizes.length > 0) {
      where.displays = {
        some: {
          size_inches: { in: filters.displaySizes },
        },
      }
    }

    // Add compatibility score filtering
    if (filters.minCompatibilityScore !== undefined) {
      where.laptop_llm_compatibility = {
        some: {
          score: { gte: filters.minCompatibilityScore },
        },
      }
    }

    return where
  }

  /**
   * Calculate average compatibility score
   */
  private calculateAverageCompatibilityScore(compatibility: CompatibilityScore[]): number | undefined {
    if (!compatibility || compatibility.length === 0) return undefined

    const scores = compatibility
      .map(c => c.score)
      .filter(score => score !== null && score !== undefined)

    if (scores.length === 0) return undefined

    return scores.reduce((sum, score) => sum + score, 0) / scores.length
  }

  /**
   * Calculate relevance score for search results
   */
  private calculateRelevanceScore(
    laptop: LaptopForRelevance,
    searchTerms: string[]
  ): number {
    let score = 0

    searchTerms.forEach(term => {
      const lowerTerm = term.toLowerCase()

      // Model name match (highest weight)
      if (laptop.model_name.toLowerCase().includes(lowerTerm)) {
        score += 10
      }

      // Brand name match
      if (laptop.brands?.name.toLowerCase().includes(lowerTerm)) {
        score += 8
      }

      // Description match
      if (laptop.description?.toLowerCase().includes(lowerTerm)) {
        score += 5
      }

      // CPU model match
      laptop.laptop_cpus?.forEach(cpu => {
        if (cpu.cpus.model.toLowerCase().includes(lowerTerm)) {
          score += 6
        }
      })

      // GPU model match
      laptop.laptop_gpus?.forEach(gpu => {
        if (gpu.gpus.model.toLowerCase().includes(lowerTerm)) {
          score += 6
        }
      })
    })

    return score
  }
}
