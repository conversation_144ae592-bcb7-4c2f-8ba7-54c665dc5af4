// Analytics service for tracking user behavior and generating insights

import { BaseService } from './base.service'
import type { PaginationParams, SearchResults, PriceAlert } from '@/shared/types'

export interface SearchAnalyticsData {
  searchQuery?: string
  filtersApplied?: Record<string, any>
  resultsCount: number
  userId?: string
  sessionId?: string
  ipAddress?: string
  userAgent?: string
}

export interface LaptopViewData {
  laptopId: number
  userId?: string
  sessionId?: string
  ipAddress?: string
  userAgent?: string
}

export interface PriceAlertData {
  userId: string
  laptopId: number
  targetPrice: number
  currency: string
}

export interface AnalyticsInsights {
  popularSearchTerms: Array<{ term: string; count: number }>
  topViewedLaptops: Array<{ laptopId: number; model: string; brand: string; views: number }>
  searchTrends: Array<{ date: string; searches: number }>
  userEngagement: {
    totalSearches: number
    totalViews: number
    averageSessionDuration: number
    bounceRate: number
  }
  priceAlertStats: {
    totalAlerts: number
    activeAlerts: number
    triggeredAlerts: number
    averageTargetPrice: number
  }
}

export interface PopularFilters {
  brands: Array<{ name: string; usage: number }>
  priceRanges: Array<{ range: string; usage: number }>
  ramSizes: Array<{ size: string; usage: number }>
  storageSizes: Array<{ size: string; usage: number }>
}

export class AnalyticsService extends BaseService {
  /**
   * Track a search event
   */
  async trackSearch(data: SearchAnalyticsData): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      await this.db.search_analytics.create({
        data: {
          search_query: data.searchQuery,
          filters_applied: data.filtersApplied,
          results_count: data.resultsCount,
          user_id: data.userId,
          session_id: data.sessionId,
          ip_address: data.ipAddress,
          user_agent: data.userAgent,
          searched_at: new Date(),
        },
      })
    }, 'trackSearch')
  }

  /**
   * Track a laptop view event
   */
  async trackLaptopView(data: LaptopViewData): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      await this.db.laptop_views.create({
        data: {
          laptop_id: data.laptopId,
          user_id: data.userId,
          session_id: data.sessionId,
          ip_address: data.ipAddress,
          user_agent: data.userAgent,
          viewed_at: new Date(),
        },
      })
    }, 'trackLaptopView')
  }

  /**
   * Create a price alert
   */
  async createPriceAlert(data: PriceAlertData): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      await this.db.price_alerts.create({
        data: {
          user_id: data.userId,
          laptop_id: data.laptopId,
          target_price: data.targetPrice,
          currency: data.currency,
          is_active: true,
          notification_sent: false,
          created_at: new Date(),
        },
      })
    }, 'createPriceAlert')
  }

  /**
   * Get user's price alerts
   */
  async getUserPriceAlerts(
    userId: string,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<SearchResults<PriceAlert>> {
    this.validatePagination(pagination)

    return this.executeWithErrorHandling(async () => {
      const [alerts, total] = await Promise.all([
        this.db.price_alerts.findMany({
          where: { user_id: userId },
          skip: this.getOffset(pagination.page, pagination.limit),
          take: pagination.limit,
          include: {
            laptops: {
              include: {
                brands: true,
                laptop_listings: {
                  orderBy: { price: 'asc' },
                  take: 1,
                  include: { sources: true },
                },
              },
            },
          },
          orderBy: { created_at: 'desc' },
        }),
        this.db.price_alerts.count({
          where: { user_id: userId },
        }),
      ])

      return this.createPaginatedResults(alerts, total, pagination)
    }, 'getUserPriceAlerts')
  }

  /**
   * Update price alert status
   */
  async updatePriceAlert(
    alertId: number,
    isActive: boolean,
    triggered?: boolean
  ): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      const updateData: Prisma.PriceAlertsUpdateInput = { is_active: isActive }

      if (triggered !== undefined) {
        updateData.notification_sent = triggered
        if (triggered) {
          updateData.triggered_at = new Date()
        }
      }

      await this.db.price_alerts.update({
        where: { id: alertId },
        data: updateData,
      })
    }, 'updatePriceAlert')
  }

  /**
   * Get analytics insights
   */
  async getAnalyticsInsights(
    startDate?: Date,
    endDate?: Date
  ): Promise<AnalyticsInsights> {
    return this.executeWithErrorHandling(async () => {
      const dateFilter = this.buildDateFilter(startDate, endDate)

      const [
        popularSearchTerms,
        topViewedLaptops,
        searchTrends,
        userEngagement,
        priceAlertStats,
      ] = await Promise.all([
        this.getPopularSearchTerms(dateFilter),
        this.getTopViewedLaptops(dateFilter),
        this.getSearchTrends(dateFilter),
        this.getUserEngagement(dateFilter),
        this.getPriceAlertStats(dateFilter),
      ])

      return {
        popularSearchTerms,
        topViewedLaptops,
        searchTrends,
        userEngagement,
        priceAlertStats,
      }
    }, 'getAnalyticsInsights')
  }

  /**
   * Get popular filters usage
   */
  async getPopularFilters(
    startDate?: Date,
    endDate?: Date
  ): Promise<PopularFilters> {
    return this.executeWithErrorHandling(async () => {
      const dateFilter = this.buildDateFilter(startDate, endDate)

      const searchAnalytics = await this.db.search_analytics.findMany({
        where: {
          searched_at: dateFilter,
          filters_applied: { not: null },
        },
        select: { filters_applied: true },
      })

      const filterUsage = {
        brands: new Map<string, number>(),
        priceRanges: new Map<string, number>(),
        ramSizes: new Map<string, number>(),
        storageSizes: new Map<string, number>(),
      }

      searchAnalytics.forEach(search => {
        const filters = search.filters_applied as Record<string, any>
        
        if (filters.brands) {
          filters.brands.forEach((brand: string) => {
            filterUsage.brands.set(brand, (filterUsage.brands.get(brand) || 0) + 1)
          })
        }

        if (filters.priceRange) {
          const range = `${filters.priceRange.min}-${filters.priceRange.max}`
          filterUsage.priceRanges.set(range, (filterUsage.priceRanges.get(range) || 0) + 1)
        }

        if (filters.ramRange) {
          const range = `${filters.ramRange.min}-${filters.ramRange.max}GB`
          filterUsage.ramSizes.set(range, (filterUsage.ramSizes.get(range) || 0) + 1)
        }

        if (filters.storageRange) {
          const range = `${filters.storageRange.min}-${filters.storageRange.max}GB`
          filterUsage.storageSizes.set(range, (filterUsage.storageSizes.get(range) || 0) + 1)
        }
      })

      return {
        brands: Array.from(filterUsage.brands.entries())
          .map(([name, usage]) => ({ name, usage }))
          .sort((a, b) => b.usage - a.usage)
          .slice(0, 10),
        priceRanges: Array.from(filterUsage.priceRanges.entries())
          .map(([range, usage]) => ({ range, usage }))
          .sort((a, b) => b.usage - a.usage)
          .slice(0, 10),
        ramSizes: Array.from(filterUsage.ramSizes.entries())
          .map(([size, usage]) => ({ size, usage }))
          .sort((a, b) => b.usage - a.usage)
          .slice(0, 10),
        storageSizes: Array.from(filterUsage.storageSizes.entries())
          .map(([size, usage]) => ({ size, usage }))
          .sort((a, b) => b.usage - a.usage)
          .slice(0, 10),
      }
    }, 'getPopularFilters')
  }

  /**
   * Get user behavior patterns
   */
  async getUserBehaviorPatterns(userId?: string): Promise<{
    searchPatterns: Array<{ hour: number; searches: number }>
    sessionDuration: number
    averageResultsViewed: number
    conversionRate: number
  }> {
    return this.executeWithErrorHandling(async () => {
      const userFilter = userId ? { user_id: userId } : {}

      const [searchPatterns, sessionStats] = await Promise.all([
        this.getSearchPatternsByHour(userFilter),
        this.getSessionStatistics(userFilter),
      ])

      return {
        searchPatterns,
        sessionDuration: sessionStats.averageDuration,
        averageResultsViewed: sessionStats.averageResultsViewed,
        conversionRate: sessionStats.conversionRate,
      }
    }, 'getUserBehaviorPatterns')
  }

  // Private helper methods

  private buildDateFilter(startDate?: Date, endDate?: Date): Prisma.DateTimeFilter | undefined {
    if (!startDate && !endDate) return undefined

    const filter: Prisma.DateTimeFilter = {}
    if (startDate) filter.gte = startDate
    if (endDate) filter.lte = endDate

    return filter
  }

  private async getPopularSearchTerms(dateFilter: Prisma.DateTimeFilter | undefined): Promise<Array<{ term: string; count: number }>> {
    const searches = await this.db.search_analytics.findMany({
      where: {
        searched_at: dateFilter,
        search_query: { not: null },
      },
      select: { search_query: true },
    })

    const termCounts = new Map<string, number>()
    
    searches.forEach(search => {
      if (search.search_query) {
        const terms = search.search_query.toLowerCase().split(/\s+/)
        terms.forEach(term => {
          if (term.length > 2) { // Ignore very short terms
            termCounts.set(term, (termCounts.get(term) || 0) + 1)
          }
        })
      }
    })

    return Array.from(termCounts.entries())
      .map(([term, count]) => ({ term, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 20)
  }

  private async getTopViewedLaptops(dateFilter: Prisma.DateTimeFilter | undefined): Promise<Array<{ laptopId: number; model: string; brand: string; views: number }>> {
    const views = await this.db.laptop_views.groupBy({
      by: ['laptop_id'],
      where: { viewed_at: dateFilter },
      _count: { id: true },
      orderBy: { _count: { id: 'desc' } },
      take: 10,
    })

    const laptopIds = views.map(v => v.laptop_id)
    const laptops = await this.db.laptops.findMany({
      where: { id: { in: laptopIds } },
      include: { brands: true },
    })

    const laptopMap = new Map(laptops.map(laptop => [laptop.id, laptop]))

    return views.map(view => {
      const laptop = laptopMap.get(view.laptop_id)
      return {
        laptopId: view.laptop_id,
        model: laptop?.model_name || 'Unknown',
        brand: laptop?.brands?.name || 'Unknown',
        views: view._count.id,
      }
    })
  }

  private async getSearchTrends(dateFilter: Prisma.DateTimeFilter | undefined): Promise<Array<{ date: string; searches: number }>> {
    const searches = await this.db.search_analytics.groupBy({
      by: ['searched_at'],
      where: { searched_at: dateFilter },
      _count: { id: true },
      orderBy: { searched_at: 'asc' },
    })

    // Group by date (day)
    const dailyCounts = new Map<string, number>()
    
    searches.forEach(search => {
      const date = search.searched_at.toISOString().split('T')[0]
      dailyCounts.set(date, (dailyCounts.get(date) || 0) + search._count.id)
    })

    return Array.from(dailyCounts.entries())
      .map(([date, searches]) => ({ date, searches }))
      .sort((a, b) => a.date.localeCompare(b.date))
  }

  private async getUserEngagement(dateFilter: Prisma.DateTimeFilter | undefined): Promise<{
    totalSearches: number
    totalViews: number
    averageSessionDuration: number
    bounceRate: number
  }> {
    const [searchCount, viewCount] = await Promise.all([
      this.db.search_analytics.count({
        where: { searched_at: dateFilter },
      }),
      this.db.laptop_views.count({
        where: { viewed_at: dateFilter },
      }),
    ])

    // Calculate session metrics (simplified)
    const sessions = await this.db.search_analytics.groupBy({
      by: ['session_id'],
      where: {
        searched_at: dateFilter,
        session_id: { not: null },
      },
      _count: { id: true },
    })

    const totalSessions = sessions.length
    const bounceRate = totalSessions > 0 
      ? sessions.filter(s => s._count.id === 1).length / totalSessions 
      : 0

    return {
      totalSearches: searchCount,
      totalViews: viewCount,
      averageSessionDuration: 300, // Placeholder - would need session tracking
      bounceRate: Math.round(bounceRate * 100) / 100,
    }
  }

  private async getPriceAlertStats(dateFilter: Prisma.DateTimeFilter | undefined): Promise<{
    totalAlerts: number
    activeAlerts: number
    triggeredAlerts: number
    averageTargetPrice: number
  }> {
    const [total, active, triggered, avgPrice] = await Promise.all([
      this.db.price_alerts.count({
        where: { created_at: dateFilter },
      }),
      this.db.price_alerts.count({
        where: {
          created_at: dateFilter,
          is_active: true,
        },
      }),
      this.db.price_alerts.count({
        where: {
          created_at: dateFilter,
          notification_sent: true,
        },
      }),
      this.db.price_alerts.aggregate({
        where: { created_at: dateFilter },
        _avg: { target_price: true },
      }),
    ])

    return {
      totalAlerts: total,
      activeAlerts: active,
      triggeredAlerts: triggered,
      averageTargetPrice: Number(avgPrice._avg.target_price) || 0,
    }
  }

  private async getSearchPatternsByHour(userFilter: Prisma.SearchAnalyticsWhereInput): Promise<Array<{ hour: number; searches: number }>> {
    const searches = await this.db.search_analytics.findMany({
      where: userFilter,
      select: { searched_at: true },
    })

    const hourCounts = new Array(24).fill(0)
    
    searches.forEach(search => {
      const hour = search.searched_at.getHours()
      hourCounts[hour]++
    })

    return hourCounts.map((searches, hour) => ({ hour, searches }))
  }

  private async getSessionStatistics(userFilter: Prisma.SearchAnalyticsWhereInput): Promise<{
    averageDuration: number
    averageResultsViewed: number
    conversionRate: number
  }> {
    // Simplified session statistics
    const searches = await this.db.search_analytics.findMany({
      where: userFilter,
      select: { results_count: true },
    })

    const averageResultsViewed = searches.length > 0
      ? searches.reduce((sum, s) => sum + s.results_count, 0) / searches.length
      : 0

    return {
      averageDuration: 300, // Placeholder
      averageResultsViewed: Math.round(averageResultsViewed * 100) / 100,
      conversionRate: 0.15, // Placeholder
    }
  }
}
