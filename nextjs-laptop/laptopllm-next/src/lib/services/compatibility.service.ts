// LLM Compatibility service for calculating and managing compatibility scores

import { BaseService } from './base.service'
import type {
  LLMModel,
  LLMCompatibilityScore,
  PaginationParams,
  SearchResults,
} from '@/shared/types'

export interface CompatibilityCalculationInput {
  ramGb: number
  cpuCores: number
  cpuArchitecture: string
  gpuVramGb?: number
  storageGb: number
  storageType: string
  cpuBaseClockGhz?: number
  cpuBoostClockGhz?: number
}

export interface CompatibilityResult {
  overallScore: number
  breakdown: {
    ramScore: number
    cpuScore: number
    gpuScore: number
    storageScore: number
  }
  canRunOffline: boolean
  estimatedTokensPerSecond?: number
  maxContextLength?: number
  estimatedMemoryUsageGb?: number
  qualitativeAssessment: string
  recommendations: string[]
}

export interface LLMModelRequirements {
  minRamGb: number
  recommendedRamGb: number
  minCpuCores: number
  recommendedCpuCores: number
  minStorageGb: number
  requiresGpu: boolean
  minGpuVramGb?: number
  supportedArchitectures: string[]
  parametersInBillions: number
}

export class CompatibilityService extends BaseService {
  /**
   * Calculate compatibility score between laptop specs and LLM model
   */
  async calculateCompatibility(
    laptopSpecs: CompatibilityCalculationInput,
    llmModelId?: number
  ): Promise<CompatibilityResult> {
    return this.executeWithErrorHandling(async () => {
      let llmModel: LLMModel | null = null
      
      if (llmModelId) {
        llmModel = await this.db.llm_models.findUnique({
          where: { id: llmModelId },
        })
        
        if (!llmModel) {
          throw new Error(`LLM model with ID ${llmModelId} not found`)
        }
      }

      const requirements = llmModel 
        ? this.extractModelRequirements(llmModel)
        : this.getDefaultRequirements()

      const breakdown = {
        ramScore: this.calculateRamScore(laptopSpecs.ramGb, requirements),
        cpuScore: this.calculateCpuScore(laptopSpecs, requirements),
        gpuScore: this.calculateGpuScore(laptopSpecs.gpuVramGb, requirements),
        storageScore: this.calculateStorageScore(laptopSpecs, requirements),
      }

      const overallScore = this.calculateOverallScore(breakdown)
      const canRunOffline = this.determineOfflineCapability(laptopSpecs, requirements)
      
      return {
        overallScore,
        breakdown,
        canRunOffline,
        estimatedTokensPerSecond: this.estimateTokensPerSecond(laptopSpecs, requirements),
        maxContextLength: this.estimateMaxContextLength(laptopSpecs, requirements),
        estimatedMemoryUsageGb: this.estimateMemoryUsage(requirements),
        qualitativeAssessment: this.generateQualitativeAssessment(overallScore),
        recommendations: this.generateRecommendations(laptopSpecs, requirements, breakdown),
      }
    }, 'calculateCompatibility')
  }

  /**
   * Get compatibility scores for a laptop with all LLM models
   */
  async getLaptopCompatibility(
    laptopId: number,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<SearchResults<LLMCompatibilityScore & { llmModel: LLMModel }>> {
    this.validatePagination(pagination)

    return this.executeWithErrorHandling(async () => {
      const [compatibility, total] = await Promise.all([
        this.db.laptop_llm_compatibility.findMany({
          where: { laptop_id: laptopId },
          skip: this.getOffset(pagination.page, pagination.limit),
          take: pagination.limit,
          include: {
            llm_models: true,
          },
          orderBy: { score: 'desc' },
        }),
        this.db.laptop_llm_compatibility.count({
          where: { laptop_id: laptopId },
        }),
      ])

      const results = compatibility.map(comp => ({
        laptopId: comp.laptop_id,
        llmId: comp.llm_id,
        overallScore: comp.score || 0,
        canRunOffline: comp.can_run_offline || false,
        estimatedTokensPerSecond: comp.estimated_tokens_per_second,
        maxContextLength: comp.max_context_length,
        recommendedBatchSize: comp.recommended_batch_size,
        estimatedMemoryUsageGb: Number(comp.estimated_memory_usage_gb),
        qualitativeAssessment: comp.qualitative_assessment,
        breakdown: {
          cpuScore: 0, // These would need to be stored separately or calculated
          ramScore: 0,
          gpuScore: 0,
          storageScore: 0,
        },
        llmModel: comp.llm_models as LLMModel,
      }))

      return this.createPaginatedResults(results, total, pagination)
    }, 'getLaptopCompatibility')
  }

  /**
   * Store calculated compatibility score
   */
  async storeCompatibilityScore(
    laptopId: number,
    llmId: number,
    result: CompatibilityResult
  ): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      await this.db.laptop_llm_compatibility.upsert({
        where: {
          laptop_id_llm_id: {
            laptop_id: laptopId,
            llm_id: llmId,
          },
        },
        update: {
          score: result.overallScore,
          can_run_offline: result.canRunOffline,
          estimated_tokens_per_second: result.estimatedTokensPerSecond,
          max_context_length: result.maxContextLength,
          estimated_memory_usage_gb: result.estimatedMemoryUsageGb,
          qualitative_assessment: result.qualitativeAssessment,
        },
        create: {
          laptop_id: laptopId,
          llm_id: llmId,
          score: result.overallScore,
          can_run_offline: result.canRunOffline,
          estimated_tokens_per_second: result.estimatedTokensPerSecond,
          max_context_length: result.maxContextLength,
          estimated_memory_usage_gb: result.estimatedMemoryUsageGb,
          qualitative_assessment: result.qualitativeAssessment,
        },
      })
    }, 'storeCompatibilityScore')
  }

  /**
   * Batch calculate compatibility for multiple laptops and models
   */
  async batchCalculateCompatibility(
    laptopIds: number[],
    llmModelIds?: number[]
  ): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      const laptops = await this.db.laptops.findMany({
        where: { id: { in: laptopIds } },
        include: {
          laptop_cpus: {
            include: { cpus: true },
          },
          laptop_gpus: {
            include: { gpus: true },
          },
          laptop_ram: {
            include: { ram_configurations: true },
          },
          laptop_storage: {
            include: { storage_devices: true },
          },
        },
      })

      const llmModels = await this.db.llm_models.findMany({
        where: llmModelIds ? { id: { in: llmModelIds } } : undefined,
      })

      const calculations: Array<{
        laptopId: number
        llmId: number
        result: CompatibilityResult
      }> = []

      for (const laptop of laptops) {
        const laptopSpecs = this.extractLaptopSpecs(laptop)
        
        for (const llmModel of llmModels) {
          const result = await this.calculateCompatibility(laptopSpecs, llmModel.id)
          calculations.push({
            laptopId: laptop.id,
            llmId: llmModel.id,
            result,
          })
        }
      }

      // Store results in batches
      await this.executeTransaction(async (tx) => {
        for (const calc of calculations) {
          await tx.laptop_llm_compatibility.upsert({
            where: {
              laptop_id_llm_id: {
                laptop_id: calc.laptopId,
                llm_id: calc.llmId,
              },
            },
            update: {
              score: calc.result.overallScore,
              can_run_offline: calc.result.canRunOffline,
              estimated_tokens_per_second: calc.result.estimatedTokensPerSecond,
              max_context_length: calc.result.maxContextLength,
              estimated_memory_usage_gb: calc.result.estimatedMemoryUsageGb,
              qualitative_assessment: calc.result.qualitativeAssessment,
            },
            create: {
              laptop_id: calc.laptopId,
              llm_id: calc.llmId,
              score: calc.result.overallScore,
              can_run_offline: calc.result.canRunOffline,
              estimated_tokens_per_second: calc.result.estimatedTokensPerSecond,
              max_context_length: calc.result.maxContextLength,
              estimated_memory_usage_gb: calc.result.estimatedMemoryUsageGb,
              qualitative_assessment: calc.result.qualitativeAssessment,
            },
          })
        }
      })
    }, 'batchCalculateCompatibility')
  }

  /**
   * Get top compatible laptops for a specific LLM model
   */
  async getTopCompatibleLaptops(
    llmModelId: number,
    limit: number = 10
  ): Promise<Array<{ laptop: { laptops: Record<string, unknown> }; compatibilityScore: number }>> {
    return this.executeWithErrorHandling(async () => {
      const results = await this.db.laptop_llm_compatibility.findMany({
        where: { llm_id: llmModelId },
        orderBy: { score: 'desc' },
        take: limit,
        include: {
          laptops: {
            include: {
              brands: true,
              laptop_listings: {
                orderBy: { price: 'asc' },
                take: 1,
                include: { sources: true },
              },
            },
          },
        },
      })

      return results.map(result => ({
        laptop: result.laptops,
        compatibilityScore: result.score || 0,
      }))
    }, 'getTopCompatibleLaptops')
  }

  // Private helper methods

  private extractModelRequirements(llmModel: LLMModel): LLMModelRequirements {
    const requirements = llmModel.requirements || {}
    
    return {
      minRamGb: requirements.minRamGb || llmModel.min_ram_gb || 8,
      recommendedRamGb: requirements.recommendedRamGb || (llmModel.min_ram_gb || 8) * 2,
      minCpuCores: requirements.minCpuCores || 4,
      recommendedCpuCores: requirements.recommendedCpuCores || 8,
      minStorageGb: requirements.minStorageGb || 50,
      requiresGpu: requirements.requiresGpu || llmModel.requires_gpu || false,
      minGpuVramGb: requirements.minGpuVramGb || llmModel.min_vram_gb,
      supportedArchitectures: requirements.supportedArchitectures || ['x86_64', 'ARM64'],
      parametersInBillions: Number(llmModel.parameters_billions) || 7,
    }
  }

  private getDefaultRequirements(): LLMModelRequirements {
    return {
      minRamGb: 8,
      recommendedRamGb: 16,
      minCpuCores: 4,
      recommendedCpuCores: 8,
      minStorageGb: 50,
      requiresGpu: false,
      supportedArchitectures: ['x86_64', 'ARM64'],
      parametersInBillions: 7,
    }
  }

  private calculateRamScore(ramGb: number, requirements: LLMModelRequirements): number {
    if (ramGb < requirements.minRamGb) return 0
    if (ramGb >= requirements.recommendedRamGb) return 100
    
    const ratio = (ramGb - requirements.minRamGb) / (requirements.recommendedRamGb - requirements.minRamGb)
    return Math.round(50 + (ratio * 50))
  }

  private calculateCpuScore(
    specs: CompatibilityCalculationInput,
    requirements: LLMModelRequirements
  ): number {
    let score = 0
    
    // Core count score (60% weight)
    if (specs.cpuCores >= requirements.recommendedCpuCores) {
      score += 60
    } else if (specs.cpuCores >= requirements.minCpuCores) {
      const ratio = (specs.cpuCores - requirements.minCpuCores) / 
                   (requirements.recommendedCpuCores - requirements.minCpuCores)
      score += 30 + (ratio * 30)
    }
    
    // Architecture score (20% weight)
    if (requirements.supportedArchitectures.includes(specs.cpuArchitecture)) {
      score += 20
    }
    
    // Clock speed score (20% weight)
    if (specs.cpuBaseClockGhz) {
      if (specs.cpuBaseClockGhz >= 3.0) score += 20
      else if (specs.cpuBaseClockGhz >= 2.5) score += 15
      else if (specs.cpuBaseClockGhz >= 2.0) score += 10
      else score += 5
    }
    
    return Math.min(100, Math.round(score))
  }

  private calculateGpuScore(gpuVramGb: number | undefined, requirements: LLMModelRequirements): number {
    if (!requirements.requiresGpu) return 100
    if (!gpuVramGb) return 0
    if (!requirements.minGpuVramGb) return 50
    
    if (gpuVramGb >= requirements.minGpuVramGb * 2) return 100
    if (gpuVramGb >= requirements.minGpuVramGb) return 75
    if (gpuVramGb >= requirements.minGpuVramGb * 0.75) return 50
    
    return 25
  }

  private calculateStorageScore(
    specs: CompatibilityCalculationInput,
    requirements: LLMModelRequirements
  ): number {
    let score = 0
    
    // Capacity score (70% weight)
    if (specs.storageGb >= requirements.minStorageGb * 4) {
      score += 70
    } else if (specs.storageGb >= requirements.minStorageGb * 2) {
      score += 60
    } else if (specs.storageGb >= requirements.minStorageGb) {
      score += 50
    } else {
      const ratio = specs.storageGb / requirements.minStorageGb
      score += ratio * 50
    }
    
    // Storage type score (30% weight)
    if (specs.storageType.toLowerCase().includes('ssd') || 
        specs.storageType.toLowerCase().includes('nvme')) {
      score += 30
    } else if (specs.storageType.toLowerCase().includes('emmc')) {
      score += 15
    } else {
      score += 10
    }
    
    return Math.min(100, Math.round(score))
  }

  private calculateOverallScore(breakdown: CompatibilityResult['breakdown']): number {
    // Weighted average: RAM (40%), CPU (30%), Storage (20%), GPU (10%)
    const weights = { ram: 0.4, cpu: 0.3, storage: 0.2, gpu: 0.1 }
    
    return Math.round(
      breakdown.ramScore * weights.ram +
      breakdown.cpuScore * weights.cpu +
      breakdown.storageScore * weights.storage +
      breakdown.gpuScore * weights.gpu
    )
  }

  private determineOfflineCapability(
    specs: CompatibilityCalculationInput,
    requirements: LLMModelRequirements
  ): boolean {
    return specs.ramGb >= requirements.minRamGb &&
           specs.cpuCores >= requirements.minCpuCores &&
           specs.storageGb >= requirements.minStorageGb &&
           (!requirements.requiresGpu || (specs.gpuVramGb && specs.gpuVramGb >= (requirements.minGpuVramGb || 0)))
  }

  private estimateTokensPerSecond(
    specs: CompatibilityCalculationInput,
    requirements: LLMModelRequirements
  ): number {
    // Simplified estimation based on specs
    let baseRate = 10 // Base tokens per second
    
    // CPU contribution
    baseRate *= (specs.cpuCores / 4) * ((specs.cpuBaseClockGhz || 2.5) / 2.5)
    
    // RAM contribution
    if (specs.ramGb >= requirements.recommendedRamGb) {
      baseRate *= 1.5
    } else if (specs.ramGb >= requirements.minRamGb) {
      baseRate *= 1.2
    }
    
    // GPU contribution
    if (specs.gpuVramGb && requirements.requiresGpu) {
      baseRate *= 2.0
    }
    
    // Model size penalty
    const sizeMultiplier = Math.max(0.1, 1 - (requirements.parametersInBillions - 7) * 0.1)
    baseRate *= sizeMultiplier
    
    return Math.round(baseRate)
  }

  private estimateMaxContextLength(
    specs: CompatibilityCalculationInput,
    requirements: LLMModelRequirements
  ): number {
    // Base context length
    let contextLength = 2048
    
    // Scale with available RAM
    const availableRam = specs.ramGb - requirements.minRamGb
    if (availableRam > 0) {
      contextLength += availableRam * 512
    }
    
    return Math.min(32768, contextLength)
  }

  private estimateMemoryUsage(requirements: LLMModelRequirements): number {
    // Rough estimation: parameters * 2 bytes per parameter (for 16-bit)
    return requirements.parametersInBillions * 2
  }

  private generateQualitativeAssessment(score: number): string {
    if (score >= 90) return 'Excellent - Optimal performance expected'
    if (score >= 75) return 'Good - Strong performance with minor limitations'
    if (score >= 60) return 'Fair - Adequate performance with some constraints'
    if (score >= 40) return 'Poor - Limited performance, consider upgrades'
    return 'Inadequate - Significant performance issues expected'
  }

  private generateRecommendations(
    specs: CompatibilityCalculationInput,
    requirements: LLMModelRequirements,
    breakdown: CompatibilityResult['breakdown']
  ): string[] {
    const recommendations: string[] = []
    
    if (breakdown.ramScore < 75) {
      recommendations.push(`Consider upgrading RAM to at least ${requirements.recommendedRamGb}GB`)
    }
    
    if (breakdown.cpuScore < 75) {
      recommendations.push(`A CPU with ${requirements.recommendedCpuCores}+ cores would improve performance`)
    }
    
    if (breakdown.storageScore < 75) {
      recommendations.push('An SSD with more capacity would improve model loading times')
    }
    
    if (breakdown.gpuScore < 75 && requirements.requiresGpu) {
      recommendations.push(`A dedicated GPU with ${requirements.minGpuVramGb}GB+ VRAM is recommended`)
    }
    
    if (recommendations.length === 0) {
      recommendations.push('Your system meets all requirements for optimal performance')
    }
    
    return recommendations
  }

  private extractLaptopSpecs(laptop: { laptop_cpus: { cpus: { cores: number; cpu_architectures: { name: string }; base_clock_ghz: number; boost_clock_ghz: number } }[]; laptop_gpus: { gpus: { vram_gb: number } }[]; laptop_ram: { ram_configurations: { capacity_gb: number } }[]; laptop_storage: { storage_devices: { capacity_gb: number; type: string } }[] }): CompatibilityCalculationInput {
    // Extract specs from laptop data structure
    const cpu = laptop.laptop_cpus?.[0]?.cpus
    const gpu = laptop.laptop_gpus?.[0]?.gpus
    const ram = laptop.laptop_ram?.[0]?.ram_configurations
    const storage = laptop.laptop_storage?.[0]?.storage_devices

    return {
      ramGb: ram?.capacity_gb || 8,
      cpuCores: cpu?.cores || 4,
      cpuArchitecture: cpu?.cpu_architectures?.name || 'x86_64',
      gpuVramGb: gpu?.vram_gb,
      storageGb: storage?.capacity_gb || 256,
      storageType: storage?.type || 'SSD',
      cpuBaseClockGhz: Number(cpu?.base_clock_ghz) || 2.5,
      cpuBoostClockGhz: Number(cpu?.boost_clock_ghz),
    }
  }
}
