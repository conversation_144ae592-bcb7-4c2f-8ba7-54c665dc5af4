// Tests for LaptopService

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { LaptopService } from '../laptop.service'
import type { CreateLaptopData, LaptopFilters } from '../laptop.service'

type MockDb = {
  laptops: {
    findMany: vi.Mock
    findUnique: vi.Mock
    create: vi.Mock
    update: vi.Mock
    delete: vi.Mock
    count: vi.Mock
    groupBy: vi.Mock
    aggregate: vi.Mock
  }
  brands: {
    findMany: vi.Mock
  }
}

// Mock Prisma
vi.mock('../../prisma', () => ({
  prisma: {
    laptops: {
      findMany: vi.fn(),
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn(),
      groupBy: vi.fn(),
      aggregate: vi.fn(),
    },
    brands: {
      findMany: vi.fn(),
    },
  },
}))

describe('LaptopService', () => {
  let laptopService: LaptopService

  beforeEach(() => {
    laptopService = new LaptopService()
    vi.clearAllMocks()
  })

  describe('getLaptops', () => {
    it('should return paginated laptop results', async () => {
      const mockLaptops = [
        {
          id: 1,
          model_name: 'Test Laptop',
          brand_id: 1,
          brands: { name: 'Test Brand' },
          laptop_cpus: [],
          laptop_gpus: [],
          laptop_ram: [],
          laptop_storage: [],
          displays: [],
          physical_specs: null,
          laptop_llm_compatibility: [],
        },
      ]

      const mockCount = 1

      // Mock the database calls
      const mockDb = laptopService['db'] as MockDb
      mockDb.laptops.findMany.mockResolvedValue(mockLaptops)
      mockDb.laptops.count.mockResolvedValue(mockCount)

      const result = await laptopService.getLaptops(
        {},
        { page: 1, limit: 20 }
      )

      expect(result.data).toHaveLength(1)
      expect(result.pagination.total).toBe(1)
      expect(result.pagination.page).toBe(1)
      expect(result.pagination.limit).toBe(20)
      expect(mockDb.laptops.findMany).toHaveBeenCalledWith({
        where: {},
        orderBy: undefined,
        skip: 0,
        take: 20,
        include: expect.any(Object),
      })
    })

    it('should apply filters correctly', async () => {
      const filters: LaptopFilters = {
        brandIds: [1, 2],
        minPrice: 500,
        maxPrice: 2000,
        isAvailable: true,
      }

      const mockDb = laptopService['db'] as MockDb
      mockDb.laptops.findMany.mockResolvedValue([])
      mockDb.laptops.count.mockResolvedValue(0)

      await laptopService.getLaptops(filters, { page: 1, limit: 20 })

      expect(mockDb.laptops.findMany).toHaveBeenCalledWith({
        where: {
          brand_id: { in: [1, 2] },
          is_available: true,
          msrp: { gte: 500, lte: 2000 },
        },
        orderBy: undefined,
        skip: 0,
        take: 20,
        include: expect.any(Object),
      })
    })

    it('should validate pagination parameters', async () => {
      await expect(
        laptopService.getLaptops({}, { page: 0, limit: 20 })
      ).rejects.toThrow('Page must be greater than 0')

      await expect(
        laptopService.getLaptops({}, { page: 1, limit: 101 })
      ).rejects.toThrow('Limit must be between 1 and 100')
    })
  })

  describe('getLaptopById', () => {
    it('should return laptop with full details', async () => {
      const mockLaptop = {
        id: 1,
        model_name: 'Test Laptop',
        brand_id: 1,
        brands: { name: 'Test Brand' },
        laptop_cpus: [],
        laptop_gpus: [],
        laptop_ram: [],
        laptop_storage: [],
        displays: [],
        physical_specs: null,
        laptop_listings: [],
        laptop_llm_compatibility: [],
        laptop_scores: null,
      }

      const mockDb = laptopService['db'] as MockDb
      mockDb.laptops.findUnique.mockResolvedValue(mockLaptop)

      const result = await laptopService.getLaptopById(1)

      expect(result).toBeDefined()
      expect(result?.laptop.id).toBe(1)
      expect(result?.laptop.model_name).toBe('Test Laptop')
      expect(mockDb.laptops.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
        include: expect.any(Object),
      })
    })

    it('should return null for non-existent laptop', async () => {
      const mockDb = laptopService['db'] as MockDb
      mockDb.laptops.findUnique.mockResolvedValue(null)

      const result = await laptopService.getLaptopById(999)

      expect(result).toBeNull()
    })
  })

  describe('searchLaptops', () => {
    it('should search laptops by query', async () => {
      const mockLaptops = [
        {
          id: 1,
          model_name: 'Gaming Laptop',
          brand_id: 1,
          brands: { name: 'Test Brand' },
          laptop_cpus: [],
          laptop_gpus: [],
          laptop_llm_compatibility: [],
        },
      ]

      const mockDb = laptopService['db'] as MockDb
      mockDb.laptops.findMany.mockResolvedValue(mockLaptops)
      mockDb.laptops.count.mockResolvedValue(1)

      const result = await laptopService.searchLaptops(
        'gaming',
        {},
        { page: 1, limit: 20 }
      )

      expect(result.data).toHaveLength(1)
      expect(result.data[0].laptop.model_name).toBe('Gaming Laptop')
      expect(mockDb.laptops.findMany).toHaveBeenCalledWith({
        where: expect.objectContaining({
          OR: expect.any(Array),
        }),
        orderBy: undefined,
        skip: 0,
        take: 20,
        include: expect.any(Object),
      })
    })

    it('should calculate relevance scores', async () => {
      const mockLaptops = [
        {
          id: 1,
          model_name: 'Gaming Laptop Pro',
          brands: { name: 'Test Brand' },
          description: 'High-performance gaming laptop',
          laptop_cpus: [],
          laptop_gpus: [],
          laptop_llm_compatibility: [],
        },
      ]

      const mockDb = laptopService['db'] as MockDb
      mockDb.laptops.findMany.mockResolvedValue(mockLaptops)
      mockDb.laptops.count.mockResolvedValue(1)

      const result = await laptopService.searchLaptops(
        'gaming laptop',
        {},
        { page: 1, limit: 20 }
      )

      expect(result.data[0].relevanceScore).toBeGreaterThan(0)
    })
  })

  describe('createLaptop', () => {
    it('should create a new laptop', async () => {
      const laptopData: CreateLaptopData = {
        model_name: 'New Laptop',
        brand_id: 1,
        description: 'A new laptop',
        msrp: 1500,
      }

      const mockCreatedLaptop = {
        id: 1,
        ...laptopData,
        brands: { name: 'Test Brand' },
        created_at: new Date(),
        updated_at: new Date(),
      }

      const mockDb = laptopService['db'] as MockDb
      mockDb.laptops.create.mockResolvedValue(mockCreatedLaptop)

      const result = await laptopService.createLaptop(laptopData)

      expect(result.id).toBe(1)
      expect(result.model_name).toBe('New Laptop')
      expect(mockDb.laptops.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          model_name: 'New Laptop',
          brand_id: 1,
          description: 'A new laptop',
          msrp: 1500,
          created_at: expect.any(Date),
          updated_at: expect.any(Date),
        }),
        include: { brands: true },
      })
    })
  })

  describe('updateLaptop', () => {
    it('should update an existing laptop', async () => {
      const updateData = {
        id: 1,
        model_name: 'Updated Laptop',
        description: 'Updated description',
      }

      const mockUpdatedLaptop = {
        id: 1,
        model_name: 'Updated Laptop',
        brand_id: 1,
        description: 'Updated description',
        brands: { name: 'Test Brand' },
        updated_at: new Date(),
      }

      const mockDb = laptopService['db'] as MockDb
      mockDb.laptops.update.mockResolvedValue(mockUpdatedLaptop)

      const result = await laptopService.updateLaptop(updateData)

      expect(result.model_name).toBe('Updated Laptop')
      expect(mockDb.laptops.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: expect.objectContaining({
          model_name: 'Updated Laptop',
          description: 'Updated description',
          updated_at: expect.any(Date),
        }),
        include: { brands: true },
      })
    })
  })

  describe('deleteLaptop', () => {
    it('should delete a laptop', async () => {
      const mockDb = laptopService['db'] as MockDb
      mockDb.laptops.delete.mockResolvedValue({ id: 1 })

      await laptopService.deleteLaptop(1)

      expect(mockDb.laptops.delete).toHaveBeenCalledWith({
        where: { id: 1 },
      })
    })
  })

  describe('getLaptopStats', () => {
    it('should return laptop statistics', async () => {
      const mockDb = laptopService['db'] as MockDb
      mockDb.laptops.count.mockResolvedValue(100)
      mockDb.laptops.groupBy.mockResolvedValue([
        { brand_id: 1, _count: { id: 50 } },
        { brand_id: 2, _count: { id: 30 } },
      ])
      mockDb.laptops.aggregate.mockResolvedValue({
        _min: { msrp: 500 },
        _max: { msrp: 3000 },
        _avg: { msrp: 1500 },
      })
      mockDb.brands.findMany.mockResolvedValue([
        { id: 1, name: 'Brand A' },
        { id: 2, name: 'Brand B' },
      ])

      const result = await laptopService.getLaptopStats()

      expect(result.total).toBe(100)
      expect(result.byBrand).toHaveLength(2)
      expect(result.byBrand[0].brand).toBe('Brand A')
      expect(result.byBrand[0].count).toBe(50)
      expect(result.priceRange.min).toBe(500)
      expect(result.priceRange.max).toBe(3000)
      expect(result.averagePrice).toBe(1500)
    })
  })

  describe('private methods', () => {
    it('should calculate average compatibility score', () => {
      const compatibility = [
        { score: 80 },
        { score: 90 },
        { score: 70 },
      ]

      const avgScore = laptopService['calculateAverageCompatibilityScore'](compatibility)
      expect(avgScore).toBe(80)
    })

    it('should return undefined for empty compatibility array', () => {
      const avgScore = laptopService['calculateAverageCompatibilityScore']([])
      expect(avgScore).toBeUndefined()
    })

    it('should calculate relevance score for search terms', () => {
      const laptop = {
        model_name: 'Gaming Laptop Pro',
        brands: { name: 'ASUS' },
        description: 'High-performance gaming laptop',
        laptop_cpus: [{ cpus: { model: 'Intel i7' } }],
        laptop_gpus: [{ gpus: { model: 'RTX 4080' } }],
      }

      const searchTerms = ['gaming', 'laptop', 'intel']
      const score = laptopService['calculateRelevanceScore'](laptop, searchTerms)
      
      expect(score).toBeGreaterThan(0)
      // Score should include matches for 'gaming' and 'laptop' in model name, and 'intel' in CPU
      expect(score).toBeGreaterThanOrEqual(20)
    })
  })
})
