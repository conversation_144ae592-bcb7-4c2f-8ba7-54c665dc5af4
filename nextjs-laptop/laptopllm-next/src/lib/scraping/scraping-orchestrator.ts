// Scraping orchestrator that coordinates Firecrawl and Puppeteer services

import { Prisma, Laptops } from '@prisma/client'
import { BaseService } from '../services/base.service'
import { FirecrawlService, type LaptopScrapingTarget, type LaptopData } from './firecrawl.service'
import { PuppeteerService } from './puppeteer.service'
import type { ScrapingTarget, ScrapedData } from './base-scraper'
import { LaptopDataProcessor, type ProcessedLaptopData } from '../data-processing/laptop-data-processor'
import { MetricsTracker, type ScrapingMetrics } from '../data-processing/metrics-tracker'

export interface ScrapingStrategy {
  primary: 'firecrawl' | 'puppeteer'
  fallback: 'firecrawl' | 'puppeteer' | 'none'
  retryWithFallback: boolean
}

export interface ScrapingJobConfig {
  strategy: ScrapingStrategy
  batchSize: number
  maxConcurrent: number
  delayBetweenBatches: number
  enableScreenshots: boolean
  saveToDatabase: boolean
}

export interface ScrapingJobResult {
  jobId: string
  status: 'running' | 'completed' | 'failed' | 'cancelled'
  progress: {
    total: number
    completed: number
    failed: number
    percentage: number
  }
  results: {
    successful: LaptopData[]
    failed: Array<{ url: string; error: string }>
  }
  metrics: {
    startTime: Date
    endTime?: Date
    duration?: number
    averageTimePerUrl?: number
    successRate: number
  }
}

export class ScrapingOrchestrator extends BaseService {
  private firecrawlService: FirecrawlService
  private puppeteerService: PuppeteerService
  private dataProcessor: LaptopDataProcessor
  private metricsTracker: MetricsTracker
  private activeJobs: Map<string, ScrapingJobResult> = new Map()

  constructor() {
    super()
    this.firecrawlService = new FirecrawlService()
    this.puppeteerService = new PuppeteerService()
    this.dataProcessor = new LaptopDataProcessor()
    this.metricsTracker = new MetricsTracker()
  }

  /**
   * Start a comprehensive laptop scraping job
   */
  async startLaptopScrapingJob(
    targets: LaptopScrapingTarget[],
    config: Partial<ScrapingJobConfig> = {}
  ): Promise<string> {
    const jobConfig: ScrapingJobConfig = {
      strategy: {
        primary: 'firecrawl',
        fallback: 'puppeteer',
        retryWithFallback: true,
      },
      batchSize: 10,
      maxConcurrent: 3,
      delayBetweenBatches: 2000,
      enableScreenshots: false,
      saveToDatabase: true,
      ...config,
    }

    const jobId = this.generateJobId()
    const jobResult: ScrapingJobResult = {
      jobId,
      status: 'running',
      progress: {
        total: targets.length,
        completed: 0,
        failed: 0,
        percentage: 0,
      },
      results: {
        successful: [],
        failed: [],
      },
      metrics: {
        startTime: new Date(),
        successRate: 0,
      },
    }

    this.activeJobs.set(jobId, jobResult)

    // Start job in background
    this.runScrapingJob(targets, jobConfig, jobResult, jobId).catch(error => {
      console.error(`Scraping job ${jobId} failed:`, error)
      jobResult.status = 'failed'
    })

    return jobId
  }

  /**
   * Get job status and results
   */
  getJobStatus(jobId: string): ScrapingJobResult | null {
    return this.activeJobs.get(jobId) || null
  }

  /**
   * Cancel a running job
   */
  async cancelJob(jobId: string): Promise<boolean> {
    const job = this.activeJobs.get(jobId)
    if (!job || job.status !== 'running') {
      return false
    }

    job.status = 'cancelled'
    job.metrics.endTime = new Date()
    job.metrics.duration = job.metrics.endTime.getTime() - job.metrics.startTime.getTime()

    return true
  }

  /**
   * Scrape a single laptop product with fallback strategy
   */
  async scrapeLaptopWithFallback(
    target: LaptopScrapingTarget,
    strategy: ScrapingStrategy = {
      primary: 'firecrawl',
      fallback: 'puppeteer',
      retryWithFallback: true,
    }
  ): Promise<LaptopData> {
    let lastError: Error | null = null

    // Try primary strategy
    try {
      if (strategy.primary === 'firecrawl') {
        return await this.firecrawlService.scrapeLaptopProduct(target)
      } else {
        return await this.puppeteerService.scrapeLaptopProduct(target)
      }
    } catch (error) {
      lastError = error as Error
      console.warn(`Primary scraping method (${strategy.primary}) failed for ${target.url}:`, error)
    }

    // Try fallback strategy if enabled
    if (strategy.retryWithFallback && strategy.fallback !== 'none') {
      try {
        console.log(`Trying fallback method (${strategy.fallback}) for ${target.url}`)
        
        if (strategy.fallback === 'firecrawl') {
          return await this.firecrawlService.scrapeLaptopProduct(target)
        } else {
          return await this.puppeteerService.scrapeLaptopProduct(target)
        }
      } catch (fallbackError) {
        console.error(`Fallback scraping method (${strategy.fallback}) also failed for ${target.url}:`, fallbackError)
        lastError = fallbackError as Error
      }
    }

    throw lastError || new Error('All scraping methods failed')
  }

  /**
   * Discover laptop URLs from e-commerce sites
   */
  async discoverLaptopUrls(
    sites: Array<{
      baseUrl: string
      siteType: LaptopScrapingTarget['siteType']
      searchPaths?: string[]
      maxPages?: number
    }>
  ): Promise<LaptopScrapingTarget[]> {
    const discoveredTargets: LaptopScrapingTarget[] = []

    for (const site of sites) {
      try {
        console.log(`Discovering laptop URLs from ${site.baseUrl}`)

        // Use Firecrawl to crawl and discover URLs
        const urls = await this.firecrawlService.crawlForLaptops(site.baseUrl, {
          maxPages: site.maxPages || 50,
          includePaths: site.searchPaths || ['/laptop', '/notebook', '/gaming'],
          laptopKeywords: ['laptop', 'notebook', 'gaming', 'ultrabook', 'chromebook'],
        })

        const targets: LaptopScrapingTarget[] = urls.map(url => ({
          url,
          siteType: site.siteType,
        }))

        discoveredTargets.push(...targets)
        console.log(`Discovered ${targets.length} laptop URLs from ${site.baseUrl}`)

      } catch (error) {
        console.error(`Failed to discover URLs from ${site.baseUrl}:`, error)
      }
    }

    return discoveredTargets
  }

  /**
   * Search for laptops across multiple sources
   */
  async searchLaptopsAcrossSources(
    query: string,
    options: {
      maxResultsPerSource?: number
      sources?: Array<'web' | 'amazon' | 'bestbuy' | 'newegg'>
    } = {}
  ): Promise<LaptopData[]> {
    const results: LaptopData[] = []
    const maxResults = options.maxResultsPerSource || 10
    const sources = options.sources || ['web']

    for (const source of sources) {
      try {
        let sourceResults: LaptopData[] = []

        switch (source) {
          case 'web':
            sourceResults = await this.firecrawlService.searchLaptops(query, {
              limit: maxResults,
            })
            break

          case 'amazon':
            sourceResults = await this.firecrawlService.searchLaptops(
              `${query} site:amazon.com`,
              { limit: maxResults }
            )
            break

          case 'bestbuy':
            sourceResults = await this.firecrawlService.searchLaptops(
              `${query} site:bestbuy.com`,
              { limit: maxResults }
            )
            break

          case 'newegg':
            sourceResults = await this.firecrawlService.searchLaptops(
              `${query} site:newegg.com`,
              { limit: maxResults }
            )
            break
        }

        results.push(...sourceResults)
        console.log(`Found ${sourceResults.length} laptops from ${source}`)

      } catch (error) {
        console.error(`Failed to search ${source} for "${query}":`, error)
      }
    }

    return results
  }

  /**
   * Save scraped laptop data to database with processing, validation, and metrics tracking
   */
  async saveLaptopData(laptopData: LaptopData[], sourceId?: string, jobId?: string): Promise<{
    processed: number
    successful: number
    newLaptops: number
    updatedLaptops: number
    duplicates: number
    averageConfidence: number
  }> {
    return this.executeWithErrorHandling(async () => {
      const startTime = Date.now()

      // Process and validate data
      const processedData = await this.dataProcessor.processLaptopData(laptopData)

      console.log(`Processing ${laptopData.length} raw laptops → ${processedData.length} validated laptops`)

      let newLaptops = 0
      let updatedLaptops = 0
      let successful = 0
      let duplicates = 0
      const confidenceScores: number[] = []
      const errors: string[] = []

      for (const laptop of processedData) {
        try {
          // Track confidence scores
          confidenceScores.push(laptop.confidence)

          // Check for duplicates
          if (laptop.processingMetadata.duplicateScore && laptop.processingMetadata.duplicateScore > 0.8) {
            duplicates++
            continue
          }

          // Check if laptop already exists using fingerprint and normalized data
          const existingLaptop = await this.findExistingLaptop(laptop)

          if (existingLaptop) {
            // Update existing laptop
            await this.updateExistingLaptop(existingLaptop.id, laptop)
            updatedLaptops++
            console.log(`Updated existing laptop: ${laptop.normalizedTitle} (confidence: ${laptop.confidence})`)
          } else {
            // Create new laptop
            await this.createNewLaptop(laptop)
            newLaptops++
            console.log(`Created new laptop: ${laptop.normalizedTitle} (confidence: ${laptop.confidence})`)
          }

          successful++
        } catch (error) {
          const errorMsg = `Failed to save laptop data for ${laptop.normalizedTitle}: ${(error as Error).message}`
          console.error(errorMsg)
          errors.push(errorMsg)
        }
      }

      const processingTimeMs = Date.now() - startTime
      const averageConfidence = confidenceScores.length > 0
        ? confidenceScores.reduce((sum, score) => sum + score, 0) / confidenceScores.length
        : 0

      // Track metrics if job info is provided
      if (sourceId && jobId) {
        const metrics: ScrapingMetrics = {
          sourceId,
          jobId,
          startTime: new Date(startTime),
          endTime: new Date(),
          itemsDiscovered: laptopData.length,
          itemsProcessed: processedData.length,
          itemsSuccessful: successful,
          itemsFailed: processedData.length - successful,
          duplicatesFound: duplicates,
          newLaptopsCreated: newLaptops,
          existingLaptopsUpdated: updatedLaptops,
          averageConfidence,
          processingTimeMs,
          errorMessages: errors,
        }

        await this.metricsTracker.completeJobTracking(metrics)
      }

      return {
        processed: processedData.length,
        successful,
        newLaptops,
        updatedLaptops,
        duplicates,
        averageConfidence,
      }
    }, 'saveLaptopData')
  }

  /**
   * Clean up completed jobs (keep only recent ones)
   */
  cleanupCompletedJobs(maxAge: number = 24 * 60 * 60 * 1000): void {
    const now = Date.now()
    
    for (const [jobId, job] of this.activeJobs.entries()) {
      if (job.status !== 'running') {
        const jobAge = now - job.metrics.startTime.getTime()
        if (jobAge > maxAge) {
          this.activeJobs.delete(jobId)
        }
      }
    }
  }

  // Private helper methods

  private async runScrapingJob(
    targets: LaptopScrapingTarget[],
    config: ScrapingJobConfig,
    jobResult: ScrapingJobResult,
    jobId: string
  ): Promise<void> {
    const batches = this.createBatches(targets, config.batchSize)

    // Start metrics tracking
    const sourceId = targets.length > 0 && targets[0] ? this.extractSourceIdFromUrl(targets[0].url) : 'unknown'
    await this.metricsTracker.startJobTracking(sourceId, jobId)

    type SuccessResult = { success: true; data: LaptopData; url: string };
    type ErrorResult = { success: false; error: string; url: string };
    type BatchResult = SuccessResult | ErrorResult;

    for (let i = 0; i < batches.length; i++) {
      if (jobResult.status === 'cancelled') {
        break
      }

      const batch = batches[i]
      if (!batch) continue
      console.log(`Processing batch ${i + 1}/${batches.length} (${batch.length} URLs)`)

      // Process batch with concurrency limit
      const batchPromises = batch.map(target =>
        this.scrapeLaptopWithFallback(target, config.strategy)
          .then((result): SuccessResult => ({ success: true, data: result, url: target.url }))
          .catch((error): ErrorResult => ({ success: false, error: (error as Error).message, url: target.url }))
      )

      const batchResults: BatchResult[] = await Promise.all(batchPromises)

      // Update job results
      for (const result of batchResults) {
        if (result.success) {
          jobResult.results.successful.push(result.data)
          jobResult.progress.completed++
        } else {
          jobResult.results.failed.push({ url: result.url, error: result.error })
          jobResult.progress.failed++
        }
      }

      // Update progress
      jobResult.progress.percentage = Math.round(
        ((jobResult.progress.completed + jobResult.progress.failed) / jobResult.progress.total) * 100
      )

      // Save to database if enabled
      if (config.saveToDatabase) {
        const successfulResults = batchResults
          .filter((r): r is SuccessResult => r.success)
          .map(r => r.data)

        if (successfulResults.length > 0) {
          // Extract sourceId from the first target (assuming all targets in a job are from the same source)
          const sourceId = targets.length > 0 && targets[0] ? this.extractSourceIdFromUrl(targets[0].url) : undefined
          const saveResult = await this.saveLaptopData(successfulResults, sourceId, jobId)

          // Update job progress with save results
          await this.metricsTracker.updateJobProgress(jobId, sourceId || 'unknown', {
            itemsProcessed: saveResult.processed,
            itemsSuccessful: saveResult.successful,
            phase: 'database_save',
            message: `Saved ${saveResult.successful}/${saveResult.processed} laptops (${saveResult.newLaptops} new, ${saveResult.updatedLaptops} updated)`,
          })
        }
      }

      // Delay between batches
      if (i < batches.length - 1 && config.delayBetweenBatches > 0) {
        await new Promise(resolve => setTimeout(resolve, config.delayBetweenBatches))
      }
    }

    // Finalize job
    jobResult.status = 'completed'
    jobResult.metrics.endTime = new Date()
    jobResult.metrics.duration = jobResult.metrics.endTime.getTime() - jobResult.metrics.startTime.getTime()
    jobResult.metrics.averageTimePerUrl = jobResult.metrics.duration / targets.length
    jobResult.metrics.successRate = jobResult.progress.completed / jobResult.progress.total
  }

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = []
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize))
    }
    return batches
  }

  private generateJobId(): string {
    return `scraping_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Extract source ID from URL (simplified - in production this would be more sophisticated)
   */
  private extractSourceIdFromUrl(url: string): string {
    try {
      const domain = new URL(url).hostname.replace('www.', '')
      return domain.replace(/\./g, '_')
    } catch {
      return 'unknown_source'
    }
  }

  /**
   * Find existing laptop using multiple strategies
   */
  private async findExistingLaptop(laptop: ProcessedLaptopData): Promise<Laptops | null> {
    // Strategy 1: Exact fingerprint match
    if (laptop.fingerprint) {
      const existing = await this.db.laptops.findFirst({
        where: {
          fingerprint: laptop.fingerprint,
        },
      })
      if (existing) return existing
    }

    // Strategy 2: Title similarity and key spec matching
    const similarLaptops = await this.db.laptops.findMany({
      where: {
        OR: [
          { model_name: { contains: laptop.normalizedTitle, mode: 'insensitive' } },
          {
            brands: {
              name: {
                equals: laptop.specifications?.brand,
                mode: 'insensitive',
              },
            },
          },
        ],
      },
    })

    for (const existing of similarLaptops) {
      const similarity = this.calculateTitleSimilarity(existing.model_name, laptop.normalizedTitle)
      if (similarity > 0.9) {
        // Further check key specs to confirm match
        return existing
      }
    }

    return null
  }

  private calculateTitleSimilarity(title1: string, title2: string): number {
    // Simple similarity check - can be replaced with a more sophisticated library
    const lower1 = title1.toLowerCase()
    const lower2 = title2.toLowerCase()
    const words1 = new Set(lower1.split(/\s+/))
    const words2 = new Set(lower2.split(/\s+/))
    const intersection = new Set([...words1].filter(x => words2.has(x)))
    const union = new Set([...words1, ...words2])
    return intersection.size / union.size
  }

  private async updateExistingLaptop(laptopId: number, laptopData: ProcessedLaptopData): Promise<void> {
    // Update the main laptop entry if needed (e.g., description, image_url)
    // For simplicity, we focus on creating/updating related specs here

    // Update or create a listing
    await this.db.laptop_listings.upsert({
      where: {
        laptop_id_source_id_url: {
          laptop_id: laptopId,
          source_id: laptopData.sourceId,
          url: laptopData.url,
        },
      },
      update: {
        price: laptopData.price.current,
        in_stock: laptopData.availability.inStock,
        url: laptopData.url,
        updated_at: new Date(),
      },
      create: {
        laptop_id: laptopId,
        source_id: laptopData.sourceId,
        price: laptopData.price.current,
        in_stock: laptopData.availability.inStock,
        url: laptopData.url,
        listing_date: new Date(),
        created_at: new Date(),
        updated_at: new Date(),
      },
    })

    // Update specifications
    if (laptopData.specifications) {
      await this.updateLaptopSpecifications(laptopId, laptopData.specifications as Prisma.JsonObject)
    }
  }

  private async createNewLaptop(laptopData: ProcessedLaptopData): Promise<void> {
    const brandName = this.extractBrandFromTitle(laptopData.normalizedTitle)
    const brand = await this.db.brands.upsert({
      where: { name: brandName },
      update: {},
      create: {
        name: brandName,
        website: this.getBrandWebsite(brandName),
      },
    })

    // Create the laptop entry
    const newLaptop = await this.db.laptops.create({
      data: {
        model_name: laptopData.normalizedTitle,
        brand_id: brand.id,
        description: laptopData.description,
        image_url: laptopData.images?.[0],
        release_date: laptopData.releaseDate ? new Date(laptopData.releaseDate) : undefined,
        msrp: laptopData.price.original,
        created_at: new Date(),
        updated_at: new Date(),
      },
    })

    // Create the listing entry
    await this.db.laptop_listings.create({
      data: {
        laptop_id: newLaptop.id,
        source_id: laptopData.sourceId,
        price: laptopData.price.current,
        in_stock: laptopData.availability.inStock,
        url: laptopData.url,
        listing_date: new Date(),
        created_at: new Date(),
        updated_at: new Date(),
      },
    })

    // Create specifications
    if (laptopData.specifications) {
      await this.createLaptopSpecifications(newLaptop.id, laptopData.specifications as Prisma.JsonObject)
    }
  }

  private async updateLaptopSpecifications(laptopId: number, specs: Prisma.JsonObject): Promise<void> {
    // This is a simplified update - a real implementation would be more granular
    // For now, we'll just create new specs, assuming they don't exist
    await this.createLaptopSpecifications(laptopId, specs)
  }

  private async createLaptopSpecifications(laptopId: number, specs: Prisma.JsonObject): Promise<void> {
    try {
      if (specs.cpu) {
        const cpu = await this.findOrCreateCpu(specs.cpu as Prisma.CpusCreateInput)
        await this.db.laptop_cpus.create({
          data: {
            laptop_id: laptopId,
            cpu_id: cpu.id,
          },
        })
      }

      if (specs.gpu) {
        const gpu = await this.findOrCreateGpu(specs.gpu as Prisma.GpusCreateInput)
        await this.db.laptop_gpus.create({
          data: {
            laptop_id: laptopId,
            gpu_id: gpu.id,
          },
        })
      }

      if (specs.memory) {
        const ram = await this.findOrCreateRamConfiguration(specs.memory as Prisma.RamConfigurationsCreateInput)
        await this.db.laptop_ram.create({
          data: {
            laptop_id: laptopId,
            ram_configuration_id: ram.id,
            slots_used: 1,
          },
        })
      }

      if (specs.storage) {
        const storage = await this.findOrCreateStorageDevice(specs.storage as Prisma.StorageDevicesCreateInput)
        await this.db.laptop_storage.create({
          data: {
            laptop_id: laptopId,
            storage_devices: {
              connect: { id: storage.id }
            }
          },
        })
      }
    } catch (error) {
      console.error(`Failed to create specifications for laptop ${laptopId}:`, error)
    }
  }

  private extractBrandFromTitle(title: string): string {
    const knownBrands = ['dell', 'hp', 'lenovo', 'apple', 'acer', 'asus', 'msi', 'razer', 'samsung', 'microsoft']
    const lowerTitle = title.toLowerCase()
    for (const brand of knownBrands) {
      if (lowerTitle.includes(brand)) {
        return brand.charAt(0).toUpperCase() + brand.slice(1)
      }
    }
    return 'Unknown'
  }

  private getBrandWebsite(brandName: string): string {
    const lowerBrand = brandName.toLowerCase()
    if (lowerBrand === 'hp') return 'https://www.hp.com'
    return `https://www.${lowerBrand}.com`
  }

  private async findOrCreateCpu(cpuSpec: Prisma.CpusCreateInput): Promise<Prisma.CpusCreateInput> {
    try {
      const existingCpu = await this.db.cpus.findFirst({
        where: { model: cpuSpec.model },
      })
      if (existingCpu) return existingCpu

      const manufacturerName = this.extractCpuManufacturer(cpuSpec.model)
      const manufacturer = await this.db.manufacturers.upsert({
        where: { name: manufacturerName },
        update: {},
        create: { name: manufacturerName },
      })

      return await this.db.cpus.create({
        data: {
          model: cpuSpec.model,
          cores: cpuSpec.cores,
          threads: cpuSpec.threads,
          base_clock_ghz: cpuSpec.base_clock_ghz,
          boost_clock_ghz: cpuSpec.boost_clock_ghz,
          manufacturer_id: manufacturer.id,
        },
      })
    } catch (error) {
      console.error(`Error finding or creating CPU ${cpuSpec.model}:`, error)
      throw error
    }
  }

  private async findOrCreateGpu(gpuSpec: Prisma.GpusCreateInput): Promise<Prisma.GpusCreateInput> {
    try {
      const existingGpu = await this.db.gpus.findFirst({
        where: { model: gpuSpec.model },
      })
      if (existingGpu) return existingGpu

      const manufacturerName = this.extractGpuManufacturer(gpuSpec.model)
      const gpuManufacturer = await this.db.manufacturers.upsert({
        where: { name: manufacturerName },
        update: {},
        create: { name: manufacturerName },
      })

      return await this.db.gpus.create({
        data: {
          model: gpuSpec.model,
          vram_gb: gpuSpec.vram_gb,
          is_discrete: gpuSpec.is_discrete,
          manufacturer_id: gpuManufacturer.id,
        },
      })
    } catch (error) {
      console.error(`Error finding or creating GPU ${gpuSpec.model}:`, error)
      throw error
    }
  }

  private extractCpuManufacturer(model: string): string {
    const lowerModel = model.toLowerCase()
    if (lowerModel.includes('intel') || lowerModel.includes('core')) return 'Intel'
    if (lowerModel.includes('amd') || lowerModel.includes('ryzen')) return 'AMD'
    if (lowerModel.includes('apple') || lowerModel.includes('m1') || lowerModel.includes('m2')) return 'Apple'
    return 'Other'
  }

  private extractGpuManufacturer(model: string): string {
    const lowerModel = model.toLowerCase()
    if (lowerModel.includes('nvidia') || lowerModel.includes('geforce') || lowerModel.includes('rtx')) return 'NVIDIA'
    if (lowerModel.includes('amd') || lowerModel.includes('radeon')) return 'AMD'
    if (lowerModel.includes('intel')) return 'Intel'
    return 'Other'
  }

  private async findOrCreateRamConfiguration(memorySpec: Prisma.RamConfigurationsCreateInput): Promise<Prisma.RamConfigurationsCreateInput> {
    try {
      const ram = await this.db.ram_configurations.upsert({
        where: {
          size_gb_speed_mhz_ram_type_id: {
            size_gb: memorySpec.size_gb || 0,
            speed_mhz: memorySpec.speed_mhz || 0,
            ram_type_id: memorySpec.ram_type_id || 0,
          },
        },
        update: {},
        create: {
          size_gb: memorySpec.size_gb || 0,
          speed_mhz: memorySpec.speed_mhz || 0,
          is_dual_channel: memorySpec.is_dual_channel || false,
          ram_type_id: memorySpec.ram_type_id || 0,
        },
      })
      return ram
    } catch (error) {
      console.error('Error finding or creating RAM configuration:', error)
      throw error
    }
  }

  private async findOrCreateStorageDevice(storageSpec: Prisma.StorageDevicesCreateInput): Promise<Prisma.StorageDevicesCreateInput> {
    try {
      const storage = await this.db.storage_devices.upsert({
        where: {
          capacity_gb_type_interface_id: {
            capacity_gb: storageSpec.capacity_gb,
            type: storageSpec.type,
            interface_id: storageSpec.interface_id || 0,
          },
        },
        update: {},
        create: {
          capacity_gb: storageSpec.capacity_gb,
          type: storageSpec.type,
          interface_id: storageSpec.interface_id || 0,
        },
      })
      return storage
    } catch (error) {
      console.error('Error finding or creating storage device:', error)
      throw error
    }
  }
}
