/**
 * Enhanced Scraping Service
 * Integrates the existing scraping infrastructure with the new queue system
 */

import { QueueManager } from '../queue/queue-manager'
import { JobManager } from '../jobs/job-manager'
import { ScrapingService } from './scraping-service'
import { ScrapingOrchestrator } from './scraping-orchestrator'
import pino from 'pino'
import { v4 as uuidv4 } from 'uuid'
import type {
  ScrapingJobData,
  ScrapingTarget,
  ScrapingJobConfig,
  JobPriority,
  QueueManagerConfig
} from '../queue/types'

export interface EnhancedScrapingConfig {
  queueConfig: QueueManagerConfig
  defaultJobConfig: Partial<ScrapingJobConfig>
  batchProcessing: {
    enabled: boolean
    maxBatchSize: number
    batchTimeout: number
  }
  monitoring: {
    enabled: boolean
    metricsInterval: number
  }
}

export class EnhancedScrapingService {
  private queueManager: QueueManager
  private jobManager: JobManager
  private scrapingService: ScrapingService
  private orchestrator: ScrapingOrchestrator
  private logger: pino.Logger
  private config: EnhancedScrapingConfig

  constructor(config: EnhancedScrapingConfig) {
    this.config = config
    this.logger = pino({
      name: 'enhanced-scraping-service',
      level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
      transport: process.env.NODE_ENV === 'development' ? {
        target: 'pino-pretty',
        options: { colorize: true }
      } : undefined
    })

    this.queueManager = new QueueManager(config.queueConfig)
    this.jobManager = new JobManager()
    this.scrapingService = new ScrapingService()
    this.orchestrator = new ScrapingOrchestrator()
  }

  /**
   * Initialize the enhanced scraping service
   */
  async initialize(): Promise<void> {
    try {
      await this.queueManager.initialize()
      this.setupJobProcessors()
      this.setupEventHandlers()
      
      if (this.config.monitoring.enabled) {
        this.startMonitoring()
      }

      this.logger.info('Enhanced Scraping Service initialized successfully')
    } catch (error) {
      this.logger.error('Failed to initialize Enhanced Scraping Service:', error)
      throw error
    }
  }

  /**
   * Submit a scraping job
   */
  async submitScrapingJob(
    targets: ScrapingTarget[],
    options: {
      priority?: JobPriority
      config?: Partial<ScrapingJobConfig>
      sourceId?: string
      batchId?: string
      delay?: number
    } = {}
  ): Promise<string> {
    const jobId = uuidv4()
    const jobData: ScrapingJobData = {
      id: jobId,
      type: 'scraping',
      priority: options.priority || 'normal',
      createdAt: new Date(),
      targets,
      config: {
        ...this.getDefaultScrapingConfig(),
        ...options.config
      },
      sourceId: options.sourceId,
      batchId: options.batchId || uuidv4()
    }

    // Create job in database
    await this.jobManager.createJob(jobData, jobData.config.retryAttempts)

    // Add job to queue
    await this.queueManager.addJob('scraping-jobs', jobData, {
      delay: options.delay
    })

    this.logger.info(`Scraping job '${jobId}' submitted with ${targets.length} targets`)
    return jobId
  }

  /**
   * Submit multiple scraping jobs as a batch
   */
  async submitBatchScrapingJobs(
    targetGroups: ScrapingTarget[][],
    options: {
      priority?: JobPriority
      config?: Partial<ScrapingJobConfig>
      sourceId?: string
      staggerDelay?: number
    } = {}
  ): Promise<string[]> {
    const batchId = uuidv4()
    const jobIds: string[] = []

    for (let i = 0; i < targetGroups.length; i++) {
      const targets = targetGroups[i]
      const delay = options.staggerDelay ? i * options.staggerDelay : undefined

      const jobId = await this.submitScrapingJob(targets, {
        ...options,
        batchId,
        delay
      })

      jobIds.push(jobId)
    }

    this.logger.info(`Batch scraping jobs submitted: ${jobIds.length} jobs in batch '${batchId}'`)
    return jobIds
  }

  /**
   * Get job status
   */
  async getJobStatus(jobId: string) {
    const [dbJob, queueJob] = await Promise.all([
      this.jobManager.getJob(jobId),
      this.queueManager.getJob('scraping-jobs', jobId)
    ])

    return {
      database: dbJob,
      queue: queueJob ? {
        id: queueJob.id,
        name: queueJob.name,
        data: queueJob.data,
        progress: queueJob.progress,
        returnvalue: queueJob.returnvalue,
        failedReason: queueJob.failedReason,
        stacktrace: queueJob.stacktrace,
        opts: queueJob.opts,
        timestamp: queueJob.timestamp,
        delay: queueJob.delay,
        processedOn: queueJob.processedOn,
        finishedOn: queueJob.finishedOn
      } : null
    }
  }

  /**
   * Get batch status
   */
  async getBatchStatus(batchId: string) {
    const { jobs } = await this.jobManager.getJobs({
      batchId,
      limit: 1000
    })

    const stats = {
      total: jobs.length,
      waiting: jobs.filter(j => j.status === 'waiting').length,
      active: jobs.filter(j => j.status === 'active').length,
      completed: jobs.filter(j => j.status === 'completed').length,
      failed: jobs.filter(j => j.status === 'failed').length,
      cancelled: jobs.filter(j => j.status === 'cancelled').length
    }

    return {
      batchId,
      stats,
      jobs: jobs.map(job => ({
        id: job.id,
        status: job.status,
        progress: job.metrics?.itemsProcessed || 0,
        error: job.error,
        createdAt: job.createdAt,
        completedAt: job.completedAt
      }))
    }
  }

  /**
   * Cancel a job
   */
  async cancelJob(jobId: string): Promise<void> {
    const queueJob = await this.queueManager.getJob('scraping-jobs', jobId)
    if (queueJob) {
      await queueJob.remove()
    }

    await this.jobManager.updateJobStatus(jobId, 'cancelled')
    this.logger.info(`Job '${jobId}' cancelled`)
  }

  /**
   * Cancel all jobs in a batch
   */
  async cancelBatch(batchId: string): Promise<void> {
    const { jobs } = await this.jobManager.getJobs({
      batchId,
      status: 'waiting'
    })

    for (const job of jobs) {
      await this.cancelJob(job.id)
    }

    this.logger.info(`Batch '${batchId}' cancelled (${jobs.length} jobs)`)
  }

  /**
   * Get queue statistics
   */
  async getQueueStats() {
    return await this.queueManager.getAllQueueStats()
  }

  /**
   * Get job statistics
   */
  async getJobStats(timeRange?: { from: Date; to: Date }) {
    return await this.jobManager.getJobStats(timeRange)
  }

  /**
   * Setup job processors
   */
  private setupJobProcessors(): void {
    // The actual job processing logic will be handled by workers
    // This is just the configuration
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    this.queueManager.on('active', async (eventData) => {
      await this.jobManager.updateJobStatus(eventData.jobId, 'active')
    })

    this.queueManager.on('completed', async (eventData) => {
      await this.jobManager.updateJobStatus(eventData.jobId, 'completed', {
        result: eventData.data
      })
    })

    this.queueManager.on('failed', async (eventData) => {
      await this.jobManager.updateJobStatus(eventData.jobId, 'failed', {
        error: eventData.data?.message || 'Job failed',
        incrementAttempts: true
      })
    })

    this.queueManager.on('progress', async (eventData) => {
      // Update job progress in database if needed
    })
  }

  /**
   * Start monitoring
   */
  private startMonitoring(): void {
    setInterval(async () => {
      try {
        const stats = await this.getQueueStats()
        // Log or store metrics
        this.logger.debug('Queue stats:', stats)
      } catch (error) {
        this.logger.error('Failed to collect queue metrics:', error)
      }
    }, this.config.monitoring.metricsInterval)
  }

  /**
   * Get default scraping configuration
   */
  private getDefaultScrapingConfig(): ScrapingJobConfig {
    return {
      strategy: {
        primary: 'crawl4ai',
        fallback: 'firecrawl',
        retryWithFallback: true
      },
      batchSize: 10,
      maxConcurrent: 3,
      delayBetweenBatches: 5000,
      enableScreenshots: false,
      saveToDatabase: true,
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 5000,
      ...this.config.defaultJobConfig
    }
  }

  /**
   * Shutdown gracefully
   */
  async shutdown(): Promise<void> {
    this.logger.info('Shutting down Enhanced Scraping Service...')
    await this.queueManager.shutdown()
    this.logger.info('Enhanced Scraping Service shutdown complete')
  }
}
