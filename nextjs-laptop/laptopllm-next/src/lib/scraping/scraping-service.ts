import { Crawl4AIService } from './crawl4ai.service'
import { FirecrawlService } from './firecrawl.service'
import { PuppeteerService } from './puppeteer.service'
import type { ScrapingTarget, ScrapedData } from './base-scraper'

export interface ScrapingConfig {
  url: string
  selectors?: Record<string, string>
  waitFor?: number
  timeout?: number
  userAgent?: string
  headers?: Record<string, string>
  siteType?: 'amazon' | 'bestbuy' | 'newegg' | 'microcenter' | 'bhphoto' | 'adorama' | 'generic'
}

export interface ScrapingResult {
  success: boolean
  data?: ScrapedData
  error?: string
  scraper: string
  duration: number
  timestamp: Date
}

export interface ScrapingHealth {
  healthy: boolean
  scrapers: {
    crawl4ai: boolean
    firecrawl: boolean
    puppeteer: boolean
  }
  timestamp: Date
}

/**
 * Main scraping service that orchestrates multiple scrapers
 * Implements fallback strategy: Crawl4AI → Firecrawl → Puppeteer
 */
export class ScrapingService {
  private primaryScraper: Crawl4AIService
  private secondaryScraper: FirecrawlService
  private fallbackScraper: PuppeteerService
  private requestQueue: Map<string, Promise<ScrapingResult>> = new Map()
  private lastRequestTime = 0
  private readonly minRequestInterval = 100 // 100ms between requests

  constructor() {
    this.primaryScraper = new Crawl4AIService()
    this.secondaryScraper = new FirecrawlService()
    this.fallbackScraper = new PuppeteerService()
  }

  /**
   * Scrape a single URL with fallback strategy: Crawl4AI → Firecrawl → Puppeteer
   */
  async scrape(config: ScrapingConfig): Promise<ScrapingResult> {
    // Implement rate limiting
    await this.enforceRateLimit()

    // Check if we already have a pending request for this URL
    const cacheKey = this.getCacheKey(config)
    if (this.requestQueue.has(cacheKey)) {
      return this.requestQueue.get(cacheKey)!
    }

    // Create new request
    const requestPromise = this.performScrape(config)
    this.requestQueue.set(cacheKey, requestPromise)

    try {
      const result = await requestPromise
      return result
    } finally {
      // Clean up cache
      this.requestQueue.delete(cacheKey)
    }
  }

  /**
   * Scrape multiple URLs in parallel with rate limiting
   */
  async scrapeMultiple(configs: ScrapingConfig[]): Promise<ScrapingResult[]> {
    // Convert configs to targets and use individual scraping for better fallback control
    const results: ScrapingResult[] = []

    for (const config of configs) {
      try {
        const result = await this.scrape(config)
        results.push(result)
      } catch (err) {
        results.push({
          success: false,
          error: err instanceof Error ? err.message : 'Unknown error',
          scraper: 'unknown',
          duration: 0,
          timestamp: new Date(),
        })
      }
    }

    return results
  }

  /**
   * Get health status of all scrapers
   */
  async getHealth(): Promise<ScrapingHealth> {
    const [crawl4aiHealth, firecrawlHealthy, puppeteerHealthy] = await Promise.all([
      this.primaryScraper.healthCheck().then(h => h.status === 'healthy').catch(() => false),
      Promise.resolve(true).catch(() => false), // Firecrawl health check placeholder
      Promise.resolve(true).catch(() => false), // Puppeteer health check placeholder
    ])

    return {
      healthy: crawl4aiHealth || firecrawlHealthy || puppeteerHealthy,
      scrapers: {
        crawl4ai: crawl4aiHealth,
        firecrawl: firecrawlHealthy,
        puppeteer: puppeteerHealthy,
      },
      timestamp: new Date(),
    }
  }

  /**
   * Perform the actual scraping with fallback logic: Crawl4AI → Firecrawl → Puppeteer
   */
  private async performScrape(config: ScrapingConfig): Promise<ScrapingResult> {
    const startTime = Date.now()

    // Convert config to target format
    const target: ScrapingTarget = {
      url: config.url,
      selector: config.selectors ? Object.values(config.selectors)[0] || '' : '',
      waitFor: config.waitFor,
      actions: [],
    }

    // Try Crawl4AI first (primary)
    try {
      console.log(`🔄 Trying Crawl4AI for ${config.url}`)
      const result = await this.primaryScraper.scrape(target)
      if (result.success) {
        return {
          success: true,
          data: result,
          scraper: 'crawl4ai',
          duration: Date.now() - startTime,
          timestamp: new Date(),
        }
      }
    } catch (error) {
      console.warn('🚨 Crawl4AI scraper failed:', error)
    }

    // Try Firecrawl second (secondary)
    try {
      console.log(`🔄 Trying Firecrawl for ${config.url}`)
      const result = await this.secondaryScraper.scrape(target)
      if (result.success) {
        return {
          success: true,
          data: result,
          scraper: 'firecrawl',
          duration: Date.now() - startTime,
          timestamp: new Date(),
        }
      }
    } catch (error) {
      console.warn('🚨 Firecrawl scraper failed:', error)
    }

    // Try Puppeteer last (fallback)
    try {
      console.log(`🔄 Trying Puppeteer for ${config.url}`)
      const result = await this.fallbackScraper.scrape(target)
      if (result.success) {
        return {
          success: true,
          data: result,
          scraper: 'puppeteer',
          duration: Date.now() - startTime,
          timestamp: new Date(),
        }
      }
    } catch (error) {
      console.warn('🚨 Puppeteer scraper failed:', error)
    }

    // All scrapers failed
    return {
      success: false,
      error: 'All scrapers failed to process the request',
      scraper: 'none',
      duration: Date.now() - startTime,
      timestamp: new Date(),
    }
  }

  /**
   * Enforce rate limiting between requests
   */
  private async enforceRateLimit(): Promise<void> {
    const now = Date.now()
    const timeSinceLastRequest = now - this.lastRequestTime
    
    if (timeSinceLastRequest < this.minRequestInterval) {
      const delay = this.minRequestInterval - timeSinceLastRequest
      await new Promise(resolve => setTimeout(resolve, delay))
    }
    
    this.lastRequestTime = Date.now()
  }

  /**
   * Generate cache key for request deduplication
   */
  private getCacheKey(config: ScrapingConfig): string {
    return `${config.url}:${JSON.stringify(config.selectors || {})}`
  }

  /**
   * Extract laptop data using the best available scraper
   */
  async extractLaptopData(url: string, siteType: string = 'generic'): Promise<any> {
    try {
      // Try Crawl4AI first for structured extraction
      return await this.primaryScraper.extractLaptopData(url, siteType)
    } catch (error) {
      console.warn('Crawl4AI laptop extraction failed, trying Firecrawl:', error)

      try {
        // Fallback to Firecrawl - use general scraping instead
        const scrapingTarget: ScrapingTarget = {
          url,
          selector: '',
          actions: [],
        }
        const result = await this.secondaryScraper.scrape(scrapingTarget)
        return result.metadata || null
      } catch (firecrawlError) {
        console.warn('Firecrawl laptop extraction failed:', firecrawlError)
        throw new Error('All laptop extraction methods failed')
      }
    }
  }

  /**
   * Crawl for laptop URLs using the best available scraper
   */
  async crawlForLaptops(baseUrl: string, options: Record<string, unknown> = {}): Promise<string[]> {
    try {
      // Try Crawl4AI first
      return await this.primaryScraper.crawlForLaptops(baseUrl, options)
    } catch (error) {
      console.warn('Crawl4AI crawling failed, trying Firecrawl:', error)

      try {
        // Fallback to Firecrawl - return empty array for now
        console.warn('Firecrawl crawling not implemented, returning empty array')
        return []
      } catch (firecrawlError) {
        console.warn('Firecrawl crawling failed:', firecrawlError)
        return []
      }
    }
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    // Clear pending requests
    this.requestQueue.clear()

    // Cleanup Puppeteer browser
    try {
      if ('close' in this.fallbackScraper && typeof this.fallbackScraper.close === 'function') {
        await this.fallbackScraper.close()
      }
    } catch (error) {
      console.warn('Error closing Puppeteer browser:', error)
    }
  }
}

// Export singleton instance
export const scrapingService = new ScrapingService()
