/**
 * Enhanced Scraping Configuration
 * Configuration for the enhanced scraping pipeline with queue system
 */

import { scrapingJobProcessor } from '../../queue/workers/scraping-worker'
import type { EnhancedScrapingConfig } from '../enhanced-scraping-service'

export const enhancedScrapingConfig: EnhancedScrapingConfig = {
  queueConfig: {
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100,
    },
    queues: [
      {
        name: 'scraping-jobs',
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          delay: 0,
        },
        concurrency: 3,
        rateLimiter: {
          max: 10,
          duration: 60000, // 10 requests per minute
        },
      },
      {
        name: 'data-processing',
        defaultJobOptions: {
          removeOnComplete: 50,
          removeOnFail: 25,
          attempts: 2,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
        },
        concurrency: 2,
      },
      {
        name: 'notifications',
        defaultJobOptions: {
          removeOnComplete: 20,
          removeOnFail: 10,
          attempts: 3,
          backoff: {
            type: 'fixed',
            delay: 5000,
          },
        },
        concurrency: 1,
      },
    ],
    workers: [
      {
        queueName: 'scraping-jobs',
        concurrency: 3,
        processor: scrapingJobProcessor,
        options: {
          stalledInterval: 30000,
          maxStalledCount: 1,
          retryProcessDelay: 5000,
        },
      },
    ],
    monitoring: {
      enabled: true,
      metricsInterval: 60000, // 1 minute
      alertThresholds: {
        failureRate: 20, // 20%
        queueLength: 100,
        processingTime: 300000, // 5 minutes
        memoryUsage: 80, // 80%
      },
      notifications: {
        email: process.env.ALERT_EMAIL ? [process.env.ALERT_EMAIL] : undefined,
        webhook: process.env.ALERT_WEBHOOK,
        slack: process.env.SLACK_WEBHOOK,
      },
    },
    circuitBreaker: {
      failureThreshold: 5,
      resetTimeout: 60000, // 1 minute
      monitoringPeriod: 300000, // 5 minutes
      expectedErrors: [
        'ECONNREFUSED',
        'ENOTFOUND',
        'ETIMEDOUT',
        'Rate limit exceeded',
        'Too Many Requests',
      ],
    },
    retry: {
      attempts: 3,
      delay: 2000,
      backoff: 'exponential',
      maxDelay: 30000,
      jitter: true,
    },
    healthCheck: {
      interval: 30000, // 30 seconds
      timeout: 10000, // 10 seconds
      retries: 3,
      endpoints: [
        {
          name: 'crawl4ai',
          url: process.env.CRAWL4AI_URL || 'http://localhost:11235/health',
          method: 'GET',
          expectedStatus: 200,
        },
        {
          name: 'firecrawl',
          url: 'https://api.firecrawl.dev/v1/health',
          method: 'GET',
          expectedStatus: 200,
        },
      ],
    },
  },
  defaultJobConfig: {
    strategy: {
      primary: 'crawl4ai',
      fallback: 'firecrawl',
      retryWithFallback: true,
    },
    batchSize: 10,
    maxConcurrent: 3,
    delayBetweenBatches: 5000,
    enableScreenshots: false,
    saveToDatabase: true,
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 5000,
  },
  batchProcessing: {
    enabled: true,
    maxBatchSize: 50,
    batchTimeout: 300000, // 5 minutes
  },
  monitoring: {
    enabled: true,
    metricsInterval: 60000, // 1 minute
  },
}

// Environment-specific overrides
if (process.env.NODE_ENV === 'development') {
  // Development settings
  enhancedScrapingConfig.queueConfig.queues.forEach(queue => {
    queue.defaultJobOptions.removeOnComplete = 10
    queue.defaultJobOptions.removeOnFail = 5
  })
  
  enhancedScrapingConfig.defaultJobConfig.batchSize = 5
  enhancedScrapingConfig.defaultJobConfig.maxConcurrent = 2
  enhancedScrapingConfig.monitoring.metricsInterval = 30000 // 30 seconds
}

if (process.env.NODE_ENV === 'production') {
  // Production settings
  enhancedScrapingConfig.queueConfig.queues.forEach(queue => {
    queue.concurrency = Math.max(queue.concurrency, 5)
    if (queue.rateLimiter) {
      queue.rateLimiter.max = Math.min(queue.rateLimiter.max * 2, 50)
    }
  })
  
  enhancedScrapingConfig.defaultJobConfig.batchSize = 20
  enhancedScrapingConfig.defaultJobConfig.maxConcurrent = 5
  enhancedScrapingConfig.batchProcessing.maxBatchSize = 100
}

// Site-specific configurations
export const siteConfigs = {
  amazon: {
    rateLimiter: { max: 5, duration: 60000 },
    delayBetweenRequests: 2000,
    timeout: 45000,
    retryAttempts: 5,
    selectors: {
      title: '#productTitle',
      price: '.a-price-whole',
      currency: '.a-price-symbol',
      availability: '#availability span',
      specifications: '#feature-bullets ul',
      images: '#landingImage, #imgTagWrapperId img',
      reviews: '[data-hook="average-star-rating"]',
    },
  },
  bestbuy: {
    rateLimiter: { max: 8, duration: 60000 },
    delayBetweenRequests: 1500,
    timeout: 30000,
    retryAttempts: 3,
    selectors: {
      title: '.sr-only',
      price: '.pricing-current-price',
      availability: '.fulfillment-add-to-cart-button',
      specifications: '.specification-list',
      images: '.primary-image',
      reviews: '.sr-only',
    },
  },
  newegg: {
    rateLimiter: { max: 10, duration: 60000 },
    delayBetweenRequests: 1000,
    timeout: 25000,
    retryAttempts: 3,
    selectors: {
      title: '.product-title',
      price: '.price-current',
      availability: '.product-inventory',
      specifications: '.table-horizontal',
      images: '.product-view-img-original',
      reviews: '.rating',
    },
  },
  microcenter: {
    rateLimiter: { max: 6, duration: 60000 },
    delayBetweenRequests: 2500,
    timeout: 35000,
    retryAttempts: 4,
    selectors: {
      title: '[data-name="product-title"]',
      price: '.price',
      availability: '.stock',
      specifications: '.specs',
      images: '.product-image-main',
      reviews: '.rating-stars',
    },
  },
  bhphoto: {
    rateLimiter: { max: 12, duration: 60000 },
    delayBetweenRequests: 800,
    timeout: 20000,
    retryAttempts: 2,
    selectors: {
      title: '[data-selenium="productTitle"]',
      price: '[data-selenium="pricingPrice"]',
      availability: '[data-selenium="stockStatus"]',
      specifications: '.specifications-list',
      images: '.main-image',
      reviews: '.reviews-summary',
    },
  },
  adorama: {
    rateLimiter: { max: 10, duration: 60000 },
    delayBetweenRequests: 1200,
    timeout: 25000,
    retryAttempts: 3,
    selectors: {
      title: '.product-title',
      price: '.your-price',
      availability: '.availability-message',
      specifications: '.product-specifications',
      images: '.product-image',
      reviews: '.rating-summary',
    },
  },
}

// Export utility functions
export function getSiteConfig(siteType: string) {
  return siteConfigs[siteType as keyof typeof siteConfigs] || siteConfigs.amazon
}

export function createScrapingTarget(url: string, siteType: string) {
  const config = getSiteConfig(siteType)
  
  return {
    url,
    siteType: siteType as any,
    selectors: config.selectors,
    waitFor: config.delayBetweenRequests,
    actions: [
      { type: 'wait' as const, value: 1000 },
      { type: 'scroll' as const, selector: 'body' },
    ],
  }
}

export default enhancedScrapingConfig
