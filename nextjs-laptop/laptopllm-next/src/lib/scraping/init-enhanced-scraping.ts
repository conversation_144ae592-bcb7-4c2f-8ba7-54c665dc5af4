/**
 * Enhanced Scraping Initialization
 * Initialize and configure the enhanced scraping system
 */

import { EnhancedScrapingService } from './enhanced-scraping-service'
import { enhancedScrapingConfig } from './config/enhanced-scraping.config'
import pino from 'pino'

const logger = pino({
  name: 'enhanced-scraping-init',
  level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
  transport: process.env.NODE_ENV === 'development' ? {
    target: 'pino-pretty',
    options: { colorize: true }
  } : undefined
})

let enhancedScrapingService: EnhancedScrapingService | null = null

/**
 * Get or initialize the enhanced scraping service
 */
export async function getEnhancedScrapingService(): Promise<EnhancedScrapingService> {
  if (!enhancedScrapingService) {
    logger.info('Initializing Enhanced Scraping Service...')
    
    try {
      enhancedScrapingService = new EnhancedScrapingService(enhancedScrapingConfig)
      await enhancedScrapingService.initialize()
      
      logger.info('Enhanced Scraping Service initialized successfully')
    } catch (error) {
      logger.error('Failed to initialize Enhanced Scraping Service:', error)
      throw error
    }
  }
  
  return enhancedScrapingService
}

/**
 * Shutdown the enhanced scraping service
 */
export async function shutdownEnhancedScrapingService(): Promise<void> {
  if (enhancedScrapingService) {
    logger.info('Shutting down Enhanced Scraping Service...')
    
    try {
      await enhancedScrapingService.shutdown()
      enhancedScrapingService = null
      
      logger.info('Enhanced Scraping Service shutdown complete')
    } catch (error) {
      logger.error('Error during Enhanced Scraping Service shutdown:', error)
      throw error
    }
  }
}

/**
 * Check if the enhanced scraping service is initialized
 */
export function isEnhancedScrapingServiceInitialized(): boolean {
  return enhancedScrapingService !== null
}

/**
 * Health check for the enhanced scraping service
 */
export async function healthCheckEnhancedScrapingService(): Promise<{
  status: 'healthy' | 'unhealthy'
  details: Record<string, unknown>
}> {
  try {
    if (!enhancedScrapingService) {
      return {
        status: 'unhealthy',
        details: { error: 'Service not initialized' }
      }
    }

    const [queueStats, jobStats] = await Promise.all([
      enhancedScrapingService.getQueueStats(),
      enhancedScrapingService.getJobStats()
    ])

    return {
      status: 'healthy',
      details: {
        queueStats,
        jobStats,
        timestamp: new Date().toISOString()
      }
    }
  } catch (error) {
    logger.error('Health check failed:', error)
    return {
      status: 'unhealthy',
      details: { 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    }
  }
}

// Graceful shutdown handling
if (typeof process !== 'undefined') {
  process.on('SIGTERM', async () => {
    logger.info('Received SIGTERM, shutting down gracefully...')
    await shutdownEnhancedScrapingService()
    process.exit(0)
  })

  process.on('SIGINT', async () => {
    logger.info('Received SIGINT, shutting down gracefully...')
    await shutdownEnhancedScrapingService()
    process.exit(0)
  })
}

export default {
  getEnhancedScrapingService,
  shutdownEnhancedScrapingService,
  isEnhancedScrapingServiceInitialized,
  healthCheckEnhancedScrapingService
}
