// Scraping services exports

export { BaseScraper } from './base-scraper'
export { Crawl4AIService } from './crawl4ai.service'
export { FirecrawlService } from './firecrawl.service'
export { PuppeteerService } from './puppeteer.service'
export { ScrapingOrchestrator } from './scraping-orchestrator'
export { ScrapingService, scrapingService } from './scraping-service'

// Service instances (singletons)
import { Crawl4AIService } from './crawl4ai.service'
import { FirecrawlService } from './firecrawl.service'
import { PuppeteerService } from './puppeteer.service'
import { ScrapingOrchestrator } from './scraping-orchestrator'

export const crawl4aiService = new Crawl4AIService()
export const firecrawlService = new FirecrawlService()
export const puppeteerService = new PuppeteerService()
export const scrapingOrchestrator = new ScrapingOrchestrator()

// Type exports
export type {
  ScrapingConfig,
  ScrapingTarget,
  ScrapedData,
} from './base-scraper'

export type {
  Crawl4AIConfig,
  Crawl4AITarget,
  Crawl4AIResponse,
} from './crawl4ai.service'

export type {
  FirecrawlConfig,
  LaptopScrapingTarget,
  LaptopData,
} from './firecrawl.service'

export type {
  PuppeteerConfig,
} from './puppeteer.service'

export type {
  ScrapingConfig as ServiceScrapingConfig,
  ScrapingResult,
  ScrapingHealth,
} from './scraping-service'

export type {
  ScrapingStrategy,
  ScrapingJobConfig,
  ScrapingJobResult,
} from './scraping-orchestrator'
