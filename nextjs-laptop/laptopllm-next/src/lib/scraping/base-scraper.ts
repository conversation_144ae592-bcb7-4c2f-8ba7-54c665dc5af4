// Base scraper class with common functionality

import { BaseService } from '../services/base.service'
import type { ScrapingJobResult, ScrapingJobStatus } from '@/shared/types'

export interface ScrapingConfig {
  maxRetries: number
  retryDelay: number
  timeout: number
  rateLimit: number
  userAgent: string
  headers: Record<string, string>
}

export interface ScrapingTarget {
  url: string
  selector?: string
  waitFor?: number
  actions?: Array<{
    type: 'click' | 'scroll' | 'wait' | 'type'
    selector?: string
    value?: string | number
  }>
}

export interface ScrapedData {
  url: string
  title?: string
  content: string
  metadata: Record<string, unknown>
  timestamp: Date
  success: boolean
  error?: string
}

export abstract class BaseScraper extends BaseService {
  protected config: ScrapingConfig
  protected isRunning: boolean = false
  protected jobId?: string

  constructor(config: Partial<ScrapingConfig> = {}) {
    super()
    this.config = {
      maxRetries: 3,
      retryDelay: 1000,
      timeout: 30000,
      rateLimit: 1000,
      userAgent: 'LaptopLLM-Finder/1.0 (+https://laptopllm-finder.com)',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
      ...config,
    }
  }

  /**
   * Abstract method to be implemented by specific scrapers
   */
  abstract scrape(target: ScrapingTarget): Promise<ScrapedData>

  /**
   * Scrape multiple targets with rate limiting
   */
  async scrapeMultiple(targets: ScrapingTarget[]): Promise<ScrapedData[]> {
    const results: ScrapedData[] = []
    
    for (const target of targets) {
      try {
        const result = await this.scrapeWithRetry(target)
        results.push(result)
        
        // Rate limiting
        if (this.config.rateLimit > 0) {
          await this.delay(this.config.rateLimit)
        }
      } catch (error) {
        console.error(`Failed to scrape ${target.url}:`, error)
        results.push({
          url: target.url,
          content: '',
          metadata: {},
          timestamp: new Date(),
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        })
      }
    }
    
    return results
  }

  /**
   * Scrape with retry logic
   */
  protected async scrapeWithRetry(target: ScrapingTarget): Promise<ScrapedData> {
    let lastError: Error | null = null
    
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        return await this.scrape(target)
      } catch (error) {
        lastError = error as Error
        console.warn(`Scraping attempt ${attempt} failed for ${target.url}:`, error)
        
        if (attempt < this.config.maxRetries) {
          await this.delay(this.config.retryDelay * attempt)
        }
      }
    }
    
    throw lastError || new Error('All retry attempts failed')
  }

  /**
   * Start a scraping job
   */
  async startJob(
    targets: ScrapingTarget[],
    jobType: string = 'general'
  ): Promise<string> {
    if (this.isRunning) {
      throw new Error('Scraper is already running')
    }

    this.isRunning = true
    this.jobId = this.generateJobId()

    try {
      // Create job record in database
      await this.createJobRecord(this.jobId, jobType, targets.length)

      // Start scraping in background
      this.runJobInBackground(targets, jobType)

      return this.jobId
    } catch (error) {
      this.isRunning = false
      this.jobId = undefined
      throw error
    }
  }

  /**
   * Get job status
   */
  async getJobStatus(jobId: string): Promise<ScrapingJobStatus> {
    return this.executeWithErrorHandling(async () => {
      const job = await this.db.scrapingJob.findUnique({
        where: { id: jobId },
        include: {
          scrapingLog: {
            orderBy: { timestamp: 'desc' },
            take: 10,
          },
        },
      })

      if (!job) {
        throw new Error(`Job ${jobId} not found`)
      }

      return {
        id: job.id,
        status: job.status as 'running' | 'completed' | 'failed' | 'cancelled',
        progress: {
          completed: job.completed_urls || 0,
          total: job.total_urls || 0,
          failed: job.failed_urls || 0,
        },
        startedAt: job.started_at,
        completedAt: job.completed_at,
        error: job.error_message,
        logs: job.scrapingLog.map(log => ({
          timestamp: log.timestamp,
          level: log.level as 'info' | 'warn' | 'error',
          message: log.message,
          url: log.url,
          metadata: log.metadata as Record<string, unknown>,
        })),
      }
    }, 'getJobStatus')
  }

  /**
   * Cancel a running job
   */
  async cancelJob(jobId: string): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      await this.db.scrapingJob.update({
        where: { id: jobId },
        data: {
          status: 'cancelled',
          completed_at: new Date(),
        },
      })

      if (this.jobId === jobId) {
        this.isRunning = false
        this.jobId = undefined
      }
    }, 'cancelJob')
  }

  /**
   * Validate URL before scraping
   */
  protected validateUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url)
      return ['http:', 'https:'].includes(parsedUrl.protocol)
    } catch {
      return false
    }
  }

  /**
   * Extract domain from URL
   */
  protected extractDomain(url: string): string {
    try {
      return new URL(url).hostname
    } catch {
      return 'unknown'
    }
  }

  /**
   * Clean and normalize scraped text
   */
  protected cleanText(text: string): string {
    return text
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n')
      .trim()
  }

  /**
   * Extract metadata from scraped content
   */
  protected extractMetadata(content: string, url: string): Record<string, unknown> {
    const metadata: Record<string, unknown> = {
      domain: this.extractDomain(url),
      contentLength: content.length,
      wordCount: content.split(/\s+/).length,
      extractedAt: new Date().toISOString(),
    }

    // Extract basic SEO metadata patterns
    const titleMatch = content.match(/<title[^>]*>([^<]+)<\/title>/i)
    if (titleMatch) {
      metadata.title = this.cleanText(titleMatch[1])
    }

    const descriptionMatch = content.match(/<meta[^>]*name="description"[^>]*content="([^"]+)"/i)
    if (descriptionMatch) {
      metadata.description = this.cleanText(descriptionMatch[1])
    }

    return metadata
  }

  /**
   * Log scraping activity
   */
  protected async logActivity(
    level: 'info' | 'warn' | 'error',
    message: string,
    url?: string,
    metadata?: Record<string, unknown>
  ): Promise<void> {
    if (!this.jobId) return

    try {
      await this.db.scrapingLog.create({
        data: {
          job_id: this.jobId,
          level,
          message,
          url,
          metadata,
          timestamp: new Date(),
        },
      })
    } catch (error) {
      console.error('Failed to log scraping activity:', error)
    }
  }

  /**
   * Update job progress
   */
  protected async updateJobProgress(
    completed: number,
    failed: number = 0
  ): Promise<void> {
    if (!this.jobId) return

    try {
      await this.db.scrapingJob.update({
        where: { id: this.jobId },
        data: {
          completed_urls: completed,
          failed_urls: failed,
          updated_at: new Date(),
        },
      })
    } catch (error) {
      console.error('Failed to update job progress:', error)
    }
  }

  // Private helper methods

  private generateJobId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private async createJobRecord(
    jobId: string,
    jobType: string,
    totalUrls: number
  ): Promise<void> {
    await this.db.scrapingJob.create({
      data: {
        id: jobId,
        job_type: jobType,
        status: 'running',
        total_urls: totalUrls,
        completed_urls: 0,
        failed_urls: 0,
        started_at: new Date(),
        created_at: new Date(),
        updated_at: new Date(),
      },
    })
  }

  private async runJobInBackground(
    targets: ScrapingTarget[],
    jobType: string
  ): Promise<void> {
    try {
      await this.logActivity('info', `Starting ${jobType} job with ${targets.length} targets`)
      
      const results = await this.scrapeMultiple(targets)
      const successful = results.filter(r => r.success).length
      const failed = results.filter(r => !r.success).length

      await this.updateJobProgress(successful, failed)
      
      await this.db.scrapingJob.update({
        where: { id: this.jobId! },
        data: {
          status: 'completed',
          completed_at: new Date(),
        },
      })

      await this.logActivity('info', `Job completed: ${successful} successful, ${failed} failed`)
    } catch (error) {
      await this.db.scrapingJob.update({
        where: { id: this.jobId! },
        data: {
          status: 'failed',
          completed_at: new Date(),
          error_message: error instanceof Error ? error.message : 'Unknown error',
        },
      })

      await this.logActivity('error', `Job failed: ${error}`)
    } finally {
      this.isRunning = false
      this.jobId = undefined
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}
