// Crawl4AI service for web scraping

import axios, { type AxiosInstance } from 'axios'
import { BaseScraper, type ScrapingTarget, type ScrapedData, type ScrapingConfig } from './base-scraper'

export interface Crawl4AIConfig extends ScrapingConfig {
  apiUrl: string
  pollInterval: number
  maxPollAttempts: number
  extractionSchema?: Record<string, unknown> | undefined
  enableJavaScript: boolean
  waitForSelector?: string | undefined
  blockResources: string[]
  screenshotMode: boolean
}

export interface Crawl4AITarget extends ScrapingTarget {
  siteType?: 'amazon' | 'bestbuy' | 'newegg' | 'microcenter' | 'bhphoto' | 'adorama' | 'generic'
  extractionSchema?: Record<string, unknown>
  waitForSelector?: string
  productSelectors?: {
    title?: string
    price?: string
    specs?: string
    images?: string
    availability?: string
    reviews?: string
  }
}

export interface Crawl4AIResponse {
  task_id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  result?: {
    url: string
    markdown?: string
    html?: string
    cleaned_html?: string
    media?: Array<{ type: string; src: string; alt?: string }>
    links?: Array<{ text: string; href: string }>
    metadata?: {
      title?: string
      description?: string
      keywords?: string[]
      language?: string
      author?: string
    }
    extracted_content?: {
      links?: Array<{ text: string; href: string }>
      products?: Array<Record<string, unknown>>
      structured_data?: Record<string, unknown>
      [key: string]: unknown
    }
    screenshot?: string
  }
  error?: string
  created_at: string
  updated_at: string
}

// New API response format for direct crawl endpoint
export interface Crawl4AICrawlResult {
  url: string
  markdown?: {
    raw_markdown?: string
    markdown?: string
  }
  html?: string
  cleaned_html?: string
  status_code?: number
  media?: Array<{ type: string; src: string; alt?: string }>
  links?: Array<{ text: string; href: string }>
  metadata?: {
    title?: string
    description?: string
    keywords?: string[]
    language?: string
    author?: string
  }
  extracted_content?: {
    links?: Array<{ text: string; href: string }>
    products?: Array<Record<string, unknown>>
    structured_data?: Record<string, unknown>
    [key: string]: unknown
  }
  screenshot?: string
}

export interface Crawl4AICrawlResponse {
  success: boolean
  results?: Crawl4AICrawlResult[]
  error?: string
  server_processing_time_s?: number
}

export class Crawl4AIService extends BaseScraper {
  private crawl4aiConfig: Crawl4AIConfig
  private axiosInstance: AxiosInstance

  constructor(config: Partial<Crawl4AIConfig> = {}) {
    super(config)
    
    this.crawl4aiConfig = {
      ...this.config,
      apiUrl: config.apiUrl || process.env.CRAWLAI_API_URL || 'http://localhost:11235',
      pollInterval: config.pollInterval || 3000,
      maxPollAttempts: config.maxPollAttempts || 20,
      extractionSchema: config.extractionSchema || undefined,
      enableJavaScript: config.enableJavaScript ?? true,
      waitForSelector: config.waitForSelector || undefined,
      blockResources: config.blockResources || ['image', 'stylesheet', 'font'],
      screenshotMode: config.screenshotMode ?? false,
    }

    this.axiosInstance = axios.create({
      baseURL: this.crawl4aiConfig.apiUrl,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...this.config.headers,
      },
    })
  }

  /**
   * Scrape a single URL using Crawl4AI
   */
  async scrape(target: ScrapingTarget): Promise<ScrapedData> {
    if (!this.validateUrl(target.url)) {
      throw new Error(`Invalid URL: ${target.url}`)
    }

    try {
      await this.logActivity('info', `Starting Crawl4AI scrape`, target.url)

      // Submit crawl job and get results directly
      const result = await this.submitCrawlJob(target)

      if (!result.success || !result.results || result.results.length === 0) {
        throw new Error(`Crawl4AI scraping failed: ${result.error || 'No result data'}`)
      }

      const crawlResult = result.results[0] // Get first result
      if (!crawlResult) {
        throw new Error('No crawl result data available')
      }

      const content = crawlResult.markdown?.raw_markdown || crawlResult.cleaned_html || crawlResult.html || ''
      const metadata = this.extractMetadata(content, target.url)

      // Add Crawl4AI-specific metadata
      if (crawlResult.metadata) {
        Object.assign(metadata, {
          crawl4ai: {
            title: crawlResult.metadata.title,
            description: crawlResult.metadata.description,
            keywords: crawlResult.metadata.keywords,
            language: crawlResult.metadata.language,
            author: crawlResult.metadata.author,
            statusCode: crawlResult.status_code,
            processingTime: result.server_processing_time_s,
          },
        })
      }

      // Add media and links if available
      if (crawlResult.media) {
        metadata.media = crawlResult.media
      }

      if (crawlResult.links) {
        metadata.links = crawlResult.links
      }

      // Add extracted content if schema was used
      if (crawlResult.extracted_content) {
        metadata.extractedContent = crawlResult.extracted_content
      }

      await this.logActivity('info', `Successfully scraped ${content.length} characters`, target.url)

      return {
        url: target.url,
        title: crawlResult.metadata?.title || 'Untitled',
        content: this.cleanText(content),
        metadata,
        timestamp: new Date(),
        success: true,
      }

    } catch (error) {
      await this.logActivity('error', `Crawl4AI scraping failed: ${error}`, target.url)
      throw error
    }
  }

  /**
   * Submit a crawl job to Crawl4AI using the new API format
   */
  private async submitCrawlJob(target: ScrapingTarget): Promise<Crawl4AICrawlResponse> {
    // Build browser configuration - simplified
    const browserConfig = {
      type: 'BrowserConfig',
      params: {
        headless: true
      }
    }

    // Build crawler configuration - simplified
    const crawlerConfig = {
      type: 'CrawlerRunConfig',
      params: {
        stream: false,
        cache_mode: 'bypass'
      }
    }

    const crawlRequest = {
      urls: [target.url],
      browser_config: browserConfig,
      crawler_config: crawlerConfig
    }

    const response = await this.axiosInstance.post('/crawl', crawlRequest)

    // The new API returns results directly, not a task_id for polling
    if (!response.data?.success) {
      throw new Error(`Crawl4AI request failed: ${response.data?.error || 'Unknown error'}`)
    }

    return response.data
  }





  /**
   * Extract laptop-specific data using Crawl4AI's structured extraction
   */
  async extractLaptopData(url: string, siteType: 'amazon' | 'bestbuy' | 'newegg' | 'microcenter' | 'bhphoto' | 'adorama' | 'generic' = 'generic'): Promise<ScrapedData> {
    const laptopSchema = this.getLaptopExtractionSchema(siteType)

    const target: Crawl4AITarget = {
      url,
      siteType,
      extractionSchema: laptopSchema,
    }

    try {
      const result = await this.scrape(target)
      return result.metadata?.extractedContent || null
    } catch (error) {
      await this.logActivity('error', `Failed to extract laptop data: ${error}`, url)
      throw error
    }
  }

  /**
   * Get laptop extraction schema based on site type
   */
  private getLaptopExtractionSchema(siteType: string): Record<string, unknown> {
    interface LaptopSchema extends Record<string, unknown> {
      type: 'object'
      properties: {
        title: { type: 'string', description: 'Product title or name' }
        brand?: { type: 'string', description: 'Laptop brand (Dell, HP, Lenovo, etc.)' }
        model?: { type: 'string', description: 'Laptop model number or name' }
        price?: {
          type: 'object'
          properties: {
            current: { type: 'number', description: 'Current price' }
            original?: { type: 'number', description: 'Original price if on sale' }
            currency?: { type: 'string', description: 'Currency code (USD, EUR, etc.)' }
          }
        }
        specifications?: {
          type: 'object'
          properties: {
            processor?: { type: 'string', description: 'CPU information' }
            memory?: { type: 'string', description: 'RAM amount and type' }
            storage?: { type: 'string', description: 'Storage type and capacity' }
            graphics?: { type: 'string', description: 'GPU information' }
            display?: { type: 'string', description: 'Screen size and resolution' }
            os?: { type: 'string', description: 'Operating system' }
          }
        }
        availability?: {
          type: 'object'
          properties: {
            inStock?: { type: 'boolean', description: 'Whether item is in stock' }
            quantity?: { type: 'number', description: 'Available quantity if shown' }
            shippingInfo?: { type: 'string', description: 'Shipping information' }
          }
        }
        images?: {
          type: 'array'
          items: { type: 'string' }
          description: 'Product image URLs'
        }
        reviews?: {
          type: 'object'
          properties: {
            rating?: { type: 'number', description: 'Average rating' }
            count?: { type: 'number', description: 'Number of reviews' }
            highlights?: {
              type: 'array'
              items: { type: 'string' }
              description: 'Review highlights or key points'
            }
          }
        }
        asin?: { type: 'string', description: 'Amazon ASIN' }
        prime?: { type: 'boolean', description: 'Prime eligible' }
        sku?: { type: 'string', description: 'Best Buy SKU' }
        geekSquad?: { type: 'boolean', description: 'Geek Squad services available' }
      }
      required: ['title']
    }

    const baseSchema: LaptopSchema = {
      type: 'object',
      properties: {
        title: { type: 'string', description: 'Product title or name' },
        brand: { type: 'string', description: 'Laptop brand (Dell, HP, Lenovo, etc.)' },
        model: { type: 'string', description: 'Laptop model number or name' },
        price: {
          type: 'object',
          properties: {
            current: { type: 'number', description: 'Current price' },
            original: { type: 'number', description: 'Original price if on sale' },
            currency: { type: 'string', description: 'Currency code (USD, EUR, etc.)' },
          },
        },
        specifications: {
          type: 'object',
          properties: {
            processor: { type: 'string', description: 'CPU information' },
            memory: { type: 'string', description: 'RAM amount and type' },
            storage: { type: 'string', description: 'Storage type and capacity' },
            graphics: { type: 'string', description: 'GPU information' },
            display: { type: 'string', description: 'Screen size and resolution' },
            os: { type: 'string', description: 'Operating system' },
          },
        },
        availability: {
          type: 'object',
          properties: {
            inStock: { type: 'boolean', description: 'Whether item is in stock' },
            quantity: { type: 'number', description: 'Available quantity if shown' },
            shippingInfo: { type: 'string', description: 'Shipping information' },
          },
        },
        images: {
          type: 'array',
          items: { type: 'string' },
          description: 'Product image URLs',
        },
        reviews: {
          type: 'object',
          properties: {
            rating: { type: 'number', description: 'Average rating' },
            count: { type: 'number', description: 'Number of reviews' },
            highlights: {
              type: 'array',
              items: { type: 'string' },
              description: 'Review highlights or key points',
            },
          },
        },
      },
      required: ['title'],
    }

    // Site-specific schema modifications
    switch (siteType) {
      case 'amazon':
        return {
          ...baseSchema,
          properties: {
            ...baseSchema.properties,
            asin: { type: 'string', description: 'Amazon ASIN' },
            prime: { type: 'boolean', description: 'Prime eligible' },
          },
        }
      case 'bestbuy':
        return {
          ...baseSchema,
          properties: {
            ...baseSchema.properties,
            sku: { type: 'string', description: 'Best Buy SKU' },
            geekSquad: { type: 'boolean', description: 'Geek Squad services available' },
          },
        }
      default:
        return baseSchema
    }
  }

  /**
   * Crawl a website to discover laptop product URLs
   */
  async crawlForLaptops(
    baseUrl: string,
    options: {
      maxPages?: number
      includePaths?: string[]
      excludePaths?: string[]
      laptopKeywords?: string[]
    } = {}
  ): Promise<string[]> {
    try {
      await this.logActivity('info', `Starting Crawl4AI crawl for laptop URLs`, baseUrl)

      // Use Crawl4AI to crawl and extract links
      const browserConfig = {
        type: 'BrowserConfig',
        params: {
          headless: true,
          page_timeout: 30
        }
      }

      const crawlerConfig = {
        type: 'CrawlerRunConfig',
        params: {
          stream: false,
          cache_mode: 'bypass',
          extraction_strategy: {
            type: 'JsonCssExtractionStrategy',
            params: {
              schema: {
                type: 'dict',
                value: {
                  type: 'object',
                  properties: {
                    links: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          text: { type: 'string' },
                          href: { type: 'string' },
                        },
                      },
                    },
                  },
                }
              }
            }
          }
        }
      }

      const crawlRequest = {
        urls: [baseUrl],
        browser_config: browserConfig,
        crawler_config: crawlerConfig
      }

      const response = await this.axiosInstance.post('/crawl', crawlRequest)

      if (!response.data?.success || !response.data.results || response.data.results.length === 0) {
        return []
      }

      const result = response.data.results[0]
      if (!result.extracted_content?.links) {
        return []
      }

      // Filter links for laptop-related URLs
      const laptopKeywords = options.laptopKeywords || ['laptop', 'notebook', 'gaming', 'ultrabook', 'chromebook']
      const includePaths = options.includePaths || ['/laptop', '/notebook', '/gaming', '/computer']
      const excludePaths = options.excludePaths || ['/support', '/warranty', '/contact', '/about']

      const laptopUrls = result.extracted_content.links
        .filter((link: { text: string; href: string }) => {
          const href = link.href?.toLowerCase() || ''
          const text = link.text?.toLowerCase() || ''

          // Check if URL contains laptop-related paths
          const hasIncludePath = includePaths.some(path => href.includes(path.toLowerCase()))
          const hasExcludePath = excludePaths.some(path => href.includes(path.toLowerCase()))

          // Check if text contains laptop keywords
          const hasLaptopKeyword = laptopKeywords.some(keyword =>
            text.includes(keyword.toLowerCase()) || href.includes(keyword.toLowerCase())
          )

          return (hasIncludePath || hasLaptopKeyword) && !hasExcludePath
        })
        .map((link: { text: string; href: string }) => {
          // Convert relative URLs to absolute
          try {
            return new URL(link.href, baseUrl).href
          } catch {
            return null
          }
        })
        .filter((url: string | null) => url !== null)

      await this.logActivity('info', `Found ${laptopUrls.length} laptop URLs`, baseUrl)
      return laptopUrls

    } catch (error) {
      await this.logActivity('error', `Failed to crawl for laptops: ${error}`, baseUrl)
      throw error
    }
  }

  /**
   * Health check for Crawl4AI service
   */
  async healthCheck(): Promise<{ status: string; version?: string; error?: string }> {
    try {
      const response = await this.axiosInstance.get('/health', { timeout: 5000 })
      return {
        status: 'healthy',
        version: response.data?.version,
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }
}
