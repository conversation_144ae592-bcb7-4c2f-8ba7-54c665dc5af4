import { describe, it, expect } from 'vitest'
import {
  cn,
  formatPrice,
  formatBytes,
  formatPercentage,
  slugify,
  getCompatibilityColor,
  getCompatibilityLabel,
  getPerformanceLevel,
} from '../utils'

describe('utils', () => {
  describe('cn', () => {
    it('should merge class names correctly', () => {
      expect(cn('px-2 py-1', 'px-4')).toBe('py-1 px-4')
      expect(cn('text-red-500', 'text-blue-500')).toBe('text-blue-500')
    })

    it('should handle conditional classes', () => {
      expect(cn('base', true && 'conditional', false && 'hidden')).toBe('base conditional')
    })
  })

  describe('formatPrice', () => {
    it('should format USD prices correctly', () => {
      expect(formatPrice(1234.56)).toBe('$1,234.56')
      expect(formatPrice(0)).toBe('$0.00')
    })

    it('should handle different currencies', () => {
      expect(formatPrice(1234.56, 'EUR')).toBe('€1,234.56')
    })
  })

  describe('formatBytes', () => {
    it('should format bytes correctly', () => {
      expect(formatBytes(0)).toBe('0 Bytes')
      expect(formatBytes(1024)).toBe('1 KB')
      expect(formatBytes(1048576)).toBe('1 MB')
      expect(formatBytes(1073741824)).toBe('1 GB')
    })

    it('should handle decimals', () => {
      expect(formatBytes(1536, 1)).toBe('1.5 KB')
      expect(formatBytes(1536, 0)).toBe('2 KB')
    })
  })

  describe('formatPercentage', () => {
    it('should format percentages correctly', () => {
      expect(formatPercentage(0.5)).toBe('50.0%')
      expect(formatPercentage(0.123)).toBe('12.3%')
      expect(formatPercentage(1)).toBe('100.0%')
    })

    it('should handle different decimal places', () => {
      expect(formatPercentage(0.123456, 2)).toBe('12.35%')
      expect(formatPercentage(0.123456, 0)).toBe('12%')
    })
  })

  describe('slugify', () => {
    it('should create valid slugs', () => {
      expect(slugify('Hello World')).toBe('hello-world')
      expect(slugify('Dell XPS 15 (2023)')).toBe('dell-xps-15-2023')
      expect(slugify('MacBook Pro 16"')).toBe('macbook-pro-16')
    })

    it('should handle special characters', () => {
      expect(slugify('Test & Example!')).toBe('test-example')
      expect(slugify('Multiple   Spaces')).toBe('multiple-spaces')
    })
  })

  describe('getCompatibilityColor', () => {
    it('should return correct colors for scores', () => {
      expect(getCompatibilityColor(90)).toBe('compatibility-excellent')
      expect(getCompatibilityColor(70)).toBe('compatibility-good')
      expect(getCompatibilityColor(50)).toBe('compatibility-fair')
      expect(getCompatibilityColor(30)).toBe('compatibility-poor')
    })

    it('should handle edge cases', () => {
      expect(getCompatibilityColor(80)).toBe('compatibility-excellent')
      expect(getCompatibilityColor(60)).toBe('compatibility-good')
      expect(getCompatibilityColor(40)).toBe('compatibility-fair')
      expect(getCompatibilityColor(0)).toBe('compatibility-poor')
    })
  })

  describe('getCompatibilityLabel', () => {
    it('should return correct labels for scores', () => {
      expect(getCompatibilityLabel(90)).toBe('Excellent')
      expect(getCompatibilityLabel(70)).toBe('Good')
      expect(getCompatibilityLabel(50)).toBe('Fair')
      expect(getCompatibilityLabel(30)).toBe('Poor')
    })
  })

  describe('getPerformanceLevel', () => {
    it('should return correct performance levels', () => {
      expect(getPerformanceLevel(80)).toBe('high')
      expect(getPerformanceLevel(50)).toBe('medium')
      expect(getPerformanceLevel(30)).toBe('low')
    })

    it('should handle edge cases', () => {
      expect(getPerformanceLevel(70)).toBe('high')
      expect(getPerformanceLevel(40)).toBe('medium')
      expect(getPerformanceLevel(0)).toBe('low')
    })
  })
})
