// Laptop Card Component - Displays laptop information in a card format

'use client'

import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Badge } from './badge'
import { Button } from './button'
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from './card'
import { Progress } from './progress'
import { Heart, HeartOff, GitCompare, Eye, ExternalLink } from 'lucide-react'
import type { LaptopData } from '@/lib/scraping/firecrawl.service'
import type { CompatibilityScore } from '@/lib/llm/compatibility.service'
import { useComparison, useFavorites } from '@/contexts/app-context'

interface LaptopCardProps {
  laptop: LaptopData
  compatibilityScore?: CompatibilityScore
  showCompatibility?: boolean
  onViewDetails?: (laptop: LaptopData) => void
  onCompare?: (laptop: LaptopData) => void
  className?: string
}

export function LaptopCard({
  laptop,
  compatibilityScore,
  showCompatibility = true,
  onViewDetails,
  onCompare,
  className = '',
}: LaptopCardProps) {
  const comparison = useComparison()
  const favorites = useFavorites()

  const isInComparison = comparison.has(laptop.url)
  const isFavorite = favorites.has(laptop.url)

  const formatPrice = (price: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(price)
  }

  const getCompatibilityColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getCompatibilityLabel = (score: number) => {
    if (score >= 80) return 'Excellent'
    if (score >= 60) return 'Good'
    if (score >= 40) return 'Fair'
    return 'Limited'
  }

  const handleToggleFavorite = () => {
    favorites.toggle(laptop.url)
  }

  const handleToggleComparison = () => {
    if (isInComparison) {
      comparison.remove(laptop.url)
    } else {
      comparison.add(laptop)
    }
  }

  const handleCompare = () => {
    if (onCompare) {
      onCompare(laptop)
    } else {
      handleToggleComparison()
    }
  }

  return (
    <Card className={`h-full flex flex-col ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold line-clamp-2 mb-1">
              {laptop.title}
            </CardTitle>
            {laptop.brand && (
              <Badge variant="secondary" className="text-xs">
                {laptop.brand}
              </Badge>
            )}
          </div>
          {laptop.image && (
            <div className="ml-3 flex-shrink-0">
              <Image
                src={laptop.image}
                alt={laptop.title}
                width={80}
                height={60}
                className="rounded-md object-cover"
              />
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="flex-1 space-y-4">
        {/* Price */}
        {laptop.price?.current && (
          <div className="flex items-center justify-between">
            <span className="text-2xl font-bold text-primary">
              {formatPrice(laptop.price.current, laptop.price.currency)}
            </span>
            {laptop.price.original && laptop.price.original > laptop.price.current && (
              <span className="text-sm text-muted-foreground line-through">
                {formatPrice(laptop.price.original, laptop.price.currency)}
              </span>
            )}
          </div>
        )}

        {/* Specifications */}
        {laptop.specifications && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-muted-foreground">Key Specs</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              {laptop.specifications.ram && (
                <div>
                  <span className="font-medium">RAM:</span> {laptop.specifications.ram}
                </div>
              )}
              {laptop.specifications.cpu && (
                <div className="col-span-2">
                  <span className="font-medium">CPU:</span> {laptop.specifications.cpu}
                </div>
              )}
              {laptop.specifications.gpu && (
                <div className="col-span-2">
                  <span className="font-medium">GPU:</span> {laptop.specifications.gpu}
                </div>
              )}
              {laptop.specifications.storage && (
                <div>
                  <span className="font-medium">Storage:</span> {laptop.specifications.storage}
                </div>
              )}
            </div>
          </div>
        )}

        {/* LLM Compatibility */}
        {showCompatibility && compatibilityScore && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-muted-foreground">LLM Compatibility</h4>
            
            {/* Overall Score */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Overall Score</span>
                <span className={`text-sm font-bold ${getCompatibilityColor(compatibilityScore.overall)}`}>
                  {compatibilityScore.overall}% ({getCompatibilityLabel(compatibilityScore.overall)})
                </span>
              </div>
              <Progress value={compatibilityScore.overall} className="h-2" />
            </div>

            {/* Breakdown */}
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex justify-between">
                <span>Memory:</span>
                <span className={getCompatibilityColor(compatibilityScore.breakdown.memory)}>
                  {compatibilityScore.breakdown.memory}%
                </span>
              </div>
              <div className="flex justify-between">
                <span>Processing:</span>
                <span className={getCompatibilityColor(compatibilityScore.breakdown.processing)}>
                  {compatibilityScore.breakdown.processing}%
                </span>
              </div>
              <div className="flex justify-between">
                <span>Graphics:</span>
                <span className={getCompatibilityColor(compatibilityScore.breakdown.graphics)}>
                  {compatibilityScore.breakdown.graphics}%
                </span>
              </div>
              <div className="flex justify-between">
                <span>Storage:</span>
                <span className={getCompatibilityColor(compatibilityScore.breakdown.storage)}>
                  {compatibilityScore.breakdown.storage}%
                </span>
              </div>
            </div>

            {/* Performance Estimate */}
            {compatibilityScore.estimatedPerformance && (
              <div className="text-xs text-muted-foreground">
                Est. {compatibilityScore.estimatedPerformance.tokensPerSecond} tokens/sec
              </div>
            )}
          </div>
        )}

        {/* Warnings */}
        {compatibilityScore?.warnings && compatibilityScore.warnings.length > 0 && (
          <div className="space-y-1">
            <h5 className="text-xs font-medium text-orange-600">Considerations</h5>
            <ul className="text-xs text-orange-600 space-y-1">
              {compatibilityScore.warnings.slice(0, 2).map((warning, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-1">•</span>
                  <span className="line-clamp-2">{warning}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-3 space-y-2">
        <div className="flex w-full gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleToggleFavorite}
            className="px-3"
          >
            {isFavorite ? (
              <Heart className="h-4 w-4 fill-current text-red-500" />
            ) : (
              <HeartOff className="h-4 w-4" />
            )}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleCompare}
            disabled={!isInComparison && comparison.isFull}
            className="px-3"
          >
            <GitCompare className={`h-4 w-4 ${isInComparison ? 'text-blue-500' : ''}`} />
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="flex-1"
            onClick={() => onViewDetails?.(laptop)}
          >
            <Eye className="h-4 w-4 mr-2" />
            Details
          </Button>
        </div>
        
        {laptop.url && (
          <Button asChild size="sm" className="w-full">
            <Link href={laptop.url} target="_blank" rel="noopener noreferrer">
              View on {laptop.source}
            </Link>
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}

// Skeleton component for loading state
export function LaptopCardSkeleton() {
  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-2">
            <div className="h-5 bg-muted rounded w-3/4 animate-pulse" />
            <div className="h-4 bg-muted rounded w-1/4 animate-pulse" />
          </div>
          <div className="ml-3 w-20 h-15 bg-muted rounded animate-pulse" />
        </div>
      </CardHeader>

      <CardContent className="flex-1 space-y-4">
        <div className="h-8 bg-muted rounded w-1/3 animate-pulse" />
        
        <div className="space-y-2">
          <div className="h-4 bg-muted rounded w-1/4 animate-pulse" />
          <div className="grid grid-cols-2 gap-2">
            <div className="h-3 bg-muted rounded animate-pulse" />
            <div className="h-3 bg-muted rounded animate-pulse" />
            <div className="h-3 bg-muted rounded col-span-2 animate-pulse" />
            <div className="h-3 bg-muted rounded col-span-2 animate-pulse" />
          </div>
        </div>

        <div className="space-y-3">
          <div className="h-4 bg-muted rounded w-1/3 animate-pulse" />
          <div className="space-y-2">
            <div className="flex justify-between">
              <div className="h-3 bg-muted rounded w-1/4 animate-pulse" />
              <div className="h-3 bg-muted rounded w-1/6 animate-pulse" />
            </div>
            <div className="h-2 bg-muted rounded animate-pulse" />
          </div>
        </div>
      </CardContent>

      <CardFooter className="pt-3 space-y-2">
        <div className="flex w-full gap-2">
          <div className="h-8 bg-muted rounded flex-1 animate-pulse" />
          <div className="h-8 bg-muted rounded flex-1 animate-pulse" />
        </div>
        <div className="h-8 bg-muted rounded w-full animate-pulse" />
      </CardFooter>
    </Card>
  )
}
