// Main Layout Component - Primary application layout with navigation

'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import {
  Laptop,
  Search,
  Settings,
  Menu,
  Home,
  BookOpen,
  BarChart3,
  Github,
  ExternalLink,
  Shield,
  Tag
} from 'lucide-react'

interface MainLayoutProps {
  children: React.ReactNode
}

const navigation = [
  {
    name: 'Home',
    href: '/',
    icon: Home,
    description: 'Find the perfect laptop for LLM applications'
  },
  {
    name: 'Search',
    href: '/search',
    icon: Search,
    description: 'Advanced laptop search and filtering'
  },
  {
    name: 'Deals',
    href: '/deals',
    icon: Tag,
    description: 'Best laptop deals with real-time price tracking'
  },
  {
    name: 'Compare',
    href: '/compare',
    icon: BarChart3,
    description: 'Compare laptops side by side'
  },
  {
    name: 'Guide',
    href: '/guide',
    icon: BookOpen,
    description: 'Learn about LLM hardware requirements'
  },
]

export function MainLayout({ children }: MainLayoutProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const pathname = usePathname()

  const isActivePath = (href: string) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="flex items-center justify-center w-8 h-8 bg-primary rounded-md">
              <Laptop className="h-5 w-5 text-primary-foreground" />
            </div>
            <div className="flex flex-col">
              <span className="font-bold text-lg leading-none">LaptopLLM</span>
              <span className="text-xs text-muted-foreground leading-none">Finder</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6 ml-8">
            {navigation.map((item) => {
              const Icon = item.icon
              const isActive = isActivePath(item.href)
              
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center space-x-2 text-sm font-medium transition-colors hover:text-primary ${
                    isActive 
                      ? 'text-primary' 
                      : 'text-muted-foreground'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.name}</span>
                </Link>
              )
            })}
          </nav>

          {/* Right side */}
          <div className="ml-auto flex items-center space-x-4">
            {/* Beta Badge */}
            <Badge variant="secondary" className="hidden sm:inline-flex">
              Beta
            </Badge>

            {/* Admin Dashboard Link */}
            <Link href="/admin">
              <button className="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5">
                <Shield className="h-4 w-4" />
                <span className="hidden sm:inline">Admin</span>
              </button>
            </Link>

            {/* GitHub Link */}
            <Button variant="ghost" size="sm" asChild>
              <Link
                href="https://github.com/almarales/lexical-analyzer-ui"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Github className="h-4 w-4" />
                <span className="sr-only">GitHub</span>
              </Link>
            </Button>

            {/* Settings */}
            <Button variant="ghost" size="sm">
              <Settings className="h-4 w-4" />
              <span className="sr-only">Settings</span>
            </Button>

            {/* Mobile Menu */}
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="md:hidden">
                  <Menu className="h-4 w-4" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80">
                <div className="flex flex-col space-y-4 mt-6">
                  <div className="flex items-center space-x-2 px-2">
                    <Laptop className="h-6 w-6 text-primary" />
                    <div>
                      <div className="font-bold">LaptopLLM Finder</div>
                      <div className="text-xs text-muted-foreground">
                        Find the perfect laptop for LLM applications
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <nav className="flex flex-col space-y-2">
                    {navigation.map((item) => {
                      const Icon = item.icon
                      const isActive = isActivePath(item.href)

                      return (
                        <Link
                          key={item.name}
                          href={item.href}
                          onClick={() => setIsMobileMenuOpen(false)}
                          className={`flex items-center space-x-3 px-2 py-3 rounded-md transition-colors ${
                            isActive
                              ? 'bg-primary/10 text-primary'
                              : 'text-muted-foreground hover:text-primary hover:bg-muted'
                          }`}
                        >
                          <Icon className="h-5 w-5" />
                          <div className="flex flex-col">
                            <span className="font-medium">{item.name}</span>
                            <span className="text-xs text-muted-foreground">
                              {item.description}
                            </span>
                          </div>
                        </Link>
                      )
                    })}

                    {/* Admin Dashboard Link for Mobile */}
                    <Link
                      href="/admin"
                      onClick={() => setIsMobileMenuOpen(false)}
                      className={`flex items-center space-x-3 px-2 py-3 rounded-md transition-colors ${
                        isActivePath('/admin')
                          ? 'bg-primary/10 text-primary'
                          : 'text-muted-foreground hover:text-primary hover:bg-muted'
                      }`}
                    >
                      <Shield className="h-5 w-5" />
                      <div className="flex flex-col">
                        <span className="font-medium">Admin Dashboard</span>
                        <span className="text-xs text-muted-foreground">
                          System administration and monitoring
                        </span>
                      </div>
                    </Link>
                  </nav>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      <footer className="border-t bg-muted/50">
        <div className="container py-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* About */}
            <div className="space-y-3">
              <h3 className="text-sm font-semibold">About LaptopLLM Finder</h3>
              <p className="text-sm text-muted-foreground">
                Find the perfect laptop for running Large Language Models locally. 
                Compare specifications, compatibility scores, and get personalized recommendations.
              </p>
            </div>

            {/* Quick Links */}
            <div className="space-y-3">
              <h3 className="text-sm font-semibold">Quick Links</h3>
              <ul className="space-y-2 text-sm">
                {navigation.map((item) => (
                  <li key={item.name}>
                    <Link 
                      href={item.href}
                      className="text-muted-foreground hover:text-primary transition-colors"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Resources */}
            <div className="space-y-3">
              <h3 className="text-sm font-semibold">Resources</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link 
                    href="/guide/requirements"
                    className="text-muted-foreground hover:text-primary transition-colors"
                  >
                    Hardware Requirements
                  </Link>
                </li>
                <li>
                  <Link 
                    href="/guide/models"
                    className="text-muted-foreground hover:text-primary transition-colors"
                  >
                    LLM Models Guide
                  </Link>
                </li>
                <li>
                  <Link 
                    href="/guide/setup"
                    className="text-muted-foreground hover:text-primary transition-colors"
                  >
                    Setup Instructions
                  </Link>
                </li>
                <li>
                  <Link 
                    href="/api/docs"
                    className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-1"
                  >
                    API Documentation
                    <ExternalLink className="h-3 w-3" />
                  </Link>
                </li>
              </ul>
            </div>

            {/* Contact */}
            <div className="space-y-3">
              <h3 className="text-sm font-semibold">Connect</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link 
                    href="https://github.com/almarales/lexical-analyzer-ui"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-1"
                  >
                    <Github className="h-4 w-4" />
                    GitHub Repository
                  </Link>
                </li>
                <li>
                  <Link 
                    href="https://github.com/almarales/lexical-analyzer-ui/issues"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary transition-colors"
                  >
                    Report Issues
                  </Link>
                </li>
                <li>
                  <Link 
                    href="https://github.com/almarales/lexical-analyzer-ui/discussions"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary transition-colors"
                  >
                    Discussions
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <Separator className="my-6" />

          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div className="text-sm text-muted-foreground">
              © 2024 LaptopLLM Finder. Built with Next.js and Tailwind CSS.
            </div>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <Link href="/privacy" className="hover:text-primary transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="hover:text-primary transition-colors">
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
