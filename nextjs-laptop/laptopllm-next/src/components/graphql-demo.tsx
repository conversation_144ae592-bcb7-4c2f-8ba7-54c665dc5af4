/**
 * GraphQL Demo Component
 * Demonstrates GraphQL integration with various queries
 */

'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  useHealthCheck,
  useGetLaptopAnalytics,
  useGetHotDeals,
  useGetAvailableBrands,
  useGetPriceRange,
  useDebouncedLaptopSearch
} from '@/lib/graphql/hooks'
import { AlertCircle, Search, TrendingUp, Tag, DollarSign } from 'lucide-react'

export function GraphQLDemo() {
  const [searchQuery, setSearchQuery] = useState('')

  // GraphQL queries
  const { data: healthData, loading: healthLoading } = useHealthCheck()
  const { data: analyticsData, loading: analyticsLoading } = useGetLaptopAnalytics()
  const { data: dealsData, loading: dealsLoading } = useGetHotDeals(5)
  const { data: brandsData, loading: brandsLoading } = useGetAvailableBrands()
  const { data: priceRangeData, loading: priceRangeLoading } = useGetPriceRange()
  
  // Debounced search
  const { 
    data: searchData, 
    loading: searchLoading, 
    error: searchError 
  } = useDebouncedLaptopSearch(searchQuery)

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">GraphQL API Demo</h1>
        <p className="text-muted-foreground">
          Demonstrating GraphQL integration for LaptopLLM Finder
        </p>
      </div>

      {/* Health Check */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            API Health Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          {healthLoading ? (
            <Skeleton className="h-4 w-32" />
          ) : (
            <Badge variant={healthData?.health ? 'default' : 'destructive'}>
              {healthData?.health || 'API Unavailable'}
            </Badge>
          )}
        </CardContent>
      </Card>

      <Tabs defaultValue="search" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="search">Search</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="deals">Hot Deals</TabsTrigger>
          <TabsTrigger value="brands">Brands</TabsTrigger>
          <TabsTrigger value="pricing">Pricing</TabsTrigger>
        </TabsList>

        {/* Search Tab */}
        <TabsContent value="search" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Laptop Search
              </CardTitle>
              <CardDescription>
                Search laptops using GraphQL with real-time results
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                placeholder="Search for laptops..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              
              {searchError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Error searching laptops: {searchError.message}
                  </AlertDescription>
                </Alert>
              )}

              {searchLoading && (
                <div className="space-y-2">
                  {[...Array(3)].map((_, i) => (
                    <Skeleton key={i} className="h-20 w-full" />
                  ))}
                </div>
              )}

              {searchData?.laptops.edges.length > 0 && (
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Found {searchData.laptops.totalCount} laptops
                  </p>
                  {searchData.laptops.edges.slice(0, 5).map(({ node: laptop }) => (
                    <Card key={laptop.id} className="p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-semibold">{laptop.title}</h3>
                          <p className="text-sm text-muted-foreground">
                            {laptop.brand} • {laptop.model}
                          </p>
                          {laptop.compatibility && (
                            <Badge variant="outline" className="mt-1">
                              Compatibility: {laptop.compatibility.averageScore}/100
                            </Badge>
                          )}
                        </div>
                        <div className="text-right">
                          {laptop.price && (
                            <p className="font-semibold">
                              ${laptop.price.toLocaleString()} {laptop.currency}
                            </p>
                          )}
                          {laptop.rating && (
                            <p className="text-sm text-muted-foreground">
                              ⭐ {laptop.rating}/5 ({laptop.reviewCount} reviews)
                            </p>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}

              {searchQuery && !searchLoading && searchData?.laptops.edges.length === 0 && (
                <p className="text-center text-muted-foreground py-8">
                  No laptops found for "{searchQuery}"
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Laptop Analytics
              </CardTitle>
              <CardDescription>
                Overview of laptop database statistics
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analyticsLoading ? (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {[...Array(4)].map((_, i) => (
                    <Skeleton key={i} className="h-20" />
                  ))}
                </div>
              ) : analyticsData ? (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold">{analyticsData.laptopAnalytics.totalLaptops}</p>
                    <p className="text-sm text-muted-foreground">Total Laptops</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">
                      ${analyticsData.laptopAnalytics.averagePrice.toFixed(0)}
                    </p>
                    <p className="text-sm text-muted-foreground">Average Price</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">
                      {analyticsData.laptopAnalytics.compatibilityStats.averageScore.toFixed(1)}
                    </p>
                    <p className="text-sm text-muted-foreground">Avg Compatibility</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">
                      {analyticsData.laptopAnalytics.brandDistribution.length}
                    </p>
                    <p className="text-sm text-muted-foreground">Brands</p>
                  </div>
                </div>
              ) : (
                <p className="text-center text-muted-foreground">No analytics data available</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Hot Deals Tab */}
        <TabsContent value="deals" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Hot Deals
              </CardTitle>
              <CardDescription>
                Current active deals and discounts
              </CardDescription>
            </CardHeader>
            <CardContent>
              {dealsLoading ? (
                <div className="space-y-2">
                  {[...Array(3)].map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full" />
                  ))}
                </div>
              ) : dealsData?.hotDeals.length > 0 ? (
                <div className="space-y-2">
                  {dealsData.hotDeals.map((deal) => (
                    <Card key={deal.id} className="p-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="font-semibold">{deal.title}</h3>
                          <p className="text-sm text-muted-foreground">{deal.store}</p>
                        </div>
                        <div className="text-right">
                          <Badge variant="destructive">
                            -{deal.discountPercentage.toFixed(0)}%
                          </Badge>
                          <p className="text-sm">
                            <span className="line-through text-muted-foreground">
                              ${deal.originalPrice}
                            </span>
                            {' '}
                            <span className="font-semibold text-green-600">
                              ${deal.discountedPrice}
                            </span>
                          </p>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <p className="text-center text-muted-foreground">No active deals found</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Brands Tab */}
        <TabsContent value="brands" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Tag className="h-5 w-5" />
                Available Brands
              </CardTitle>
              <CardDescription>
                All laptop brands in the database
              </CardDescription>
            </CardHeader>
            <CardContent>
              {brandsLoading ? (
                <div className="flex flex-wrap gap-2">
                  {[...Array(8)].map((_, i) => (
                    <Skeleton key={i} className="h-6 w-16" />
                  ))}
                </div>
              ) : brandsData?.availableBrands.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {brandsData.availableBrands.map((brand) => (
                    <Badge key={brand} variant="outline">
                      {brand}
                    </Badge>
                  ))}
                </div>
              ) : (
                <p className="text-center text-muted-foreground">No brands available</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Pricing Tab */}
        <TabsContent value="pricing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Price Range
              </CardTitle>
              <CardDescription>
                Price statistics across all laptops
              </CardDescription>
            </CardHeader>
            <CardContent>
              {priceRangeLoading ? (
                <div className="grid grid-cols-3 gap-4">
                  {[...Array(3)].map((_, i) => (
                    <Skeleton key={i} className="h-16" />
                  ))}
                </div>
              ) : priceRangeData?.priceRange ? (
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold">
                      ${priceRangeData.priceRange.min.toLocaleString()}
                    </p>
                    <p className="text-sm text-muted-foreground">Minimum Price</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">
                      ${priceRangeData.priceRange.average.toLocaleString()}
                    </p>
                    <p className="text-sm text-muted-foreground">Average Price</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">
                      ${priceRangeData.priceRange.max.toLocaleString()}
                    </p>
                    <p className="text-sm text-muted-foreground">Maximum Price</p>
                  </div>
                </div>
              ) : (
                <p className="text-center text-muted-foreground">No pricing data available</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
