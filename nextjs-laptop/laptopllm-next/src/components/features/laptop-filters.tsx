// Laptop Filters Component - Advanced filtering interface for laptop search

'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { ChevronDown, X, Filter, RotateCcw } from 'lucide-react'
import type { RecommendationCriteria } from '@/lib/llm/recommendation.service'

interface LaptopFiltersProps {
  criteria: RecommendationCriteria
  onCriteriaChange: (criteria: RecommendationCriteria) => void
  onApplyFilters: () => void
  onResetFilters: () => void
  isLoading?: boolean
  className?: string
}

const LAPTOP_BRANDS = [
  'ASUS', 'Dell', 'HP', 'Lenovo', 'Apple', 'MSI', 'Acer', 'Razer', 
  'Alienware', 'Samsung', 'LG', 'Microsoft', 'Framework'
]

const LLM_MODELS = [
  { id: 'llama2-7b', name: 'Llama 2 7B' },
  { id: 'llama2-13b', name: 'Llama 2 13B' },
  { id: 'llama2-70b', name: 'Llama 2 70B' },
  { id: 'codellama-7b', name: 'Code Llama 7B' },
  { id: 'codellama-13b', name: 'Code Llama 13B' },
  { id: 'mistral-7b', name: 'Mistral 7B' },
  { id: 'mixtral-8x7b', name: 'Mixtral 8x7B' },
  { id: 'phi3-mini', name: 'Phi-3 Mini' },
  { id: 'phi3-medium', name: 'Phi-3 Medium' },
]

export function LaptopFilters({
  criteria,
  onCriteriaChange,
  onApplyFilters,
  onResetFilters,
  isLoading = false,
  className = '',
}: LaptopFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [budgetOpen, setBudgetOpen] = useState(true)
  const [specsOpen, setSpecsOpen] = useState(false)
  const [modelsOpen, setModelsOpen] = useState(false)
  const [advancedOpen, setAdvancedOpen] = useState(false)

  const updateCriteria = (updates: Partial<RecommendationCriteria>) => {
    onCriteriaChange({ ...criteria, ...updates })
  }

  const updateBudget = (field: keyof NonNullable<RecommendationCriteria['budget']>, value: number | string) => {
    updateCriteria({
      budget: {
        ...criteria.budget,
        [field]: value,
      }
    })
  }

  const toggleBrand = (brand: string) => {
    const currentBrands = criteria.brands || []
    const newBrands = currentBrands.includes(brand)
      ? currentBrands.filter(b => b !== brand)
      : [...currentBrands, brand]
    
    updateCriteria({ brands: newBrands.length > 0 ? newBrands : undefined })
  }

  const toggleModel = (modelId: string) => {
    const currentModels = criteria.preferredModels || []
    const newModels = currentModels.includes(modelId)
      ? currentModels.filter(m => m !== modelId)
      : [...currentModels, modelId]
    
    updateCriteria({ preferredModels: newModels.length > 0 ? newModels : undefined })
  }

  const hasActiveFilters = () => {
    return !!(
      criteria.budget?.min ||
      criteria.budget?.max ||
      criteria.brands?.length ||
      criteria.preferredModels?.length ||
      criteria.usageType ||
      criteria.performance ||
      criteria.portability ||
      criteria.minScreenSize ||
      criteria.maxScreenSize
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
            {hasActiveFilters() && (
              <Badge variant="secondary" className="ml-2">
                Active
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onResetFilters}
              disabled={!hasActiveFilters() || isLoading}
            >
              <RotateCcw className="h-4 w-4 mr-1" />
              Reset
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <ChevronDown className={`h-4 w-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Budget Section */}
        <Collapsible open={budgetOpen} onOpenChange={setBudgetOpen}>
          <CollapsibleTrigger className="flex items-center justify-between w-full p-2 hover:bg-muted rounded-md">
            <Label className="text-sm font-medium">Budget Range</Label>
            <ChevronDown className={`h-4 w-4 transition-transform ${budgetOpen ? 'rotate-180' : ''}`} />
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 pt-2">
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label htmlFor="min-budget" className="text-xs">Minimum ($)</Label>
                <Input
                  id="min-budget"
                  type="number"
                  placeholder="500"
                  value={criteria.budget?.min || ''}
                  onChange={(e) => updateBudget('min', parseInt(e.target.value) || undefined)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="max-budget" className="text-xs">Maximum ($)</Label>
                <Input
                  id="max-budget"
                  type="number"
                  placeholder="3000"
                  value={criteria.budget?.max || ''}
                  onChange={(e) => updateBudget('max', parseInt(e.target.value) || undefined)}
                />
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        <Separator />

        {/* Quick Filters */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Usage Type</Label>
          <Select
            value={criteria.usageType || ''}
            onValueChange={(value) => updateCriteria({ usageType: value as any || undefined })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select usage type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Any</SelectItem>
              <SelectItem value="development">Development</SelectItem>
              <SelectItem value="research">Research</SelectItem>
              <SelectItem value="production">Production</SelectItem>
              <SelectItem value="education">Education</SelectItem>
              <SelectItem value="hobby">Hobby</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-2">
            <Label className="text-sm font-medium">Performance</Label>
            <Select
              value={criteria.performance || ''}
              onValueChange={(value) => updateCriteria({ performance: value as any || undefined })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Any" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Any</SelectItem>
                <SelectItem value="basic">Basic</SelectItem>
                <SelectItem value="good">Good</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="extreme">Extreme</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium">Portability</Label>
            <Select
              value={criteria.portability || ''}
              onValueChange={(value) => updateCriteria({ portability: value as any || undefined })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Any" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Any</SelectItem>
                <SelectItem value="ultraportable">Ultraportable</SelectItem>
                <SelectItem value="portable">Portable</SelectItem>
                <SelectItem value="desktop_replacement">Desktop Replacement</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {isExpanded && (
          <>
            <Separator />

            {/* Brands Section */}
            <Collapsible open={specsOpen} onOpenChange={setSpecsOpen}>
              <CollapsibleTrigger className="flex items-center justify-between w-full p-2 hover:bg-muted rounded-md">
                <Label className="text-sm font-medium">
                  Preferred Brands
                  {criteria.brands && criteria.brands.length > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {criteria.brands.length}
                    </Badge>
                  )}
                </Label>
                <ChevronDown className={`h-4 w-4 transition-transform ${specsOpen ? 'rotate-180' : ''}`} />
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-3 pt-2">
                <div className="flex flex-wrap gap-2">
                  {LAPTOP_BRANDS.map((brand) => (
                    <Badge
                      key={brand}
                      variant={criteria.brands?.includes(brand) ? 'default' : 'outline'}
                      className="cursor-pointer hover:bg-primary/80"
                      onClick={() => toggleBrand(brand)}
                    >
                      {brand}
                      {criteria.brands?.includes(brand) && (
                        <X className="h-3 w-3 ml-1" />
                      )}
                    </Badge>
                  ))}
                </div>
              </CollapsibleContent>
            </Collapsible>

            <Separator />

            {/* LLM Models Section */}
            <Collapsible open={modelsOpen} onOpenChange={setModelsOpen}>
              <CollapsibleTrigger className="flex items-center justify-between w-full p-2 hover:bg-muted rounded-md">
                <Label className="text-sm font-medium">
                  Preferred LLM Models
                  {criteria.preferredModels && criteria.preferredModels.length > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {criteria.preferredModels.length}
                    </Badge>
                  )}
                </Label>
                <ChevronDown className={`h-4 w-4 transition-transform ${modelsOpen ? 'rotate-180' : ''}`} />
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-3 pt-2">
                <div className="space-y-2">
                  {LLM_MODELS.map((model) => (
                    <div key={model.id} className="flex items-center space-x-2">
                      <Switch
                        id={model.id}
                        checked={criteria.preferredModels?.includes(model.id) || false}
                        onCheckedChange={() => toggleModel(model.id)}
                      />
                      <Label htmlFor={model.id} className="text-sm cursor-pointer">
                        {model.name}
                      </Label>
                    </div>
                  ))}
                </div>
              </CollapsibleContent>
            </Collapsible>

            <Separator />

            {/* Advanced Filters */}
            <Collapsible open={advancedOpen} onOpenChange={setAdvancedOpen}>
              <CollapsibleTrigger className="flex items-center justify-between w-full p-2 hover:bg-muted rounded-md">
                <Label className="text-sm font-medium">Advanced Filters</Label>
                <ChevronDown className={`h-4 w-4 transition-transform ${advancedOpen ? 'rotate-180' : ''}`} />
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-4 pt-2">
                {/* Screen Size */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Screen Size (inches)</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-2">
                      <Label htmlFor="min-screen" className="text-xs">Minimum</Label>
                      <Input
                        id="min-screen"
                        type="number"
                        step="0.1"
                        placeholder="13"
                        value={criteria.minScreenSize || ''}
                        onChange={(e) => updateCriteria({ minScreenSize: parseFloat(e.target.value) || undefined })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="max-screen" className="text-xs">Maximum</Label>
                      <Input
                        id="max-screen"
                        type="number"
                        step="0.1"
                        placeholder="17"
                        value={criteria.maxScreenSize || ''}
                        onChange={(e) => updateCriteria({ maxScreenSize: parseFloat(e.target.value) || undefined })}
                      />
                    </div>
                  </div>
                </div>

                {/* Battery Life */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Battery Life</Label>
                  <Select
                    value={criteria.batteryLife || ''}
                    onValueChange={(value) => updateCriteria({ batteryLife: value as any || undefined })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Any" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Any</SelectItem>
                      <SelectItem value="short">Short (4h)</SelectItem>
                      <SelectItem value="medium">Medium (8h)</SelectItem>
                      <SelectItem value="long">Long (12h+)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CollapsibleContent>
            </Collapsible>
          </>
        )}

        <Separator />

        {/* Apply Button */}
        <Button 
          onClick={onApplyFilters} 
          className="w-full"
          disabled={isLoading}
        >
          {isLoading ? 'Applying Filters...' : 'Apply Filters'}
        </Button>
      </CardContent>
    </Card>
  )
}
