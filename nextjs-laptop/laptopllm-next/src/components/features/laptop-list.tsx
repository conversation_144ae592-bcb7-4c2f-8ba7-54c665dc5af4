// Laptop List Component - Displays laptops with sorting and pagination

'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { LaptopCard, LaptopCardSkeleton } from '@/components/ui/laptop-card'
import { 
  ChevronLeft, 
  ChevronRight, 
  Grid3X3, 
  List, 
  SortAsc, 
  SortDesc,
  Laptop,
  AlertCircle
} from 'lucide-react'
import type { LaptopData } from '@/lib/scraping/firecrawl.service'
import type { LaptopRecommendation } from '@/lib/llm/recommendation.service'

interface LaptopListProps {
  recommendations?: LaptopRecommendation[]
  laptops?: LaptopData[]
  isLoading?: boolean
  error?: string
  onViewDetails?: (laptop: LaptopData) => void
  onCompare?: (laptop: LaptopData) => void
  className?: string
}

type SortOption = 'relevance' | 'price-low' | 'price-high' | 'compatibility' | 'name'
type ViewMode = 'grid' | 'list'

export function LaptopList({
  recommendations,
  laptops,
  isLoading = false,
  error,
  onViewDetails,
  onCompare,
  className = '',
}: LaptopListProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(12)
  const [sortBy, setSortBy] = useState<SortOption>('relevance')
  const [viewMode, setViewMode] = useState<ViewMode>('grid')

  // Determine data source
  const isRecommendations = !!recommendations
  const items = recommendations || laptops || []
  const totalItems = items.length

  // Sorting logic
  const sortedItems = React.useMemo(() => {
    if (items.length === 0) return []

    const sorted = [...items].sort((a, b) => {
      const laptopA = isRecommendations ? (a as LaptopRecommendation).laptop : (a as LaptopData)
      const laptopB = isRecommendations ? (b as LaptopRecommendation).laptop : (b as LaptopData)

      switch (sortBy) {
        case 'price-low':
          const priceA = laptopA.price?.current || Infinity
          const priceB = laptopB.price?.current || Infinity
          return priceA - priceB

        case 'price-high':
          const priceA2 = laptopA.price?.current || 0
          const priceB2 = laptopB.price?.current || 0
          return priceB2 - priceA2

        case 'compatibility':
          if (isRecommendations) {
            const recA = a as LaptopRecommendation
            const recB = b as LaptopRecommendation
            return recB.overallScore - recA.overallScore
          }
          return 0

        case 'name':
          return laptopA.title.localeCompare(laptopB.title)

        case 'relevance':
        default:
          if (isRecommendations) {
            const recA = a as LaptopRecommendation
            const recB = b as LaptopRecommendation
            return recA.ranking - recB.ranking
          }
          return 0
      }
    })

    return sorted
  }, [items, sortBy, isRecommendations])

  // Pagination
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentItems = sortedItems.slice(startIndex, endIndex)

  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)))
  }

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(parseInt(value))
    setCurrentPage(1)
  }

  // Loading state
  if (isLoading) {
    return (
      <div className={className}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: itemsPerPage }).map((_, index) => (
            <LaptopCardSkeleton key={index} />
          ))}
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Error Loading Laptops</h3>
          <p className="text-muted-foreground text-center max-w-md">
            {error}
          </p>
        </CardContent>
      </Card>
    )
  }

  // Empty state
  if (totalItems === 0) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Laptop className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Laptops Found</h3>
          <p className="text-muted-foreground text-center max-w-md">
            Try adjusting your filters or search criteria to find more results.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={className}>
      {/* Header with controls */}
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle className="text-lg">
                {isRecommendations ? 'Recommended Laptops' : 'Laptops'}
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Showing {startIndex + 1}-{Math.min(endIndex, totalItems)} of {totalItems} results
              </p>
            </div>

            <div className="flex items-center gap-3">
              {/* Sort */}
              <div className="flex items-center gap-2">
                <Select value={sortBy} onValueChange={(value) => setSortBy(value as SortOption)}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="relevance">
                      {isRecommendations ? 'Best Match' : 'Relevance'}
                    </SelectItem>
                    <SelectItem value="price-low">Price: Low to High</SelectItem>
                    <SelectItem value="price-high">Price: High to Low</SelectItem>
                    {isRecommendations && (
                      <SelectItem value="compatibility">Compatibility Score</SelectItem>
                    )}
                    <SelectItem value="name">Name A-Z</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* View Mode */}
              <div className="flex items-center border rounded-md">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-r-none"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-l-none"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Results */}
      <div className={
        viewMode === 'grid' 
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
          : 'space-y-4'
      }>
        {currentItems.map((item, index) => {
          const laptop = isRecommendations ? (item as LaptopRecommendation).laptop : (item as LaptopData)
          const recommendation = isRecommendations ? (item as LaptopRecommendation) : undefined
          
          // For recommendations, create a compatibility score from the first model
          const compatibilityScore = recommendation?.compatibilityScores.values().next().value

          return (
            <div key={`${laptop.url}-${index}`} className="relative">
              {/* Ranking badge for recommendations */}
              {recommendation && (
                <Badge 
                  variant="secondary" 
                  className="absolute top-2 left-2 z-10"
                >
                  #{recommendation.ranking}
                </Badge>
              )}
              
              <LaptopCard
                laptop={laptop}
                compatibilityScore={compatibilityScore}
                showCompatibility={isRecommendations}
                onViewDetails={onViewDetails}
                onCompare={onCompare}
                className={viewMode === 'list' ? 'flex-row' : ''}
              />
            </div>
          )
        })}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card className="mt-6">
          <CardContent className="py-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              {/* Items per page */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Show:</span>
                <Select value={itemsPerPage.toString()} onValueChange={handleItemsPerPageChange}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="6">6</SelectItem>
                    <SelectItem value="12">12</SelectItem>
                    <SelectItem value="24">24</SelectItem>
                    <SelectItem value="48">48</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm text-muted-foreground">per page</span>
              </div>

              {/* Page navigation */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => goToPage(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>

                <div className="flex items-center gap-1">
                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum
                    if (totalPages <= 5) {
                      pageNum = i + 1
                    } else if (currentPage <= 3) {
                      pageNum = i + 1
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i
                    } else {
                      pageNum = currentPage - 2 + i
                    }

                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => goToPage(pageNum)}
                        className="w-10"
                      >
                        {pageNum}
                      </Button>
                    )
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => goToPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>

              {/* Page info */}
              <div className="text-sm text-muted-foreground">
                Page {currentPage} of {totalPages}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
