// Global application context for shared state management

'use client'

import React, { createContext, useContext, useReducer, useEffect } from 'react'
import type { LaptopData } from '@/lib/scraping/firecrawl.service'
import type { RecommendationCriteria } from '@/lib/llm/recommendation.service'

// Types
export interface ComparisonItem {
  laptop: LaptopData
  addedAt: Date
}

export interface AppState {
  // Comparison state
  comparisonList: ComparisonItem[]
  maxComparisons: number
  
  // Favorites state
  favorites: string[] // laptop URLs
  
  // Search state
  recentSearches: string[]
  maxRecentSearches: number
  
  // User preferences
  preferences: {
    defaultCriteria: RecommendationCriteria
    viewMode: 'grid' | 'list'
    sortBy: 'relevance' | 'price' | 'compatibility' | 'name'
    itemsPerPage: number
    currency: 'USD' | 'EUR' | 'GBP'
    theme: 'light' | 'dark' | 'system'
  }
  
  // UI state
  ui: {
    sidebarOpen: boolean
    filtersOpen: boolean
    loading: boolean
    error: string | null
  }
}

// Actions
export type AppAction =
  | { type: 'ADD_TO_COMPARISON'; payload: LaptopData }
  | { type: 'REMOVE_FROM_COMPARISON'; payload: string } // laptop URL
  | { type: 'CLEAR_COMPARISON' }
  | { type: 'ADD_TO_FAVORITES'; payload: string }
  | { type: 'REMOVE_FROM_FAVORITES'; payload: string }
  | { type: 'TOGGLE_FAVORITE'; payload: string }
  | { type: 'ADD_RECENT_SEARCH'; payload: string }
  | { type: 'CLEAR_RECENT_SEARCHES' }
  | { type: 'UPDATE_PREFERENCES'; payload: Partial<AppState['preferences']> }
  | { type: 'SET_VIEW_MODE'; payload: 'grid' | 'list' }
  | { type: 'SET_SORT_BY'; payload: AppState['preferences']['sortBy'] }
  | { type: 'SET_SIDEBAR_OPEN'; payload: boolean }
  | { type: 'SET_FILTERS_OPEN'; payload: boolean }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'RESET_STATE' }

// Initial state
const initialState: AppState = {
  comparisonList: [],
  maxComparisons: 4,
  favorites: [],
  recentSearches: [],
  maxRecentSearches: 10,
  preferences: {
    defaultCriteria: {
      budget: { min: 0, max: 5000 },
      preferredBrands: [],
      llmModels: [],
      usageType: 'development',
      performanceLevel: 'high',
      portability: 'balanced',
    },
    viewMode: 'grid',
    sortBy: 'relevance',
    itemsPerPage: 12,
    currency: 'USD',
    theme: 'system',
  },
  ui: {
    sidebarOpen: false,
    filtersOpen: false,
    loading: false,
    error: null,
  },
}

// Reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'ADD_TO_COMPARISON': {
      const laptop = action.payload
      const exists = state.comparisonList.some(item => item.laptop.url === laptop.url)
      
      if (exists || state.comparisonList.length >= state.maxComparisons) {
        return state
      }
      
      return {
        ...state,
        comparisonList: [
          ...state.comparisonList,
          { laptop, addedAt: new Date() }
        ],
      }
    }
    
    case 'REMOVE_FROM_COMPARISON': {
      return {
        ...state,
        comparisonList: state.comparisonList.filter(
          item => item.laptop.url !== action.payload
        ),
      }
    }
    
    case 'CLEAR_COMPARISON': {
      return {
        ...state,
        comparisonList: [],
      }
    }
    
    case 'ADD_TO_FAVORITES': {
      const url = action.payload
      if (state.favorites.includes(url)) {
        return state
      }
      
      return {
        ...state,
        favorites: [...state.favorites, url],
      }
    }
    
    case 'REMOVE_FROM_FAVORITES': {
      return {
        ...state,
        favorites: state.favorites.filter(url => url !== action.payload),
      }
    }
    
    case 'TOGGLE_FAVORITE': {
      const url = action.payload
      const isFavorite = state.favorites.includes(url)
      
      return {
        ...state,
        favorites: isFavorite
          ? state.favorites.filter(fav => fav !== url)
          : [...state.favorites, url],
      }
    }
    
    case 'ADD_RECENT_SEARCH': {
      const query = action.payload.trim()
      if (!query || state.recentSearches.includes(query)) {
        return state
      }
      
      const newSearches = [query, ...state.recentSearches]
        .slice(0, state.maxRecentSearches)
      
      return {
        ...state,
        recentSearches: newSearches,
      }
    }
    
    case 'CLEAR_RECENT_SEARCHES': {
      return {
        ...state,
        recentSearches: [],
      }
    }
    
    case 'UPDATE_PREFERENCES': {
      return {
        ...state,
        preferences: {
          ...state.preferences,
          ...action.payload,
        },
      }
    }
    
    case 'SET_VIEW_MODE': {
      return {
        ...state,
        preferences: {
          ...state.preferences,
          viewMode: action.payload,
        },
      }
    }
    
    case 'SET_SORT_BY': {
      return {
        ...state,
        preferences: {
          ...state.preferences,
          sortBy: action.payload,
        },
      }
    }
    
    case 'SET_SIDEBAR_OPEN': {
      return {
        ...state,
        ui: {
          ...state.ui,
          sidebarOpen: action.payload,
        },
      }
    }
    
    case 'SET_FILTERS_OPEN': {
      return {
        ...state,
        ui: {
          ...state.ui,
          filtersOpen: action.payload,
        },
      }
    }
    
    case 'SET_LOADING': {
      return {
        ...state,
        ui: {
          ...state.ui,
          loading: action.payload,
        },
      }
    }
    
    case 'SET_ERROR': {
      return {
        ...state,
        ui: {
          ...state.ui,
          error: action.payload,
        },
      }
    }
    
    case 'RESET_STATE': {
      return initialState
    }
    
    default:
      return state
  }
}

// Context
const AppContext = createContext<{
  state: AppState
  dispatch: React.Dispatch<AppAction>
} | null>(null)

// Provider component
export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState)
  
  // Load state from localStorage on mount
  useEffect(() => {
    try {
      const savedState = localStorage.getItem('laptopllm-app-state')
      if (savedState) {
        const parsed = JSON.parse(savedState)
        
        // Restore specific parts of the state
        if (parsed.favorites) {
          parsed.favorites.forEach((url: string) => {
            dispatch({ type: 'ADD_TO_FAVORITES', payload: url })
          })
        }
        
        if (parsed.recentSearches) {
          parsed.recentSearches.forEach((query: string) => {
            dispatch({ type: 'ADD_RECENT_SEARCH', payload: query })
          })
        }
        
        if (parsed.preferences) {
          dispatch({ type: 'UPDATE_PREFERENCES', payload: parsed.preferences })
        }
      }
    } catch (error) {
      console.warn('Failed to load app state from localStorage:', error)
    }
  }, [])
  
  // Save state to localStorage when it changes
  useEffect(() => {
    try {
      const stateToSave = {
        favorites: state.favorites,
        recentSearches: state.recentSearches,
        preferences: state.preferences,
      }
      localStorage.setItem('laptopllm-app-state', JSON.stringify(stateToSave))
    } catch (error) {
      console.warn('Failed to save app state to localStorage:', error)
    }
  }, [state.favorites, state.recentSearches, state.preferences])
  
  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  )
}

// Hook to use the context
export function useAppContext() {
  const context = useContext(AppContext)
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider')
  }
  return context
}

// Convenience hooks
export function useComparison() {
  const { state, dispatch } = useAppContext()
  
  return {
    items: state.comparisonList,
    count: state.comparisonList.length,
    maxItems: state.maxComparisons,
    isFull: state.comparisonList.length >= state.maxComparisons,
    add: (laptop: LaptopData) => dispatch({ type: 'ADD_TO_COMPARISON', payload: laptop }),
    remove: (url: string) => dispatch({ type: 'REMOVE_FROM_COMPARISON', payload: url }),
    clear: () => dispatch({ type: 'CLEAR_COMPARISON' }),
    has: (url: string) => state.comparisonList.some(item => item.laptop.url === url),
  }
}

export function useFavorites() {
  const { state, dispatch } = useAppContext()
  
  return {
    favorites: state.favorites,
    count: state.favorites.length,
    add: (url: string) => dispatch({ type: 'ADD_TO_FAVORITES', payload: url }),
    remove: (url: string) => dispatch({ type: 'REMOVE_FROM_FAVORITES', payload: url }),
    toggle: (url: string) => dispatch({ type: 'TOGGLE_FAVORITE', payload: url }),
    has: (url: string) => state.favorites.includes(url),
  }
}

export function useRecentSearches() {
  const { state, dispatch } = useAppContext()
  
  return {
    searches: state.recentSearches,
    add: (query: string) => dispatch({ type: 'ADD_RECENT_SEARCH', payload: query }),
    clear: () => dispatch({ type: 'CLEAR_RECENT_SEARCHES' }),
  }
}

export function useAppPreferences() {
  const { state, dispatch } = useAppContext()
  
  return {
    preferences: state.preferences,
    update: (prefs: Partial<AppState['preferences']>) => 
      dispatch({ type: 'UPDATE_PREFERENCES', payload: prefs }),
    setViewMode: (mode: 'grid' | 'list') => 
      dispatch({ type: 'SET_VIEW_MODE', payload: mode }),
    setSortBy: (sortBy: AppState['preferences']['sortBy']) => 
      dispatch({ type: 'SET_SORT_BY', payload: sortBy }),
  }
}

export function useAppUI() {
  const { state, dispatch } = useAppContext()
  
  return {
    ui: state.ui,
    setSidebarOpen: (open: boolean) => dispatch({ type: 'SET_SIDEBAR_OPEN', payload: open }),
    setFiltersOpen: (open: boolean) => dispatch({ type: 'SET_FILTERS_OPEN', payload: open }),
    setLoading: (loading: boolean) => dispatch({ type: 'SET_LOADING', payload: loading }),
    setError: (error: string | null) => dispatch({ type: 'SET_ERROR', payload: error }),
  }
}
