import { describe, it, expect, beforeEach } from 'vitest'
import { CompatibilityService } from '@/lib/compatibility/compatibility-service'
import type { LaptopData, LLMModel, CompatibilityScore } from '@/shared/types'

describe('CompatibilityService Integration Tests', () => {
  let compatibilityService: CompatibilityService

  beforeEach(() => {
    compatibilityService = new CompatibilityService()
  })

  describe('Compatibility Score Calculation', () => {
    it('should calculate high compatibility for powerful gaming laptop', async () => {
      const laptop: LaptopData = {
        url: 'test-laptop-1',
        title: 'ASUS ROG Strix G15',
        brand: 'ASUS',
        model: 'ROG Strix G15',
        price: 1299,
        currency: 'USD',
        specifications: {
          cpu: {
            brand: 'AMD',
            model: 'Ryzen 7 6800H',
            cores: 8,
            threads: 16,
            baseFrequency: 3.2,
            boostFrequency: 4.7,
            architecture: 'Zen 3+',
            tdp: 45,
          },
          gpu: {
            brand: 'NVIDIA',
            model: 'GeForce RTX 3060',
            memory: 6,
            memoryType: 'GDDR6',
            tdp: 115,
          },
          memory: {
            size: 16,
            type: 'DDR4',
            speed: 3200,
            slots: 2,
            maxCapacity: 32,
          },
          storage: {
            type: 'SSD',
            capacity: 512,
            interface: 'NVMe PCIe 3.0',
          },
          display: {
            size: 15.6,
            resolution: '1920x1080',
            refreshRate: 144,
            panelType: 'IPS',
          },
        },
        features: {
          category: 'Gaming',
          portability: 'Medium',
          batteryLife: 6,
          weight: 2.3,
          thermalDesign: 'Advanced',
        },
        availability: {
          inStock: true,
          stores: ['Amazon', 'Best Buy'],
        },
        images: ['image1.jpg'],
        scrapedAt: new Date(),
        lastUpdated: new Date(),
      }

      const model: LLMModel = {
        name: 'Llama 2 7B',
        size: '7B',
        memoryRequirement: 8,
        recommendedMemory: 16,
        computeRequirement: 'Medium',
        framework: 'PyTorch',
      }

      const score = await compatibilityService.calculateCompatibility(laptop, model)

      expect(score.overall).toBeGreaterThan(75)
      expect(score.performance).toBeGreaterThan(70)
      expect(score.memory).toBeGreaterThan(80)
      expect(score.efficiency).toBeGreaterThan(60)
      expect(score.recommendation).toBe('Excellent')
    })

    it('should calculate medium compatibility for business laptop', async () => {
      const laptop: LaptopData = {
        url: 'test-laptop-2',
        title: 'Dell XPS 15 9520',
        brand: 'Dell',
        model: 'XPS 15 9520',
        price: 1599,
        currency: 'USD',
        specifications: {
          cpu: {
            brand: 'Intel',
            model: 'Core i7-12700H',
            cores: 14,
            threads: 20,
            baseFrequency: 2.3,
            boostFrequency: 4.7,
            architecture: 'Alder Lake',
            tdp: 45,
          },
          gpu: {
            brand: 'NVIDIA',
            model: 'GeForce RTX 3050 Ti',
            memory: 4,
            memoryType: 'GDDR6',
            tdp: 60,
          },
          memory: {
            size: 16,
            type: 'DDR5',
            speed: 4800,
            slots: 2,
            maxCapacity: 64,
          },
          storage: {
            type: 'SSD',
            capacity: 512,
            interface: 'NVMe PCIe 4.0',
          },
          display: {
            size: 15.6,
            resolution: '3840x2400',
            refreshRate: 60,
            panelType: 'OLED',
          },
        },
        features: {
          category: 'Business',
          portability: 'High',
          batteryLife: 8,
          weight: 1.96,
          thermalDesign: 'Standard',
        },
        availability: {
          inStock: true,
          stores: ['Dell', 'Amazon'],
        },
        images: ['image2.jpg'],
        scrapedAt: new Date(),
        lastUpdated: new Date(),
      }

      const model: LLMModel = {
        name: 'Mistral 7B',
        size: '7B',
        memoryRequirement: 8,
        recommendedMemory: 16,
        computeRequirement: 'Medium',
        framework: 'PyTorch',
      }

      const score = await compatibilityService.calculateCompatibility(laptop, model)

      expect(score.overall).toBeGreaterThan(60)
      expect(score.overall).toBeLessThan(80)
      expect(score.performance).toBeGreaterThan(50)
      expect(score.memory).toBeGreaterThan(70)
      expect(score.efficiency).toBeGreaterThan(70)
      expect(score.recommendation).toBe('Good')
    })

    it('should calculate low compatibility for basic laptop', async () => {
      const laptop: LaptopData = {
        url: 'test-laptop-3',
        title: 'Basic Laptop',
        brand: 'Generic',
        model: 'Basic Model',
        price: 599,
        currency: 'USD',
        specifications: {
          cpu: {
            brand: 'Intel',
            model: 'Core i3-1115G4',
            cores: 2,
            threads: 4,
            baseFrequency: 3.0,
            boostFrequency: 4.1,
            architecture: 'Tiger Lake',
            tdp: 15,
          },
          gpu: {
            brand: 'Intel',
            model: 'Iris Xe Graphics',
            memory: 0, // Shared memory
            memoryType: 'Shared',
            tdp: 15,
          },
          memory: {
            size: 8,
            type: 'DDR4',
            speed: 3200,
            slots: 1,
            maxCapacity: 16,
          },
          storage: {
            type: 'SSD',
            capacity: 256,
            interface: 'SATA',
          },
          display: {
            size: 14,
            resolution: '1920x1080',
            refreshRate: 60,
            panelType: 'TN',
          },
        },
        features: {
          category: 'Ultrabook',
          portability: 'High',
          batteryLife: 10,
          weight: 1.2,
          thermalDesign: 'Passive',
        },
        availability: {
          inStock: true,
          stores: ['Amazon'],
        },
        images: ['image3.jpg'],
        scrapedAt: new Date(),
        lastUpdated: new Date(),
      }

      const model: LLMModel = {
        name: 'Llama 2 13B',
        size: '13B',
        memoryRequirement: 16,
        recommendedMemory: 32,
        computeRequirement: 'High',
        framework: 'PyTorch',
      }

      const score = await compatibilityService.calculateCompatibility(laptop, model)

      expect(score.overall).toBeLessThan(50)
      expect(score.performance).toBeLessThan(40)
      expect(score.memory).toBeLessThan(60)
      expect(score.efficiency).toBeLessThan(50)
      expect(score.recommendation).toBe('Poor')
    })
  })

  describe('Model Recommendations', () => {
    it('should recommend appropriate models for high-end laptop', async () => {
      const laptop: LaptopData = {
        url: 'test-laptop-high-end',
        title: 'High-End Gaming Laptop',
        brand: 'ASUS',
        model: 'ROG Strix G17',
        price: 2499,
        currency: 'USD',
        specifications: {
          cpu: {
            brand: 'AMD',
            model: 'Ryzen 9 6900HX',
            cores: 8,
            threads: 16,
            baseFrequency: 3.3,
            boostFrequency: 4.9,
            architecture: 'Zen 3+',
            tdp: 45,
          },
          gpu: {
            brand: 'NVIDIA',
            model: 'GeForce RTX 3080',
            memory: 16,
            memoryType: 'GDDR6',
            tdp: 165,
          },
          memory: {
            size: 32,
            type: 'DDR5',
            speed: 4800,
            slots: 2,
            maxCapacity: 64,
          },
          storage: {
            type: 'SSD',
            capacity: 1024,
            interface: 'NVMe PCIe 4.0',
          },
          display: {
            size: 17.3,
            resolution: '2560x1440',
            refreshRate: 165,
            panelType: 'IPS',
          },
        },
        features: {
          category: 'Gaming',
          portability: 'Low',
          batteryLife: 4,
          weight: 2.9,
          thermalDesign: 'Advanced',
        },
        availability: {
          inStock: true,
          stores: ['Amazon', 'Best Buy', 'Newegg'],
        },
        images: ['image4.jpg'],
        scrapedAt: new Date(),
        lastUpdated: new Date(),
      }

      const recommendations = await compatibilityService.getModelRecommendations(laptop)

      expect(recommendations).toHaveLength(5)
      
      // Should include large models
      const largeModels = recommendations.filter(r => r.model.size.includes('13B') || r.model.size.includes('70B'))
      expect(largeModels.length).toBeGreaterThan(0)
      
      // All recommendations should have good scores
      recommendations.forEach(rec => {
        expect(rec.score.overall).toBeGreaterThan(60)
      })
      
      // Should be sorted by score (descending)
      for (let i = 1; i < recommendations.length; i++) {
        expect(recommendations[i-1].score.overall).toBeGreaterThanOrEqual(recommendations[i].score.overall)
      }
    })

    it('should recommend smaller models for low-end laptop', async () => {
      const laptop: LaptopData = {
        url: 'test-laptop-low-end',
        title: 'Budget Laptop',
        brand: 'Acer',
        model: 'Aspire 5',
        price: 499,
        currency: 'USD',
        specifications: {
          cpu: {
            brand: 'AMD',
            model: 'Ryzen 3 5300U',
            cores: 4,
            threads: 8,
            baseFrequency: 2.6,
            boostFrequency: 3.8,
            architecture: 'Zen 2',
            tdp: 15,
          },
          gpu: {
            brand: 'AMD',
            model: 'Radeon Graphics',
            memory: 0,
            memoryType: 'Shared',
            tdp: 15,
          },
          memory: {
            size: 8,
            type: 'DDR4',
            speed: 3200,
            slots: 2,
            maxCapacity: 32,
          },
          storage: {
            type: 'SSD',
            capacity: 256,
            interface: 'NVMe PCIe 3.0',
          },
          display: {
            size: 15.6,
            resolution: '1920x1080',
            refreshRate: 60,
            panelType: 'TN',
          },
        },
        features: {
          category: 'Budget',
          portability: 'Medium',
          batteryLife: 8,
          weight: 1.8,
          thermalDesign: 'Basic',
        },
        availability: {
          inStock: true,
          stores: ['Amazon', 'Walmart'],
        },
        images: ['image5.jpg'],
        scrapedAt: new Date(),
        lastUpdated: new Date(),
      }

      const recommendations = await compatibilityService.getModelRecommendations(laptop)

      expect(recommendations).toHaveLength(5)
      
      // Should primarily recommend smaller models
      const smallModels = recommendations.filter(r => r.model.size.includes('7B') || r.model.size.includes('3B'))
      expect(smallModels.length).toBeGreaterThan(2)
      
      // Should not recommend very large models with poor scores
      const poorScores = recommendations.filter(r => r.score.overall < 30)
      expect(poorScores.length).toBeLessThan(3)
    })
  })

  describe('Performance Estimation', () => {
    it('should estimate performance metrics accurately', async () => {
      const laptop: LaptopData = {
        url: 'test-laptop-perf',
        title: 'Performance Test Laptop',
        brand: 'MSI',
        model: 'GE76 Raider',
        price: 1899,
        currency: 'USD',
        specifications: {
          cpu: {
            brand: 'Intel',
            model: 'Core i7-12700H',
            cores: 14,
            threads: 20,
            baseFrequency: 2.3,
            boostFrequency: 4.7,
            architecture: 'Alder Lake',
            tdp: 45,
          },
          gpu: {
            brand: 'NVIDIA',
            model: 'GeForce RTX 3070',
            memory: 8,
            memoryType: 'GDDR6',
            tdp: 140,
          },
          memory: {
            size: 16,
            type: 'DDR4',
            speed: 3200,
            slots: 2,
            maxCapacity: 64,
          },
          storage: {
            type: 'SSD',
            capacity: 1024,
            interface: 'NVMe PCIe 4.0',
          },
          display: {
            size: 17.3,
            resolution: '1920x1080',
            refreshRate: 144,
            panelType: 'IPS',
          },
        },
        features: {
          category: 'Gaming',
          portability: 'Low',
          batteryLife: 5,
          weight: 2.9,
          thermalDesign: 'Advanced',
        },
        availability: {
          inStock: true,
          stores: ['Amazon', 'Best Buy'],
        },
        images: ['image6.jpg'],
        scrapedAt: new Date(),
        lastUpdated: new Date(),
      }

      const model: LLMModel = {
        name: 'Llama 2 7B',
        size: '7B',
        memoryRequirement: 8,
        recommendedMemory: 16,
        computeRequirement: 'Medium',
        framework: 'PyTorch',
      }

      const performance = await compatibilityService.estimatePerformance(laptop, model)

      expect(performance.tokensPerSecond).toBeGreaterThan(10)
      expect(performance.tokensPerSecond).toBeLessThan(50)
      expect(performance.memoryUsage).toBeGreaterThan(6)
      expect(performance.memoryUsage).toBeLessThan(12)
      expect(performance.powerConsumption).toBeGreaterThan(80)
      expect(performance.powerConsumption).toBeLessThan(200)
      expect(performance.thermalLoad).toBeGreaterThan(40)
      expect(performance.thermalLoad).toBeLessThan(100)
    })
  })
})
