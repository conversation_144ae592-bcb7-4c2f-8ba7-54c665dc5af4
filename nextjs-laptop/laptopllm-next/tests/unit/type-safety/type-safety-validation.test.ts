/**
 * Type safety validation tests
 * 
 * This test suite validates that type safety improvements work correctly
 * and don't introduce runtime errors after replacing `any` types.
 */

import { describe, it, expect, vi } from 'vitest'

// Mock implementations to test type safety
interface MockLaptopData {
  id: string
  brand: string
  model: string
  price: number
  specifications: {
    cpu: string
    memory: number
    storage: number
  }
}

interface MockScrapingResult {
  success: boolean
  data?: MockLaptopData[]
  error?: string
  metadata: {
    timestamp: Date
    source: string
  }
}

interface MockAxiosResponse<T = unknown> {
  data: T
  status: number
  statusText: string
  headers: Record<string, string>
}

class TypeSafetyValidator {
  /**
   * Test that properly typed functions work correctly
   */
  testTypedFunction<T>(
    fn: (input: T) => T,
    input: T,
    expectedOutput: T
  ): boolean {
    try {
      const result = fn(input)
      return JSON.stringify(result) === JSON.stringify(expectedOutput)
    } catch {
      return false
    }
  }

  /**
   * Test that type guards work properly
   */
  testTypeGuard<T>(
    guard: (value: unknown) => value is T,
    validValue: T,
    invalidValue: unknown
  ): boolean {
    return guard(validValue) && !guard(invalidValue)
  }

  /**
   * Test that generic functions maintain type safety
   */
  testGenericFunction<T, R>(
    fn: (input: T) => R,
    input: T,
    validator: (output: R) => boolean
  ): boolean {
    try {
      const result = fn(input)
      return validator(result)
    } catch {
      return false
    }
  }

  /**
   * Validate that API response typing works correctly
   */
  validateApiResponseTyping(): boolean {
    // Simulate properly typed API response
    const mockResponse: MockAxiosResponse<MockScrapingResult> = {
      data: {
        success: true,
        data: [
          {
            id: '1',
            brand: 'Dell',
            model: 'XPS 13',
            price: 999,
            specifications: {
              cpu: 'Intel i7',
              memory: 16,
              storage: 512
            }
          }
        ],
        metadata: {
          timestamp: new Date(),
          source: 'test'
        }
      },
      status: 200,
      statusText: 'OK',
      headers: {}
    }

    // Type checking should work without any type assertions
    const laptops = mockResponse.data.data
    return laptops !== undefined && laptops.length > 0 && laptops[0].brand === 'Dell'
  }

  /**
   * Validate that error handling with proper types works
   */
  validateErrorHandling(): boolean {
    try {
      throw new Error('Test error')
    } catch (error: unknown) {
      // Proper error handling without any
      if (error instanceof Error) {
        return error.message === 'Test error'
      }
      return false
    }
  }

  /**
   * Test that configuration objects are properly typed
   */
  validateConfigurationTyping(): boolean {
    interface TestConfig {
      apiUrl: string
      timeout: number
      retries: number
      headers?: Record<string, string>
    }

    const config: TestConfig = {
      apiUrl: 'https://api.example.com',
      timeout: 5000,
      retries: 3,
      headers: {
        'Content-Type': 'application/json'
      }
    }

    // TypeScript should enforce the structure
    return (
      typeof config.apiUrl === 'string' &&
      typeof config.timeout === 'number' &&
      typeof config.retries === 'number' &&
      (config.headers === undefined || typeof config.headers === 'object')
    )
  }
}

// Type guard functions for testing
function isLaptopData(value: unknown): value is MockLaptopData {
  return (
    typeof value === 'object' &&
    value !== null &&
    'id' in value &&
    'brand' in value &&
    'model' in value &&
    'price' in value &&
    'specifications' in value
  )
}

function isScrapingResult(value: unknown): value is MockScrapingResult {
  return (
    typeof value === 'object' &&
    value !== null &&
    'success' in value &&
    'metadata' in value
  )
}

// Generic utility functions for testing
function processData<T>(data: T): T {
  // Process data while maintaining type safety
  return data
}

function transformArray<T, R>(
  array: T[],
  transformer: (item: T) => R
): R[] {
  return array.map(transformer)
}

describe('Type Safety Validation', () => {
  let validator: TypeSafetyValidator

  beforeAll(() => {
    validator = new TypeSafetyValidator()
  })

  it('should validate that typed functions work correctly', () => {
    const testData: MockLaptopData = {
      id: '1',
      brand: 'Apple',
      model: 'MacBook Pro',
      price: 1999,
      specifications: {
        cpu: 'M2 Pro',
        memory: 32,
        storage: 1024
      }
    }

    const result = validator.testTypedFunction(
      processData,
      testData,
      testData
    )

    expect(result).toBe(true)
  })

  it('should validate that type guards work properly', () => {
    const validLaptop: MockLaptopData = {
      id: '1',
      brand: 'HP',
      model: 'Spectre x360',
      price: 1299,
      specifications: {
        cpu: 'Intel i7',
        memory: 16,
        storage: 512
      }
    }

    const invalidData = { invalid: 'data' }

    const result = validator.testTypeGuard(
      isLaptopData,
      validLaptop,
      invalidData
    )

    expect(result).toBe(true)
  })

  it('should validate that generic functions maintain type safety', () => {
    const laptops: MockLaptopData[] = [
      {
        id: '1',
        brand: 'Lenovo',
        model: 'ThinkPad X1',
        price: 1599,
        specifications: {
          cpu: 'Intel i7',
          memory: 16,
          storage: 512
        }
      }
    ]

    const result = validator.testGenericFunction(
      (data: MockLaptopData[]) => transformArray(data, laptop => laptop.brand),
      laptops,
      (brands: string[]) => brands.length === 1 && brands[0] === 'Lenovo'
    )

    expect(result).toBe(true)
  })

  it('should validate API response typing', () => {
    const result = validator.validateApiResponseTyping()
    expect(result).toBe(true)
  })

  it('should validate proper error handling without any', () => {
    const result = validator.validateErrorHandling()
    expect(result).toBe(true)
  })

  it('should validate configuration object typing', () => {
    const result = validator.validateConfigurationTyping()
    expect(result).toBe(true)
  })

  it('should ensure type safety in async operations', async () => {
    // Test async function with proper typing
    async function fetchLaptopData(): Promise<MockLaptopData[]> {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve([
            {
              id: '1',
              brand: 'ASUS',
              model: 'ZenBook',
              price: 899,
              specifications: {
                cpu: 'AMD Ryzen 7',
                memory: 16,
                storage: 512
              }
            }
          ])
        }, 10)
      })
    }

    const laptops = await fetchLaptopData()
    
    expect(Array.isArray(laptops)).toBe(true)
    expect(laptops.length).toBe(1)
    expect(laptops[0].brand).toBe('ASUS')
  })

  it('should validate union type handling', () => {
    type ProcessingResult = MockLaptopData | Error

    function processLaptop(data: unknown): ProcessingResult {
      if (isLaptopData(data)) {
        return data
      }
      return new Error('Invalid laptop data')
    }

    const validData: MockLaptopData = {
      id: '1',
      brand: 'MSI',
      model: 'Gaming Laptop',
      price: 1799,
      specifications: {
        cpu: 'Intel i9',
        memory: 32,
        storage: 1024
      }
    }

    const validResult = processLaptop(validData)
    const invalidResult = processLaptop({ invalid: 'data' })

    expect(validResult).toEqual(validData)
    expect(invalidResult).toBeInstanceOf(Error)
  })

  it('should validate optional property handling', () => {
    interface OptionalConfig {
      required: string
      optional?: number
      nullable: string | null
    }

    function processConfig(config: OptionalConfig): boolean {
      // Type system should handle optional and nullable properties correctly
      return (
        typeof config.required === 'string' &&
        (config.optional === undefined || typeof config.optional === 'number') &&
        (config.nullable === null || typeof config.nullable === 'string')
      )
    }

    const config1: OptionalConfig = {
      required: 'test',
      optional: 42,
      nullable: 'value'
    }

    const config2: OptionalConfig = {
      required: 'test',
      nullable: null
    }

    expect(processConfig(config1)).toBe(true)
    expect(processConfig(config2)).toBe(true)
  })

  it('should validate that removed any types do not break existing functionality', () => {
    // Mock a service that previously used any types
    class MockScrapingService {
      private config: { timeout: number; retries: number }

      constructor(config: { timeout: number; retries: number }) {
        this.config = config
      }

      async scrapeData(url: string): Promise<MockScrapingResult> {
        // Simulate scraping with proper types
        return {
          success: true,
          data: [
            {
              id: '1',
              brand: 'Test Brand',
              model: 'Test Model',
              price: 999,
              specifications: {
                cpu: 'Test CPU',
                memory: 8,
                storage: 256
              }
            }
          ],
          metadata: {
            timestamp: new Date(),
            source: url
          }
        }
      }
    }

    const service = new MockScrapingService({ timeout: 5000, retries: 3 })
    
    expect(service).toBeInstanceOf(MockScrapingService)
    
    // Test that the service works with proper typing
    return service.scrapeData('https://example.com').then(result => {
      expect(result.success).toBe(true)
      expect(result.data).toBeDefined()
      expect(result.metadata.source).toBe('https://example.com')
    })
  })
})
