/**
 * Critical Any Type Fixes Test Suite
 * 
 * This test suite focuses on identifying and providing automated fixes
 * for critical `any` type usages in core functionality areas.
 */

import { describe, it, expect, beforeAll } from 'vitest'
import { readFileSync, existsSync } from 'fs'
import { join } from 'path'

interface CriticalFix {
  file: string
  line: number
  originalCode: string
  fixedCode: string
  imports: string[]
  reasoning: string
  priority: 'critical' | 'high'
  category: 'scraping' | 'database' | 'api' | 'components' | 'types'
}

class CriticalAnyFixer {
  private projectRoot: string
  private criticalFiles: string[]

  constructor(projectRoot: string) {
    this.projectRoot = projectRoot
    this.criticalFiles = [
      'src/lib/scraping/crawl4ai.service.ts',
      'src/lib/scraping/firecrawl.service.ts',
      'src/lib/scraping/base-scraper.ts',
      'src/shared/types/index.ts',
      'src/shared/types/api.ts',
      'src/features/scraper/types/index.ts',
      'src/features/laptops/types/index.ts',
      'src/lib/services/base.service.ts'
    ]
  }

  /**
   * Analyze critical files and generate fixes
   */
  async analyzeCriticalFiles(): Promise<CriticalFix[]> {
    const fixes: CriticalFix[] = []

    for (const file of this.criticalFiles) {
      const fullPath = join(this.projectRoot, file)
      
      if (existsSync(fullPath)) {
        try {
          const fileFixes = await this.analyzeFile(fullPath, file)
          fixes.push(...fileFixes)
        } catch (error) {
          console.warn(`Failed to analyze ${file}:`, error)
        }
      }
    }

    return fixes.sort((a, b) => {
      const priorityOrder = { critical: 0, high: 1 }
      return priorityOrder[a.priority] - priorityOrder[b.priority]
    })
  }

  /**
   * Analyze a single file for critical any usages
   */
  private async analyzeFile(filePath: string, relativePath: string): Promise<CriticalFix[]> {
    const content = readFileSync(filePath, 'utf-8')
    const lines = content.split('\n')
    const fixes: CriticalFix[] = []

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      const lineNumber = i + 1

      // Skip comments
      if (line.trim().startsWith('//') || line.trim().startsWith('*')) {
        continue
      }

      const fix = this.generateFix(relativePath, lineNumber, line)
      if (fix) {
        fixes.push(fix)
      }
    }

    return fixes
  }

  /**
   * Generate specific fixes based on patterns
   */
  private generateFix(file: string, line: number, code: string): CriticalFix | null {
    const trimmedCode = code.trim()

    // Scraping service fixes
    if (file.includes('scraping') || file.includes('scraper')) {
      // Axios instance fix
      if (trimmedCode.includes('axiosInstance: any')) {
        return {
          file,
          line,
          originalCode: trimmedCode,
          fixedCode: trimmedCode.replace(': any', ': AxiosInstance'),
          imports: ['import type { AxiosInstance } from "axios"'],
          reasoning: 'Axios provides proper typing for HTTP client instances',
          priority: 'critical',
          category: 'scraping'
        }
      }

      // Response handling fix
      if (trimmedCode.includes('response') && trimmedCode.includes(': any')) {
        return {
          file,
          line,
          originalCode: trimmedCode,
          fixedCode: trimmedCode.replace(': any', ': AxiosResponse<ScrapingResult>'),
          imports: [
            'import type { AxiosResponse } from "axios"',
            'import type { ScrapingResult } from "@/shared/types"'
          ],
          reasoning: 'HTTP responses should be properly typed with expected data structure',
          priority: 'critical',
          category: 'scraping'
        }
      }

      // Configuration fix
      if (trimmedCode.includes('config') && trimmedCode.includes(': any')) {
        return {
          file,
          line,
          originalCode: trimmedCode,
          fixedCode: trimmedCode.replace(': any', ': ScrapingConfig'),
          imports: ['import type { ScrapingConfig } from "@/shared/types"'],
          reasoning: 'Configuration objects should use defined interfaces',
          priority: 'high',
          category: 'scraping'
        }
      }

      // Extracted content fix
      if (trimmedCode.includes('extracted_content') && trimmedCode.includes(': any')) {
        return {
          file,
          line,
          originalCode: trimmedCode,
          fixedCode: trimmedCode.replace(': any', ': ExtractedContent'),
          imports: [
            `interface ExtractedContent {
  links?: Array<{ text: string; href: string }>
  products?: Array<Record<string, unknown>>
  structured_data?: Record<string, unknown>
  [key: string]: unknown
}`
          ],
          reasoning: 'Extracted content should have a defined structure',
          priority: 'high',
          category: 'scraping'
        }
      }
    }

    // Database and API fixes
    if (file.includes('api') || file.includes('service') || file.includes('database')) {
      // Generic data fix
      if (trimmedCode.includes('data: any')) {
        return {
          file,
          line,
          originalCode: trimmedCode,
          fixedCode: trimmedCode.replace('data: any', 'data: unknown'),
          imports: [],
          reasoning: 'Use unknown for type safety, then validate with type guards',
          priority: 'critical',
          category: 'api'
        }
      }

      // API response fix
      if (trimmedCode.includes('apiResponse') && trimmedCode.includes(': any')) {
        return {
          file,
          line,
          originalCode: trimmedCode,
          fixedCode: trimmedCode.replace(': any', ': ApiResponse<T>'),
          imports: ['import type { ApiResponse } from "@/shared/types"'],
          reasoning: 'API responses should use generic typing',
          priority: 'critical',
          category: 'api'
        }
      }
    }

    // Type definition fixes
    if (file.includes('types')) {
      // Interface property fix
      if (trimmedCode.includes(': any') && !trimmedCode.includes('//')) {
        const propertyName = trimmedCode.split(':')[0]?.trim()
        
        if (propertyName?.includes('laptop') || propertyName?.includes('spec')) {
          return {
            file,
            line,
            originalCode: trimmedCode,
            fixedCode: trimmedCode.replace(': any', ': LaptopSpecifications | unknown'),
            imports: ['import type { LaptopSpecifications } from "@/shared/schemas"'],
            reasoning: 'Laptop-related properties should use defined specifications',
            priority: 'high',
            category: 'types'
          }
        }

        if (propertyName?.includes('compatibility')) {
          return {
            file,
            line,
            originalCode: trimmedCode,
            fixedCode: trimmedCode.replace(': any', ': LLMCompatibilityScore'),
            imports: ['import type { LLMCompatibilityScore } from "@/shared/schemas"'],
            reasoning: 'Compatibility properties should use defined scoring types',
            priority: 'high',
            category: 'types'
          }
        }

        // Generic fallback for type definitions
        return {
          file,
          line,
          originalCode: trimmedCode,
          fixedCode: trimmedCode.replace(': any', ': unknown'),
          imports: [],
          reasoning: 'Replace any with unknown for type safety',
          priority: 'high',
          category: 'types'
        }
      }
    }

    // Component fixes
    if (file.includes('component') || file.includes('hook')) {
      // Props fix
      if (trimmedCode.includes('props') && trimmedCode.includes(': any')) {
        return {
          file,
          line,
          originalCode: trimmedCode,
          fixedCode: trimmedCode.replace(': any', ': Record<string, unknown>'),
          imports: [],
          reasoning: 'Component props should be explicitly typed',
          priority: 'high',
          category: 'components'
        }
      }

      // Event handler fix
      if (trimmedCode.includes('event') && trimmedCode.includes(': any')) {
        return {
          file,
          line,
          originalCode: trimmedCode,
          fixedCode: trimmedCode.replace(': any', ': React.MouseEvent | React.ChangeEvent'),
          imports: ['import type { MouseEvent, ChangeEvent } from "react"'],
          reasoning: 'Event handlers should use proper React event types',
          priority: 'high',
          category: 'components'
        }
      }
    }

    return null
  }

  /**
   * Generate a comprehensive fix report
   */
  generateFixReport(fixes: CriticalFix[]): string {
    let report = '# Critical Any Type Fixes Report\n\n'
    
    const criticalFixes = fixes.filter(f => f.priority === 'critical')
    const highFixes = fixes.filter(f => f.priority === 'high')
    
    report += `## Summary\n\n`
    report += `- **Critical fixes needed:** ${criticalFixes.length}\n`
    report += `- **High priority fixes:** ${highFixes.length}\n`
    report += `- **Total fixes:** ${fixes.length}\n\n`

    if (criticalFixes.length > 0) {
      report += `## Critical Fixes (Immediate Action Required)\n\n`
      criticalFixes.forEach((fix, index) => {
        report += this.formatFix(fix, index + 1)
      })
    }

    if (highFixes.length > 0) {
      report += `## High Priority Fixes\n\n`
      highFixes.forEach((fix, index) => {
        report += this.formatFix(fix, index + 1)
      })
    }

    return report
  }

  private formatFix(fix: CriticalFix, index: number): string {
    let formatted = `### ${index}. ${fix.file}:${fix.line}\n\n`
    formatted += `**Category:** ${fix.category}\n\n`
    formatted += `**Priority:** ${fix.priority}\n\n`
    formatted += `**Original Code:**\n\`\`\`typescript\n${fix.originalCode}\n\`\`\`\n\n`
    formatted += `**Fixed Code:**\n\`\`\`typescript\n${fix.fixedCode}\n\`\`\`\n\n`
    
    if (fix.imports.length > 0) {
      formatted += `**Required Imports:**\n\`\`\`typescript\n${fix.imports.join('\n')}\n\`\`\`\n\n`
    }
    
    formatted += `**Reasoning:** ${fix.reasoning}\n\n`
    formatted += '---\n\n'
    
    return formatted
  }
}

describe('Critical Any Type Fixes', () => {
  let fixer: CriticalAnyFixer
  let criticalFixes: CriticalFix[]

  beforeAll(async () => {
    fixer = new CriticalAnyFixer(process.cwd())
    criticalFixes = await fixer.analyzeCriticalFiles()
  })

  it('should identify critical any usages in core files', () => {
    console.log(`\nFound ${criticalFixes.length} critical any type usages`)
    
    const criticalCount = criticalFixes.filter(f => f.priority === 'critical').length
    const highCount = criticalFixes.filter(f => f.priority === 'high').length
    
    console.log(`Critical: ${criticalCount}, High: ${highCount}`)
    
    expect(criticalFixes).toBeDefined()
  })

  it('should prioritize scraping service fixes', () => {
    const scrapingFixes = criticalFixes.filter(f => f.category === 'scraping')
    
    if (scrapingFixes.length > 0) {
      console.log('\n=== SCRAPING SERVICE FIXES ===')
      scrapingFixes.forEach(fix => {
        console.log(`${fix.file}:${fix.line} - ${fix.reasoning}`)
      })
    }

    // Scraping services are critical for the project
    expect(scrapingFixes.every(f => f.priority === 'critical' || f.priority === 'high')).toBe(true)
  })

  it('should provide actionable fixes with proper imports', () => {
    const fixesWithImports = criticalFixes.filter(f => f.imports.length > 0)
    
    expect(fixesWithImports.length).toBeGreaterThan(0)
    
    fixesWithImports.forEach(fix => {
      expect(fix.fixedCode).not.toContain('any')
      expect(fix.imports.every(imp => 
        imp.startsWith('import') || imp.startsWith('interface') || imp.startsWith('type')
      )).toBe(true)
    })
  })

  it('should generate a comprehensive fix report', () => {
    const report = fixer.generateFixReport(criticalFixes)
    
    expect(report).toContain('# Critical Any Type Fixes Report')
    expect(report).toContain('## Summary')
    
    if (criticalFixes.some(f => f.priority === 'critical')) {
      expect(report).toContain('## Critical Fixes')
    }
    
    console.log('\n' + report)
  })

  it('should ensure fixes maintain functionality', () => {
    // Test that suggested fixes are syntactically valid
    criticalFixes.forEach(fix => {
      // Basic syntax validation
      expect(fix.fixedCode).not.toContain('any')
      expect(fix.fixedCode.includes(':')).toBe(true)
      
      // Ensure we're not just removing types
      expect(fix.fixedCode.trim().length).toBeGreaterThan(0)
    })
  })

  it('should categorize fixes by domain', () => {
    const categories = ['scraping', 'database', 'api', 'components', 'types'] as const
    const categoryCounts = categories.reduce((acc, category) => {
      acc[category] = criticalFixes.filter(f => f.category === category).length
      return acc
    }, {} as Record<string, number>)

    console.log('\n=== FIXES BY CATEGORY ===')
    Object.entries(categoryCounts).forEach(([category, count]) => {
      if (count > 0) {
        console.log(`${category}: ${count} fixes`)
      }
    })

    // At least one category should have fixes
    expect(Object.values(categoryCounts).some(count => count > 0)).toBe(true)
  })
})
