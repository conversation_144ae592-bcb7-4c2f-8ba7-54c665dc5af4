/**
 * Type replacement suggestions and validation tests
 * 
 * This test suite provides specific type replacement suggestions for common
 * patterns in the LaptopLLM project and validates that replacements work correctly.
 */

import { describe, it, expect } from 'vitest'

interface TypeReplacement {
  pattern: string
  context: string
  originalType: string
  suggestedType: string
  imports?: string[]
  reasoning: string
  example: string
}

class TypeReplacementSuggester {
  private replacements: TypeReplacement[] = [
    // Scraping service types
    {
      pattern: 'axiosInstance: any',
      context: 'HTTP client instances',
      originalType: 'any',
      suggestedType: 'AxiosInstance',
      imports: ['import type { AxiosInstance } from "axios"'],
      reasoning: 'Axios provides proper typing for HTTP client instances',
      example: 'private axiosInstance: AxiosInstance'
    },
    {
      pattern: 'response: any',
      context: 'HTTP responses in scraping services',
      originalType: 'any',
      suggestedType: 'AxiosResponse<T> | ScrapingResult',
      imports: ['import type { AxiosResponse } from "axios"', 'import type { ScrapingResult } from "@/shared/types"'],
      reasoning: 'HTTP responses should be properly typed with expected data structure',
      example: 'const response: AxiosResponse<ScrapingResult> = await this.axiosInstance.get(url)'
    },
    {
      pattern: 'extracted_content: any',
      context: 'Scraped content data',
      originalType: 'any',
      suggestedType: 'ExtractedContent',
      imports: ['// Define ExtractedContent interface based on actual usage'],
      reasoning: 'Extracted content should have a defined structure for type safety',
      example: `interface ExtractedContent {
  links?: Array<{ text: string; href: string }>
  products?: Array<Record<string, unknown>>
  structured_data?: Record<string, unknown>
  [key: string]: unknown
}`
    },
    {
      pattern: 'scrapingConfig: any',
      context: 'Scraping configuration objects',
      originalType: 'any',
      suggestedType: 'ScrapingConfig | Crawl4AIConfig',
      imports: ['import type { ScrapingConfig, Crawl4AIConfig } from "@/shared/types"'],
      reasoning: 'Configuration objects should use defined interfaces for validation',
      example: 'private config: ScrapingConfig & Crawl4AIConfig'
    },

    // Database and API types
    {
      pattern: 'data: any',
      context: 'Database query results',
      originalType: 'any',
      suggestedType: 'LaptopData | ScrapedLaptopData | unknown',
      imports: ['import type { LaptopData, ScrapedLaptopData } from "@/shared/types"'],
      reasoning: 'Database results should be typed according to the expected schema',
      example: 'const laptops: LaptopData[] = await query.findMany()'
    },
    {
      pattern: 'apiResponse: any',
      context: 'API response handling',
      originalType: 'any',
      suggestedType: 'ApiResponse<T>',
      imports: ['import type { ApiResponse } from "@/shared/types"'],
      reasoning: 'API responses should use generic typing for proper data structure',
      example: 'const response: ApiResponse<LaptopData[]> = await fetch(url)'
    },

    // Component and React types
    {
      pattern: 'props: any',
      context: 'React component props',
      originalType: 'any',
      suggestedType: 'ComponentProps | Record<string, unknown>',
      imports: ['import type { ComponentProps } from "react"'],
      reasoning: 'Component props should be explicitly typed for better development experience',
      example: 'interface LaptopCardProps { laptop: LaptopData; onSelect?: (id: string) => void }'
    },
    {
      pattern: 'event: any',
      context: 'Event handlers',
      originalType: 'any',
      suggestedType: 'React.MouseEvent | React.ChangeEvent | Event',
      imports: ['import type { MouseEvent, ChangeEvent } from "react"'],
      reasoning: 'Event objects should use proper React event types',
      example: 'const handleClick = (event: MouseEvent<HTMLButtonElement>) => {}'
    },

    // Utility and generic types
    {
      pattern: 'params: any',
      context: 'Function parameters',
      originalType: 'any',
      suggestedType: 'Record<string, unknown> | unknown',
      imports: [],
      reasoning: 'Generic parameters should use unknown for type safety or specific interfaces',
      example: 'function processData(params: Record<string, unknown>) {}'
    },
    {
      pattern: 'result: any',
      context: 'Function return values',
      originalType: 'any',
      suggestedType: 'unknown | Promise<T>',
      imports: [],
      reasoning: 'Return values should be explicitly typed or use unknown for safety',
      example: 'async function fetchData(): Promise<LaptopData[]> {}'
    },

    // LLM and compatibility types
    {
      pattern: 'compatibility: any',
      context: 'LLM compatibility data',
      originalType: 'any',
      suggestedType: 'LLMCompatibilityScore | CompatibilityResult',
      imports: ['import type { LLMCompatibilityScore } from "@/shared/types"'],
      reasoning: 'Compatibility calculations should use defined scoring interfaces',
      example: 'const score: LLMCompatibilityScore = calculateCompatibility(laptop, model)'
    },
    {
      pattern: 'specifications: any',
      context: 'Laptop specifications',
      originalType: 'any',
      suggestedType: 'LaptopSpecifications | HardwareSpecs',
      imports: ['import type { LaptopSpecifications } from "@/shared/types"'],
      reasoning: 'Hardware specifications should follow defined schema structure',
      example: 'const specs: LaptopSpecifications = { cpu: cpuSpec, memory: memorySpec }'
    },

    // Error handling types
    {
      pattern: 'error: any',
      context: 'Error handling',
      originalType: 'any',
      suggestedType: 'Error | unknown',
      imports: [],
      reasoning: 'Errors should be typed as Error or unknown for proper handling',
      example: 'catch (error: unknown) { if (error instanceof Error) { console.log(error.message) } }'
    },

    // JSON and dynamic data
    {
      pattern: 'json: any',
      context: 'JSON data parsing',
      originalType: 'any',
      suggestedType: 'unknown | JsonValue',
      imports: ['type JsonValue = string | number | boolean | null | JsonValue[] | { [key: string]: JsonValue }'],
      reasoning: 'JSON data should be validated before use, starting with unknown',
      example: 'const data: unknown = JSON.parse(jsonString); // Then validate with schema'
    }
  ]

  /**
   * Get replacement suggestions for a given context
   */
  getSuggestions(context: string, codeSnippet: string): TypeReplacement[] {
    return this.replacements.filter(replacement => 
      codeSnippet.includes(replacement.pattern) || 
      context.toLowerCase().includes(replacement.context.toLowerCase())
    )
  }

  /**
   * Get all available replacements
   */
  getAllReplacements(): TypeReplacement[] {
    return this.replacements
  }

  /**
   * Generate a type replacement report
   */
  generateReport(): string {
    let report = '# TypeScript Any Type Replacement Guide\n\n'
    
    const categories = this.groupByCategory()
    
    for (const [category, replacements] of Object.entries(categories)) {
      report += `## ${category}\n\n`
      
      for (const replacement of replacements) {
        report += `### ${replacement.pattern}\n\n`
        report += `**Context:** ${replacement.context}\n\n`
        report += `**Suggested Type:** \`${replacement.suggestedType}\`\n\n`
        report += `**Reasoning:** ${replacement.reasoning}\n\n`
        
        if (replacement.imports && replacement.imports.length > 0) {
          report += `**Required Imports:**\n\`\`\`typescript\n${replacement.imports.join('\n')}\n\`\`\`\n\n`
        }
        
        report += `**Example:**\n\`\`\`typescript\n${replacement.example}\n\`\`\`\n\n`
        report += '---\n\n'
      }
    }
    
    return report
  }

  private groupByCategory(): Record<string, TypeReplacement[]> {
    const categories: Record<string, TypeReplacement[]> = {}
    
    for (const replacement of this.replacements) {
      const category = this.categorizeReplacement(replacement)
      if (!categories[category]) {
        categories[category] = []
      }
      categories[category].push(replacement)
    }
    
    return categories
  }

  private categorizeReplacement(replacement: TypeReplacement): string {
    const context = replacement.context.toLowerCase()
    
    if (context.includes('scrap') || context.includes('http')) {
      return 'Scraping & HTTP'
    }
    if (context.includes('database') || context.includes('api')) {
      return 'Database & API'
    }
    if (context.includes('react') || context.includes('component')) {
      return 'React Components'
    }
    if (context.includes('llm') || context.includes('compatibility')) {
      return 'LLM & Compatibility'
    }
    if (context.includes('error')) {
      return 'Error Handling'
    }
    
    return 'General Utilities'
  }
}

describe('Type Replacement Suggestions', () => {
  let suggester: TypeReplacementSuggester

  beforeAll(() => {
    suggester = new TypeReplacementSuggester()
  })

  it('should provide comprehensive replacement suggestions', () => {
    const allReplacements = suggester.getAllReplacements()
    
    expect(allReplacements.length).toBeGreaterThan(10)
    expect(allReplacements.every(r => r.suggestedType !== 'any')).toBe(true)
  })

  it('should categorize replacements by domain', () => {
    const scrapingReplacements = suggester.getSuggestions('scraping', 'axiosInstance: any')
    const reactReplacements = suggester.getSuggestions('component', 'props: any')
    
    expect(scrapingReplacements.length).toBeGreaterThan(0)
    expect(reactReplacements.length).toBeGreaterThan(0)
  })

  it('should provide proper imports for suggested types', () => {
    const replacements = suggester.getAllReplacements()
    const replacementsWithImports = replacements.filter(r => r.imports && r.imports.length > 0)
    
    expect(replacementsWithImports.length).toBeGreaterThan(5)
    
    // Check that imports are properly formatted
    replacementsWithImports.forEach(replacement => {
      replacement.imports?.forEach(importStatement => {
        expect(importStatement).toMatch(/^(import|\/\/|type)/)
      })
    })
  })

  it('should generate a comprehensive replacement report', () => {
    const report = suggester.generateReport()
    
    expect(report).toContain('# TypeScript Any Type Replacement Guide')
    expect(report).toContain('## Scraping & HTTP')
    expect(report).toContain('## React Components')
    expect(report).toContain('**Suggested Type:**')
    expect(report).toContain('**Example:**')
  })

  it('should provide context-specific suggestions', () => {
    const testCases = [
      {
        context: 'HTTP client',
        code: 'private axiosInstance: any',
        expectedSuggestion: 'AxiosInstance'
      },
      {
        context: 'React component',
        code: 'interface Props { data: any }',
        expectedSuggestion: 'ComponentProps'
      },
      {
        context: 'Database query',
        code: 'const result: any = await query()',
        expectedSuggestion: 'LaptopData'
      }
    ]

    testCases.forEach(testCase => {
      const suggestions = suggester.getSuggestions(testCase.context, testCase.code)
      expect(suggestions.length).toBeGreaterThan(0)
      
      const relevantSuggestion = suggestions.find(s => 
        s.suggestedType.includes(testCase.expectedSuggestion)
      )
      expect(relevantSuggestion).toBeDefined()
    })
  })

  it('should validate that suggested types are available in the project', () => {
    const replacements = suggester.getAllReplacements()
    
    // Check that suggested types reference actual project types
    const projectTypes = [
      'LaptopData', 'ScrapingConfig', 'LLMCompatibilityScore',
      'ApiResponse', 'ScrapedLaptopData', 'Crawl4AIConfig'
    ]

    const suggestionsUsingProjectTypes = replacements.filter(r =>
      projectTypes.some(type => r.suggestedType.includes(type))
    )

    expect(suggestionsUsingProjectTypes.length).toBeGreaterThan(5)
  })
})
