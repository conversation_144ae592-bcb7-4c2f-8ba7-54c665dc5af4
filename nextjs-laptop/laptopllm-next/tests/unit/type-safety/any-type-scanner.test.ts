/**
 * Comprehensive TypeScript `any` type scanner and fixer
 * 
 * This test suite identifies all instances of the `any` type throughout the project
 * and provides suggestions for proper type replacements.
 */

import { describe, it, expect, beforeAll } from 'vitest'
import { readFileSync, readdirSync, statSync } from 'fs'
import { join, extname, relative } from 'path'

interface AnyTypeUsage {
  file: string
  line: number
  column: number
  context: string
  type: 'variable' | 'parameter' | 'return-type' | 'property' | 'generic' | 'assertion' | 'unknown'
  severity: 'critical' | 'high' | 'medium' | 'low'
  suggestion: string
  codeSnippet: string
}

interface TypeScanResult {
  totalFiles: number
  scannedFiles: number
  anyUsages: AnyTypeUsage[]
  summary: {
    critical: number
    high: number
    medium: number
    low: number
    byType: Record<string, number>
    byFile: Record<string, number>
  }
}

class AnyTypeScanner {
  private projectRoot: string
  private excludePatterns: RegExp[]
  private includeExtensions: string[]

  constructor(projectRoot: string) {
    this.projectRoot = projectRoot
    this.excludePatterns = [
      /node_modules/,
      /\.next/,
      /dist/,
      /build/,
      /coverage/,
      /\.git/,
      /generated/,
      /playwright-report/,
      /test-results/,
      /\.d\.ts$/,
      /\.config\./,
      /\.setup\./
    ]
    this.includeExtensions = ['.ts', '.tsx']
  }

  /**
   * Scan all TypeScript files for `any` type usage
   */
  async scanProject(): Promise<TypeScanResult> {
    const files = this.getAllTypeScriptFiles()
    const anyUsages: AnyTypeUsage[] = []
    let scannedFiles = 0

    for (const file of files) {
      try {
        const usages = await this.scanFile(file)
        anyUsages.push(...usages)
        scannedFiles++
      } catch (error) {
        console.warn(`Failed to scan file ${file}:`, error)
      }
    }

    return {
      totalFiles: files.length,
      scannedFiles,
      anyUsages,
      summary: this.generateSummary(anyUsages)
    }
  }

  /**
   * Get all TypeScript files in the project
   */
  private getAllTypeScriptFiles(): string[] {
    const files: string[] = []
    
    const scanDirectory = (dir: string): void => {
      try {
        const entries = readdirSync(dir)
        
        for (const entry of entries) {
          const fullPath = join(dir, entry)
          const relativePath = relative(this.projectRoot, fullPath)
          
          // Skip excluded patterns
          if (this.excludePatterns.some(pattern => pattern.test(relativePath))) {
            continue
          }
          
          const stat = statSync(fullPath)
          
          if (stat.isDirectory()) {
            scanDirectory(fullPath)
          } else if (this.includeExtensions.includes(extname(fullPath))) {
            files.push(fullPath)
          }
        }
      } catch (error) {
        // Skip directories we can't read
      }
    }

    scanDirectory(this.projectRoot)
    return files
  }

  /**
   * Scan a single file for `any` type usage
   */
  private async scanFile(filePath: string): Promise<AnyTypeUsage[]> {
    const content = readFileSync(filePath, 'utf-8')
    const lines = content.split('\n')
    const usages: AnyTypeUsage[] = []
    const relativePath = relative(this.projectRoot, filePath)

    // Regex patterns to detect different `any` usage contexts
    const patterns = [
      // Variable declarations: let x: any, const y: any
      {
        regex: /(?:let|const|var)\s+(\w+)\s*:\s*any\b/g,
        type: 'variable' as const,
        severity: 'high' as const
      },
      // Function parameters: function(param: any)
      {
        regex: /(?:function\s+\w+|(?:const|let)\s+\w+\s*=\s*(?:async\s+)?(?:function|\()|(?:async\s+)?(?:function|\()|:\s*(?:async\s+)?(?:function|\()).*?\(\s*[^)]*?(\w+)\s*:\s*any\b/g,
        type: 'parameter' as const,
        severity: 'critical' as const
      },
      // Return types: ): any
      {
        regex: /\)\s*:\s*any\b/g,
        type: 'return-type' as const,
        severity: 'high' as const
      },
      // Interface/type properties: prop: any
      {
        regex: /(\w+)\s*:\s*any\b/g,
        type: 'property' as const,
        severity: 'medium' as const
      },
      // Generic parameters: <T = any>
      {
        regex: /<[^>]*?=\s*any\b/g,
        type: 'generic' as const,
        severity: 'medium' as const
      },
      // Type assertions: as any
      {
        regex: /\bas\s+any\b/g,
        type: 'assertion' as const,
        severity: 'high' as const
      },
      // Array types: any[]
      {
        regex: /any\[\]/g,
        type: 'property' as const,
        severity: 'medium' as const
      }
    ]

    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex]
      const lineNumber = lineIndex + 1

      // Skip comments and strings (basic detection)
      if (line.trim().startsWith('//') || line.trim().startsWith('*')) {
        continue
      }

      for (const pattern of patterns) {
        let match
        pattern.regex.lastIndex = 0 // Reset regex state
        
        while ((match = pattern.regex.exec(line)) !== null) {
          const column = match.index + 1
          const suggestion = this.generateTypeSuggestion(relativePath, line, pattern.type)
          
          usages.push({
            file: relativePath,
            line: lineNumber,
            column,
            context: line.trim(),
            type: pattern.type,
            severity: this.determineSeverity(relativePath, pattern.type),
            suggestion,
            codeSnippet: this.getCodeSnippet(lines, lineIndex)
          })
        }
      }
    }

    return usages
  }

  /**
   * Generate appropriate type suggestions based on context
   */
  private generateTypeSuggestion(filePath: string, line: string, usageType: string): string {
    // Domain-specific type suggestions based on file path and context
    if (filePath.includes('scraping') || filePath.includes('scraper')) {
      if (line.includes('response') || line.includes('result')) {
        return 'ScrapingResult | ScrapedData | Crawl4AIResponse'
      }
      if (line.includes('config') || line.includes('options')) {
        return 'ScrapingConfig | Crawl4AIConfig'
      }
      if (line.includes('axios') || line.includes('http')) {
        return 'AxiosInstance | AxiosResponse<T>'
      }
    }

    if (filePath.includes('laptop') || filePath.includes('llm')) {
      if (line.includes('spec') || line.includes('specification')) {
        return 'LaptopSpecifications | LaptopSpecs'
      }
      if (line.includes('compatibility')) {
        return 'LLMCompatibilityScore | CompatibilityResult'
      }
      if (line.includes('data') || line.includes('laptop')) {
        return 'LaptopData | ScrapedLaptopData'
      }
    }

    if (filePath.includes('api') || filePath.includes('service')) {
      if (line.includes('response')) {
        return 'ApiResponse<T> | Response'
      }
      if (line.includes('request')) {
        return 'ApiRequest | RequestConfig'
      }
    }

    // Generic suggestions based on usage type
    switch (usageType) {
      case 'parameter':
        return 'unknown | Record<string, unknown> | T (generic)'
      case 'return-type':
        return 'Promise<T> | T | void'
      case 'property':
        return 'unknown | string | number | Record<string, unknown>'
      case 'assertion':
        return 'Remove assertion or use proper type'
      default:
        return 'unknown | Record<string, unknown>'
    }
  }

  /**
   * Determine severity based on file location and usage type
   */
  private determineSeverity(filePath: string, usageType: string): 'critical' | 'high' | 'medium' | 'low' {
    // Critical areas
    if (filePath.includes('services') || filePath.includes('api') || filePath.includes('database')) {
      return usageType === 'parameter' ? 'critical' : 'high'
    }

    // High priority areas
    if (filePath.includes('scraping') || filePath.includes('types') || filePath.includes('schemas')) {
      return usageType === 'parameter' ? 'high' : 'medium'
    }

    // Medium priority
    if (filePath.includes('components') || filePath.includes('hooks')) {
      return 'medium'
    }

    // Low priority (tests, utilities)
    return 'low'
  }

  /**
   * Get code snippet around the line with `any` usage
   */
  private getCodeSnippet(lines: string[], lineIndex: number): string {
    const start = Math.max(0, lineIndex - 2)
    const end = Math.min(lines.length, lineIndex + 3)
    
    return lines.slice(start, end)
      .map((line, index) => {
        const actualLineNumber = start + index + 1
        const marker = actualLineNumber === lineIndex + 1 ? '>>> ' : '    '
        return `${marker}${actualLineNumber}: ${line}`
      })
      .join('\n')
  }

  /**
   * Generate summary statistics
   */
  private generateSummary(usages: AnyTypeUsage[]) {
    const summary = {
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
      byType: {} as Record<string, number>,
      byFile: {} as Record<string, number>
    }

    for (const usage of usages) {
      summary[usage.severity]++
      summary.byType[usage.type] = (summary.byType[usage.type] || 0) + 1
      summary.byFile[usage.file] = (summary.byFile[usage.file] || 0) + 1
    }

    return summary
  }
}

describe('TypeScript Any Type Scanner', () => {
  let scanner: AnyTypeScanner
  let scanResult: TypeScanResult

  beforeAll(async () => {
    scanner = new AnyTypeScanner(process.cwd())
    scanResult = await scanner.scanProject()
  })

  it('should scan all TypeScript files in the project', () => {
    expect(scanResult.totalFiles).toBeGreaterThan(0)
    expect(scanResult.scannedFiles).toBe(scanResult.totalFiles)
  })

  it('should identify any type usages with proper categorization', () => {
    console.log('\n=== ANY TYPE USAGE REPORT ===')
    console.log(`Total files scanned: ${scanResult.scannedFiles}`)
    console.log(`Total any usages found: ${scanResult.anyUsages.length}`)
    console.log('\nSeverity breakdown:')
    console.log(`  Critical: ${scanResult.summary.critical}`)
    console.log(`  High: ${scanResult.summary.high}`)
    console.log(`  Medium: ${scanResult.summary.medium}`)
    console.log(`  Low: ${scanResult.summary.low}`)

    // The test should pass but log findings for review
    expect(scanResult).toBeDefined()
  })

  it('should provide detailed findings for critical any usages', () => {
    const criticalUsages = scanResult.anyUsages.filter(usage => usage.severity === 'critical')
    
    if (criticalUsages.length > 0) {
      console.log('\n=== CRITICAL ANY TYPE USAGES ===')
      criticalUsages.forEach((usage, index) => {
        console.log(`\n${index + 1}. ${usage.file}:${usage.line}:${usage.column}`)
        console.log(`   Type: ${usage.type}`)
        console.log(`   Context: ${usage.context}`)
        console.log(`   Suggestion: ${usage.suggestion}`)
        console.log(`   Code snippet:\n${usage.codeSnippet}`)
      })
    }

    // Critical usages should be addressed
    expect(criticalUsages.length).toBeLessThan(10) // Allow some, but flag for attention
  })
})
