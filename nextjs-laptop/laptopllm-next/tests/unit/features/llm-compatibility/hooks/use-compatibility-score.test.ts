import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useCompatibilityScore } from '@/features/llm-compatibility/hooks/use-compatibility'
import { compatibilityApiService } from '@/features/llm-compatibility/services/compatibility-api'
import type { CompatibilityScore } from '@/features/llm-compatibility/types'
import { ReactNode } from 'react'

// Mock CompatibilityApiService
vi.mock('@/features/llm-compatibility/services/compatibility-api')

const mockCompatibilityApiService = {
  calculateCompatibility: vi.fn(),
}

vi.mocked(compatibilityApiService).calculateCompatibility = mockCompatibilityApiService.calculateCompatibility

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('useCompatibilityScore', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('successful compatibility calculation', () => {
    it('should calculate compatibility score successfully', async () => {
      const mockScore: CompatibilityScore = {
        overall: 85,
        performance: 80,
        memory: 90,
        efficiency: 85,
        recommendation: 'Excellent',
        breakdown: {
          cpu: 85,
          gpu: 75,
          memory: 90,
          storage: 80,
        },
        warnings: [],
        optimizations: ['Enable GPU acceleration'],
      }

      mockCompatibilityApiService.calculateCompatibility.mockResolvedValue(mockScore)

      const { result } = renderHook(
        () => useCompatibilityScore('laptop-1', 'model-1'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockScore)
      expect(mockCompatibilityApiService.calculateCompatibility).toHaveBeenCalledWith(
        'laptop-1',
        'model-1'
      )
    })

    it('should handle high compatibility scores', async () => {
      const mockScore: CompatibilityScore = {
        overall: 95,
        performance: 95,
        memory: 100,
        efficiency: 90,
        recommendation: 'Excellent',
        breakdown: {
          cpu: 95,
          gpu: 90,
          memory: 100,
          storage: 95,
        },
        warnings: [],
        optimizations: [],
      }

      mockCompatibilityApiService.calculateCompatibility.mockResolvedValue(mockScore)

      const { result } = renderHook(
        () => useCompatibilityScore('laptop-high-end', 'model-small'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data?.overall).toBe(95)
      expect(result.current.data?.recommendation).toBe('Excellent')
      expect(result.current.data?.warnings).toHaveLength(0)
    })

    it('should handle low compatibility scores with warnings', async () => {
      const mockScore: CompatibilityScore = {
        overall: 35,
        performance: 30,
        memory: 40,
        efficiency: 35,
        recommendation: 'Poor',
        breakdown: {
          cpu: 40,
          gpu: 20,
          memory: 45,
          storage: 35,
        },
        warnings: [
          'Insufficient GPU memory',
          'CPU may struggle with large models',
          'Consider upgrading RAM',
        ],
        optimizations: [
          'Use quantized model',
          'Reduce batch size',
          'Enable CPU-only mode',
        ],
      }

      mockCompatibilityApiService.calculateCompatibility.mockResolvedValue(mockScore)

      const { result } = renderHook(
        () => useCompatibilityScore('laptop-low-end', 'model-large'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data?.overall).toBe(35)
      expect(result.current.data?.recommendation).toBe('Poor')
      expect(result.current.data?.warnings).toHaveLength(3)
      expect(result.current.data?.optimizations).toHaveLength(3)
    })
  })

  describe('error handling', () => {
    it('should handle API errors', async () => {
      mockCompatibilityApiService.calculateCompatibility.mockRejectedValue(
        new Error('Failed to calculate compatibility')
      )

      const { result } = renderHook(
        () => useCompatibilityScore('laptop-1', 'model-1'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toBeInstanceOf(Error)
      expect(result.current.error?.message).toBe('Failed to calculate compatibility')
    })

    it('should handle network errors', async () => {
      mockCompatibilityApiService.calculateCompatibility.mockRejectedValue(
        new Error('Network error')
      )

      const { result } = renderHook(
        () => useCompatibilityScore('laptop-1', 'model-1'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error?.message).toBe('Network error')
    })
  })

  describe('loading states', () => {
    it('should show loading state initially', () => {
      mockCompatibilityApiService.calculateCompatibility.mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )

      const { result } = renderHook(
        () => useCompatibilityScore('laptop-1', 'model-1'),
        { wrapper: createWrapper() }
      )

      expect(result.current.isLoading).toBe(true)
      expect(result.current.data).toBeUndefined()
      expect(result.current.isError).toBe(false)
    })

    it('should not fetch when laptopId is missing', () => {
      const { result } = renderHook(
        () => useCompatibilityScore('', 'model-1'),
        { wrapper: createWrapper() }
      )

      expect(result.current.isLoading).toBe(false)
      expect(result.current.data).toBeUndefined()
      expect(mockCompatibilityApiService.calculateCompatibility).not.toHaveBeenCalled()
    })

    it('should not fetch when modelId is missing', () => {
      const { result } = renderHook(
        () => useCompatibilityScore('laptop-1', ''),
        { wrapper: createWrapper() }
      )

      expect(result.current.isLoading).toBe(false)
      expect(result.current.data).toBeUndefined()
      expect(mockCompatibilityApiService.calculateCompatibility).not.toHaveBeenCalled()
    })

    it('should not fetch when both IDs are missing', () => {
      const { result } = renderHook(
        () => useCompatibilityScore('', ''),
        { wrapper: createWrapper() }
      )

      expect(result.current.isLoading).toBe(false)
      expect(result.current.data).toBeUndefined()
      expect(mockCompatibilityApiService.calculateCompatibility).not.toHaveBeenCalled()
    })
  })

  describe('caching behavior', () => {
    it('should cache results for same laptop-model combination', async () => {
      const mockScore: CompatibilityScore = {
        overall: 85,
        performance: 80,
        memory: 90,
        efficiency: 85,
        recommendation: 'Excellent',
        breakdown: {
          cpu: 85,
          gpu: 75,
          memory: 90,
          storage: 80,
        },
        warnings: [],
        optimizations: [],
      }

      mockCompatibilityApiService.calculateCompatibility.mockResolvedValue(mockScore)

      // First render
      const { result: result1 } = renderHook(
        () => useCompatibilityScore('laptop-1', 'model-1'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result1.current.isSuccess).toBe(true)
      })

      // Second render with same IDs should use cache
      const { result: result2 } = renderHook(
        () => useCompatibilityScore('laptop-1', 'model-1'),
        { wrapper: createWrapper() }
      )

      expect(result2.current.data).toEqual(mockScore)
      // Should only be called once due to caching
      expect(mockCompatibilityApiService.calculateCompatibility).toHaveBeenCalledTimes(1)
    })

    it('should generate different cache keys for different combinations', async () => {
      const mockScore1: CompatibilityScore = {
        overall: 85,
        performance: 80,
        memory: 90,
        efficiency: 85,
        recommendation: 'Excellent',
        breakdown: { cpu: 85, gpu: 75, memory: 90, storage: 80 },
        warnings: [],
        optimizations: [],
      }

      const mockScore2: CompatibilityScore = {
        overall: 75,
        performance: 70,
        memory: 80,
        efficiency: 75,
        recommendation: 'Good',
        breakdown: { cpu: 75, gpu: 65, memory: 80, storage: 70 },
        warnings: [],
        optimizations: [],
      }

      mockCompatibilityApiService.calculateCompatibility
        .mockResolvedValueOnce(mockScore1)
        .mockResolvedValueOnce(mockScore2)

      // First combination
      const { result: result1 } = renderHook(
        () => useCompatibilityScore('laptop-1', 'model-1'),
        { wrapper: createWrapper() }
      )

      // Second combination
      const { result: result2 } = renderHook(
        () => useCompatibilityScore('laptop-2', 'model-1'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result1.current.isSuccess).toBe(true)
        expect(result2.current.isSuccess).toBe(true)
      })

      expect(result1.current.data).toEqual(mockScore1)
      expect(result2.current.data).toEqual(mockScore2)
      expect(mockCompatibilityApiService.calculateCompatibility).toHaveBeenCalledTimes(2)
    })
  })

  describe('query configuration', () => {
    it('should have correct stale time configuration', async () => {
      const mockScore: CompatibilityScore = {
        overall: 85,
        performance: 80,
        memory: 90,
        efficiency: 85,
        recommendation: 'Excellent',
        breakdown: { cpu: 85, gpu: 75, memory: 90, storage: 80 },
        warnings: [],
        optimizations: [],
      }

      mockCompatibilityApiService.calculateCompatibility.mockResolvedValue(mockScore)

      const { result } = renderHook(
        () => useCompatibilityScore('laptop-1', 'model-1'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      // Data should be considered fresh for 30 minutes
      expect(result.current.isStale).toBe(false)
    })

    it('should retry failed requests', async () => {
      mockCompatibilityApiService.calculateCompatibility
        .mockRejectedValueOnce(new Error('Temporary error'))
        .mockRejectedValueOnce(new Error('Temporary error'))
        .mockResolvedValueOnce({
          overall: 85,
          performance: 80,
          memory: 90,
          efficiency: 85,
          recommendation: 'Excellent',
          breakdown: { cpu: 85, gpu: 75, memory: 90, storage: 80 },
          warnings: [],
          optimizations: [],
        })

      const { result } = renderHook(
        () => useCompatibilityScore('laptop-1', 'model-1'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      // Should have retried 2 times before succeeding
      expect(mockCompatibilityApiService.calculateCompatibility).toHaveBeenCalledTimes(3)
    })
  })
})
