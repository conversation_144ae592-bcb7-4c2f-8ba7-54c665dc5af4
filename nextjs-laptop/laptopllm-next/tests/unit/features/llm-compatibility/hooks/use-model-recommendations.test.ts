import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useModelRecommendations } from '@/features/llm-compatibility/hooks/use-compatibility'
import { compatibilityApiService } from '@/features/llm-compatibility/services/compatibility-api'
import type { ModelRecommendation, CompatibilityFilter } from '@/features/llm-compatibility/types'
import { ReactNode } from 'react'

// Mock CompatibilityApiService
vi.mock('@/features/llm-compatibility/services/compatibility-api')

const mockCompatibilityApiService = {
  getModelRecommendations: vi.fn(),
}

vi.mocked(compatibilityApiService).getModelRecommendations = mockCompatibilityApiService.getModelRecommendations

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('useModelRecommendations', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('successful recommendations', () => {
    it('should get model recommendations for laptop', async () => {
      const mockRecommendations: ModelRecommendation[] = [
        {
          model: {
            id: 'model-1',
            name: 'Llama 2 7B',
            size: '7B',
            memoryRequirement: 8,
            framework: 'PyTorch',
          } as any,
          score: {
            overall: 90,
            performance: 85,
            memory: 95,
            efficiency: 90,
            recommendation: 'Excellent',
            breakdown: { cpu: 85, gpu: 80, memory: 95, storage: 85 },
            warnings: [],
            optimizations: [],
          },
          performance: {
            tokensPerSecond: 75,
            memoryUsage: 8,
            powerConsumption: 45,
            thermalLoad: 60,
            batteryLife: 6,
          } as any,
          reasoning: [
            'Optimal memory usage for this laptop',
            'Good performance with current CPU',
            'Efficient power consumption',
          ],
          category: 'optimal',
          confidence: 0.95,
        },
        {
          model: {
            id: 'model-2',
            name: 'Phi-3 Mini',
            size: '3.8B',
            memoryRequirement: 4,
            framework: 'ONNX',
          } as any,
          score: {
            overall: 85,
            performance: 80,
            memory: 90,
            efficiency: 95,
            recommendation: 'Excellent',
            breakdown: { cpu: 80, gpu: 75, memory: 90, storage: 80 },
            warnings: [],
            optimizations: [],
          },
          performance: {
            tokensPerSecond: 60,
            memoryUsage: 4,
            powerConsumption: 25,
            thermalLoad: 40,
            batteryLife: 8,
          } as any,
          reasoning: [
            'Very efficient model',
            'Low memory requirements',
            'Excellent battery life',
          ],
          category: 'optimal',
          confidence: 0.92,
        },
      ]

      mockCompatibilityApiService.getModelRecommendations.mockResolvedValue(mockRecommendations)

      const { result } = renderHook(
        () => useModelRecommendations('laptop-1'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockRecommendations)
      expect(mockCompatibilityApiService.getModelRecommendations).toHaveBeenCalledWith(
        'laptop-1',
        undefined
      )
    })

    it('should apply filters to recommendations', async () => {
      const mockRecommendations: ModelRecommendation[] = [
        {
          model: {
            id: 'model-1',
            name: 'Llama 2 7B',
            size: '7B',
          } as any,
          score: {
            overall: 90,
            performance: 85,
            memory: 95,
            efficiency: 90,
            recommendation: 'Excellent',
          } as any,
          performance: {} as any,
          reasoning: ['High performance model'],
          category: 'optimal',
          confidence: 0.95,
        },
      ]

      mockCompatibilityApiService.getModelRecommendations.mockResolvedValue(mockRecommendations)

      const filters: CompatibilityFilter = {
        minScore: 85,
        modelSizes: ['7B', '13B'],
        frameworks: ['PyTorch'],
        memoryRequirements: { min: 8, max: 16 },
      }

      const { result } = renderHook(
        () => useModelRecommendations('laptop-1', filters),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockRecommendations)
      expect(mockCompatibilityApiService.getModelRecommendations).toHaveBeenCalledWith(
        'laptop-1',
        filters
      )
    })

    it('should handle empty recommendations', async () => {
      mockCompatibilityApiService.getModelRecommendations.mockResolvedValue([])

      const { result } = renderHook(
        () => useModelRecommendations('laptop-low-end'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual([])
      expect(result.current.data).toHaveLength(0)
    })
  })

  describe('recommendation categories', () => {
    it('should categorize recommendations correctly', async () => {
      const mockRecommendations: ModelRecommendation[] = [
        {
          model: { id: 'model-optimal', name: 'Optimal Model' } as any,
          score: { overall: 95 } as any,
          performance: {} as any,
          reasoning: ['Perfect match'],
          category: 'optimal',
          confidence: 0.98,
        },
        {
          model: { id: 'model-good', name: 'Good Model' } as any,
          score: { overall: 80 } as any,
          performance: {} as any,
          reasoning: ['Good performance'],
          category: 'good',
          confidence: 0.85,
        },
        {
          model: { id: 'model-acceptable', name: 'Acceptable Model' } as any,
          score: { overall: 65 } as any,
          performance: {} as any,
          reasoning: ['Acceptable with limitations'],
          category: 'acceptable',
          confidence: 0.70,
        },
        {
          model: { id: 'model-poor', name: 'Poor Model' } as any,
          score: { overall: 35 } as any,
          performance: {} as any,
          reasoning: ['Not recommended'],
          category: 'not-recommended',
          confidence: 0.60,
        },
      ]

      mockCompatibilityApiService.getModelRecommendations.mockResolvedValue(mockRecommendations)

      const { result } = renderHook(
        () => useModelRecommendations('laptop-1'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      const recommendations = result.current.data!
      expect(recommendations).toHaveLength(4)
      
      const optimal = recommendations.find(r => r.category === 'optimal')
      const good = recommendations.find(r => r.category === 'good')
      const acceptable = recommendations.find(r => r.category === 'acceptable')
      const notRecommended = recommendations.find(r => r.category === 'not-recommended')

      expect(optimal?.confidence).toBe(0.98)
      expect(good?.confidence).toBe(0.85)
      expect(acceptable?.confidence).toBe(0.70)
      expect(notRecommended?.confidence).toBe(0.60)
    })
  })

  describe('error handling', () => {
    it('should handle API errors', async () => {
      mockCompatibilityApiService.getModelRecommendations.mockRejectedValue(
        new Error('Failed to get recommendations')
      )

      const { result } = renderHook(
        () => useModelRecommendations('laptop-1'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toBeInstanceOf(Error)
      expect(result.current.error?.message).toBe('Failed to get recommendations')
    })

    it('should handle network errors', async () => {
      mockCompatibilityApiService.getModelRecommendations.mockRejectedValue(
        new Error('Network error')
      )

      const { result } = renderHook(
        () => useModelRecommendations('laptop-1'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error?.message).toBe('Network error')
    })
  })

  describe('loading states', () => {
    it('should show loading state initially', () => {
      mockCompatibilityApiService.getModelRecommendations.mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )

      const { result } = renderHook(
        () => useModelRecommendations('laptop-1'),
        { wrapper: createWrapper() }
      )

      expect(result.current.isLoading).toBe(true)
      expect(result.current.data).toBeUndefined()
      expect(result.current.isError).toBe(false)
    })

    it('should not fetch when laptopId is missing', () => {
      const { result } = renderHook(
        () => useModelRecommendations(''),
        { wrapper: createWrapper() }
      )

      expect(result.current.isLoading).toBe(false)
      expect(result.current.data).toBeUndefined()
      expect(mockCompatibilityApiService.getModelRecommendations).not.toHaveBeenCalled()
    })
  })

  describe('filtering and sorting', () => {
    it('should handle complex filters', async () => {
      const mockRecommendations: ModelRecommendation[] = [
        {
          model: {
            id: 'model-1',
            name: 'Filtered Model',
            size: '7B',
            framework: 'PyTorch',
          } as any,
          score: { overall: 90 } as any,
          performance: {} as any,
          reasoning: ['Meets all criteria'],
          category: 'optimal',
          confidence: 0.95,
        },
      ]

      mockCompatibilityApiService.getModelRecommendations.mockResolvedValue(mockRecommendations)

      const complexFilters: CompatibilityFilter = {
        minScore: 80,
        maxScore: 95,
        modelSizes: ['7B', '13B'],
        frameworks: ['PyTorch', 'TensorFlow'],
        computeRequirements: ['Medium', 'High'],
        memoryRequirements: { min: 8, max: 32 },
        useCases: ['development', 'research'],
      }

      const { result } = renderHook(
        () => useModelRecommendations('laptop-1', complexFilters),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockCompatibilityApiService.getModelRecommendations).toHaveBeenCalledWith(
        'laptop-1',
        complexFilters
      )
      expect(result.current.data).toEqual(mockRecommendations)
    })
  })

  describe('caching behavior', () => {
    it('should cache results for same laptop and filters', async () => {
      const mockRecommendations: ModelRecommendation[] = [
        {
          model: { id: 'model-1', name: 'Test Model' } as any,
          score: { overall: 85 } as any,
          performance: {} as any,
          reasoning: ['Good match'],
          category: 'good',
          confidence: 0.85,
        },
      ]

      mockCompatibilityApiService.getModelRecommendations.mockResolvedValue(mockRecommendations)

      const filters: CompatibilityFilter = { minScore: 80 }

      // First render
      const { result: result1 } = renderHook(
        () => useModelRecommendations('laptop-1', filters),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result1.current.isSuccess).toBe(true)
      })

      // Second render with same parameters should use cache
      const { result: result2 } = renderHook(
        () => useModelRecommendations('laptop-1', filters),
        { wrapper: createWrapper() }
      )

      expect(result2.current.data).toEqual(mockRecommendations)
      // Should only be called once due to caching
      expect(mockCompatibilityApiService.getModelRecommendations).toHaveBeenCalledTimes(1)
    })

    it('should generate different cache keys for different filters', async () => {
      const mockRecommendations1: ModelRecommendation[] = [
        {
          model: { id: 'model-1', name: 'Model 1' } as any,
          score: { overall: 85 } as any,
          performance: {} as any,
          reasoning: [],
          category: 'good',
          confidence: 0.85,
        },
      ]

      const mockRecommendations2: ModelRecommendation[] = [
        {
          model: { id: 'model-2', name: 'Model 2' } as any,
          score: { overall: 90 } as any,
          performance: {} as any,
          reasoning: [],
          category: 'optimal',
          confidence: 0.90,
        },
      ]

      mockCompatibilityApiService.getModelRecommendations
        .mockResolvedValueOnce(mockRecommendations1)
        .mockResolvedValueOnce(mockRecommendations2)

      const filters1: CompatibilityFilter = { minScore: 80 }
      const filters2: CompatibilityFilter = { minScore: 85 }

      // First call with filters1
      const { result: result1 } = renderHook(
        () => useModelRecommendations('laptop-1', filters1),
        { wrapper: createWrapper() }
      )

      // Second call with filters2
      const { result: result2 } = renderHook(
        () => useModelRecommendations('laptop-1', filters2),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result1.current.isSuccess).toBe(true)
        expect(result2.current.isSuccess).toBe(true)
      })

      expect(result1.current.data).toEqual(mockRecommendations1)
      expect(result2.current.data).toEqual(mockRecommendations2)
      expect(mockCompatibilityApiService.getModelRecommendations).toHaveBeenCalledTimes(2)
    })
  })
})
