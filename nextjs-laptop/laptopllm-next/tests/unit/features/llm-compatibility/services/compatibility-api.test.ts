import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { CompatibilityApiService } from '@/features/llm-compatibility/services/compatibility-api'
import type { 
  CompatibilityScore, 
  CompatibilityAnalysis, 
  ModelRecommendation,
  CompatibilityReport,
  PerformanceBenchmark,
  CompatibilityFilter
} from '@/features/llm-compatibility/types'

// Mock fetch globally
const mockFetch = vi.fn()
global.fetch = mockFetch

describe('CompatibilityApiService', () => {
  let service: CompatibilityApiService

  beforeEach(() => {
    vi.clearAllMocks()
    service = new CompatibilityApiService()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('calculateCompatibility', () => {
    it('should calculate compatibility score successfully', async () => {
      const mockScore: CompatibilityScore = {
        overall: 85,
        performance: 80,
        memory: 90,
        efficiency: 85,
        recommendation: 'Excellent',
        breakdown: {
          cpu: 85,
          gpu: 75,
          memory: 90,
          storage: 80,
        },
        warnings: [],
        optimizations: ['Enable GPU acceleration'],
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockScore),
      })

      const result = await service.calculateCompatibility('laptop-1', 'model-1')

      expect(result).toEqual(mockScore)
      expect(mockFetch).toHaveBeenCalledWith('/api/compatibility/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ laptopId: 'laptop-1', modelId: 'model-1' }),
      })
    })

    it('should handle API errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        statusText: 'Internal Server Error',
      })

      await expect(
        service.calculateCompatibility('laptop-1', 'model-1')
      ).rejects.toThrow('Failed to calculate compatibility: Internal Server Error')
    })

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      await expect(
        service.calculateCompatibility('laptop-1', 'model-1')
      ).rejects.toThrow('Network error')
    })
  })

  describe('getCompatibilityAnalysis', () => {
    it('should get detailed compatibility analysis', async () => {
      const mockAnalysis: CompatibilityAnalysis = {
        laptop: {
          id: 'laptop-1',
          title: 'Dell XPS 13',
          brand: 'Dell',
          specifications: {
            cpu: { brand: 'Intel', model: 'Core i7' },
            memory: { size: 16 },
          },
        } as any,
        model: {
          id: 'model-1',
          name: 'GPT-4',
          size: '175B',
          memoryRequirement: 16,
        } as any,
        score: {
          overall: 85,
          performance: 80,
          memory: 90,
          efficiency: 85,
          recommendation: 'Excellent',
        } as any,
        performance: {
          tokensPerSecond: 50,
          memoryUsage: 12,
          powerConsumption: 65,
        } as any,
        recommendations: ['Enable GPU acceleration', 'Increase memory'],
        limitations: ['Limited by CPU performance'],
        optimizations: ['Use quantized model', 'Enable mixed precision'],
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockAnalysis),
      })

      const result = await service.getCompatibilityAnalysis('laptop-1', 'model-1')

      expect(result).toEqual(mockAnalysis)
      expect(mockFetch).toHaveBeenCalledWith('/api/compatibility/analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ laptopId: 'laptop-1', modelId: 'model-1' }),
      })
    })
  })

  describe('getModelRecommendations', () => {
    it('should get model recommendations for laptop', async () => {
      const mockRecommendations: ModelRecommendation[] = [
        {
          model: {
            id: 'model-1',
            name: 'Llama 2 7B',
            size: '7B',
          } as any,
          score: {
            overall: 90,
            performance: 85,
            memory: 95,
            efficiency: 90,
            recommendation: 'Excellent',
          } as any,
          performance: {
            tokensPerSecond: 75,
            memoryUsage: 8,
            powerConsumption: 45,
          } as any,
          reasoning: ['Optimal memory usage', 'Good performance'],
          category: 'optimal',
          confidence: 0.95,
        },
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockRecommendations),
      })

      const result = await service.getModelRecommendations('laptop-1', { minScore: 80 })

      expect(result).toEqual(mockRecommendations)
      expect(mockFetch).toHaveBeenCalledWith('/api/compatibility/recommendations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ laptopId: 'laptop-1', filters: { minScore: 80 } }),
      })
    })

    it('should handle empty filters', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve([]),
      })

      await service.getModelRecommendations('laptop-1')

      expect(mockFetch).toHaveBeenCalledWith('/api/compatibility/recommendations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ laptopId: 'laptop-1', filters: undefined }),
      })
    })
  })

  describe('getCompatibilityReport', () => {
    it('should generate comprehensive compatibility report', async () => {
      const mockReport: CompatibilityReport = {
        laptop: {
          id: 'laptop-1',
          title: 'Dell XPS 13',
        } as any,
        recommendations: [],
        summary: {
          bestModel: { id: 'model-1', name: 'Llama 2 7B' } as any,
          bestScore: 90,
          averageScore: 75,
          totalModelsAnalyzed: 10,
        },
        hardwareAnalysis: {
          cpu: {
            score: 85,
            strengths: ['High single-core performance'],
            weaknesses: ['Limited cores'],
          },
          gpu: {
            score: 60,
            strengths: ['Integrated graphics'],
            weaknesses: ['No dedicated GPU'],
            accelerationSupport: false,
          },
          memory: {
            score: 90,
            adequateFor: ['Small models'],
            limitedFor: ['Large models'],
          },
          thermal: {
            score: 75,
            sustainedPerformance: true,
            throttlingRisk: 'low',
          },
        },
        useCaseRecommendations: {
          development: [],
          research: [],
          production: [],
          education: [],
        },
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockReport),
      })

      const result = await service.getCompatibilityReport('laptop-1')

      expect(result).toEqual(mockReport)
      expect(mockFetch).toHaveBeenCalledWith('/api/compatibility/report/laptop-1')
    })
  })

  describe('estimatePerformance', () => {
    it('should estimate performance metrics', async () => {
      const mockPerformance = {
        tokensPerSecond: 50,
        memoryUsage: 12,
        powerConsumption: 65,
        thermalLoad: 70,
        batteryLife: 4.5,
        estimatedLatency: 20,
        throughput: 2500,
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockPerformance),
      })

      const result = await service.estimatePerformance('laptop-1', 'model-1')

      expect(result).toEqual(mockPerformance)
      expect(mockFetch).toHaveBeenCalledWith('/api/compatibility/performance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ laptopId: 'laptop-1', modelId: 'model-1' }),
      })
    })
  })

  describe('compareLaptopsForModel', () => {
    it('should compare multiple laptops for a model', async () => {
      const mockComparison = {
        model: { id: 'model-1', name: 'Llama 2 7B' } as any,
        laptops: [
          { id: 'laptop-1', title: 'Dell XPS 13' },
          { id: 'laptop-2', title: 'MacBook Pro' },
        ] as any,
        scores: [
          { overall: 85, performance: 80 },
          { overall: 95, performance: 90 },
        ] as any,
        recommendations: [],
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockComparison),
      })

      const result = await service.compareLaptopsForModel(['laptop-1', 'laptop-2'], 'model-1')

      expect(result).toEqual(mockComparison)
      expect(mockFetch).toHaveBeenCalledWith('/api/compatibility/compare-laptops', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ laptopIds: ['laptop-1', 'laptop-2'], modelId: 'model-1' }),
      })
    })
  })

  describe('compareModelsForLaptop', () => {
    it('should compare multiple models for a laptop', async () => {
      const mockComparison = {
        laptop: { id: 'laptop-1', title: 'Dell XPS 13' } as any,
        models: [
          { id: 'model-1', name: 'Llama 2 7B' },
          { id: 'model-2', name: 'GPT-3.5' },
        ] as any,
        scores: [
          { overall: 85, performance: 80 },
          { overall: 75, performance: 70 },
        ] as any,
        recommendations: [],
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockComparison),
      })

      const result = await service.compareModelsForLaptop('laptop-1', ['model-1', 'model-2'])

      expect(result).toEqual(mockComparison)
      expect(mockFetch).toHaveBeenCalledWith('/api/compatibility/compare-models', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ laptopId: 'laptop-1', modelIds: ['model-1', 'model-2'] }),
      })
    })
  })

  describe('getAvailableModels', () => {
    it('should get available models with filters', async () => {
      const mockModels = [
        {
          id: 'model-1',
          name: 'Llama 2 7B',
          size: '7B',
          memoryRequirement: 8,
          framework: 'PyTorch',
        },
        {
          id: 'model-2',
          name: 'GPT-3.5',
          size: '175B',
          memoryRequirement: 16,
          framework: 'OpenAI',
        },
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockModels),
      })

      const filters: CompatibilityFilter = {
        minScore: 70,
        modelSizes: ['7B', '13B'],
        frameworks: ['PyTorch'],
      }

      const result = await service.getAvailableModels(filters)

      expect(result).toEqual(mockModels)
      expect(mockFetch).toHaveBeenCalledWith('/api/compatibility/models', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ filters }),
      })
    })
  })

  describe('getOptimizationSuggestions', () => {
    it('should get optimization suggestions', async () => {
      const mockSuggestions = {
        hardware: [
          'Upgrade to 32GB RAM for better performance',
          'Consider external GPU for acceleration',
        ],
        software: [
          'Use quantized model variants',
          'Enable mixed precision training',
        ],
        configuration: [
          'Adjust batch size to 4',
          'Set temperature to 0.7',
        ],
        performance: [
          'Expected 25% improvement with RAM upgrade',
          'GPU acceleration could provide 3x speedup',
        ],
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockSuggestions),
      })

      const result = await service.getOptimizationSuggestions('laptop-1', 'model-1')

      expect(result).toEqual(mockSuggestions)
      expect(mockFetch).toHaveBeenCalledWith('/api/compatibility/optimizations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ laptopId: 'laptop-1', modelId: 'model-1' }),
      })
    })
  })

  describe('getCompatibilityStats', () => {
    it('should get compatibility statistics', async () => {
      const mockStats = {
        totalCompatibilityChecks: 1500,
        averageScore: 72.5,
        topPerformingLaptops: [
          { id: 'laptop-1', title: 'MacBook Pro M2', averageScore: 95 },
          { id: 'laptop-2', title: 'Dell XPS 15', averageScore: 88 },
        ],
        mostCompatibleModels: [
          { id: 'model-1', name: 'Llama 2 7B', averageScore: 85 },
          { id: 'model-2', name: 'Phi-3 Mini', averageScore: 82 },
        ],
        scoreDistribution: {
          excellent: 25,
          good: 35,
          fair: 25,
          poor: 15,
        },
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockStats),
      })

      const result = await service.getCompatibilityStats()

      expect(result).toEqual(mockStats)
      expect(mockFetch).toHaveBeenCalledWith('/api/compatibility/stats')
    })
  })
})
