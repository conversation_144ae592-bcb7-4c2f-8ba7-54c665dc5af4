import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { CompatibilityScoreCard } from '@/features/llm-compatibility/components/CompatibilityScoreCard'
import type { CompatibilityScore } from '@/features/llm-compatibility/types'

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  Cpu: ({ ...props }) => <div data-testid="cpu-icon" {...props} />,
  MemoryStick: ({ ...props }) => <div data-testid="memory-icon" {...props} />,
  HardDrive: ({ ...props }) => <div data-testid="storage-icon" {...props} />,
  Zap: ({ ...props }) => <div data-testid="gpu-icon" {...props} />,
  Thermometer: ({ ...props }) => <div data-testid="thermal-icon" {...props} />,
  TrendingUp: ({ ...props }) => <div data-testid="trending-icon" {...props} />,
  AlertTriangle: ({ ...props }) => <div data-testid="warning-icon" {...props} />,
  CheckCircle: ({ ...props }) => <div data-testid="check-icon" {...props} />,
  Info: ({ ...props }) => <div data-testid="info-icon" {...props} />,
  Lightbulb: ({ ...props }) => <div data-testid="lightbulb-icon" {...props} />,
  Target: ({ ...props }) => <div data-testid="target-icon" {...props} />,
  Clock: ({ ...props }) => <div data-testid="clock-icon" {...props} />,
  Battery: ({ ...props }) => <div data-testid="battery-icon" {...props} />,
  Gauge: ({ ...props }) => <div data-testid="gauge-icon" {...props} />,
}))

describe('CompatibilityScoreCard', () => {
  const mockHighScore: CompatibilityScore = {
    overall: 90,
    performance: 85,
    memory: 95,
    efficiency: 88,
    recommendation: 'Excellent',
    breakdown: {
      cpu: 85,
      gpu: 80,
      memory: 95,
      storage: 85,
    },
    warnings: [],
    optimizations: ['Enable GPU acceleration for better performance'],
  }

  const mockMediumScore: CompatibilityScore = {
    overall: 65,
    performance: 60,
    memory: 70,
    efficiency: 65,
    recommendation: 'Good',
    breakdown: {
      cpu: 70,
      gpu: 50,
      memory: 75,
      storage: 65,
    },
    warnings: ['GPU memory may be limited for large models'],
    optimizations: [
      'Consider using quantized models',
      'Reduce batch size for better performance',
    ],
  }

  const mockLowScore: CompatibilityScore = {
    overall: 35,
    performance: 30,
    memory: 40,
    efficiency: 35,
    recommendation: 'Poor',
    breakdown: {
      cpu: 40,
      gpu: 20,
      memory: 45,
      storage: 35,
    },
    warnings: [
      'Insufficient GPU memory for most models',
      'CPU may struggle with inference',
      'Consider upgrading hardware',
    ],
    optimizations: [
      'Use CPU-only inference',
      'Try smaller model variants',
      'Enable aggressive quantization',
    ],
  }

  describe('basic rendering', () => {
    it('should render compatibility score card with high score', () => {
      render(<CompatibilityScoreCard score={mockHighScore} />)

      expect(screen.getByText('Compatibility Score')).toBeInTheDocument()
      expect(screen.getByText('90')).toBeInTheDocument()
      expect(screen.getByText('Excellent')).toBeInTheDocument()
    })

    it('should render overall score with correct styling for high score', () => {
      render(<CompatibilityScoreCard score={mockHighScore} />)

      const scoreElement = screen.getByText('90')
      expect(scoreElement).toHaveClass('text-green-600')
    })

    it('should render overall score with correct styling for medium score', () => {
      render(<CompatibilityScoreCard score={mockMediumScore} />)

      const scoreElement = screen.getByText('65')
      expect(scoreElement).toHaveClass('text-yellow-600')
    })

    it('should render overall score with correct styling for low score', () => {
      render(<CompatibilityScoreCard score={mockLowScore} />)

      const scoreElement = screen.getByText('35')
      expect(scoreElement).toHaveClass('text-red-600')
    })
  })

  describe('breakdown scores', () => {
    it('should display all breakdown scores', () => {
      render(<CompatibilityScoreCard score={mockHighScore} />)

      expect(screen.getByText('CPU: 85')).toBeInTheDocument()
      expect(screen.getByText('GPU: 80')).toBeInTheDocument()
      expect(screen.getByText('Memory: 95')).toBeInTheDocument()
      expect(screen.getByText('Storage: 85')).toBeInTheDocument()
    })

    it('should render progress bars for breakdown scores', () => {
      render(<CompatibilityScoreCard score={mockHighScore} />)

      const progressBars = screen.getAllByRole('progressbar')
      expect(progressBars).toHaveLength(4) // CPU, GPU, Memory, Storage
    })

    it('should display correct progress values', () => {
      render(<CompatibilityScoreCard score={mockHighScore} />)

      const cpuProgress = screen.getByLabelText('CPU compatibility: 85%')
      const gpuProgress = screen.getByLabelText('GPU compatibility: 80%')
      const memoryProgress = screen.getByLabelText('Memory compatibility: 95%')
      const storageProgress = screen.getByLabelText('Storage compatibility: 85%')

      expect(cpuProgress).toHaveAttribute('aria-valuenow', '85')
      expect(gpuProgress).toHaveAttribute('aria-valuenow', '80')
      expect(memoryProgress).toHaveAttribute('aria-valuenow', '95')
      expect(storageProgress).toHaveAttribute('aria-valuenow', '85')
    })
  })

  describe('performance metrics', () => {
    it('should display performance metrics when available', () => {
      render(<CompatibilityScoreCard score={mockHighScore} />)

      expect(screen.getByText('Performance: 85')).toBeInTheDocument()
      expect(screen.getByText('Memory: 95')).toBeInTheDocument()
      expect(screen.getByText('Efficiency: 88')).toBeInTheDocument()
    })

    it('should render performance progress bars', () => {
      render(<CompatibilityScoreCard score={mockHighScore} />)

      const performanceProgress = screen.getByLabelText('Performance score: 85%')
      const memoryProgress = screen.getByLabelText('Memory score: 95%')
      const efficiencyProgress = screen.getByLabelText('Efficiency score: 88%')

      expect(performanceProgress).toBeInTheDocument()
      expect(memoryProgress).toBeInTheDocument()
      expect(efficiencyProgress).toBeInTheDocument()
    })
  })

  describe('warnings display', () => {
    it('should not display warnings section when no warnings', () => {
      render(<CompatibilityScoreCard score={mockHighScore} />)

      expect(screen.queryByText('Warnings')).not.toBeInTheDocument()
    })

    it('should display warnings when present', () => {
      render(<CompatibilityScoreCard score={mockMediumScore} />)

      expect(screen.getByText('Warnings')).toBeInTheDocument()
      expect(screen.getByText('GPU memory may be limited for large models')).toBeInTheDocument()
    })

    it('should display multiple warnings', () => {
      render(<CompatibilityScoreCard score={mockLowScore} />)

      expect(screen.getByText('Warnings')).toBeInTheDocument()
      expect(screen.getByText('Insufficient GPU memory for most models')).toBeInTheDocument()
      expect(screen.getByText('CPU may struggle with inference')).toBeInTheDocument()
      expect(screen.getByText('Consider upgrading hardware')).toBeInTheDocument()
    })

    it('should render warning icons', () => {
      render(<CompatibilityScoreCard score={mockLowScore} />)

      const warningIcons = screen.getAllByTestId('warning-icon')
      expect(warningIcons.length).toBeGreaterThan(0)
    })
  })

  describe('optimizations display', () => {
    it('should display optimizations when present', () => {
      render(<CompatibilityScoreCard score={mockHighScore} />)

      expect(screen.getByText('Optimizations')).toBeInTheDocument()
      expect(screen.getByText('Enable GPU acceleration for better performance')).toBeInTheDocument()
    })

    it('should display multiple optimizations', () => {
      render(<CompatibilityScoreCard score={mockMediumScore} />)

      expect(screen.getByText('Optimizations')).toBeInTheDocument()
      expect(screen.getByText('Consider using quantized models')).toBeInTheDocument()
      expect(screen.getByText('Reduce batch size for better performance')).toBeInTheDocument()
    })

    it('should render optimization icons', () => {
      render(<CompatibilityScoreCard score={mockMediumScore} />)

      const lightbulbIcons = screen.getAllByTestId('lightbulb-icon')
      expect(lightbulbIcons.length).toBeGreaterThan(0)
    })
  })

  describe('interactive features', () => {
    it('should support tabs for different views', () => {
      render(<CompatibilityScoreCard score={mockHighScore} />)

      const overviewTab = screen.getByRole('tab', { name: /overview/i })
      const detailsTab = screen.getByRole('tab', { name: /details/i })

      expect(overviewTab).toBeInTheDocument()
      expect(detailsTab).toBeInTheDocument()
    })

    it('should switch between tabs', () => {
      render(<CompatibilityScoreCard score={mockHighScore} />)

      const detailsTab = screen.getByRole('tab', { name: /details/i })
      fireEvent.click(detailsTab)

      // Should show detailed breakdown
      expect(screen.getByText('CPU: 85')).toBeInTheDocument()
    })
  })

  describe('loading state', () => {
    it('should show loading skeleton when score is undefined', () => {
      render(<CompatibilityScoreCard loading />)

      expect(screen.getByTestId('compatibility-score-skeleton')).toBeInTheDocument()
      expect(screen.queryByText('Compatibility Score')).not.toBeInTheDocument()
    })
  })

  describe('error handling', () => {
    it('should handle missing breakdown gracefully', () => {
      const scoreWithoutBreakdown = {
        ...mockHighScore,
        breakdown: undefined,
      }

      render(<CompatibilityScoreCard score={scoreWithoutBreakdown as any} />)

      expect(screen.getByText('90')).toBeInTheDocument()
      expect(screen.getByText('Excellent')).toBeInTheDocument()
      // Should not crash when breakdown is missing
    })

    it('should handle missing performance metrics gracefully', () => {
      const scoreWithoutPerformance = {
        overall: 85,
        recommendation: 'Good',
        breakdown: {
          cpu: 85,
          gpu: 80,
          memory: 90,
          storage: 85,
        },
        warnings: [],
        optimizations: [],
      }

      render(<CompatibilityScoreCard score={scoreWithoutPerformance as any} />)

      expect(screen.getByText('85')).toBeInTheDocument()
      expect(screen.getByText('Good')).toBeInTheDocument()
    })
  })

  describe('accessibility', () => {
    it('should have proper ARIA labels for progress bars', () => {
      render(<CompatibilityScoreCard score={mockHighScore} />)

      expect(screen.getByLabelText('CPU compatibility: 85%')).toBeInTheDocument()
      expect(screen.getByLabelText('GPU compatibility: 80%')).toBeInTheDocument()
      expect(screen.getByLabelText('Memory compatibility: 95%')).toBeInTheDocument()
      expect(screen.getByLabelText('Storage compatibility: 85%')).toBeInTheDocument()
    })

    it('should have proper heading structure', () => {
      render(<CompatibilityScoreCard score={mockHighScore} />)

      const heading = screen.getByRole('heading', { name: /compatibility score/i })
      expect(heading).toBeInTheDocument()
    })

    it('should have proper tab navigation', () => {
      render(<CompatibilityScoreCard score={mockHighScore} />)

      const tabList = screen.getByRole('tablist')
      const tabs = screen.getAllByRole('tab')

      expect(tabList).toBeInTheDocument()
      expect(tabs).toHaveLength(2) // Overview and Details tabs
    })
  })

  describe('custom styling', () => {
    it('should apply custom className', () => {
      render(<CompatibilityScoreCard score={mockHighScore} className="custom-class" />)

      const card = screen.getByRole('article')
      expect(card).toHaveClass('custom-class')
    })

    it('should support compact mode', () => {
      render(<CompatibilityScoreCard score={mockHighScore} compact />)

      const card = screen.getByRole('article')
      expect(card).toHaveClass('compact')
    })
  })
})
