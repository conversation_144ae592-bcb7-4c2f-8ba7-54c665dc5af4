import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { ScrapingService } from '@/lib/scraping/scraping-service'
import { Crawl4AIService } from '@/lib/scraping/crawl4ai.service'
import { FirecrawlService } from '@/lib/scraping/firecrawl.service'
import { PuppeteerService } from '@/lib/scraping/puppeteer.service'
import type { ScrapingConfig, ScrapingResult, ScrapingHealth } from '@/lib/scraping/scraping-service'

// Mock all scraper services
vi.mock('@/lib/scraping/crawl4ai.service')
vi.mock('@/lib/scraping/firecrawl.service')
vi.mock('@/lib/scraping/puppeteer.service')

const mockCrawl4AIService: Partial<Crawl4AIService> = {
  scrape: vi.fn(),
  extractLaptopData: vi.fn(),
  isHealthy: vi.fn(),
  cleanup: vi.fn(),
}

const mockFirecrawlService: Partial<FirecrawlService> = {
  scrape: vi.fn(),
  isHealthy: vi.fn(),
  cleanup: vi.fn(),
}

const mockPuppeteerService: Partial<PuppeteerService> = {
  scrape: vi.fn(),
  isHealthy: vi.fn(),
  cleanup: vi.fn(),
}

vi.mocked(Crawl4AIService).mockImplementation(() => mockCrawl4AIService as Crawl4AIService)
vi.mocked(FirecrawlService).mockImplementation(() => mockFirecrawlService as FirecrawlService)
vi.mocked(PuppeteerService).mockImplementation(() => mockPuppeteerService as PuppeteerService)

describe('ScrapingService', () => {
  let scrapingService: ScrapingService

  beforeEach(() => {
    vi.clearAllMocks()
    scrapingService = new ScrapingService()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('scraping hierarchy', () => {
    it('should use Crawl4AI as primary scraper successfully', async () => {
      const mockResult: ScrapingResult = {
        success: true,
        content: 'Scraped content',
        metadata: { title: 'Test Page' },
        url: 'https://example.com',
        timestamp: new Date(),
      }

      vi.mocked(mockCrawl4AIService.scrape).mockResolvedValue(mockResult)

      const config: ScrapingConfig = {
        url: 'https://example.com',
        timeout: 10000,
      }

      const result = await scrapingService.scrape(config)

      expect(result.success).toBe(true)
      expect(result.scraper).toBe('crawl4ai')
      expect(result.data).toEqual(mockResult)
      expect(mockCrawl4AIService.scrape).toHaveBeenCalledWith({
        url: 'https://example.com',
        selector: '',
        waitFor: undefined,
        actions: [],
      })
      expect(mockFirecrawlService.scrape).not.toHaveBeenCalled()
      expect(mockPuppeteerService.scrape).not.toHaveBeenCalled()
    })

    it('should fallback to Firecrawl when Crawl4AI fails', async () => {
      const mockFirecrawlResult: ScrapingResult = {
        success: true,
        content: 'Firecrawl content',
        metadata: { title: 'Test Page' },
        url: 'https://example.com',
        timestamp: new Date(),
      }

      vi.mocked(mockCrawl4AIService.scrape).mockRejectedValue(new Error('Crawl4AI failed'))
      vi.mocked(mockFirecrawlService.scrape).mockResolvedValue(mockFirecrawlResult)

      const config: ScrapingConfig = {
        url: 'https://example.com',
        timeout: 10000,
      }

      const result = await scrapingService.scrape(config)

      expect(result.success).toBe(true)
      expect(result.scraper).toBe('firecrawl')
      expect(result.data).toEqual(mockFirecrawlResult)
      expect(mockCrawl4AIService.scrape).toHaveBeenCalled()
      expect(mockFirecrawlService.scrape).toHaveBeenCalled()
      expect(mockPuppeteerService.scrape).not.toHaveBeenCalled()
    })

    it('should fallback to Puppeteer when both Crawl4AI and Firecrawl fail', async () => {
      const mockPuppeteerResult: ScrapingResult = {
        success: true,
        content: 'Puppeteer content',
        metadata: { title: 'Test Page' },
        url: 'https://example.com',
        timestamp: new Date(),
      }

      vi.mocked(mockCrawl4AIService.scrape).mockRejectedValue(new Error('Crawl4AI failed'))
      vi.mocked(mockFirecrawlService.scrape).mockRejectedValue(new Error('Firecrawl failed'))
      vi.mocked(mockPuppeteerService.scrape).mockResolvedValue(mockPuppeteerResult)

      const config: ScrapingConfig = {
        url: 'https://example.com',
        timeout: 10000,
      }

      const result = await scrapingService.scrape(config)

      expect(result.success).toBe(true)
      expect(result.scraper).toBe('puppeteer')
      expect(result.data).toEqual(mockPuppeteerResult)
      expect(mockCrawl4AIService.scrape).toHaveBeenCalled()
      expect(mockFirecrawlService.scrape).toHaveBeenCalled()
      expect(mockPuppeteerService.scrape).toHaveBeenCalled()
    })

    it('should fail when all scrapers fail', async () => {
      vi.mocked(mockCrawl4AIService.scrape).mockRejectedValue(new Error('Crawl4AI failed'))
      vi.mocked(mockFirecrawlService.scrape).mockRejectedValue(new Error('Firecrawl failed'))
      vi.mocked(mockPuppeteerService.scrape).mockRejectedValue(new Error('Puppeteer failed'))

      const config: ScrapingConfig = {
        url: 'https://example.com',
        timeout: 10000,
      }

      const result = await scrapingService.scrape(config)

      expect(result.success).toBe(false)
      expect(result.error).toBe('All scraping methods failed')
      expect(result.scraper).toBe('none')
      expect(mockCrawl4AIService.scrape).toHaveBeenCalled()
      expect(mockFirecrawlService.scrape).toHaveBeenCalled()
      expect(mockPuppeteerService.scrape).toHaveBeenCalled()
    })
  })

  describe('request deduplication', () => {
    it('should deduplicate concurrent requests to same URL', async () => {
      const mockResult: ScrapingResult = {
        success: true,
        content: 'Scraped content',
        metadata: { title: 'Test Page' },
        url: 'https://example.com',
        timestamp: new Date(),
      }

      vi.mocked(mockCrawl4AIService.scrape).mockResolvedValue(mockResult)

      const config: ScrapingConfig = {
        url: 'https://example.com',
        timeout: 10000,
      }

      // Make two concurrent requests
      const [result1, result2] = await Promise.all([
        scrapingService.scrape(config),
        scrapingService.scrape(config),
      ])

      expect(result1.success).toBe(true)
      expect(result2.success).toBe(true)
      expect(result1.data).toEqual(result2.data)
      // Should only call the scraper once due to deduplication
      expect(mockCrawl4AIService.scrape).toHaveBeenCalledTimes(1)
    })

    it('should not deduplicate requests with different URLs', async () => {
      const mockResult1: ScrapingResult = {
        success: true,
        content: 'Content 1',
        metadata: { title: 'Page 1' },
        url: 'https://example1.com',
        timestamp: new Date(),
      }

      const mockResult2: ScrapingResult = {
        success: true,
        content: 'Content 2',
        metadata: { title: 'Page 2' },
        url: 'https://example2.com',
        timestamp: new Date(),
      }

      vi.mocked(mockCrawl4AIService.scrape)
        .mockResolvedValueOnce(mockResult1)
        .mockResolvedValueOnce(mockResult2)

      const config1: ScrapingConfig = { url: 'https://example1.com' }
      const config2: ScrapingConfig = { url: 'https://example2.com' }

      const [result1, result2] = await Promise.all([
        scrapingService.scrape(config1),
        scrapingService.scrape(config2),
      ])

      expect(result1.success).toBe(true)
      expect(result2.success).toBe(true)
      expect(result1.data).toEqual(mockResult1)
      expect(result2.data).toEqual(mockResult2)
      expect(mockCrawl4AIService.scrape).toHaveBeenCalledTimes(2)
    })
  })

  describe('rate limiting', () => {
    it('should respect minimum request interval', async () => {
      const mockResult: ScrapingResult = {
        success: true,
        content: 'Scraped content',
        metadata: { title: 'Test Page' },
        url: 'https://example.com',
        timestamp: new Date(),
      }

      vi.mocked(mockCrawl4AIService.scrape).mockResolvedValue(mockResult)

      const config1: ScrapingConfig = { url: 'https://example1.com' }
      const config2: ScrapingConfig = { url: 'https://example2.com' }

      const startTime = Date.now()

      // Make sequential requests
      await scrapingService.scrape(config1)
      await scrapingService.scrape(config2)

      const endTime = Date.now()
      const duration = endTime - startTime

      // Should take at least 100ms due to rate limiting
      expect(duration).toBeGreaterThanOrEqual(100)
    })
  })

  describe('health check', () => {
    it('should return healthy status when all scrapers are healthy', async () => {
      vi.mocked(mockCrawl4AIService.isHealthy).mockResolvedValue(true)
      vi.mocked(mockFirecrawlService.isHealthy).mockResolvedValue(true)
      vi.mocked(mockPuppeteerService.isHealthy).mockResolvedValue(true)

      const health = await scrapingService.getHealth()

      expect(health.healthy).toBe(true)
      expect(health.scrapers.crawl4ai).toBe(true)
      expect(health.scrapers.firecrawl).toBe(true)
      expect(health.scrapers.puppeteer).toBe(true)
      expect(health.timestamp).toBeInstanceOf(Date)
    })

    it('should return unhealthy status when any scraper is unhealthy', async () => {
      vi.mocked(mockCrawl4AIService.isHealthy).mockResolvedValue(false)
      vi.mocked(mockFirecrawlService.isHealthy).mockResolvedValue(true)
      vi.mocked(mockPuppeteerService.isHealthy).mockResolvedValue(true)

      const health = await scrapingService.getHealth()

      expect(health.healthy).toBe(false)
      expect(health.scrapers.crawl4ai).toBe(false)
      expect(health.scrapers.firecrawl).toBe(true)
      expect(health.scrapers.puppeteer).toBe(true)
    })

    it('should handle health check errors gracefully', async () => {
      vi.mocked(mockCrawl4AIService.isHealthy).mockRejectedValue(new Error('Health check failed'))
      vi.mocked(mockFirecrawlService.isHealthy).mockResolvedValue(true)
      vi.mocked(mockPuppeteerService.isHealthy).mockResolvedValue(true)

      const health = await scrapingService.getHealth()

      expect(health.healthy).toBe(false)
      expect(health.scrapers.crawl4ai).toBe(false)
      expect(health.scrapers.firecrawl).toBe(true)
      expect(health.scrapers.puppeteer).toBe(true)
    })
  })

  describe('laptop data extraction', () => {
    it('should extract laptop data using Crawl4AI', async () => {
      const mockLaptopData = {
        title: 'Dell XPS 13',
        brand: 'Dell',
        model: 'XPS 13',
        price: 1299,
        specifications: {
          cpu: { brand: 'Intel', model: 'Core i7' },
          memory: { size: 16 },
          storage: { capacity: 512 },
        },
      }

      vi.mocked(mockCrawl4AIService.extractLaptopData).mockResolvedValue(mockLaptopData)

      const result = await scrapingService.extractLaptopData('https://example.com/laptop', 'generic')

      expect(result).toEqual(mockLaptopData)
      expect(mockCrawl4AIService.extractLaptopData).toHaveBeenCalledWith(
        'https://example.com/laptop',
        'generic',
      )
    })

    it('should fallback to Firecrawl when Crawl4AI laptop extraction fails', async () => {
      const mockFirecrawlResult: ScrapingResult = {
        success: true,
        content: 'Laptop content',
        metadata: { title: 'Dell XPS 13', price: '$1299' },
        url: 'https://example.com/laptop',
        timestamp: new Date(),
      }

      vi.mocked(mockCrawl4AIService.extractLaptopData).mockRejectedValue(
        new Error('Extraction failed'),
      )
      vi.mocked(mockFirecrawlService.scrape).mockResolvedValue(mockFirecrawlResult)

      const result = await scrapingService.extractLaptopData('https://example.com/laptop', 'generic')

      expect(result).toEqual(mockFirecrawlResult.metadata)
      expect(mockCrawl4AIService.extractLaptopData).toHaveBeenCalled()
      expect(mockFirecrawlService.scrape).toHaveBeenCalled()
    })

    it('should throw error when all laptop extraction methods fail', async () => {
      vi.mocked(mockCrawl4AIService.extractLaptopData).mockRejectedValue(
        new Error('Crawl4AI failed'),
      )
      vi.mocked(mockFirecrawlService.scrape).mockRejectedValue(new Error('Firecrawl failed'))

      await expect(
        scrapingService.extractLaptopData('https://example.com/laptop', 'generic'),
      ).rejects.toThrow('All laptop extraction methods failed')
    })
  })

  describe('configuration handling', () => {
    it('should handle complex scraping configuration', async () => {
      const mockResult: ScrapingResult = {
        success: true,
        content: 'Scraped content',
        metadata: { title: 'Test Page' },
        url: 'https://example.com',
        timestamp: new Date(),
      }

      vi.mocked(mockCrawl4AIService.scrape).mockResolvedValue(mockResult)

      const config: ScrapingConfig = {
        url: 'https://example.com',
        selectors: {
          title: '.product-title',
          price: '.price',
          description: '.description',
        },
        waitFor: 2000,
        timeout: 30000,
        userAgent: 'Custom User Agent',
        headers: {
          Accept: 'text/html',
          'Accept-Language': 'en-US',
        },
        siteType: 'amazon',
      }

      const result = await scrapingService.scrape(config)

      expect(result.success).toBe(true)
      expect(mockCrawl4AIService.scrape).toHaveBeenCalledWith({
        url: 'https://example.com',
        selector: '.product-title',
        waitFor: 2000,
        actions: [],
      })
    })
  })

  describe('cleanup', () => {
    it('should cleanup all scrapers', async () => {
      vi.mocked(mockCrawl4AIService.cleanup).mockResolvedValue(undefined)
      vi.mocked(mockFirecrawlService.cleanup).mockResolvedValue(undefined)
      vi.mocked(mockPuppeteerService.cleanup).mockResolvedValue(undefined)

      await scrapingService.cleanup()

      expect(mockCrawl4AIService.cleanup).toHaveBeenCalled()
      expect(mockFirecrawlService.cleanup).toHaveBeenCalled()
      expect(mockPuppeteerService.cleanup).toHaveBeenCalled()
    })

    it('should handle cleanup errors gracefully', async () => {
      vi.mocked(mockCrawl4AIService.cleanup).mockRejectedValue(new Error('Cleanup failed'))
      vi.mocked(mockFirecrawlService.cleanup).mockResolvedValue(undefined)
      vi.mocked(mockPuppeteerService.cleanup).mockResolvedValue(undefined)

      // Should not throw error
      await expect(scrapingService.cleanup()).resolves.toBeUndefined()

      expect(mockCrawl4AIService.cleanup).toHaveBeenCalled()
      expect(mockFirecrawlService.cleanup).toHaveBeenCalled()
      expect(mockPuppeteerService.cleanup).toHaveBeenCalled()
    })
  })
})