import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { LaptopCard } from '@/features/laptops/components/LaptopCard'
import type { LaptopData } from '@/features/laptops/types'

// Mock Next.js router
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

// Mock Next.js Image component
vi.mock('next/image', () => ({
  default: ({ src, alt, ...props }: any) => (
    <img src={src} alt={alt} {...props} />
  ),
}))

describe('LaptopCard', () => {
  const mockLaptop: LaptopData = {
    id: '1',
    url: 'https://example.com/laptop1',
    title: 'Dell XPS 13',
    brand: 'Dell',
    model: 'XPS 13',
    price: 1299,
    currency: 'USD',
    specifications: {
      cpu: {
        brand: 'Intel',
        model: 'Core i7-1165G7',
        cores: 4,
        threads: 8,
        baseFrequency: 2.8,
        boostFrequency: 4.7,
        architecture: 'Tiger Lake',
        tdp: 15,
      },
      gpu: {
        brand: 'Intel',
        model: 'Iris Xe Graphics',
        memory: 0,
        memoryType: 'Shared',
        tdp: 15,
      },
      memory: {
        size: 16,
        type: 'DDR4',
        speed: 3200,
        slots: 2,
        maxCapacity: 32,
      },
      storage: {
        type: 'SSD',
        capacity: 512,
        interface: 'NVMe PCIe 3.0',
      },
      display: {
        size: 13.3,
        resolution: '1920x1080',
        refreshRate: 60,
        panelType: 'IPS',
      },
    },
    features: {
      category: 'Ultrabook',
      portability: 'High',
      batteryLife: 12,
      weight: 1.2,
      thermalDesign: 'Standard',
    },
    availability: {
      inStock: true,
      stores: ['Dell', 'Amazon', 'Best Buy'],
    },
    images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
    scrapedAt: new Date('2024-01-01'),
    lastUpdated: new Date('2024-01-01'),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('basic rendering', () => {
    it('should render laptop information correctly', () => {
      render(<LaptopCard laptop={mockLaptop} />)

      expect(screen.getByText('Dell XPS 13')).toBeInTheDocument()
      expect(screen.getByText('Dell')).toBeInTheDocument()
      expect(screen.getByText('$1,299')).toBeInTheDocument()
      expect(screen.getByText('Ultrabook')).toBeInTheDocument()
    })

    it('should render laptop image', () => {
      render(<LaptopCard laptop={mockLaptop} />)

      const image = screen.getByAltText('Dell XPS 13')
      expect(image).toBeInTheDocument()
      expect(image).toHaveAttribute('src', 'https://example.com/image1.jpg')
    })

    it('should render fallback image when no images available', () => {
      const laptopWithoutImages = {
        ...mockLaptop,
        images: [],
      }

      render(<LaptopCard laptop={laptopWithoutImages} />)

      const image = screen.getByAltText('Dell XPS 13')
      expect(image).toBeInTheDocument()
      expect(image).toHaveAttribute('src', '/images/laptop-placeholder.jpg')
    })
  })

  describe('specifications display', () => {
    it('should display CPU information', () => {
      render(<LaptopCard laptop={mockLaptop} />)

      expect(screen.getByText(/Intel Core i7-1165G7/)).toBeInTheDocument()
      expect(screen.getByText(/4 cores/)).toBeInTheDocument()
    })

    it('should display memory information', () => {
      render(<LaptopCard laptop={mockLaptop} />)

      expect(screen.getByText(/16GB DDR4/)).toBeInTheDocument()
    })

    it('should display storage information', () => {
      render(<LaptopCard laptop={mockLaptop} />)

      expect(screen.getByText(/512GB SSD/)).toBeInTheDocument()
    })

    it('should display display information', () => {
      render(<LaptopCard laptop={mockLaptop} />)

      expect(screen.getByText(/13.3"/)).toBeInTheDocument()
      expect(screen.getByText(/1920x1080/)).toBeInTheDocument()
    })
  })

  describe('availability status', () => {
    it('should show in stock status', () => {
      render(<LaptopCard laptop={mockLaptop} />)

      expect(screen.getByText('In Stock')).toBeInTheDocument()
      expect(screen.getByText('In Stock')).toHaveClass('text-green-600')
    })

    it('should show out of stock status', () => {
      const outOfStockLaptop = {
        ...mockLaptop,
        availability: {
          inStock: false,
          stores: [],
        },
      }

      render(<LaptopCard laptop={outOfStockLaptop} />)

      expect(screen.getByText('Out of Stock')).toBeInTheDocument()
      expect(screen.getByText('Out of Stock')).toHaveClass('text-red-600')
    })

    it('should display available stores', () => {
      render(<LaptopCard laptop={mockLaptop} />)

      expect(screen.getByText('Available at: Dell, Amazon, Best Buy')).toBeInTheDocument()
    })
  })

  describe('price formatting', () => {
    it('should format USD price correctly', () => {
      render(<LaptopCard laptop={mockLaptop} />)

      expect(screen.getByText('$1,299')).toBeInTheDocument()
    })

    it('should format EUR price correctly', () => {
      const eurLaptop = {
        ...mockLaptop,
        price: 1199,
        currency: 'EUR' as const,
      }

      render(<LaptopCard laptop={eurLaptop} />)

      expect(screen.getByText('€1,199')).toBeInTheDocument()
    })

    it('should handle missing price', () => {
      const noPriceLaptop = {
        ...mockLaptop,
        price: undefined,
      }

      render(<LaptopCard laptop={noPriceLaptop as any} />)

      expect(screen.getByText('Price not available')).toBeInTheDocument()
    })
  })

  describe('interaction', () => {
    it('should navigate to laptop details on click', () => {
      render(<LaptopCard laptop={mockLaptop} />)

      const card = screen.getByRole('article')
      fireEvent.click(card)

      expect(mockPush).toHaveBeenCalledWith('/laptops/1')
    })

    it('should navigate to laptop details on Enter key', () => {
      render(<LaptopCard laptop={mockLaptop} />)

      const card = screen.getByRole('article')
      fireEvent.keyDown(card, { key: 'Enter', code: 'Enter' })

      expect(mockPush).toHaveBeenCalledWith('/laptops/1')
    })

    it('should not navigate on other keys', () => {
      render(<LaptopCard laptop={mockLaptop} />)

      const card = screen.getByRole('article')
      fireEvent.keyDown(card, { key: 'Space', code: 'Space' })

      expect(mockPush).not.toHaveBeenCalled()
    })
  })

  describe('compact mode', () => {
    it('should render in compact mode', () => {
      render(<LaptopCard laptop={mockLaptop} compact />)

      const card = screen.getByRole('article')
      expect(card).toHaveClass('compact')
      
      // In compact mode, some details should be hidden
      expect(screen.queryByText(/4 cores/)).not.toBeInTheDocument()
      expect(screen.queryByText(/Available at:/)).not.toBeInTheDocument()
    })

    it('should show essential information in compact mode', () => {
      render(<LaptopCard laptop={mockLaptop} compact />)

      expect(screen.getByText('Dell XPS 13')).toBeInTheDocument()
      expect(screen.getByText('$1,299')).toBeInTheDocument()
      expect(screen.getByText('In Stock')).toBeInTheDocument()
    })
  })

  describe('loading state', () => {
    it('should show loading skeleton', () => {
      render(<LaptopCard loading />)

      expect(screen.getByTestId('laptop-card-skeleton')).toBeInTheDocument()
      expect(screen.queryByText('Dell XPS 13')).not.toBeInTheDocument()
    })
  })

  describe('error handling', () => {
    it('should handle missing specifications gracefully', () => {
      const laptopWithoutSpecs = {
        ...mockLaptop,
        specifications: {},
      }

      render(<LaptopCard laptop={laptopWithoutSpecs as any} />)

      expect(screen.getByText('Dell XPS 13')).toBeInTheDocument()
      expect(screen.getByText('$1,299')).toBeInTheDocument()
      // Should not crash when specs are missing
    })

    it('should handle missing features gracefully', () => {
      const laptopWithoutFeatures = {
        ...mockLaptop,
        features: {},
      }

      render(<LaptopCard laptop={laptopWithoutFeatures as any} />)

      expect(screen.getByText('Dell XPS 13')).toBeInTheDocument()
      expect(screen.queryByText('Ultrabook')).not.toBeInTheDocument()
    })
  })

  describe('accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(<LaptopCard laptop={mockLaptop} />)

      const card = screen.getByRole('article')
      expect(card).toHaveAttribute('tabIndex', '0')
      expect(card).toHaveAttribute('aria-label', expect.stringContaining('Dell XPS 13'))
    })

    it('should have proper image alt text', () => {
      render(<LaptopCard laptop={mockLaptop} />)

      const image = screen.getByAltText('Dell XPS 13')
      expect(image).toBeInTheDocument()
    })
  })

  describe('custom actions', () => {
    it('should render custom action buttons', () => {
      const onCompare = vi.fn()
      const onFavorite = vi.fn()

      render(
        <LaptopCard 
          laptop={mockLaptop} 
          showActions
          onCompare={onCompare}
          onFavorite={onFavorite}
        />
      )

      const compareButton = screen.getByLabelText('Compare laptop')
      const favoriteButton = screen.getByLabelText('Add to favorites')

      expect(compareButton).toBeInTheDocument()
      expect(favoriteButton).toBeInTheDocument()

      fireEvent.click(compareButton)
      fireEvent.click(favoriteButton)

      expect(onCompare).toHaveBeenCalledWith(mockLaptop)
      expect(onFavorite).toHaveBeenCalledWith(mockLaptop)
    })

    it('should not render actions when showActions is false', () => {
      render(<LaptopCard laptop={mockLaptop} showActions={false} />)

      expect(screen.queryByLabelText('Compare laptop')).not.toBeInTheDocument()
      expect(screen.queryByLabelText('Add to favorites')).not.toBeInTheDocument()
    })
  })
})
