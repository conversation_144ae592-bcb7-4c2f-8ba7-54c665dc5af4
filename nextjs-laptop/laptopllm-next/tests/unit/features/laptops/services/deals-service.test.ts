import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { DealsService } from '@/features/laptops/services/deals-service'
import type { LaptopDeal, DealFilters } from '@/features/laptops/types'

// Mock Prisma
const mockPrisma = {
  deals: {
    findMany: vi.fn(),
    findUnique: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    count: vi.fn(),
  },
  laptops: {
    findMany: vi.fn(),
  },
}

vi.mock('@/lib/prisma', () => ({
  prisma: mockPrisma,
}))

describe('DealsService', () => {
  let dealsService: DealsService

  beforeEach(() => {
    vi.clearAllMocks()
    dealsService = new DealsService()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getActiveDeals', () => {
    it('should return active deals', async () => {
      const mockDeals = [
        {
          id: '1',
          laptopId: 'laptop-1',
          title: 'Black Friday Deal',
          originalPrice: 1299,
          salePrice: 999,
          discount: 300,
          discountPercentage: 23.1,
          store: 'Amazon',
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
          isActive: true,
          laptop: {
            id: 'laptop-1',
            title: 'Dell XPS 13',
            brand: 'Dell',
          },
        },
      ]

      mockPrisma.deals.findMany.mockResolvedValue(mockDeals)

      const result = await dealsService.getActiveDeals()

      expect(result).toEqual(mockDeals)
      expect(mockPrisma.deals.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          endDate: { gte: expect.any(Date) },
        },
        include: { laptop: true },
        orderBy: { discountPercentage: 'desc' },
      })
    })

    it('should filter deals by store', async () => {
      const filters: DealFilters = {
        store: ['Amazon', 'Best Buy'],
      }

      mockPrisma.deals.findMany.mockResolvedValue([])

      await dealsService.getActiveDeals(filters)

      expect(mockPrisma.deals.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          endDate: { gte: expect.any(Date) },
          store: { in: ['Amazon', 'Best Buy'] },
        },
        include: { laptop: true },
        orderBy: { discountPercentage: 'desc' },
      })
    })

    it('should filter deals by discount percentage', async () => {
      const filters: DealFilters = {
        minDiscount: 20,
        maxPrice: 1000,
      }

      mockPrisma.deals.findMany.mockResolvedValue([])

      await dealsService.getActiveDeals(filters)

      expect(mockPrisma.deals.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          endDate: { gte: expect.any(Date) },
          discountPercentage: { gte: 20 },
          salePrice: { lte: 1000 },
        },
        include: { laptop: true },
        orderBy: { discountPercentage: 'desc' },
      })
    })
  })

  describe('getDealById', () => {
    it('should return deal by ID', async () => {
      const mockDeal = {
        id: '1',
        laptopId: 'laptop-1',
        title: 'Special Deal',
        originalPrice: 1500,
        salePrice: 1200,
        discount: 300,
        discountPercentage: 20,
        store: 'Best Buy',
        isActive: true,
        laptop: {
          id: 'laptop-1',
          title: 'HP Spectre x360',
        },
      }

      mockPrisma.deals.findUnique.mockResolvedValue(mockDeal)

      const result = await dealsService.getDealById('1')

      expect(result).toEqual(mockDeal)
      expect(mockPrisma.deals.findUnique).toHaveBeenCalledWith({
        where: { id: '1' },
        include: { laptop: true },
      })
    })

    it('should return null for non-existent deal', async () => {
      mockPrisma.deals.findUnique.mockResolvedValue(null)

      const result = await dealsService.getDealById('non-existent')

      expect(result).toBeNull()
    })
  })

  describe('createDeal', () => {
    it('should create a new deal', async () => {
      const dealData: Partial<LaptopDeal> = {
        laptopId: 'laptop-1',
        title: 'New Year Sale',
        originalPrice: 1299,
        salePrice: 999,
        store: 'Amazon',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
      }

      const mockCreatedDeal = {
        id: 'new-deal-id',
        ...dealData,
        discount: 300,
        discountPercentage: 23.1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockPrisma.deals.create.mockResolvedValue(mockCreatedDeal)

      const result = await dealsService.createDeal(dealData)

      expect(result).toEqual(mockCreatedDeal)
      expect(mockPrisma.deals.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          ...dealData,
          discount: 300,
          discountPercentage: expect.closeTo(23.1, 1),
          isActive: true,
        }),
      })
    })

    it('should calculate discount correctly', async () => {
      const dealData: Partial<LaptopDeal> = {
        laptopId: 'laptop-1',
        originalPrice: 2000,
        salePrice: 1500,
        store: 'Best Buy',
      }

      mockPrisma.deals.create.mockResolvedValue({
        id: 'deal-id',
        ...dealData,
        discount: 500,
        discountPercentage: 25,
      })

      await dealsService.createDeal(dealData)

      expect(mockPrisma.deals.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          discount: 500,
          discountPercentage: 25,
        }),
      })
    })
  })

  describe('updateDeal', () => {
    it('should update existing deal', async () => {
      const updateData = {
        salePrice: 899,
        endDate: new Date('2024-02-29'),
      }

      const mockUpdatedDeal = {
        id: '1',
        ...updateData,
        discount: 400,
        discountPercentage: 30.8,
        updatedAt: new Date(),
      }

      mockPrisma.deals.update.mockResolvedValue(mockUpdatedDeal)

      const result = await dealsService.updateDeal('1', updateData)

      expect(result).toEqual(mockUpdatedDeal)
      expect(mockPrisma.deals.update).toHaveBeenCalledWith({
        where: { id: '1' },
        data: expect.objectContaining({
          ...updateData,
          updatedAt: expect.any(Date),
        }),
      })
    })

    it('should recalculate discount when price changes', async () => {
      const updateData = {
        originalPrice: 1500,
        salePrice: 1000,
      }

      mockPrisma.deals.update.mockResolvedValue({
        id: '1',
        ...updateData,
        discount: 500,
        discountPercentage: 33.3,
      })

      await dealsService.updateDeal('1', updateData)

      expect(mockPrisma.deals.update).toHaveBeenCalledWith({
        where: { id: '1' },
        data: expect.objectContaining({
          discount: 500,
          discountPercentage: expect.closeTo(33.3, 1),
        }),
      })
    })
  })

  describe('deactivateDeal', () => {
    it('should deactivate deal', async () => {
      mockPrisma.deals.update.mockResolvedValue({
        id: '1',
        isActive: false,
        updatedAt: new Date(),
      })

      const result = await dealsService.deactivateDeal('1')

      expect(result).toBe(true)
      expect(mockPrisma.deals.update).toHaveBeenCalledWith({
        where: { id: '1' },
        data: {
          isActive: false,
          updatedAt: expect.any(Date),
        },
      })
    })

    it('should handle deactivation errors', async () => {
      mockPrisma.deals.update.mockRejectedValue(new Error('Deal not found'))

      const result = await dealsService.deactivateDeal('non-existent')

      expect(result).toBe(false)
    })
  })

  describe('getTopDeals', () => {
    it('should return top deals by discount', async () => {
      const mockTopDeals = [
        {
          id: '1',
          discountPercentage: 40,
          title: 'Best Deal',
        },
        {
          id: '2',
          discountPercentage: 35,
          title: 'Great Deal',
        },
      ]

      mockPrisma.deals.findMany.mockResolvedValue(mockTopDeals)

      const result = await dealsService.getTopDeals(5)

      expect(result).toEqual(mockTopDeals)
      expect(mockPrisma.deals.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          endDate: { gte: expect.any(Date) },
        },
        include: { laptop: true },
        orderBy: { discountPercentage: 'desc' },
        take: 5,
      })
    })
  })

  describe('getDealsForLaptop', () => {
    it('should return deals for specific laptop', async () => {
      const mockDeals = [
        {
          id: '1',
          laptopId: 'laptop-1',
          store: 'Amazon',
          salePrice: 999,
        },
        {
          id: '2',
          laptopId: 'laptop-1',
          store: 'Best Buy',
          salePrice: 1049,
        },
      ]

      mockPrisma.deals.findMany.mockResolvedValue(mockDeals)

      const result = await dealsService.getDealsForLaptop('laptop-1')

      expect(result).toEqual(mockDeals)
      expect(mockPrisma.deals.findMany).toHaveBeenCalledWith({
        where: {
          laptopId: 'laptop-1',
          isActive: true,
          endDate: { gte: expect.any(Date) },
        },
        orderBy: { salePrice: 'asc' },
      })
    })
  })

  describe('getExpiredDeals', () => {
    it('should return expired deals', async () => {
      const mockExpiredDeals = [
        {
          id: '1',
          endDate: new Date('2023-12-31'),
          isActive: true,
        },
      ]

      mockPrisma.deals.findMany.mockResolvedValue(mockExpiredDeals)

      const result = await dealsService.getExpiredDeals()

      expect(result).toEqual(mockExpiredDeals)
      expect(mockPrisma.deals.findMany).toHaveBeenCalledWith({
        where: {
          endDate: { lt: expect.any(Date) },
          isActive: true,
        },
      })
    })
  })

  describe('cleanupExpiredDeals', () => {
    it('should deactivate expired deals', async () => {
      mockPrisma.deals.updateMany.mockResolvedValue({ count: 5 })

      const result = await dealsService.cleanupExpiredDeals()

      expect(result).toBe(5)
      expect(mockPrisma.deals.updateMany).toHaveBeenCalledWith({
        where: {
          endDate: { lt: expect.any(Date) },
          isActive: true,
        },
        data: {
          isActive: false,
          updatedAt: expect.any(Date),
        },
      })
    })
  })
})
