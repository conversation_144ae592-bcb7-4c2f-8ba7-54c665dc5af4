import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { LaptopService } from '@/features/laptops/services/laptop-service'
import type { LaptopData, LaptopSearchFilters, LaptopSearchResult } from '@/features/laptops/types'

// Mock Prisma
const mockPrisma = {
  laptops: {
    findMany: vi.fn(),
    findUnique: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    count: vi.fn(),
  },
  laptop_listings: {
    findMany: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
  },
}

vi.mock('@/lib/prisma', () => ({
  prisma: mockPrisma,
}))

describe('LaptopService', () => {
  let laptopService: LaptopService

  beforeEach(() => {
    vi.clearAllMocks()
    laptopService = new LaptopService()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('searchLaptops', () => {
    it('should search laptops with basic filters', async () => {
      const mockLaptops = [
        {
          id: '1',
          title: 'Dell XPS 13',
          brand: 'Dell',
          model: 'XPS 13',
          price: 1299,
          currency: 'USD',
          specifications: {
            cpu: { brand: 'Intel', model: 'Core i7-1165G7' },
            memory: { size: 16 },
            storage: { capacity: 512 },
          },
          features: { category: 'Ultrabook' },
          availability: { inStock: true },
          scrapedAt: new Date(),
          lastUpdated: new Date(),
        },
      ]

      mockPrisma.laptops.findMany.mockResolvedValue(mockLaptops)
      mockPrisma.laptops.count.mockResolvedValue(1)

      const filters: LaptopSearchFilters = {
        brand: ['Dell'],
        priceRange: { min: 1000, max: 1500 },
        category: ['Ultrabook'],
      }

      const result = await laptopService.searchLaptops(filters, { page: 1, limit: 10 })

      expect(result.laptops).toHaveLength(1)
      expect(result.laptops[0].brand).toBe('Dell')
      expect(result.total).toBe(1)
      expect(result.page).toBe(1)
      expect(mockPrisma.laptops.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            brand: { in: ['Dell'] },
            price: { gte: 1000, lte: 1500 },
          }),
          skip: 0,
          take: 10,
        })
      )
    })

    it('should handle empty search results', async () => {
      mockPrisma.laptops.findMany.mockResolvedValue([])
      mockPrisma.laptops.count.mockResolvedValue(0)

      const filters: LaptopSearchFilters = {
        brand: ['NonExistentBrand'],
      }

      const result = await laptopService.searchLaptops(filters, { page: 1, limit: 10 })

      expect(result.laptops).toHaveLength(0)
      expect(result.total).toBe(0)
      expect(result.hasMore).toBe(false)
    })

    it('should apply CPU filters correctly', async () => {
      const mockLaptops = [
        {
          id: '1',
          title: 'Gaming Laptop',
          specifications: {
            cpu: { brand: 'AMD', cores: 8 },
          },
        },
      ]

      mockPrisma.laptops.findMany.mockResolvedValue(mockLaptops)
      mockPrisma.laptops.count.mockResolvedValue(1)

      const filters: LaptopSearchFilters = {
        cpuBrand: ['AMD'],
        minCores: 6,
      }

      await laptopService.searchLaptops(filters, { page: 1, limit: 10 })

      expect(mockPrisma.laptops.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            specifications: expect.objectContaining({
              path: ['cpu', 'brand'],
              in: ['AMD'],
            }),
          }),
        })
      )
    })

    it('should apply memory and storage filters', async () => {
      const filters: LaptopSearchFilters = {
        minMemory: 16,
        minStorage: 512,
        storageType: ['SSD'],
      }

      mockPrisma.laptops.findMany.mockResolvedValue([])
      mockPrisma.laptops.count.mockResolvedValue(0)

      await laptopService.searchLaptops(filters, { page: 1, limit: 10 })

      expect(mockPrisma.laptops.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            AND: expect.arrayContaining([
              expect.objectContaining({
                specifications: expect.objectContaining({
                  path: ['memory', 'size'],
                  gte: 16,
                }),
              }),
              expect.objectContaining({
                specifications: expect.objectContaining({
                  path: ['storage', 'capacity'],
                  gte: 512,
                }),
              }),
            ]),
          }),
        })
      )
    })
  })

  describe('getLaptopById', () => {
    it('should return laptop by ID', async () => {
      const mockLaptop = {
        id: '1',
        title: 'Test Laptop',
        brand: 'TestBrand',
        model: 'TestModel',
        price: 999,
        currency: 'USD',
        specifications: {},
        features: {},
        availability: { inStock: true },
        scrapedAt: new Date(),
        lastUpdated: new Date(),
      }

      mockPrisma.laptops.findUnique.mockResolvedValue(mockLaptop)

      const result = await laptopService.getLaptopById('1')

      expect(result).toEqual(mockLaptop)
      expect(mockPrisma.laptops.findUnique).toHaveBeenCalledWith({
        where: { id: '1' },
        include: expect.any(Object),
      })
    })

    it('should return null for non-existent laptop', async () => {
      mockPrisma.laptops.findUnique.mockResolvedValue(null)

      const result = await laptopService.getLaptopById('non-existent')

      expect(result).toBeNull()
    })
  })

  describe('createLaptop', () => {
    it('should create a new laptop', async () => {
      const laptopData: Partial<LaptopData> = {
        title: 'New Laptop',
        brand: 'NewBrand',
        model: 'NewModel',
        price: 1499,
        currency: 'USD',
        specifications: {
          cpu: { brand: 'Intel', model: 'Core i7' },
          memory: { size: 16 },
          storage: { capacity: 1024 },
        },
        features: { category: 'Gaming' },
        availability: { inStock: true },
      }

      const mockCreatedLaptop = {
        id: 'new-id',
        ...laptopData,
        scrapedAt: new Date(),
        lastUpdated: new Date(),
      }

      mockPrisma.laptops.create.mockResolvedValue(mockCreatedLaptop)

      const result = await laptopService.createLaptop(laptopData)

      expect(result).toEqual(mockCreatedLaptop)
      expect(mockPrisma.laptops.create).toHaveBeenCalledWith({
        data: expect.objectContaining(laptopData),
      })
    })

    it('should handle creation errors', async () => {
      const laptopData: Partial<LaptopData> = {
        title: 'Invalid Laptop',
      }

      mockPrisma.laptops.create.mockRejectedValue(new Error('Database error'))

      await expect(laptopService.createLaptop(laptopData)).rejects.toThrow('Database error')
    })
  })

  describe('updateLaptop', () => {
    it('should update existing laptop', async () => {
      const updateData = {
        price: 1199,
        availability: { inStock: false },
      }

      const mockUpdatedLaptop = {
        id: '1',
        title: 'Updated Laptop',
        ...updateData,
        lastUpdated: new Date(),
      }

      mockPrisma.laptops.update.mockResolvedValue(mockUpdatedLaptop)

      const result = await laptopService.updateLaptop('1', updateData)

      expect(result).toEqual(mockUpdatedLaptop)
      expect(mockPrisma.laptops.update).toHaveBeenCalledWith({
        where: { id: '1' },
        data: expect.objectContaining({
          ...updateData,
          lastUpdated: expect.any(Date),
        }),
      })
    })
  })

  describe('deleteLaptop', () => {
    it('should delete laptop by ID', async () => {
      mockPrisma.laptops.delete.mockResolvedValue({ id: '1' })

      const result = await laptopService.deleteLaptop('1')

      expect(result).toBe(true)
      expect(mockPrisma.laptops.delete).toHaveBeenCalledWith({
        where: { id: '1' },
      })
    })

    it('should handle deletion errors', async () => {
      mockPrisma.laptops.delete.mockRejectedValue(new Error('Not found'))

      const result = await laptopService.deleteLaptop('non-existent')

      expect(result).toBe(false)
    })
  })

  describe('getPopularLaptops', () => {
    it('should return popular laptops', async () => {
      const mockPopularLaptops = [
        { id: '1', title: 'Popular Laptop 1', viewCount: 100 },
        { id: '2', title: 'Popular Laptop 2', viewCount: 90 },
      ]

      mockPrisma.laptops.findMany.mockResolvedValue(mockPopularLaptops)

      const result = await laptopService.getPopularLaptops(5)

      expect(result).toEqual(mockPopularLaptops)
      expect(mockPrisma.laptops.findMany).toHaveBeenCalledWith({
        orderBy: { viewCount: 'desc' },
        take: 5,
        include: expect.any(Object),
      })
    })
  })

  describe('getBrandStats', () => {
    it('should return brand statistics', async () => {
      const mockBrandStats = [
        { brand: 'Dell', count: 25 },
        { brand: 'HP', count: 20 },
        { brand: 'Lenovo', count: 18 },
      ]

      // Mock raw query result
      mockPrisma.laptops.groupBy = vi.fn().mockResolvedValue(
        mockBrandStats.map(stat => ({
          brand: stat.brand,
          _count: { brand: stat.count },
        }))
      )

      const result = await laptopService.getBrandStats()

      expect(result).toEqual(mockBrandStats)
    })
  })
})
