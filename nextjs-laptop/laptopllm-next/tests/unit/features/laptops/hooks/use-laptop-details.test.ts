import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useLaptopDetails } from '@/features/laptops/hooks/use-laptop-details'
import { LaptopService } from '@/features/laptops/services/laptop-service'
import type { LaptopData } from '@/features/laptops/types'
import { ReactNode } from 'react'

// Mock LaptopService
vi.mock('@/features/laptops/services/laptop-service')

const mockLaptopService = {
  getLaptopById: vi.fn(),
  getSimilarLaptops: vi.fn(),
  incrementViewCount: vi.fn(),
}

vi.mocked(LaptopService).mockImplementation(() => mockLaptopService as any)

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('useLaptopDetails', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('laptop details fetching', () => {
    it('should fetch laptop details by ID', async () => {
      const mockLaptop: LaptopData = {
        id: '1',
        url: 'https://example.com/laptop1',
        title: 'Dell XPS 13',
        brand: 'Dell',
        model: 'XPS 13',
        price: 1299,
        currency: 'USD',
        specifications: {
          cpu: {
            brand: 'Intel',
            model: 'Core i7-1165G7',
            cores: 4,
            threads: 8,
            baseFrequency: 2.8,
            boostFrequency: 4.7,
            architecture: 'Tiger Lake',
            tdp: 15,
          },
          gpu: {
            brand: 'Intel',
            model: 'Iris Xe Graphics',
            memory: 0,
            memoryType: 'Shared',
            tdp: 15,
          },
          memory: {
            size: 16,
            type: 'DDR4',
            speed: 3200,
            slots: 2,
            maxCapacity: 32,
          },
          storage: {
            type: 'SSD',
            capacity: 512,
            interface: 'NVMe PCIe 3.0',
          },
          display: {
            size: 13.3,
            resolution: '1920x1080',
            refreshRate: 60,
            panelType: 'IPS',
          },
        },
        features: {
          category: 'Ultrabook',
          portability: 'High',
          batteryLife: 12,
          weight: 1.2,
          thermalDesign: 'Standard',
        },
        availability: {
          inStock: true,
          stores: ['Dell', 'Amazon', 'Best Buy'],
        },
        images: ['image1.jpg', 'image2.jpg'],
        scrapedAt: new Date(),
        lastUpdated: new Date(),
      }

      mockLaptopService.getLaptopById.mockResolvedValue(mockLaptop)

      const { result } = renderHook(
        () => useLaptopDetails('1'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockLaptop)
      expect(mockLaptopService.getLaptopById).toHaveBeenCalledWith('1')
    })

    it('should handle laptop not found', async () => {
      mockLaptopService.getLaptopById.mockResolvedValue(null)

      const { result } = renderHook(
        () => useLaptopDetails('non-existent'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toBeNull()
      expect(mockLaptopService.getLaptopById).toHaveBeenCalledWith('non-existent')
    })

    it('should handle fetch errors', async () => {
      mockLaptopService.getLaptopById.mockRejectedValue(new Error('Database error'))

      const { result } = renderHook(
        () => useLaptopDetails('1'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toBeInstanceOf(Error)
      expect(result.current.error?.message).toBe('Database error')
    })

    it('should show loading state initially', () => {
      mockLaptopService.getLaptopById.mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )

      const { result } = renderHook(
        () => useLaptopDetails('1'),
        { wrapper: createWrapper() }
      )

      expect(result.current.isLoading).toBe(true)
      expect(result.current.data).toBeUndefined()
    })
  })

  describe('similar laptops', () => {
    it('should fetch similar laptops when laptop is loaded', async () => {
      const mockLaptop: LaptopData = {
        id: '1',
        url: 'https://example.com/laptop1',
        title: 'Dell XPS 13',
        brand: 'Dell',
        model: 'XPS 13',
        price: 1299,
        currency: 'USD',
        specifications: {
          cpu: { brand: 'Intel', model: 'Core i7' },
          memory: { size: 16 },
          storage: { capacity: 512 },
        },
        features: { category: 'Ultrabook' },
        availability: { inStock: true },
        images: [],
        scrapedAt: new Date(),
        lastUpdated: new Date(),
      }

      const mockSimilarLaptops = [
        {
          id: '2',
          title: 'Dell XPS 15',
          brand: 'Dell',
          price: 1599,
          specifications: {
            cpu: { brand: 'Intel', model: 'Core i7' },
            memory: { size: 16 },
          },
        },
        {
          id: '3',
          title: 'HP Spectre x360',
          brand: 'HP',
          price: 1399,
          specifications: {
            cpu: { brand: 'Intel', model: 'Core i7' },
            memory: { size: 16 },
          },
        },
      ]

      mockLaptopService.getLaptopById.mockResolvedValue(mockLaptop)
      mockLaptopService.getSimilarLaptops.mockResolvedValue(mockSimilarLaptops)

      const { result } = renderHook(
        () => useLaptopDetails('1', { includeSimilar: true }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockLaptop)
      expect(result.current.similarLaptops).toEqual(mockSimilarLaptops)
      expect(mockLaptopService.getSimilarLaptops).toHaveBeenCalledWith('1', 5)
    })

    it('should not fetch similar laptops when includeSimilar is false', async () => {
      const mockLaptop: LaptopData = {
        id: '1',
        title: 'Dell XPS 13',
        brand: 'Dell',
        specifications: {},
        features: {},
        availability: { inStock: true },
        images: [],
        scrapedAt: new Date(),
        lastUpdated: new Date(),
      } as LaptopData

      mockLaptopService.getLaptopById.mockResolvedValue(mockLaptop)

      const { result } = renderHook(
        () => useLaptopDetails('1', { includeSimilar: false }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockLaptop)
      expect(result.current.similarLaptops).toBeUndefined()
      expect(mockLaptopService.getSimilarLaptops).not.toHaveBeenCalled()
    })
  })

  describe('view count tracking', () => {
    it('should increment view count when trackViews is enabled', async () => {
      const mockLaptop: LaptopData = {
        id: '1',
        title: 'Dell XPS 13',
        brand: 'Dell',
        specifications: {},
        features: {},
        availability: { inStock: true },
        images: [],
        scrapedAt: new Date(),
        lastUpdated: new Date(),
      } as LaptopData

      mockLaptopService.getLaptopById.mockResolvedValue(mockLaptop)
      mockLaptopService.incrementViewCount.mockResolvedValue(undefined)

      const { result } = renderHook(
        () => useLaptopDetails('1', { trackViews: true }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockLaptopService.incrementViewCount).toHaveBeenCalledWith('1')
    })

    it('should not increment view count when trackViews is disabled', async () => {
      const mockLaptop: LaptopData = {
        id: '1',
        title: 'Dell XPS 13',
        brand: 'Dell',
        specifications: {},
        features: {},
        availability: { inStock: true },
        images: [],
        scrapedAt: new Date(),
        lastUpdated: new Date(),
      } as LaptopData

      mockLaptopService.getLaptopById.mockResolvedValue(mockLaptop)

      const { result } = renderHook(
        () => useLaptopDetails('1', { trackViews: false }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockLaptopService.incrementViewCount).not.toHaveBeenCalled()
    })
  })

  describe('caching behavior', () => {
    it('should cache laptop details', async () => {
      const mockLaptop: LaptopData = {
        id: '1',
        title: 'Dell XPS 13',
        brand: 'Dell',
        specifications: {},
        features: {},
        availability: { inStock: true },
        images: [],
        scrapedAt: new Date(),
        lastUpdated: new Date(),
      } as LaptopData

      mockLaptopService.getLaptopById.mockResolvedValue(mockLaptop)

      // First render
      const { result: result1 } = renderHook(
        () => useLaptopDetails('1'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result1.current.isSuccess).toBe(true)
      })

      // Second render with same ID should use cache
      const { result: result2 } = renderHook(
        () => useLaptopDetails('1'),
        { wrapper: createWrapper() }
      )

      expect(result2.current.data).toEqual(mockLaptop)
      // Should only be called once due to caching
      expect(mockLaptopService.getLaptopById).toHaveBeenCalledTimes(1)
    })
  })

  describe('options handling', () => {
    it('should handle custom similar laptops limit', async () => {
      const mockLaptop: LaptopData = {
        id: '1',
        title: 'Dell XPS 13',
        brand: 'Dell',
        specifications: {},
        features: {},
        availability: { inStock: true },
        images: [],
        scrapedAt: new Date(),
        lastUpdated: new Date(),
      } as LaptopData

      mockLaptopService.getLaptopById.mockResolvedValue(mockLaptop)
      mockLaptopService.getSimilarLaptops.mockResolvedValue([])

      const { result } = renderHook(
        () => useLaptopDetails('1', { includeSimilar: true, similarLimit: 10 }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockLaptopService.getSimilarLaptops).toHaveBeenCalledWith('1', 10)
    })

    it('should use default options when none provided', async () => {
      const mockLaptop: LaptopData = {
        id: '1',
        title: 'Dell XPS 13',
        brand: 'Dell',
        specifications: {},
        features: {},
        availability: { inStock: true },
        images: [],
        scrapedAt: new Date(),
        lastUpdated: new Date(),
      } as LaptopData

      mockLaptopService.getLaptopById.mockResolvedValue(mockLaptop)

      const { result } = renderHook(
        () => useLaptopDetails('1'),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockLaptop)
      expect(mockLaptopService.getSimilarLaptops).not.toHaveBeenCalled()
      expect(mockLaptopService.incrementViewCount).not.toHaveBeenCalled()
    })
  })
})
