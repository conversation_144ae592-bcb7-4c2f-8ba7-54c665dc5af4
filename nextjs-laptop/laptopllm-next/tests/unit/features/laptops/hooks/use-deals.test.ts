import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { renderHook, waitFor, act } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useDeals } from '@/features/laptops/hooks/use-deals'
import { DealsService } from '@/features/laptops/services/deals-service'
import type { LaptopDeal, DealFilters } from '@/features/laptops/types'
import { ReactNode } from 'react'

// Mock DealsService
vi.mock('@/features/laptops/services/deals-service')

const mockDealsService = {
  getActiveDeals: vi.fn(),
  getDealById: vi.fn(),
  getTopDeals: vi.fn(),
  getDealsForLaptop: vi.fn(),
  createDeal: vi.fn(),
  updateDeal: vi.fn(),
  deactivateDeal: vi.fn(),
}

vi.mocked(DealsService).mockImplementation(() => mockDealsService as any)

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  })

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('useDeals', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getActiveDeals', () => {
    it('should fetch active deals', async () => {
      const mockDeals: LaptopDeal[] = [
        {
          id: '1',
          laptopId: 'laptop-1',
          title: 'Black Friday Deal',
          originalPrice: 1299,
          salePrice: 999,
          discount: 300,
          discountPercentage: 23.1,
          store: 'Amazon',
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          laptop: {
            id: 'laptop-1',
            title: 'Dell XPS 13',
            brand: 'Dell',
            model: 'XPS 13',
          },
        },
      ]

      mockDealsService.getActiveDeals.mockResolvedValue(mockDeals)

      const { result } = renderHook(
        () => useDeals(),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.activeDeals.isSuccess).toBe(true)
      })

      expect(result.current.activeDeals.data).toEqual(mockDeals)
      expect(mockDealsService.getActiveDeals).toHaveBeenCalledWith(undefined)
    })

    it('should fetch active deals with filters', async () => {
      const mockDeals: LaptopDeal[] = []
      mockDealsService.getActiveDeals.mockResolvedValue(mockDeals)

      const filters: DealFilters = {
        store: ['Amazon', 'Best Buy'],
        minDiscount: 20,
        maxPrice: 1000,
      }

      const { result } = renderHook(
        () => useDeals(filters),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.activeDeals.isSuccess).toBe(true)
      })

      expect(mockDealsService.getActiveDeals).toHaveBeenCalledWith(filters)
    })

    it('should handle fetch errors', async () => {
      mockDealsService.getActiveDeals.mockRejectedValue(new Error('API error'))

      const { result } = renderHook(
        () => useDeals(),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.activeDeals.isError).toBe(true)
      })

      expect(result.current.activeDeals.error).toBeInstanceOf(Error)
      expect(result.current.activeDeals.error?.message).toBe('API error')
    })
  })

  describe('getTopDeals', () => {
    it('should fetch top deals', async () => {
      const mockTopDeals: LaptopDeal[] = [
        {
          id: '1',
          laptopId: 'laptop-1',
          title: 'Best Deal',
          originalPrice: 2000,
          salePrice: 1200,
          discount: 800,
          discountPercentage: 40,
          store: 'Amazon',
          startDate: new Date(),
          endDate: new Date(),
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ]

      mockDealsService.getTopDeals.mockResolvedValue(mockTopDeals)

      const { result } = renderHook(
        () => useDeals(),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.topDeals.isSuccess).toBe(true)
      })

      expect(result.current.topDeals.data).toEqual(mockTopDeals)
      expect(mockDealsService.getTopDeals).toHaveBeenCalledWith(10)
    })

    it('should fetch top deals with custom limit', async () => {
      const mockTopDeals: LaptopDeal[] = []
      mockDealsService.getTopDeals.mockResolvedValue(mockTopDeals)

      const { result } = renderHook(
        () => useDeals(undefined, { topDealsLimit: 5 }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.topDeals.isSuccess).toBe(true)
      })

      expect(mockDealsService.getTopDeals).toHaveBeenCalledWith(5)
    })
  })

  describe('getDealById', () => {
    it('should fetch deal by ID', async () => {
      const mockDeal: LaptopDeal = {
        id: '1',
        laptopId: 'laptop-1',
        title: 'Special Deal',
        originalPrice: 1500,
        salePrice: 1200,
        discount: 300,
        discountPercentage: 20,
        store: 'Best Buy',
        startDate: new Date(),
        endDate: new Date(),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        laptop: {
          id: 'laptop-1',
          title: 'HP Spectre x360',
          brand: 'HP',
          model: 'Spectre x360',
        },
      }

      mockDealsService.getDealById.mockResolvedValue(mockDeal)

      const { result } = renderHook(
        () => useDeals(),
        { wrapper: createWrapper() }
      )

      let dealResult: any

      await act(async () => {
        dealResult = await result.current.getDealById('1')
      })

      expect(dealResult).toEqual(mockDeal)
      expect(mockDealsService.getDealById).toHaveBeenCalledWith('1')
    })

    it('should handle deal not found', async () => {
      mockDealsService.getDealById.mockResolvedValue(null)

      const { result } = renderHook(
        () => useDeals(),
        { wrapper: createWrapper() }
      )

      let dealResult: any

      await act(async () => {
        dealResult = await result.current.getDealById('non-existent')
      })

      expect(dealResult).toBeNull()
    })
  })

  describe('getDealsForLaptop', () => {
    it('should fetch deals for specific laptop', async () => {
      const mockDeals: LaptopDeal[] = [
        {
          id: '1',
          laptopId: 'laptop-1',
          title: 'Amazon Deal',
          originalPrice: 1299,
          salePrice: 999,
          discount: 300,
          discountPercentage: 23.1,
          store: 'Amazon',
          startDate: new Date(),
          endDate: new Date(),
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '2',
          laptopId: 'laptop-1',
          title: 'Best Buy Deal',
          originalPrice: 1299,
          salePrice: 1049,
          discount: 250,
          discountPercentage: 19.2,
          store: 'Best Buy',
          startDate: new Date(),
          endDate: new Date(),
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ]

      mockDealsService.getDealsForLaptop.mockResolvedValue(mockDeals)

      const { result } = renderHook(
        () => useDeals(),
        { wrapper: createWrapper() }
      )

      let dealsResult: any

      await act(async () => {
        dealsResult = await result.current.getDealsForLaptop('laptop-1')
      })

      expect(dealsResult).toEqual(mockDeals)
      expect(mockDealsService.getDealsForLaptop).toHaveBeenCalledWith('laptop-1')
    })
  })

  describe('mutations', () => {
    it('should create a new deal', async () => {
      const newDealData = {
        laptopId: 'laptop-1',
        title: 'New Deal',
        originalPrice: 1299,
        salePrice: 999,
        store: 'Amazon',
        startDate: new Date(),
        endDate: new Date(),
      }

      const mockCreatedDeal: LaptopDeal = {
        id: 'new-deal-id',
        ...newDealData,
        discount: 300,
        discountPercentage: 23.1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockDealsService.createDeal.mockResolvedValue(mockCreatedDeal)

      const { result } = renderHook(
        () => useDeals(),
        { wrapper: createWrapper() }
      )

      await act(async () => {
        result.current.createDeal.mutate(newDealData)
      })

      await waitFor(() => {
        expect(result.current.createDeal.isSuccess).toBe(true)
      })

      expect(result.current.createDeal.data).toEqual(mockCreatedDeal)
      expect(mockDealsService.createDeal).toHaveBeenCalledWith(newDealData)
    })

    it('should update an existing deal', async () => {
      const updateData = {
        salePrice: 899,
        endDate: new Date('2024-02-29'),
      }

      const mockUpdatedDeal: LaptopDeal = {
        id: '1',
        laptopId: 'laptop-1',
        title: 'Updated Deal',
        originalPrice: 1299,
        salePrice: 899,
        discount: 400,
        discountPercentage: 30.8,
        store: 'Amazon',
        startDate: new Date(),
        endDate: new Date('2024-02-29'),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockDealsService.updateDeal.mockResolvedValue(mockUpdatedDeal)

      const { result } = renderHook(
        () => useDeals(),
        { wrapper: createWrapper() }
      )

      await act(async () => {
        result.current.updateDeal.mutate({ id: '1', data: updateData })
      })

      await waitFor(() => {
        expect(result.current.updateDeal.isSuccess).toBe(true)
      })

      expect(result.current.updateDeal.data).toEqual(mockUpdatedDeal)
      expect(mockDealsService.updateDeal).toHaveBeenCalledWith('1', updateData)
    })

    it('should deactivate a deal', async () => {
      mockDealsService.deactivateDeal.mockResolvedValue(true)

      const { result } = renderHook(
        () => useDeals(),
        { wrapper: createWrapper() }
      )

      await act(async () => {
        result.current.deactivateDeal.mutate('1')
      })

      await waitFor(() => {
        expect(result.current.deactivateDeal.isSuccess).toBe(true)
      })

      expect(result.current.deactivateDeal.data).toBe(true)
      expect(mockDealsService.deactivateDeal).toHaveBeenCalledWith('1')
    })

    it('should handle mutation errors', async () => {
      mockDealsService.createDeal.mockRejectedValue(new Error('Creation failed'))

      const { result } = renderHook(
        () => useDeals(),
        { wrapper: createWrapper() }
      )

      await act(async () => {
        result.current.createDeal.mutate({
          laptopId: 'laptop-1',
          title: 'Failed Deal',
          originalPrice: 1000,
          salePrice: 800,
          store: 'Amazon',
        })
      })

      await waitFor(() => {
        expect(result.current.createDeal.isError).toBe(true)
      })

      expect(result.current.createDeal.error).toBeInstanceOf(Error)
      expect(result.current.createDeal.error?.message).toBe('Creation failed')
    })
  })

  describe('loading states', () => {
    it('should show loading states for queries', () => {
      mockDealsService.getActiveDeals.mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )
      mockDealsService.getTopDeals.mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )

      const { result } = renderHook(
        () => useDeals(),
        { wrapper: createWrapper() }
      )

      expect(result.current.activeDeals.isLoading).toBe(true)
      expect(result.current.topDeals.isLoading).toBe(true)
      expect(result.current.activeDeals.data).toBeUndefined()
      expect(result.current.topDeals.data).toBeUndefined()
    })
  })
})
