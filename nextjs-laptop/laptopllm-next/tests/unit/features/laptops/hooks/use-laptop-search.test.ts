import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useLaptopSearch } from '@/features/laptops/hooks/use-laptop-search'
import { LaptopService } from '@/features/laptops/services/laptop-service'
import type { LaptopSearchFilters } from '@/features/laptops/types'
import { ReactNode } from 'react'

// Mock LaptopService
vi.mock('@/features/laptops/services/laptop-service')

const mockLaptopService = {
  searchLaptops: vi.fn(),
  getLaptopById: vi.fn(),
  getPopularLaptops: vi.fn(),
  getBrandStats: vi.fn(),
}

vi.mocked(LaptopService).mockImplementation(() => mockLaptopService as any)

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('useLaptopSearch', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('basic search functionality', () => {
    it('should search laptops with filters', async () => {
      const mockSearchResult = {
        laptops: [
          {
            id: '1',
            title: 'Dell XPS 13',
            brand: 'Dell',
            model: 'XPS 13',
            price: 1299,
            currency: 'USD',
            specifications: {
              cpu: { brand: 'Intel', model: 'Core i7' },
              memory: { size: 16 },
            },
            features: { category: 'Ultrabook' },
            availability: { inStock: true },
            scrapedAt: new Date(),
            lastUpdated: new Date(),
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
        hasMore: false,
      }

      mockLaptopService.searchLaptops.mockResolvedValue(mockSearchResult)

      const filters: LaptopSearchFilters = {
        brand: ['Dell'],
        priceRange: { min: 1000, max: 1500 },
      }

      const { result } = renderHook(
        () => useLaptopSearch(filters, { page: 1, limit: 10 }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockSearchResult)
      expect(mockLaptopService.searchLaptops).toHaveBeenCalledWith(
        filters,
        { page: 1, limit: 10 }
      )
    })

    it('should handle search errors', async () => {
      mockLaptopService.searchLaptops.mockRejectedValue(new Error('Search failed'))

      const filters: LaptopSearchFilters = {
        brand: ['Dell'],
      }

      const { result } = renderHook(
        () => useLaptopSearch(filters, { page: 1, limit: 10 }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toBeInstanceOf(Error)
      expect(result.current.error?.message).toBe('Search failed')
    })

    it('should show loading state initially', () => {
      mockLaptopService.searchLaptops.mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )

      const filters: LaptopSearchFilters = {
        brand: ['Dell'],
      }

      const { result } = renderHook(
        () => useLaptopSearch(filters, { page: 1, limit: 10 }),
        { wrapper: createWrapper() }
      )

      expect(result.current.isLoading).toBe(true)
      expect(result.current.data).toBeUndefined()
    })
  })

  describe('search with different filters', () => {
    it('should search with CPU filters', async () => {
      const mockResult = {
        laptops: [],
        total: 0,
        page: 1,
        limit: 10,
        hasMore: false,
      }

      mockLaptopService.searchLaptops.mockResolvedValue(mockResult)

      const filters: LaptopSearchFilters = {
        cpuBrand: ['AMD', 'Intel'],
        minCores: 8,
        cpuGeneration: ['12th Gen', '13th Gen'],
      }

      const { result } = renderHook(
        () => useLaptopSearch(filters, { page: 1, limit: 10 }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockLaptopService.searchLaptops).toHaveBeenCalledWith(
        expect.objectContaining({
          cpuBrand: ['AMD', 'Intel'],
          minCores: 8,
          cpuGeneration: ['12th Gen', '13th Gen'],
        }),
        { page: 1, limit: 10 }
      )
    })

    it('should search with memory and storage filters', async () => {
      const mockResult = {
        laptops: [],
        total: 0,
        page: 1,
        limit: 10,
        hasMore: false,
      }

      mockLaptopService.searchLaptops.mockResolvedValue(mockResult)

      const filters: LaptopSearchFilters = {
        minMemory: 16,
        memoryType: ['DDR4', 'DDR5'],
        minStorage: 512,
        storageType: ['SSD'],
      }

      const { result } = renderHook(
        () => useLaptopSearch(filters, { page: 1, limit: 10 }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockLaptopService.searchLaptops).toHaveBeenCalledWith(
        expect.objectContaining({
          minMemory: 16,
          memoryType: ['DDR4', 'DDR5'],
          minStorage: 512,
          storageType: ['SSD'],
        }),
        { page: 1, limit: 10 }
      )
    })

    it('should search with display filters', async () => {
      const mockResult = {
        laptops: [],
        total: 0,
        page: 1,
        limit: 10,
        hasMore: false,
      }

      mockLaptopService.searchLaptops.mockResolvedValue(mockResult)

      const filters: LaptopSearchFilters = {
        screenSize: { min: 13, max: 17 },
        resolution: ['1920x1080', '2560x1440'],
        refreshRate: { min: 120 },
      }

      const { result } = renderHook(
        () => useLaptopSearch(filters, { page: 1, limit: 10 }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockLaptopService.searchLaptops).toHaveBeenCalledWith(
        expect.objectContaining({
          screenSize: { min: 13, max: 17 },
          resolution: ['1920x1080', '2560x1440'],
          refreshRate: { min: 120 },
        }),
        { page: 1, limit: 10 }
      )
    })
  })

  describe('pagination', () => {
    it('should handle pagination correctly', async () => {
      const mockResult = {
        laptops: [],
        total: 100,
        page: 2,
        limit: 20,
        hasMore: true,
      }

      mockLaptopService.searchLaptops.mockResolvedValue(mockResult)

      const filters: LaptopSearchFilters = {
        brand: ['Dell'],
      }

      const { result } = renderHook(
        () => useLaptopSearch(filters, { page: 2, limit: 20 }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data?.page).toBe(2)
      expect(result.current.data?.limit).toBe(20)
      expect(result.current.data?.hasMore).toBe(true)
      expect(mockLaptopService.searchLaptops).toHaveBeenCalledWith(
        filters,
        { page: 2, limit: 20 }
      )
    })
  })

  describe('sorting', () => {
    it('should apply sorting options', async () => {
      const mockResult = {
        laptops: [],
        total: 0,
        page: 1,
        limit: 10,
        hasMore: false,
      }

      mockLaptopService.searchLaptops.mockResolvedValue(mockResult)

      const filters: LaptopSearchFilters = {
        brand: ['Dell'],
        sortBy: 'price',
        sortOrder: 'asc',
      }

      const { result } = renderHook(
        () => useLaptopSearch(filters, { page: 1, limit: 10 }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockLaptopService.searchLaptops).toHaveBeenCalledWith(
        expect.objectContaining({
          sortBy: 'price',
          sortOrder: 'asc',
        }),
        { page: 1, limit: 10 }
      )
    })
  })

  describe('query key generation', () => {
    it('should generate different query keys for different filters', async () => {
      const mockResult = {
        laptops: [],
        total: 0,
        page: 1,
        limit: 10,
        hasMore: false,
      }

      mockLaptopService.searchLaptops.mockResolvedValue(mockResult)

      const filters1: LaptopSearchFilters = {
        brand: ['Dell'],
      }

      const filters2: LaptopSearchFilters = {
        brand: ['HP'],
      }

      const { result: result1 } = renderHook(
        () => useLaptopSearch(filters1, { page: 1, limit: 10 }),
        { wrapper: createWrapper() }
      )

      const { result: result2 } = renderHook(
        () => useLaptopSearch(filters2, { page: 1, limit: 10 }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result1.current.isSuccess).toBe(true)
        expect(result2.current.isSuccess).toBe(true)
      })

      // Both searches should have been called
      expect(mockLaptopService.searchLaptops).toHaveBeenCalledTimes(2)
      expect(mockLaptopService.searchLaptops).toHaveBeenNthCalledWith(
        1,
        filters1,
        { page: 1, limit: 10 }
      )
      expect(mockLaptopService.searchLaptops).toHaveBeenNthCalledWith(
        2,
        filters2,
        { page: 1, limit: 10 }
      )
    })
  })

  describe('empty filters', () => {
    it('should handle empty filters', async () => {
      const mockResult = {
        laptops: [],
        total: 0,
        page: 1,
        limit: 10,
        hasMore: false,
      }

      mockLaptopService.searchLaptops.mockResolvedValue(mockResult)

      const filters: LaptopSearchFilters = {}

      const { result } = renderHook(
        () => useLaptopSearch(filters, { page: 1, limit: 10 }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockLaptopService.searchLaptops).toHaveBeenCalledWith(
        {},
        { page: 1, limit: 10 }
      )
    })
  })
})
