import { test, expect } from '@playwright/test'

test.describe('Search Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/search')
  })

  test('should display search interface', async ({ page }) => {
    // Check page title
    await expect(page.getByRole('heading', { name: 'Find Your Perfect LLM Laptop' })).toBeVisible()
    
    // Check search input
    await expect(page.getByPlaceholder('Search laptops...')).toBeVisible()
    
    // Check filter buttons
    await expect(page.getByRole('button', { name: 'All' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'Gaming' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'Business' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'Ultrabook' })).toBeVisible()
  })

  test('should display laptop cards with proper information', async ({ page }) => {
    // Wait for laptops to load
    await page.waitForSelector('[data-testid="laptop-card"]', { timeout: 10000 })
    
    // Check that laptop cards are displayed
    const laptopCards = page.locator('[data-testid="laptop-card"]')
    await expect(laptopCards).toHaveCount(3) // Based on mock data
    
    // Check first laptop card content
    const firstCard = laptopCards.first()
    await expect(firstCard.getByText('ASUS ROG Strix G15')).toBeVisible()
    await expect(firstCard.getByText('$1,299')).toBeVisible()
    await expect(firstCard.getByText('AMD Ryzen 7 6800H')).toBeVisible()
    await expect(firstCard.getByText('NVIDIA GeForce RTX 3060')).toBeVisible()
    
    // Check action buttons
    await expect(firstCard.getByRole('button', { name: 'Details' })).toBeVisible()
  })

  test('should handle search functionality', async ({ page }) => {
    const searchInput = page.getByPlaceholder('Search laptops...')
    
    // Type in search input
    await searchInput.fill('ASUS')
    
    // Wait for search results
    await page.waitForTimeout(1000) // Wait for debounce
    
    // Check that search is working (results should be filtered)
    const laptopCards = page.locator('[data-testid="laptop-card"]')
    await expect(laptopCards).toHaveCount(1) // Should show only ASUS laptop
    
    // Clear search
    await searchInput.clear()
    await page.waitForTimeout(1000)
    
    // Should show all laptops again
    await expect(laptopCards).toHaveCount(3)
  })

  test('should handle filter functionality', async ({ page }) => {
    // Wait for laptops to load
    await page.waitForSelector('[data-testid="laptop-card"]', { timeout: 10000 })
    
    // Click Gaming filter
    await page.getByRole('button', { name: 'Gaming' }).click()
    
    // Check that filter is active
    await expect(page.getByRole('button', { name: 'Gaming' })).toHaveClass(/bg-primary/)
    
    // Click All filter to reset
    await page.getByRole('button', { name: 'All' }).click()
    
    // Check that All filter is active
    await expect(page.getByRole('button', { name: 'All' })).toHaveClass(/bg-primary/)
  })

  test('should handle refresh functionality', async ({ page }) => {
    // Wait for laptops to load
    await page.waitForSelector('[data-testid="laptop-card"]', { timeout: 10000 })
    
    // Click refresh button
    await page.getByRole('button', { name: 'Refresh' }).click()
    
    // Check loading state
    await expect(page.getByText('Loading...')).toBeVisible()
    
    // Wait for refresh to complete
    await page.waitForSelector('[data-testid="laptop-card"]', { timeout: 10000 })
    
    // Check that laptops are still displayed
    const laptopCards = page.locator('[data-testid="laptop-card"]')
    await expect(laptopCards).toHaveCount(3)
  })

  test('should navigate to laptop details', async ({ page }) => {
    // Wait for laptops to load
    await page.waitForSelector('[data-testid="laptop-card"]', { timeout: 10000 })
    
    // Click on first laptop's details button
    const firstCard = page.locator('[data-testid="laptop-card"]').first()
    await firstCard.getByRole('button', { name: 'Details' }).click()
    
    // Should navigate to laptop details page
    await expect(page).toHaveURL(/\/laptop\//)
  })

  test('should handle favorites functionality', async ({ page }) => {
    // Wait for laptops to load
    await page.waitForSelector('[data-testid="laptop-card"]', { timeout: 10000 })
    
    // Click favorite button on first laptop
    const firstCard = page.locator('[data-testid="laptop-card"]').first()
    const favoriteButton = firstCard.locator('button').first() // Heart button
    
    await favoriteButton.click()
    
    // Check that favorite button is active (heart filled)
    await expect(favoriteButton.locator('svg')).toHaveClass(/fill-current/)
    
    // Click again to unfavorite
    await favoriteButton.click()
    
    // Check that favorite button is inactive
    await expect(favoriteButton.locator('svg')).not.toHaveClass(/fill-current/)
  })

  test('should handle comparison functionality', async ({ page }) => {
    // Wait for laptops to load
    await page.waitForSelector('[data-testid="laptop-card"]', { timeout: 10000 })
    
    // Add first laptop to comparison
    const firstCard = page.locator('[data-testid="laptop-card"]').first()
    const compareButton = firstCard.locator('button').nth(1) // Compare button
    
    await compareButton.click()
    
    // Check that compare button is active
    await expect(compareButton.locator('svg')).toHaveClass(/text-blue-500/)
    
    // Add second laptop to comparison
    const secondCard = page.locator('[data-testid="laptop-card"]').nth(1)
    await secondCard.locator('button').nth(1).click()
    
    // Navigate to compare page
    await page.goto('/compare')
    
    // Check that laptops are in comparison
    await expect(page.getByText('ASUS ROG Strix G15')).toBeVisible()
    await expect(page.getByText('MacBook Pro 16-inch M2 Pro')).toBeVisible()
  })

  test('should be responsive', async ({ page }) => {
    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 })
    await page.waitForSelector('[data-testid="laptop-card"]', { timeout: 10000 })
    
    // Check grid layout
    const laptopCards = page.locator('[data-testid="laptop-card"]')
    await expect(laptopCards).toHaveCount(3)
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.reload()
    await page.waitForSelector('[data-testid="laptop-card"]', { timeout: 10000 })
    
    // Cards should still be visible
    await expect(laptopCards.first()).toBeVisible()
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 })
    await page.reload()
    await page.waitForSelector('[data-testid="laptop-card"]', { timeout: 10000 })
    
    // Cards should be in single column
    await expect(laptopCards.first()).toBeVisible()
  })
})
