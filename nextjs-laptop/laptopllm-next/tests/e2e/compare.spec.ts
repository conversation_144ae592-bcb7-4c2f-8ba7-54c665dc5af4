import { test, expect } from '@playwright/test'

test.describe('Compare Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/compare')
  })

  test('should display compare interface', async ({ page }) => {
    // Check page title
    await expect(page.getByRole('heading', { name: 'Compare Laptops' })).toBeVisible()
    
    // Check description
    await expect(page.getByText('Compare up to 4 laptops side by side')).toBeVisible()
    
    // Check add laptop button
    await expect(page.getByRole('button', { name: 'Add Laptop' })).toBeVisible()
  })

  test('should display default comparison laptops', async ({ page }) => {
    // Wait for laptops to load
    await page.waitForTimeout(2000)
    
    // Check that default laptops are displayed (from context)
    const laptopCards = page.locator('[data-testid="comparison-laptop"]')
    await expect(laptopCards).toHaveCount(2) // Default comparison has 2 laptops
    
    // Check laptop names
    await expect(page.getByText('ASUS ROG Strix G15')).toBeVisible()
    await expect(page.getByText('MacBook Pro 16-inch M2 Pro')).toBeVisible()
  })

  test('should display comparison table', async ({ page }) => {
    // Wait for laptops to load
    await page.waitForTimeout(2000)
    
    // Check comparison table headers
    await expect(page.getByText('Specifications')).toBeVisible()
    await expect(page.getByText('Performance')).toBeVisible()
    await expect(page.getByText('LLM Compatibility')).toBeVisible()
    
    // Check specification rows
    await expect(page.getByText('CPU')).toBeVisible()
    await expect(page.getByText('GPU')).toBeVisible()
    await expect(page.getByText('RAM')).toBeVisible()
    await expect(page.getByText('Storage')).toBeVisible()
    await expect(page.getByText('Display')).toBeVisible()
    await expect(page.getByText('Price')).toBeVisible()
  })

  test('should handle laptop removal', async ({ page }) => {
    // Wait for laptops to load
    await page.waitForTimeout(2000)
    
    // Check initial count
    const laptopCards = page.locator('[data-testid="comparison-laptop"]')
    await expect(laptopCards).toHaveCount(2)
    
    // Remove first laptop
    const removeButton = page.locator('[data-testid="remove-laptop"]').first()
    await removeButton.click()
    
    // Check that laptop was removed
    await expect(laptopCards).toHaveCount(1)
    
    // Check that only one laptop remains
    await expect(page.getByText('MacBook Pro 16-inch M2 Pro')).toBeVisible()
    await expect(page.getByText('ASUS ROG Strix G15')).not.toBeVisible()
  })

  test('should handle adding laptops', async ({ page }) => {
    // Wait for initial load
    await page.waitForTimeout(2000)
    
    // Click add laptop button
    await page.getByRole('button', { name: 'Add Laptop' }).click()
    
    // Check that search modal opens
    await expect(page.getByText('Add Laptop to Comparison')).toBeVisible()
    await expect(page.getByPlaceholder('Search laptops...')).toBeVisible()
    
    // Search for a laptop
    const searchInput = page.getByPlaceholder('Search laptops...')
    await searchInput.fill('Dell')
    
    // Wait for search results
    await page.waitForTimeout(1000)
    
    // Check that search results are displayed
    await expect(page.getByText('Dell XPS 15 9520')).toBeVisible()
    
    // Add laptop to comparison
    await page.getByText('Dell XPS 15 9520').click()
    
    // Check that modal closes and laptop is added
    await expect(page.getByText('Add Laptop to Comparison')).not.toBeVisible()
    
    // Check that laptop was added to comparison
    const laptopCards = page.locator('[data-testid="comparison-laptop"]')
    await expect(laptopCards).toHaveCount(3)
    await expect(page.getByText('Dell XPS 15 9520')).toBeVisible()
  })

  test('should handle search in add laptop modal', async ({ page }) => {
    // Wait for initial load
    await page.waitForTimeout(2000)
    
    // Open add laptop modal
    await page.getByRole('button', { name: 'Add Laptop' }).click()
    
    // Test search functionality
    const searchInput = page.getByPlaceholder('Search laptops...')
    
    // Search for specific brand
    await searchInput.fill('Apple')
    await page.waitForTimeout(1000)
    
    // Should show Apple laptops
    await expect(page.getByText('MacBook')).toBeVisible()
    
    // Clear search
    await searchInput.clear()
    await searchInput.fill('Gaming')
    await page.waitForTimeout(1000)
    
    // Should show gaming laptops
    await expect(page.getByText('ROG')).toBeVisible()
    
    // Close modal
    await page.getByRole('button', { name: 'Cancel' }).click()
    await expect(page.getByText('Add Laptop to Comparison')).not.toBeVisible()
  })

  test('should display compatibility scores', async ({ page }) => {
    // Wait for laptops to load
    await page.waitForTimeout(2000)
    
    // Check compatibility section
    await expect(page.getByText('LLM Compatibility')).toBeVisible()
    
    // Check compatibility scores
    await expect(page.getByText('78%')).toBeVisible() // ASUS score
    await expect(page.getByText('85%')).toBeVisible() // MacBook score
    
    // Check compatibility labels
    await expect(page.getByText('Good')).toBeVisible()
    await expect(page.getByText('Excellent')).toBeVisible()
  })

  test('should display performance metrics', async ({ page }) => {
    // Wait for laptops to load
    await page.waitForTimeout(2000)
    
    // Check performance section
    await expect(page.getByText('Performance')).toBeVisible()
    
    // Check performance metrics
    await expect(page.getByText('42 tokens/sec')).toBeVisible()
    await expect(page.getByText('38 tokens/sec')).toBeVisible()
    
    // Check memory usage
    await expect(page.getByText('14 GB')).toBeVisible()
    await expect(page.getByText('12 GB')).toBeVisible()
    
    // Check power consumption
    await expect(page.getByText('165W')).toBeVisible()
    await expect(page.getByText('85W')).toBeVisible()
  })

  test('should handle maximum laptop limit', async ({ page }) => {
    // Wait for initial load
    await page.waitForTimeout(2000)
    
    // Add laptops until we reach the limit (4 laptops)
    for (let i = 0; i < 2; i++) {
      await page.getByRole('button', { name: 'Add Laptop' }).click()
      await page.waitForTimeout(500)
      
      // Add first available laptop
      const searchResults = page.locator('[data-testid="search-result"]').first()
      await searchResults.click()
      await page.waitForTimeout(500)
    }
    
    // Check that we have 4 laptops
    const laptopCards = page.locator('[data-testid="comparison-laptop"]')
    await expect(laptopCards).toHaveCount(4)
    
    // Add laptop button should be disabled
    await expect(page.getByRole('button', { name: 'Add Laptop' })).toBeDisabled()
  })

  test('should be responsive', async ({ page }) => {
    // Wait for laptops to load
    await page.waitForTimeout(2000)
    
    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 })
    
    // Check that comparison table is visible
    await expect(page.getByText('Specifications')).toBeVisible()
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.reload()
    await page.waitForTimeout(2000)
    
    // Comparison should still be functional
    await expect(page.getByRole('heading', { name: 'Compare Laptops' })).toBeVisible()
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 })
    await page.reload()
    await page.waitForTimeout(2000)
    
    // Should show mobile-optimized comparison
    await expect(page.getByRole('heading', { name: 'Compare Laptops' })).toBeVisible()
  })

  test('should handle empty comparison state', async ({ page }) => {
    // Remove all laptops from comparison
    await page.waitForTimeout(2000)
    
    // Remove both default laptops
    const removeButtons = page.locator('[data-testid="remove-laptop"]')
    const count = await removeButtons.count()
    
    for (let i = 0; i < count; i++) {
      await removeButtons.first().click()
      await page.waitForTimeout(500)
    }
    
    // Check empty state
    await expect(page.getByText('No laptops selected for comparison')).toBeVisible()
    await expect(page.getByText('Add laptops to start comparing')).toBeVisible()
    
    // Add laptop button should still be available
    await expect(page.getByRole('button', { name: 'Add Laptop' })).toBeVisible()
  })
})
