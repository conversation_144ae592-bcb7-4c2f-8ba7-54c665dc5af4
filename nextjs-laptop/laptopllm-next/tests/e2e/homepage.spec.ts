import { test, expect } from '@playwright/test'

test.describe('Homepage', () => {
  test('should display the main heading and description', async ({ page }) => {
    await page.goto('/')

    // Check main heading
    await expect(page.getByRole('heading', { name: 'LaptopLLM Finder' })).toBeVisible()
    
    // Check description
    await expect(page.getByText('Find the perfect laptop for running Large Language Models locally')).toBeVisible()
    
    // Check Get Started button
    await expect(page.getByRole('button', { name: 'Get Started' })).toBeVisible()
  })

  test('should display laptop cards', async ({ page }) => {
    await page.goto('/')

    // Check that laptop cards are displayed
    await expect(page.getByText('Dell XPS 15')).toBeVisible()
    await expect(page.getByText('MacBook Pro 16"')).toBeVisible()
    await expect(page.getByText('ASUS ROG Strix')).toBeVisible()

    // Check compatibility badges
    await expect(page.getByText('Excellent')).toBeVisible()
    await expect(page.getByText('Good')).toBeVisible()
    await expect(page.getByText('Fair')).toBeVisible()
  })

  test('should display features section', async ({ page }) => {
    await page.goto('/')

    // Check features section heading
    await expect(page.getByRole('heading', { name: 'Why Choose LaptopLLM Finder?' })).toBeVisible()

    // Check feature cards
    await expect(page.getByText('AI Compatibility Analysis')).toBeVisible()
    await expect(page.getByText('Real-time Price Tracking')).toBeVisible()
    await expect(page.getByText('Detailed Specifications')).toBeVisible()
  })

  test('should have responsive design', async ({ page }) => {
    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 })
    await page.goto('/')
    
    // Check that cards are in a grid layout
    const laptopCards = page.locator('.laptop-card')
    await expect(laptopCards).toHaveCount(3)

    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 })
    await page.reload()
    
    // Cards should still be visible but in a single column
    await expect(laptopCards.first()).toBeVisible()
  })

  test('should have proper meta tags', async ({ page }) => {
    await page.goto('/')

    // Check page title
    await expect(page).toHaveTitle(/LaptopLLM Finder/)
    
    // Check meta description
    const metaDescription = page.locator('meta[name="description"]')
    await expect(metaDescription).toHaveAttribute('content', /Discover and compare laptops optimized for running Large Language Models/)
  })
})
