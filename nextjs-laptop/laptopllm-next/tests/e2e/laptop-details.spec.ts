import { test, expect } from '@playwright/test'

test.describe('Laptop Details Page', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to a specific laptop details page
    await page.goto('/laptop/asus-rog-strix-g15')
  })

  test('should display laptop details', async ({ page }) => {
    // Wait for laptop data to load
    await page.waitForTimeout(2000)
    
    // Check laptop name
    await expect(page.getByRole('heading', { name: 'ASUS ROG Strix G15' })).toBeVisible()
    
    // Check price
    await expect(page.getByText('$1,299')).toBeVisible()
    
    // Check main specifications
    await expect(page.getByText('AMD Ryzen 7 6800H')).toBeVisible()
    await expect(page.getByText('NVIDIA GeForce RTX 3060')).toBeVisible()
    await expect(page.getByText('16 GB DDR4')).toBeVisible()
    await expect(page.getByText('512 GB SSD')).toBeVisible()
  })

  test('should display compatibility score', async ({ page }) => {
    // Wait for compatibility data to load
    await page.waitForTimeout(2000)
    
    // Check compatibility section
    await expect(page.getByText('LLM Compatibility Score')).toBeVisible()
    
    // Check compatibility score
    await expect(page.getByText('78%')).toBeVisible()
    await expect(page.getByText('Good')).toBeVisible()
    
    // Check compatibility details
    await expect(page.getByText('Performance Score')).toBeVisible()
    await expect(page.getByText('Memory Score')).toBeVisible()
    await expect(page.getByText('Efficiency Score')).toBeVisible()
  })

  test('should display detailed specifications', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(2000)
    
    // Check specifications section
    await expect(page.getByText('Detailed Specifications')).toBeVisible()
    
    // Check processor details
    await expect(page.getByText('Processor')).toBeVisible()
    await expect(page.getByText('8 cores, 16 threads')).toBeVisible()
    await expect(page.getByText('3.2 GHz base, 4.7 GHz boost')).toBeVisible()
    
    // Check graphics details
    await expect(page.getByText('Graphics')).toBeVisible()
    await expect(page.getByText('6 GB GDDR6')).toBeVisible()
    
    // Check memory details
    await expect(page.getByText('Memory')).toBeVisible()
    await expect(page.getByText('3200 MHz')).toBeVisible()
    
    // Check storage details
    await expect(page.getByText('Storage')).toBeVisible()
    await expect(page.getByText('NVMe PCIe 3.0')).toBeVisible()
    
    // Check display details
    await expect(page.getByText('Display')).toBeVisible()
    await expect(page.getByText('15.6" FHD')).toBeVisible()
    await expect(page.getByText('144 Hz')).toBeVisible()
  })

  test('should display performance metrics', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(2000)
    
    // Check performance section
    await expect(page.getByText('Performance Metrics')).toBeVisible()
    
    // Check LLM performance
    await expect(page.getByText('LLM Performance')).toBeVisible()
    await expect(page.getByText('42 tokens/sec')).toBeVisible()
    await expect(page.getByText('14 GB memory usage')).toBeVisible()
    
    // Check gaming performance
    await expect(page.getByText('Gaming Performance')).toBeVisible()
    await expect(page.getByText('High settings')).toBeVisible()
    
    // Check power consumption
    await expect(page.getByText('Power Consumption')).toBeVisible()
    await expect(page.getByText('165W')).toBeVisible()
  })

  test('should handle favorite functionality', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(2000)
    
    // Find favorite button
    const favoriteButton = page.getByRole('button', { name: 'Add to Favorites' })
    await expect(favoriteButton).toBeVisible()
    
    // Click favorite button
    await favoriteButton.click()
    
    // Check that button text changes
    await expect(page.getByRole('button', { name: 'Remove from Favorites' })).toBeVisible()
    
    // Click again to unfavorite
    await page.getByRole('button', { name: 'Remove from Favorites' }).click()
    
    // Check that button text changes back
    await expect(page.getByRole('button', { name: 'Add to Favorites' })).toBeVisible()
  })

  test('should handle comparison functionality', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(2000)
    
    // Find compare button
    const compareButton = page.getByRole('button', { name: 'Add to Compare' })
    await expect(compareButton).toBeVisible()
    
    // Click compare button
    await compareButton.click()
    
    // Check that button text changes
    await expect(page.getByRole('button', { name: 'Remove from Compare' })).toBeVisible()
    
    // Click again to remove from comparison
    await page.getByRole('button', { name: 'Remove from Compare' }).click()
    
    // Check that button text changes back
    await expect(page.getByRole('button', { name: 'Add to Compare' })).toBeVisible()
  })

  test('should display recommended models', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(2000)
    
    // Check recommended models section
    await expect(page.getByText('Recommended LLM Models')).toBeVisible()
    
    // Check model recommendations
    await expect(page.getByText('Llama 2 7B')).toBeVisible()
    await expect(page.getByText('Mistral 7B')).toBeVisible()
    await expect(page.getByText('CodeLlama 7B')).toBeVisible()
    
    // Check performance indicators
    await expect(page.getByText('Excellent')).toBeVisible()
    await expect(page.getByText('Good')).toBeVisible()
  })

  test('should display similar laptops', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(2000)
    
    // Check similar laptops section
    await expect(page.getByText('Similar Laptops')).toBeVisible()
    
    // Check that similar laptops are displayed
    const similarLaptops = page.locator('[data-testid="similar-laptop"]')
    await expect(similarLaptops).toHaveCount(3)
    
    // Check that similar laptops have basic info
    await expect(page.getByText('MacBook Pro 16-inch M2 Pro')).toBeVisible()
    await expect(page.getByText('Dell XPS 15 9520')).toBeVisible()
  })

  test('should handle navigation', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(2000)
    
    // Check back button
    const backButton = page.getByRole('button', { name: 'Back to Search' })
    await expect(backButton).toBeVisible()
    
    // Click back button
    await backButton.click()
    
    // Should navigate back to search page
    await expect(page).toHaveURL('/search')
  })

  test('should display price and availability', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(2000)
    
    // Check price section
    await expect(page.getByText('Price & Availability')).toBeVisible()
    await expect(page.getByText('$1,299')).toBeVisible()
    
    // Check availability status
    await expect(page.getByText('In Stock')).toBeVisible()
    
    // Check store links
    await expect(page.getByText('View on Amazon')).toBeVisible()
    await expect(page.getByText('View on Best Buy')).toBeVisible()
  })

  test('should be responsive', async ({ page }) => {
    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 })
    await page.waitForTimeout(2000)
    
    // Check that all sections are visible
    await expect(page.getByRole('heading', { name: 'ASUS ROG Strix G15' })).toBeVisible()
    await expect(page.getByText('Detailed Specifications')).toBeVisible()
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.reload()
    await page.waitForTimeout(2000)
    
    // Content should still be accessible
    await expect(page.getByRole('heading', { name: 'ASUS ROG Strix G15' })).toBeVisible()
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 })
    await page.reload()
    await page.waitForTimeout(2000)
    
    // Should show mobile-optimized layout
    await expect(page.getByRole('heading', { name: 'ASUS ROG Strix G15' })).toBeVisible()
    
    // Action buttons should be stacked
    await expect(page.getByRole('button', { name: 'Add to Favorites' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'Add to Compare' })).toBeVisible()
  })

  test('should handle loading states', async ({ page }) => {
    // Navigate to page and check loading state
    await page.goto('/laptop/asus-rog-strix-g15')
    
    // Should show loading state initially
    await expect(page.getByText('Loading...')).toBeVisible()
    
    // Wait for content to load
    await page.waitForTimeout(2000)
    
    // Loading should be gone and content visible
    await expect(page.getByText('Loading...')).not.toBeVisible()
    await expect(page.getByRole('heading', { name: 'ASUS ROG Strix G15' })).toBeVisible()
  })

  test('should handle error states', async ({ page }) => {
    // Navigate to non-existent laptop
    await page.goto('/laptop/non-existent-laptop')
    
    // Wait for error handling
    await page.waitForTimeout(2000)
    
    // Should show error message
    await expect(page.getByText('Laptop not found')).toBeVisible()
    await expect(page.getByText('The laptop you are looking for does not exist')).toBeVisible()
    
    // Should have link back to search
    await expect(page.getByRole('link', { name: 'Back to Search' })).toBeVisible()
  })
})
