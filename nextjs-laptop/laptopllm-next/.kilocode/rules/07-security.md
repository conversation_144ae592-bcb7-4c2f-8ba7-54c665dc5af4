# 07 - Seguridad

## Manejo de Credenciales

*   **Variables de Entorno**: Almacenar claves API, secretos de base de datos y otras credenciales sensibles en variables de entorno.
*   **No Hardcodear**: Nunca hardcodear credenciales directamente en el código fuente.
*   **Gestores de Secretos**: Considerar el uso de gestores de secretos (ej. AWS Secrets Manager, HashiCorp Vault) en entornos de producción.

## Validación de Entradas

*   **Sanitización y Validación**: Validar y sanitizar todas las entradas de usuario para prevenir ataques como inyección SQL, XSS (Cross-Site Scripting) y CSRF (Cross-Site Request Forgery).
*   **Validación en Frontend y Backend**: Realizar validación tanto en el lado del cliente (para UX) como en el lado del servidor (para seguridad).

## Autenticación y Autorización

*   **Uso de Librerías Seguras**: Implementar autenticación y autorización utilizando librerías y frameworks probados y seguros (ej. Passport.js, NextAuth.js).
*   **Tokens JWT**: Si se usan tokens JWT, asegurar que se validen correctamente (firma, expiración) y se almacenen de forma segura (ej. HttpOnly cookies).
*   **Control de Acceso Basado en Roles (RBAC)**: Implementar RBAC para asegurar que los usuarios solo puedan acceder a los recursos y funcionalidades para los que tienen permiso.

## Dependencias

*   **Auditoría de Dependencias**: Auditar regularmente las dependencias del proyecto en busca de vulnerabilidades conocidas (ej. `npm audit`, Snyk, Dependabot).
*   **Actualización de Dependencias**: Mantener las dependencias actualizadas para beneficiarse de los parches de seguridad.

## Logging y Monitoreo de Seguridad

*   **Registro de Eventos de Seguridad**: Registrar eventos relevantes de seguridad (ej. intentos de inicio de sesión fallidos, cambios de permisos).
*   **Monitoreo de Anomalías**: Implementar monitoreo para detectar actividades sospechosas o patrones de ataque.

## No negociable:

*   **Input validation y sanitization**
*   **HTTPS enforcement**
*   **CSP (Content Security Policy)**
*   **Authentication/Authorization patterns**
*   **Secrets management**
*   **OWASP Top 10 compliance**
