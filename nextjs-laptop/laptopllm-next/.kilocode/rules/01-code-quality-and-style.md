# 01 - Calidad de Código y Estilo

## Convenciones de Nomenclatura

*   **Variables y Funciones**: Usar `camelCase` (ej. `userName`, `getUserData`).
*   **Clases y Componentes React**: Usar `PascalCase` (ej. `UserCard`, `AppLayout`).
*   **Constantes Globales**: Usar `SCREAMING_SNAKE_CASE` (ej. `API_BASE_URL`).
*   **Archivos**: Usar `kebab-case` para archivos de componentes y módulos (ej. `user-card.tsx`, `api-service.ts`).

## Formato de Código

*   **ESLint y Prettier**: Configurar y usar ESLint y Prettier para mantener un formato de código consistente. Asegurarse de que las reglas estén integradas en el flujo de trabajo de desarrollo (ej. pre-commit hooks).
*   **Indentación**: Usar 2 espacios para la indentación en archivos JavaScript/TypeScript y JSX/TSX.
*   **Longitud de Línea**: Limitar la longitud de línea a 100-120 caracteres para mejorar la legibilidad.
*   **Comillas**: Preferir comillas simples (`'`) para cadenas de texto, a menos que se necesiten comillas dobles por interpolación o JSX.

## Legibilidad

*   **Comentarios**: Escribir comentarios claros y concisos para explicar lógica compleja, decisiones de diseño no obvias o secciones de código que puedan ser difíciles de entender.
*   **Docstrings (JSDoc/TSDoc)**: Documentar funciones, clases, interfaces y tipos con JSDoc/TSDoc para describir su propósito, parámetros, valores de retorno y cualquier efecto secundario.
*   **Claridad en la Lógica**: Escribir código que sea autoexplicativo siempre que sea posible. Evitar la lógica excesivamente anidada o las expresiones demasiado complejas. Refactorizar funciones largas en funciones más pequeñas y con un solo propósito.

## Principios SOLID

*   **Principio de Responsabilidad Única (SRP)**: Cada módulo, clase o función debe tener una única razón para cambiar. Esto significa que debe tener una sola responsabilidad bien definida.
*   **Principio Abierto/Cerrado (OCP)**: Las entidades de software (clases, módulos, funciones, etc.) deben estar abiertas para extensión, pero cerradas para modificación. Esto se logra a menudo mediante el uso de interfaces y abstracciones.
*   **Principio de Sustitución de Liskov (LSP)**: Los objetos de un programa deben ser reemplazables por instancias de sus subtipos sin alterar la corrección de ese programa.
*   **Principio de Segregación de Interfaces (ISP)**: Los clientes no deben ser forzados a depender de interfaces que no utilizan. Es mejor tener muchas interfaces pequeñas y específicas que una grande y general.
*   **Principio de Inversión de Dependencias (DIP)**: Los módulos de alto nivel no deben depender de módulos de bajo nivel. Ambos deben depender de abstracciones. Las abstracciones no deben depender de los detalles; los detalles deben depender de las abstracciones.

## Elementos Críticos Adicionales

*   **TypeScript Strict Mode**: Always use strict typing; avoid using global variables and `any`.
*   **Límites de Complejidad Ciclomática**: Establecer límites para la complejidad ciclomática de funciones y métodos para mejorar la mantenibilidad.
*   **Reglas de Imports**: Definir reglas consistentes para las rutas de importación (absolutas vs. relativas).
*   **Consistent Return Statements**: Asegurar que las funciones tengan retornos consistentes.
*   **Error Handling Patterns**: Implementar patrones de manejo de errores uniformes en toda la aplicación.
