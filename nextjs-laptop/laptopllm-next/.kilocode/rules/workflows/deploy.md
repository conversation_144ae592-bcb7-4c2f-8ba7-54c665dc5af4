# Deployment Workflow

Ejecuta los siguientes pasos para hacer deployment:

1. **Verificar estado del código:**
   ```bash
   git status
   pnpm run lint
   pnpm run type-check
   pnpm run test
   ```

2. **Build del proyecto:**
   ```bash
   pnpm run build
   ```

3. **Verificar que el build funciona:**
   ```bash
   pnpm run preview
   ```

4. **Hacer commit si hay cambios:**
   ```bash
   git add .
   git commit -m "chore: deployment build"
   ```

5. **Push a main (activa deploy automático):**
   ```bash
   git push origin main
   ```

6. **Verificar deployment:**
   - Abrir la URL de producción
   - Verificar que todo funciona correctamente
   - Revisar logs de deployment si es necesario
