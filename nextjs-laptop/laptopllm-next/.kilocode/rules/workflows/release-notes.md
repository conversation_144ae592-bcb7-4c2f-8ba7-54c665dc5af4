# Generar Release Notes

1. **Obt<PERSON> commits desde último release:**
   ```bash
   git log --oneline $(git describe --tags --abbrev=0)..HEAD
   ```

2. **Categorizar cambios:**
   - Features nuevas
   - Bug fixes
   - Performance improvements
   - Breaking changes

3. **Generar changelog:**
   ```bash
   npx conventional-changelog -p angular -i CHANGELOG.md -s
   ```

4. **Crear release en GitHub:**
   ```bash
   gh release create v[VERSION] --title "Release v[VERSION]" --notes-file release-notes.md
   ```
