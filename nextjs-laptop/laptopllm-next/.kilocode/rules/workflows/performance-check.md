# Performance Check

1. **Analizar bundle size:**
   ```bash
   pnpm run build
   pnpm run analyze
   ```

2. **Lighthouse audit:**
   ```bash
   npx lighthouse http://localhost:3000 --output=html
   ```

3. **Medir Core Web Vitals:**
   - Verificar LCP, FID, CLS
   - Identificar bottlenecks
   - Sugerir optimizaciones

4. **Generar reporte de optimización:**
   - Componentes pesados
   - Oportunidades de lazy loading
